#include "FFSTransmission/Utility/NetworkHelper.h"
#include "FFSTransmission/FFSTransmissionInterfaces.h"
#include "FFSTransmission/Utility/SettingManager.h"
#include "FFSTransmission/Utility/Singleton.hpp"

namespace cross::ffs
{

bool SettingsManager::Initialize()
{
    auto re = LoadFromFile(::GetVSDConfigFilePath(), true);
    if (re == false)
        return re;

#if TRANSMISSION_FOR_VSD
    
#else
    re = LoadFromFile(::GetIGConfigFilePath(), true);
    if (re == false)
        return re;
#endif

    std::optional<SerializeNode> result;
    result = HasSettingOption("Sim.Mock.Documents");   
    if (result)
    {
        SerializeNode& documentsNode = *result;
        for (int index = 0; index < documentsNode.Size(); index++)
        {
            mMockDocuments.emplace_back(documentsNode[index].AsString());
        }
    }

    return re;
}

std::optional<SerializeNode> SettingsManager::LoadConfigFile(const std::string& fileName)
{
    std::string fileContent;

    FILE* fp = fopen(fileName.c_str(), "rb");
    if (fp == nullptr)
    {
        LOG_ERROR("Fail to load config file path: {}", fileName);
        // Assert(0);
        return {};
    }

    int32_t fileLength = 0;
    fseek(fp, 0, SEEK_END);
    fileLength = static_cast<int32_t>(ftell(fp));
    Assert(fileLength > 0);
    fseek(fp, 0, SEEK_SET);

    fileContent.resize(fileLength);
    fread(fileContent.data(), fileLength, 1, fp);

    fclose(fp);

    bool success = false;
    SerializeNode node = SerializeNode::ParseFromJson(fileContent, &success);
    if (!success)
    {
        LOG_ERROR("Parse config file failed. Path: \"{}\".", fileName);
        return {};
    }

    return node;
}

bool SettingsManager::LoadFromFile(std::string inPath, bool keepLeft /*= true*/)
{
    const auto& absoluteJsonPath = inPath;
    if (absoluteJsonPath.size() == 0)
        return false;

    mConfig = LoadConfigFile(absoluteJsonPath);
    ExtractValue("", mConfig.value());

    return true;
}

void SettingsManager::ExtractValue(const std::string& PrevKey, const DeserializeNode& node)
{
    for (auto it = node.begin(); it != node.end(); it++)
    {
        std::string key = it.Key().data();
        key = key.substr(0, it.Key().size());
        auto value = it.Value();

        key = PrevKey.empty() ? key : PrevKey + "." + key;

        if (value.IsObject())
        {
            ExtractValue(key, value);
            continue;
        }

        if (value.IsArray())
        {
            if (value.Size() == 4 && value[0].IsNumber())
            {
                Float4 arrf4;
                arrf4.x = value[0].As<float>();
                arrf4.y = value[1].As<float>();
                arrf4.z = value[2].As<float>();
                arrf4.w = value[3].As<float>();
                mContext[key] = arrf4;
            }
            else if (value.Size() == 3 && value[0].IsNumber())
            {
                Float3 arrf3;
                arrf3.x = value[0].As<float>();
                arrf3.y = value[1].As<float>();
                arrf3.z = value[2].As<float>();
                mContext[key] = arrf3;
            }
            else if (value.Size() == 2 && value[0].IsNumber())
            {
                Float2 arrf2;
                arrf2.x = value[0].As<float>();
                arrf2.y = value[1].As<float>();
                mContext[key] = arrf2;
            }
        }
        else if (value.IsBoolean())
        {
            mContext[key] = value.AsBoolean();
        }
        else if (value.IsString())
        {
            mContext[key] = value.AsString();
        }
        else if (value.IsFloatingPoint())
        {
            mContext[key] = value.AsFloat();
        }
        else if (value.IsIntegral())
        {
            mContext[key] = value.AsInt32();
        }
    }
}

std::optional<SerializeNode> SettingsManager::HasSettingOption(const std::string& inName)
{
    return HasSettingOption(mConfig, inName);
}

std::optional<SerializeNode> SettingsManager::HasSettingOption(std::optional<SerializeNode>& node, const std::string& inName)
{
    std::optional<SerializeNode> result;
    if (node.has_value())
    {
        size_t pos = inName.find(".");
        std::string current = inName.substr(0, pos);
        std::string remains = (pos == std::string::npos ? "" : inName.substr(pos + 1));
        if (result = node->HasMember(current); result)
        {
            if (remains != "")
                return HasSettingOption(result, remains);
            else
                return result;
        }
    }

    return {};
}

}