#include "FFSTransmission/Tbuspp/PacketHandlers/FFSCommonPackets.h"

namespace cross::ffs
{

bool FFSCommandPacketContext::IsAdaptive(ce::net::FullFlightSimulatorPacket_EnumFFSCommand inOp)
{
    return inOp == ce::net::FullFlightSimulatorPacket_EnumFFSCommand_CommandControlRQ || inOp == ce::net::FullFlightSimulatorPacket_EnumFFSCommand_CommandControlRP;
}

bool FFSCommandPacketContext::OnHandleMsg(const SInt8* inBuffer, SInt32 inBufferLength, const ce::net::RouteInfo& inSource, const ce::net::RouteInfo& inDestation, UInt32 msgNum)
{
    if (!mMessage.ParseFromArray(inBuffer, inBufferLength))
    {
        auto descriptor_target = ce::net::EnumProcessType_descriptor();
        auto source_str = descriptor_target->FindValueByNumber(inSource.process())->name().c_str();

        LOG_WARN("FFSRTSPacketContext parsed failed which message from {}, your protocol probably outof date", source_str);
        return false;
    }

    ce::net::CommandPacket_EnumCommandMessage msgType = mMessage.message();
    if (msgType == ce::net::CommandPacket_EnumCommandMessage::CommandPacket_EnumCommandMessage_Dummy)
        return false;
   
    // Grab a valid sub layer handler, execute immediately
    if (mCommandHandlers.find(msgType) != mCommandHandlers.end())
    {
        auto handler = mCommandHandlers.at(msgType).get();
        handler->OnHandleMsg(reinterpret_cast<const SInt8*>(mMessage.content().data()), static_cast<SInt32>(mMessage.content().length()), inSource, inDestation, msgNum);
        return true;
    }

    auto descriptor_target = ce::net::CommandPacket_EnumCommandMessage_descriptor();
    auto msg_str = descriptor_target->FindValueByNumber(msgType)->name().c_str();
    LOG_WARN("FFSCommandPacketContext find handler failed which message type = {}, your protocol probably outof date", msg_str);
    return false;
}
bool FFSCommandPacketContext::OnSyncingMsg()
{
    return true;
}
}