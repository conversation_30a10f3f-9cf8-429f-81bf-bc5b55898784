#include "PacketContainer.h"

#include <google/protobuf/util/json_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/text_format.h>

namespace fs = std::filesystem;
namespace cross
{
auto GetFirstAndLastPakcet(const std::string& filepath)
{
    using namespace google::protobuf::util;

    //std::ifstream fileIn(filepath);
    std::string firstLine = "", lastLine = "";
    //std::getline(fileIn, firstLine);

    //fileIn.seekg(-2, std::ios::end);   // skip EOF and the last '\n'
    //for (char ch = ' '; ch != '\n'; fileIn.get(ch))
    //{
    //    fileIn.seekg(-2, std::ios::cur);
    //    if (static_cast<int>(fileIn.tellg()) <= 0)
    //    {
    //        fileIn.seekg(0);
    //        break;
    //    }
    //}
    //std::getline(fileIn, lastLine);
    //fileIn.close();

    Status status;
    FfsPacketType firstPacket, lastPacket;
    status = JsonStringToMessage(firstLine, &firstPacket);
    AssertMsg(status.ok(), "can not deserialize packet {}", firstLine);
    status = JsonStringToMessage(lastLine, &lastPacket);
    AssertMsg(status.ok(), "can not deserialize packet {}", lastLine);

    return std::make_pair(firstPacket, lastPacket);
}

PacketBlock::PacketBlock(const std::string& path)
{
    auto [firstPacket, lastPacket] = GetFirstAndLastPakcet(path);
    //StartTime = firstPacket.send_time_us();
    //EndTime = lastPacket.send_time_us();
    FilePath = path;

    mTask = nullptr;
    mIsFinish = false;
}

PacketBlock::~PacketBlock() {}

void PacketBlock::Load()
{
    using namespace google::protobuf::util;
    //if (!mIsFinish)
    //{
    //    std::ifstream fileIn(FilePath);
    //    for (std::string line; std::getline(fileIn, line);)
    //    {
    //        FfsPacketType packet;
    //        auto status = JsonStringToMessage(line, &packet);
    //        if (status.ok())
    //        {
    //            mPackets.push_back(packet);
    //        }
    //    }
    //    fileIn.close();
    //    mIsFinish = true;
    //}
}

void PacketBlock::LoadAsync()
{
    if (!mTask)
    {
        mTask = threading::Dispatch([this](const auto&) { Load(); });
        // mTask = threading::Async([this](const auto&) { Load(); });
    }
}

void PacketBlock::WaitForLoading()
{
    //if (IsLoading() && !IsFinish())
    //{
    //    // mTask->WaitForCompletion();
    //    while (!mTask->IsCompleted())
    //    {
    //        Sleep(10);
    //    }
    //}
}

// PacketContainer
PacketContainer::PacketContainer() {}

PacketContainer::PacketContainer(const std::string& directory)
{
    std::set<std::string> sortedPaths;   // sorted by filename
    //for (const auto& entry : fs::directory_iterator(directory))
    //{
    //    const auto& path = entry.path();
    //    if (path.extension().string() == ".log")
    //    {
    //        sortedPaths.insert(path.string());
    //    }
    //}

    for (const auto& path : sortedPaths)
    {
        mPacketBlockList.push_back(PacketBlock(path));
    }

    if (!mPacketBlockList.empty())
    {
        mPacketBlockList.front().LoadAsync();
        mStartSendTime = mPacketBlockList.front().StartTime;
        mEndSendTime = mPacketBlockList.back().EndTime;
    }
    else
    {
        mStartSendTime = 0;
        mEndSendTime = 0;
    }
}

PacketContainer::~PacketContainer() {}

UInt64 PacketContainer::Duration() const
{
    return mEndSendTime - mStartSendTime;
}

FfsPacketList PacketContainer::GetPackets(UInt32 startTime, UInt32 endTime)
{
    bool needReverse = false;
    if (startTime > endTime)
    {
        std::swap(startTime, endTime);
        needReverse = true;
    }

    UInt64 st = static_cast<UInt64>(startTime) * 1000 + mStartSendTime;
    //UInt64 et = std::min(static_cast<UInt64>(endTime) * 1000 + mStartSendTime, mEndSendTime);

    auto blockItr = std::lower_bound(mPacketBlockList.begin(), mPacketBlockList.end(), st, [](const PacketBlock& block, UInt64 time) { return block.EndTime < time; });

    std::vector<FfsPacketType> ret;
    /*while (st < et)
    {
        if (!blockItr->IsFinish())
        {
            if (blockItr->IsLoading())
                blockItr->WaitForLoading();
            else
                blockItr->Load();
        }
        auto packetItr = blockItr->mPackets.begin();
        if (packetItr->send_time_us() < st)
        {
            packetItr = std::lower_bound(blockItr->mPackets.begin(), blockItr->mPackets.end(), st, [](const FfsPacketType& packet, UInt64 time) { return packet.send_time_us() < time; });
        }
        for (; packetItr != blockItr->mPackets.end() && st < et; packetItr++)
        {
            st = packetItr->send_time_us();
            ret.push_back(*packetItr);
        }

        blockItr++;
    }*/

    for (int k = 0; k < 3 && blockItr != mPacketBlockList.end(); blockItr++, k++)
    {
        blockItr->LoadAsync();
    }
    if (needReverse)
    {
        std::reverse(ret.begin(), ret.end());
    }
    return ret;
}
}   // namespace cross