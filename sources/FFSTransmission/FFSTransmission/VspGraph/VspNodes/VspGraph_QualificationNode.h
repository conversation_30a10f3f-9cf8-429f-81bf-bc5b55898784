#pragma once
#include "FFSTransmission/Utility/Singleton.h"
#include "FFSTransmission/VspGraph/VspStructs/VspGraph_QualificationView.h"
#include "FFSTransmission/VspGraph/VspNodes/VspGraph_BaseNode.h"

namespace cross::ffs 
{
class Transmission_API VspGraph_QualificationNode 
    : public VspGraph_BaseNodeT<QUALIFICATION_V, ce::net::EnumVSPView::QualificationView>, 
    public Singleton<VspGraph_QualificationNode>
{
    friend class Singleton<VspGraph_QualificationNode>;
    VspGraph_QualificationNode() = default;
    virtual ~VspGraph_QualificationNode() = default;

    
    //// Pipeline Interface Begin
    ////
protected:
    virtual void PreAssemble(const VspgraphPreAssembleContext& inContext) override;
    virtual void OnInitialize(const VspgraphInitializeContext& inContext) override;
    virtual void OnResolve_Syncing(const VspgraphResolveContext& inContext) override;
    virtual void OnUpdate_Sampling(const VspgraphUpdateContext& inContext) override;

    //// Pipeline Interface End
    ////
public:
};
using QUALIFICATION_N = cross::ffs::VspGraph_QualificationNode;

}   // namespace cross::anim
