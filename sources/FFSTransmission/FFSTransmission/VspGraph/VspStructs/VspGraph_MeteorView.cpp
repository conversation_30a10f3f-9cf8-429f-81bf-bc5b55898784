#include "FFSTransmission/VspGraph/VspStructs/VspGraph_MeteorView.h"

namespace cross::ffs 
{

bool VspGraph_MeteorView_LowCloud::IsTypeChanged(VspGraph_ViewSpace space) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(
        space, 
        TFIELD(&ce::net::VSP_LowCloudView::low_cloud),
        TFIELD(&ce::net::CloudStruct::cloud_type));

    return diff.IsModifiedField();
}

bool VspGraph_MeteorView_MiddleCloud::IsTypeChanged(VspGraph_ViewSpace space) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(
        space, 
        TFIELD(&ce::net::VSP_MiddleCloudView::middle_cloud),
        TFIELD(&ce::net::CloudStruct::cloud_type));

    return diff.IsModifiedField();
}

bool VspGraph_MeteorView_HighCloud::IsTypeChanged(VspGraph_ViewSpace space) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(
        space, 
        TFIELD(&ce::net::VSP_HighCloudView::high_cloud),
        TFIELD(&ce::net::HighCloudStruct::high_cloud_type));

    return diff.IsModifiedField();
}

bool VspGraph_MeteorView_Precipitation::IsPrecipitationChangeActiveType(VspGraph_ViewSpace space) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(
        space, 
        TFIELD(&ce::net::VSP_PrecipitationView::precipitation),
        TFIELD(&ce::net::PrecipitationStruct::precipitation_type));

    if (diff.IsModifiedField())
    {
        ce::net::EnumPrecipitationType const* curType = diff.cur.Reference<ce::net::EnumPrecipitationType>();

        if (curType == nullptr)
            return false;
        if (*curType != ce::net::EnumPrecipitationType::None)
            return true; 
    }

    return false; 
}

bool VspGraph_MeteorView_Lightning::IsLightningChangeActiveType(VspGraph_ViewSpace space /* = VspGraph_ViewSpace::Sampling*/) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(
        space, 
        TFIELD(&ce::net::VSP_LightningView::lightning),
        TFIELD(&ce::net::LightningStruct::lightning_level));

    if (diff.IsModifiedField())
    {
        ce::net::EnumWeatherIntensityLevel const* curType = diff.cur.Reference<ce::net::EnumWeatherIntensityLevel>();
        if (curType == nullptr)
            return false;

        if (*curType != ce::net::EnumWeatherIntensityLevel::WeatherIntensityLevel_Zero)
            return true;
    }

    return false;
}

bool VspGraph_MeteorView_Lightning::IsLightningChangeDeactiveType(VspGraph_ViewSpace space /* = VspGraph_ViewSpace::Sampling*/) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(
        space, 
        TFIELD(&ce::net::VSP_LightningView::lightning),
        TFIELD(&ce::net::LightningStruct::lightning_level));

    if (diff.IsModifiedField())
    {
        ce::net::EnumWeatherIntensityLevel const* curType = diff.cur.Reference<ce::net::EnumWeatherIntensityLevel>();
        if (*curType == ce::net::EnumWeatherIntensityLevel::WeatherIntensityLevel_Zero)
            return true;
    }

    return false;
}

bool VspGraph_MeteorView_Wind::IsWindChanged(VspGraph_ViewSpace space) const 
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(
        space, 
        TFIELD(&ce::net::VSP_WindView::wind));
    return diff.IsModifiedField();
}

bool VspGraph_MeteorView_TimeOfDay::IsTODChanged(VspGraph_ViewSpace space) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(space, TFIELD(&ce::net::VSP_TODView::tod));

    return diff.IsModifiedField();
}

bool VspGraph_MeteorView_TimeOfDay::IsFactorChanged(VspGraph_ViewSpace space) const
{
    auto diff = IS_UNIQUE_FIELD_MODIFIED(space, TFIELD(&ce::net::VSP_TODView::tod), TFIELD(&ce::net::TimeOfDayStruct::acceleration_factor));

    return diff.IsModifiedField();
}

bool VspGraph_MeteorView_Storm::IsLightningChangeActiveType(ce::net::StormStruct*& outExecutingStorm, SInt32 index, VspGraph_ViewSpace space /*= VspGraph_ViewSpace::Sampling*/) const
{
    auto& floorView = GetStageReferenceView(space == VspGraph_ViewSpace::Syncing ? FRAME_STAGE_SYNC_EXECUTE : FRAME_STAGE_SAMP_FORWARD);
    auto& ceilView = GetStageExecutingView(space == VspGraph_ViewSpace::Syncing ? FRAME_STAGE_SYNC_EXECUTE : FRAME_STAGE_SAMP_FORWARD);

    SInt32 floorSize = floorView.storm_size();
    SInt32 ceilSize = ceilView.storm_size();
    
    // allocate new one
    if (ceilSize > floorSize)
    {
        Assert((index + 1) <= ceilSize);

        if (index >= floorSize)
            return false;
    }
    
    // delete old one
    if (ceilSize < floorSize)
    {
        Assert((index + 1) <= floorSize);

        if (index >= ceilSize)
            return false;
    }

    // equals size
    auto const& floorStorm = floorView.storm(index);
    auto const& ceilStorm = ceilView.storm(index);

    outExecutingStorm = &const_cast<ce::net::StormStruct&>(ceilStorm);
    return floorStorm.storm_lightning() != ceilStorm.storm_lightning() && ceilStorm.storm_lightning() != ce::net::EnumWeatherIntensityLevel::WeatherIntensityLevel_Zero;
}

SInt32 VspGraph_MeteorView_Storm::GetActivatedStormCount(VspGraph_ViewSpace space /*= VspGraph_ViewSpace::Sampling*/) const
{
    auto& view = GetStageExecutingView(space == VspGraph_ViewSpace::Syncing ? FRAME_STAGE_SYNC_EXECUTE : FRAME_STAGE_SAMP_FORWARD);
    return view.storm_size();
}

}   // namespace cross::ffs