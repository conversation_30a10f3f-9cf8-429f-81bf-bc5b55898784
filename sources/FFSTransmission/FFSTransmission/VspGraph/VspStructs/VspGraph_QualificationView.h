#pragma once
#include "FFSTransmission/FFSTransmissionAPI.h"
#include "FFSTransmission/Utility/FunctionTraits.h"
#include "FFSProtocol/FFS_VSP/ProtocolViews/Qualification/QualificationView.pb.h"
#include "FFSProtocol/FFS_VSP/VSP_GeneralStruct.pb.h"
#include "FFSTransmission/VspGraph/VspStructs/VspGraph_BasicView.h"

namespace cross::ffs 
{

class Transmission_API VspGraph_QualificationView : public VspGraph_ViewProperty<ce::net::VSP_QualificationView> 
{
public:
    bool IsQualificationActivating(VspGraph_ViewSpace space = VspGraph_ViewSpace::Sampling) const;
};
using QUALIFICATION_V = VspGraph_QualificationView;

}