#pragma once
#include "FFSTransmission/FFSTransmissionAPI.h"
#include "FFSTransmission/Utility/FunctionTraits.h"
#include "FFSProtocol/FFS_VSP/VSP_GeneralStruct.pb.h"
#include "FFSTransmission/VspGraph/VspStructs/VspGraph_BasicView.h"
#include "FFSProtocol/FFS_VSP/ProtocolViews/CtrlModel/CtrlModelView.pb.h"

namespace cross::ffs {
class Transmission_API VspGraph_RecordView : public VspGraph_ViewProperty<ce::net::VSP_RecordView>
{
public:
    bool IsRecordChanged(VspGraph_ViewSpace space = VspGraph_ViewSpace::Sampling) const;
};
using RECORD_V = VspGraph_RecordView;

}   // namespace cross::ffs