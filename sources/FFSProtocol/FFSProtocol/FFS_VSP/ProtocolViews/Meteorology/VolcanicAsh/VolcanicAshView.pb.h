// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FFS_VSP/ProtocolViews/Meteorology/VolcanicAsh/VolcanicAshView.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "FFS_VSP/ProtocolStructs/Meteorology/VolcanicAsh/VolcanicAshStruct.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto Protocol_API
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct Protocol_API TableStruct_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern Protocol_API const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto;
namespace ce {
namespace net {
class VSP_VolcanicAshView;
class VSP_VolcanicAshViewDefaultTypeInternal;
Protocol_API extern VSP_VolcanicAshViewDefaultTypeInternal _VSP_VolcanicAshView_default_instance_;
}  // namespace net
}  // namespace ce
PROTOBUF_NAMESPACE_OPEN
template<> Protocol_API ::ce::net::VSP_VolcanicAshView* Arena::CreateMaybeMessage<::ce::net::VSP_VolcanicAshView>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace ce {
namespace net {

// ===================================================================

class Protocol_API VSP_VolcanicAshView PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ce.net.VSP_VolcanicAshView) */ {
 public:
  inline VSP_VolcanicAshView() : VSP_VolcanicAshView(nullptr) {}
  virtual ~VSP_VolcanicAshView();

  VSP_VolcanicAshView(const VSP_VolcanicAshView& from);
  VSP_VolcanicAshView(VSP_VolcanicAshView&& from) noexcept
    : VSP_VolcanicAshView() {
    *this = ::std::move(from);
  }

  inline VSP_VolcanicAshView& operator=(const VSP_VolcanicAshView& from) {
    CopyFrom(from);
    return *this;
  }
  inline VSP_VolcanicAshView& operator=(VSP_VolcanicAshView&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const VSP_VolcanicAshView& default_instance();

  static inline const VSP_VolcanicAshView* internal_default_instance() {
    return reinterpret_cast<const VSP_VolcanicAshView*>(
               &_VSP_VolcanicAshView_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(VSP_VolcanicAshView& a, VSP_VolcanicAshView& b) {
    a.Swap(&b);
  }
  inline void Swap(VSP_VolcanicAshView* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VSP_VolcanicAshView* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline VSP_VolcanicAshView* New() const final {
    return CreateMaybeMessage<VSP_VolcanicAshView>(nullptr);
  }

  VSP_VolcanicAshView* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<VSP_VolcanicAshView>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const VSP_VolcanicAshView& from);
  void MergeFrom(const VSP_VolcanicAshView& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VSP_VolcanicAshView* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ce.net.VSP_VolcanicAshView";
  }
  protected:
  explicit VSP_VolcanicAshView(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto);
    return ::descriptor_table_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAshFieldNumber = 1,
  };
  // .ce.net.VolcanicAshStruct ash = 1;
  bool has_ash() const;
  private:
  bool _internal_has_ash() const;
  public:
  void clear_ash();
  const ::ce::net::VolcanicAshStruct& ash() const;
  ::ce::net::VolcanicAshStruct* release_ash();
  ::ce::net::VolcanicAshStruct* mutable_ash();
  void set_allocated_ash(::ce::net::VolcanicAshStruct* ash);
  private:
  const ::ce::net::VolcanicAshStruct& _internal_ash() const;
  ::ce::net::VolcanicAshStruct* _internal_mutable_ash();
  public:
  void unsafe_arena_set_allocated_ash(
      ::ce::net::VolcanicAshStruct* ash);
  ::ce::net::VolcanicAshStruct* unsafe_arena_release_ash();

  // @@protoc_insertion_point(class_scope:ce.net.VSP_VolcanicAshView)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::ce::net::VolcanicAshStruct* ash_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VSP_VolcanicAshView

// .ce.net.VolcanicAshStruct ash = 1;
inline bool VSP_VolcanicAshView::_internal_has_ash() const {
  return this != internal_default_instance() && ash_ != nullptr;
}
inline bool VSP_VolcanicAshView::has_ash() const {
  return _internal_has_ash();
}
inline const ::ce::net::VolcanicAshStruct& VSP_VolcanicAshView::_internal_ash() const {
  const ::ce::net::VolcanicAshStruct* p = ash_;
  return p != nullptr ? *p : reinterpret_cast<const ::ce::net::VolcanicAshStruct&>(
      ::ce::net::_VolcanicAshStruct_default_instance_);
}
inline const ::ce::net::VolcanicAshStruct& VSP_VolcanicAshView::ash() const {
  // @@protoc_insertion_point(field_get:ce.net.VSP_VolcanicAshView.ash)
  return _internal_ash();
}
inline void VSP_VolcanicAshView::unsafe_arena_set_allocated_ash(
    ::ce::net::VolcanicAshStruct* ash) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ash_);
  }
  ash_ = ash;
  if (ash) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ce.net.VSP_VolcanicAshView.ash)
}
inline ::ce::net::VolcanicAshStruct* VSP_VolcanicAshView::release_ash() {
  
  ::ce::net::VolcanicAshStruct* temp = ash_;
  ash_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::ce::net::VolcanicAshStruct* VSP_VolcanicAshView::unsafe_arena_release_ash() {
  // @@protoc_insertion_point(field_release:ce.net.VSP_VolcanicAshView.ash)
  
  ::ce::net::VolcanicAshStruct* temp = ash_;
  ash_ = nullptr;
  return temp;
}
inline ::ce::net::VolcanicAshStruct* VSP_VolcanicAshView::_internal_mutable_ash() {
  
  if (ash_ == nullptr) {
    auto* p = CreateMaybeMessage<::ce::net::VolcanicAshStruct>(GetArena());
    ash_ = p;
  }
  return ash_;
}
inline ::ce::net::VolcanicAshStruct* VSP_VolcanicAshView::mutable_ash() {
  // @@protoc_insertion_point(field_mutable:ce.net.VSP_VolcanicAshView.ash)
  return _internal_mutable_ash();
}
inline void VSP_VolcanicAshView::set_allocated_ash(::ce::net::VolcanicAshStruct* ash) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ash_);
  }
  if (ash) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ash)->GetArena();
    if (message_arena != submessage_arena) {
      ash = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ash, submessage_arena);
    }
    
  } else {
    
  }
  ash_ = ash;
  // @@protoc_insertion_point(field_set_allocated:ce.net.VSP_VolcanicAshView.ash)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace net
}  // namespace ce

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_FFS_5fVSP_2fProtocolViews_2fMeteorology_2fVolcanicAsh_2fVolcanicAshView_2eproto
