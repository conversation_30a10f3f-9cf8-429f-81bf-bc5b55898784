// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FFS_VSP/VSP_Packet.proto

#include "FFS_VSP/VSP_Packet.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fVSP_2fVSP_5fGeneralStruct_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SyncStruct_FFS_5fVSP_2fVSP_5fGeneralStruct_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fVSP_2fVSP_5fPacket_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto;
namespace ce {
namespace net {
class VspGraphSyncNodeDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<VspGraphSyncNode> _instance;
} _VspGraphSyncNode_default_instance_;
class VspGraphSyncRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<VspGraphSyncRequest> _instance;
} _VspGraphSyncRequest_default_instance_;
class VspGraphSyncResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<VspGraphSyncResponse> _instance;
} _VspGraphSyncResponse_default_instance_;
class VspGraphSimSyncRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<VspGraphSimSyncRequest> _instance;
} _VspGraphSimSyncRequest_default_instance_;
class VspGraphSimSyncResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<VspGraphSimSyncResponse> _instance;
} _VspGraphSimSyncResponse_default_instance_;
class VspGraphSimQueryRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<VspGraphSimQueryRequest> _instance;
} _VspGraphSimQueryRequest_default_instance_;
class VspGraphSimQueryResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<VspGraphSimQueryResponse> _instance;
} _VspGraphSimQueryResponse_default_instance_;
}  // namespace net
}  // namespace ce
static void InitDefaultsscc_info_VspGraphSimQueryRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_VspGraphSimQueryRequest_default_instance_;
    new (ptr) ::ce::net::VspGraphSimQueryRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_VspGraphSimQueryRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_VspGraphSimQueryRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_VspGraphSimQueryResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_VspGraphSimQueryResponse_default_instance_;
    new (ptr) ::ce::net::VspGraphSimQueryResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_VspGraphSimQueryResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_VspGraphSimQueryResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto}, {
      &scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_VspGraphSimSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_VspGraphSimSyncRequest_default_instance_;
    new (ptr) ::ce::net::VspGraphSimSyncRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_VspGraphSimSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_VspGraphSimSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto}, {
      &scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_VspGraphSimSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_VspGraphSimSyncResponse_default_instance_;
    new (ptr) ::ce::net::VspGraphSimSyncResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_VspGraphSimSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_VspGraphSimSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto}, {
      &scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_VspGraphSyncNode_default_instance_;
    new (ptr) ::ce::net::VspGraphSyncNode();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_VspGraphSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_VspGraphSyncRequest_default_instance_;
    new (ptr) ::ce::net::VspGraphSyncRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_VspGraphSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_VspGraphSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto}, {
      &scc_info_SyncStruct_FFS_5fVSP_2fVSP_5fGeneralStruct_2eproto.base,
      &scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_VspGraphSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_VspGraphSyncResponse_default_instance_;
    new (ptr) ::ce::net::VspGraphSyncResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_VspGraphSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_VspGraphSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_FFS_5fVSP_2fVSP_5fPacket_2eproto[7];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_FFS_5fVSP_2fVSP_5fPacket_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_FFS_5fVSP_2fVSP_5fPacket_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_FFS_5fVSP_2fVSP_5fPacket_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSyncNode, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSyncNode, message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSyncNode, content_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSyncRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSyncRequest, timeline_info_),
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSyncRequest, contents_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSyncResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimSyncRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimSyncRequest, contents_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimSyncResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimSyncResponse, contents_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimQueryRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimQueryRequest, contents_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimQueryResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::VspGraphSimQueryResponse, contents_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::ce::net::VspGraphSyncNode)},
  { 7, -1, sizeof(::ce::net::VspGraphSyncRequest)},
  { 14, -1, sizeof(::ce::net::VspGraphSyncResponse)},
  { 19, -1, sizeof(::ce::net::VspGraphSimSyncRequest)},
  { 25, -1, sizeof(::ce::net::VspGraphSimSyncResponse)},
  { 31, -1, sizeof(::ce::net::VspGraphSimQueryRequest)},
  { 37, -1, sizeof(::ce::net::VspGraphSimQueryResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_VspGraphSyncNode_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_VspGraphSyncRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_VspGraphSyncResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_VspGraphSimSyncRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_VspGraphSimSyncResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_VspGraphSimQueryRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_VspGraphSimQueryResponse_default_instance_),
};

const char descriptor_table_protodef_FFS_5fVSP_2fVSP_5fPacket_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\030FFS_VSP/VSP_Packet.proto\022\006ce.net\032\035FFS_"
  "VSP/VSP_OperateEnum.proto\032\037FFS_VSP/VSP_G"
  "eneralStruct.proto\"I\n\020VspGraphSyncNode\022$"
  "\n\007message\030\001 \001(\0162\023.ce.net.EnumVSPView\022\017\n\007"
  "content\030\002 \001(\014\"l\n\023VspGraphSyncRequest\022)\n\r"
  "timeline_info\030\001 \001(\0132\022.ce.net.SyncStruct\022"
  "*\n\010contents\030\002 \003(\0132\030.ce.net.VspGraphSyncN"
  "ode\"\026\n\024VspGraphSyncResponse\"D\n\026VspGraphS"
  "imSyncRequest\022*\n\010contents\030\001 \003(\0132\030.ce.net"
  ".VspGraphSyncNode\"E\n\027VspGraphSimSyncResp"
  "onse\022*\n\010contents\030\001 \003(\0132\030.ce.net.VspGraph"
  "SyncNode\"@\n\027VspGraphSimQueryRequest\022%\n\010c"
  "ontents\030\001 \003(\0162\023.ce.net.EnumVSPView\"F\n\030Vs"
  "pGraphSimQueryResponse\022*\n\010contents\030\001 \003(\013"
  "2\030.ce.net.VspGraphSyncNodeb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto_deps[2] = {
  &::descriptor_table_FFS_5fVSP_2fVSP_5fGeneralStruct_2eproto,
  &::descriptor_table_FFS_5fVSP_2fVSP_5fOperateEnum_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto_sccs[7] = {
  &scc_info_VspGraphSimQueryRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,
  &scc_info_VspGraphSimQueryResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,
  &scc_info_VspGraphSimSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,
  &scc_info_VspGraphSimSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,
  &scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,
  &scc_info_VspGraphSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,
  &scc_info_VspGraphSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto = {
  false, false, descriptor_table_protodef_FFS_5fVSP_2fVSP_5fPacket_2eproto, "FFS_VSP/VSP_Packet.proto", 594,
  &descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto_once, descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto_sccs, descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto_deps, 7, 2,
  schemas, file_default_instances, TableStruct_FFS_5fVSP_2fVSP_5fPacket_2eproto::offsets,
  file_level_metadata_FFS_5fVSP_2fVSP_5fPacket_2eproto, 7, file_level_enum_descriptors_FFS_5fVSP_2fVSP_5fPacket_2eproto, file_level_service_descriptors_FFS_5fVSP_2fVSP_5fPacket_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_FFS_5fVSP_2fVSP_5fPacket_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_FFS_5fVSP_2fVSP_5fPacket_2eproto)), true);
namespace ce {
namespace net {

// ===================================================================

class VspGraphSyncNode::_Internal {
 public:
};

VspGraphSyncNode::VspGraphSyncNode(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.VspGraphSyncNode)
}
VspGraphSyncNode::VspGraphSyncNode(const VspGraphSyncNode& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  content_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_content().empty()) {
    content_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_content(), 
      GetArena());
  }
  message_ = from.message_;
  // @@protoc_insertion_point(copy_constructor:ce.net.VspGraphSyncNode)
}

void VspGraphSyncNode::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  content_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  message_ = 0;
}

VspGraphSyncNode::~VspGraphSyncNode() {
  // @@protoc_insertion_point(destructor:ce.net.VspGraphSyncNode)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void VspGraphSyncNode::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  content_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void VspGraphSyncNode::ArenaDtor(void* object) {
  VspGraphSyncNode* _this = reinterpret_cast< VspGraphSyncNode* >(object);
  (void)_this;
}
void VspGraphSyncNode::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VspGraphSyncNode::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const VspGraphSyncNode& VspGraphSyncNode::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_VspGraphSyncNode_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void VspGraphSyncNode::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.VspGraphSyncNode)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  content_.ClearToEmpty();
  message_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VspGraphSyncNode::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .ce.net.EnumVSPView message = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_message(static_cast<::ce::net::EnumVSPView>(val));
        } else goto handle_unusual;
        continue;
      // bytes content = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_content();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VspGraphSyncNode::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.VspGraphSyncNode)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .ce.net.EnumVSPView message = 1;
  if (this->message() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_message(), target);
  }

  // bytes content = 2;
  if (this->content().size() > 0) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_content(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.VspGraphSyncNode)
  return target;
}

size_t VspGraphSyncNode::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.VspGraphSyncNode)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes content = 2;
  if (this->content().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_content());
  }

  // .ce.net.EnumVSPView message = 1;
  if (this->message() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_message());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VspGraphSyncNode::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.VspGraphSyncNode)
  GOOGLE_DCHECK_NE(&from, this);
  const VspGraphSyncNode* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<VspGraphSyncNode>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.VspGraphSyncNode)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.VspGraphSyncNode)
    MergeFrom(*source);
  }
}

void VspGraphSyncNode::MergeFrom(const VspGraphSyncNode& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.VspGraphSyncNode)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.content().size() > 0) {
    _internal_set_content(from._internal_content());
  }
  if (from.message() != 0) {
    _internal_set_message(from._internal_message());
  }
}

void VspGraphSyncNode::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.VspGraphSyncNode)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VspGraphSyncNode::CopyFrom(const VspGraphSyncNode& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.VspGraphSyncNode)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VspGraphSyncNode::IsInitialized() const {
  return true;
}

void VspGraphSyncNode::InternalSwap(VspGraphSyncNode* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  content_.Swap(&other->content_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(message_, other->message_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VspGraphSyncNode::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class VspGraphSyncRequest::_Internal {
 public:
  static const ::ce::net::SyncStruct& timeline_info(const VspGraphSyncRequest* msg);
};

const ::ce::net::SyncStruct&
VspGraphSyncRequest::_Internal::timeline_info(const VspGraphSyncRequest* msg) {
  return *msg->timeline_info_;
}
void VspGraphSyncRequest::clear_timeline_info() {
  if (GetArena() == nullptr && timeline_info_ != nullptr) {
    delete timeline_info_;
  }
  timeline_info_ = nullptr;
}
VspGraphSyncRequest::VspGraphSyncRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  contents_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.VspGraphSyncRequest)
}
VspGraphSyncRequest::VspGraphSyncRequest(const VspGraphSyncRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      contents_(from.contents_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_timeline_info()) {
    timeline_info_ = new ::ce::net::SyncStruct(*from.timeline_info_);
  } else {
    timeline_info_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.VspGraphSyncRequest)
}

void VspGraphSyncRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_VspGraphSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  timeline_info_ = nullptr;
}

VspGraphSyncRequest::~VspGraphSyncRequest() {
  // @@protoc_insertion_point(destructor:ce.net.VspGraphSyncRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void VspGraphSyncRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete timeline_info_;
}

void VspGraphSyncRequest::ArenaDtor(void* object) {
  VspGraphSyncRequest* _this = reinterpret_cast< VspGraphSyncRequest* >(object);
  (void)_this;
}
void VspGraphSyncRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VspGraphSyncRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const VspGraphSyncRequest& VspGraphSyncRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_VspGraphSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void VspGraphSyncRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.VspGraphSyncRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contents_.Clear();
  if (GetArena() == nullptr && timeline_info_ != nullptr) {
    delete timeline_info_;
  }
  timeline_info_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VspGraphSyncRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .ce.net.SyncStruct timeline_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_timeline_info(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .ce.net.VspGraphSyncNode contents = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_contents(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VspGraphSyncRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.VspGraphSyncRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .ce.net.SyncStruct timeline_info = 1;
  if (this->has_timeline_info()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::timeline_info(this), target, stream);
  }

  // repeated .ce.net.VspGraphSyncNode contents = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_contents_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_contents(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.VspGraphSyncRequest)
  return target;
}

size_t VspGraphSyncRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.VspGraphSyncRequest)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.VspGraphSyncNode contents = 2;
  total_size += 1UL * this->_internal_contents_size();
  for (const auto& msg : this->contents_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .ce.net.SyncStruct timeline_info = 1;
  if (this->has_timeline_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *timeline_info_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VspGraphSyncRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.VspGraphSyncRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const VspGraphSyncRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<VspGraphSyncRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.VspGraphSyncRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.VspGraphSyncRequest)
    MergeFrom(*source);
  }
}

void VspGraphSyncRequest::MergeFrom(const VspGraphSyncRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.VspGraphSyncRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  contents_.MergeFrom(from.contents_);
  if (from.has_timeline_info()) {
    _internal_mutable_timeline_info()->::ce::net::SyncStruct::MergeFrom(from._internal_timeline_info());
  }
}

void VspGraphSyncRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.VspGraphSyncRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VspGraphSyncRequest::CopyFrom(const VspGraphSyncRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.VspGraphSyncRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VspGraphSyncRequest::IsInitialized() const {
  return true;
}

void VspGraphSyncRequest::InternalSwap(VspGraphSyncRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  contents_.InternalSwap(&other->contents_);
  swap(timeline_info_, other->timeline_info_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VspGraphSyncRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class VspGraphSyncResponse::_Internal {
 public:
};

VspGraphSyncResponse::VspGraphSyncResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.VspGraphSyncResponse)
}
VspGraphSyncResponse::VspGraphSyncResponse(const VspGraphSyncResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.VspGraphSyncResponse)
}

void VspGraphSyncResponse::SharedCtor() {
}

VspGraphSyncResponse::~VspGraphSyncResponse() {
  // @@protoc_insertion_point(destructor:ce.net.VspGraphSyncResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void VspGraphSyncResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void VspGraphSyncResponse::ArenaDtor(void* object) {
  VspGraphSyncResponse* _this = reinterpret_cast< VspGraphSyncResponse* >(object);
  (void)_this;
}
void VspGraphSyncResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VspGraphSyncResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const VspGraphSyncResponse& VspGraphSyncResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_VspGraphSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void VspGraphSyncResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.VspGraphSyncResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VspGraphSyncResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VspGraphSyncResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.VspGraphSyncResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.VspGraphSyncResponse)
  return target;
}

size_t VspGraphSyncResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.VspGraphSyncResponse)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VspGraphSyncResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.VspGraphSyncResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const VspGraphSyncResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<VspGraphSyncResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.VspGraphSyncResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.VspGraphSyncResponse)
    MergeFrom(*source);
  }
}

void VspGraphSyncResponse::MergeFrom(const VspGraphSyncResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.VspGraphSyncResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void VspGraphSyncResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.VspGraphSyncResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VspGraphSyncResponse::CopyFrom(const VspGraphSyncResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.VspGraphSyncResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VspGraphSyncResponse::IsInitialized() const {
  return true;
}

void VspGraphSyncResponse::InternalSwap(VspGraphSyncResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VspGraphSyncResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class VspGraphSimSyncRequest::_Internal {
 public:
};

VspGraphSimSyncRequest::VspGraphSimSyncRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  contents_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.VspGraphSimSyncRequest)
}
VspGraphSimSyncRequest::VspGraphSimSyncRequest(const VspGraphSimSyncRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      contents_(from.contents_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.VspGraphSimSyncRequest)
}

void VspGraphSimSyncRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_VspGraphSimSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
}

VspGraphSimSyncRequest::~VspGraphSimSyncRequest() {
  // @@protoc_insertion_point(destructor:ce.net.VspGraphSimSyncRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void VspGraphSimSyncRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void VspGraphSimSyncRequest::ArenaDtor(void* object) {
  VspGraphSimSyncRequest* _this = reinterpret_cast< VspGraphSimSyncRequest* >(object);
  (void)_this;
}
void VspGraphSimSyncRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VspGraphSimSyncRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const VspGraphSimSyncRequest& VspGraphSimSyncRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_VspGraphSimSyncRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void VspGraphSimSyncRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.VspGraphSimSyncRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contents_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VspGraphSimSyncRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .ce.net.VspGraphSyncNode contents = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_contents(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VspGraphSimSyncRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.VspGraphSimSyncRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .ce.net.VspGraphSyncNode contents = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_contents_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_contents(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.VspGraphSimSyncRequest)
  return target;
}

size_t VspGraphSimSyncRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.VspGraphSimSyncRequest)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.VspGraphSyncNode contents = 1;
  total_size += 1UL * this->_internal_contents_size();
  for (const auto& msg : this->contents_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VspGraphSimSyncRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.VspGraphSimSyncRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const VspGraphSimSyncRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<VspGraphSimSyncRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.VspGraphSimSyncRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.VspGraphSimSyncRequest)
    MergeFrom(*source);
  }
}

void VspGraphSimSyncRequest::MergeFrom(const VspGraphSimSyncRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.VspGraphSimSyncRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  contents_.MergeFrom(from.contents_);
}

void VspGraphSimSyncRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.VspGraphSimSyncRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VspGraphSimSyncRequest::CopyFrom(const VspGraphSimSyncRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.VspGraphSimSyncRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VspGraphSimSyncRequest::IsInitialized() const {
  return true;
}

void VspGraphSimSyncRequest::InternalSwap(VspGraphSimSyncRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  contents_.InternalSwap(&other->contents_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VspGraphSimSyncRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class VspGraphSimSyncResponse::_Internal {
 public:
};

VspGraphSimSyncResponse::VspGraphSimSyncResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  contents_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.VspGraphSimSyncResponse)
}
VspGraphSimSyncResponse::VspGraphSimSyncResponse(const VspGraphSimSyncResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      contents_(from.contents_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.VspGraphSimSyncResponse)
}

void VspGraphSimSyncResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_VspGraphSimSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
}

VspGraphSimSyncResponse::~VspGraphSimSyncResponse() {
  // @@protoc_insertion_point(destructor:ce.net.VspGraphSimSyncResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void VspGraphSimSyncResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void VspGraphSimSyncResponse::ArenaDtor(void* object) {
  VspGraphSimSyncResponse* _this = reinterpret_cast< VspGraphSimSyncResponse* >(object);
  (void)_this;
}
void VspGraphSimSyncResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VspGraphSimSyncResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const VspGraphSimSyncResponse& VspGraphSimSyncResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_VspGraphSimSyncResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void VspGraphSimSyncResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.VspGraphSimSyncResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contents_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VspGraphSimSyncResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .ce.net.VspGraphSyncNode contents = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_contents(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VspGraphSimSyncResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.VspGraphSimSyncResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .ce.net.VspGraphSyncNode contents = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_contents_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_contents(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.VspGraphSimSyncResponse)
  return target;
}

size_t VspGraphSimSyncResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.VspGraphSimSyncResponse)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.VspGraphSyncNode contents = 1;
  total_size += 1UL * this->_internal_contents_size();
  for (const auto& msg : this->contents_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VspGraphSimSyncResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.VspGraphSimSyncResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const VspGraphSimSyncResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<VspGraphSimSyncResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.VspGraphSimSyncResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.VspGraphSimSyncResponse)
    MergeFrom(*source);
  }
}

void VspGraphSimSyncResponse::MergeFrom(const VspGraphSimSyncResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.VspGraphSimSyncResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  contents_.MergeFrom(from.contents_);
}

void VspGraphSimSyncResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.VspGraphSimSyncResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VspGraphSimSyncResponse::CopyFrom(const VspGraphSimSyncResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.VspGraphSimSyncResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VspGraphSimSyncResponse::IsInitialized() const {
  return true;
}

void VspGraphSimSyncResponse::InternalSwap(VspGraphSimSyncResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  contents_.InternalSwap(&other->contents_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VspGraphSimSyncResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class VspGraphSimQueryRequest::_Internal {
 public:
};

VspGraphSimQueryRequest::VspGraphSimQueryRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  contents_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.VspGraphSimQueryRequest)
}
VspGraphSimQueryRequest::VspGraphSimQueryRequest(const VspGraphSimQueryRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      contents_(from.contents_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.VspGraphSimQueryRequest)
}

void VspGraphSimQueryRequest::SharedCtor() {
}

VspGraphSimQueryRequest::~VspGraphSimQueryRequest() {
  // @@protoc_insertion_point(destructor:ce.net.VspGraphSimQueryRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void VspGraphSimQueryRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void VspGraphSimQueryRequest::ArenaDtor(void* object) {
  VspGraphSimQueryRequest* _this = reinterpret_cast< VspGraphSimQueryRequest* >(object);
  (void)_this;
}
void VspGraphSimQueryRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VspGraphSimQueryRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const VspGraphSimQueryRequest& VspGraphSimQueryRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_VspGraphSimQueryRequest_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void VspGraphSimQueryRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.VspGraphSimQueryRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contents_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VspGraphSimQueryRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .ce.net.EnumVSPView contents = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_contents(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_contents(static_cast<::ce::net::EnumVSPView>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VspGraphSimQueryRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.VspGraphSimQueryRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .ce.net.EnumVSPView contents = 1;
  {
    int byte_size = _contents_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          1, contents_, byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.VspGraphSimQueryRequest)
  return target;
}

size_t VspGraphSimQueryRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.VspGraphSimQueryRequest)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.EnumVSPView contents = 1;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_contents_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_contents(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _contents_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VspGraphSimQueryRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.VspGraphSimQueryRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const VspGraphSimQueryRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<VspGraphSimQueryRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.VspGraphSimQueryRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.VspGraphSimQueryRequest)
    MergeFrom(*source);
  }
}

void VspGraphSimQueryRequest::MergeFrom(const VspGraphSimQueryRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.VspGraphSimQueryRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  contents_.MergeFrom(from.contents_);
}

void VspGraphSimQueryRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.VspGraphSimQueryRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VspGraphSimQueryRequest::CopyFrom(const VspGraphSimQueryRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.VspGraphSimQueryRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VspGraphSimQueryRequest::IsInitialized() const {
  return true;
}

void VspGraphSimQueryRequest::InternalSwap(VspGraphSimQueryRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  contents_.InternalSwap(&other->contents_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VspGraphSimQueryRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class VspGraphSimQueryResponse::_Internal {
 public:
};

VspGraphSimQueryResponse::VspGraphSimQueryResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  contents_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.VspGraphSimQueryResponse)
}
VspGraphSimQueryResponse::VspGraphSimQueryResponse(const VspGraphSimQueryResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      contents_(from.contents_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.VspGraphSimQueryResponse)
}

void VspGraphSimQueryResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_VspGraphSimQueryResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
}

VspGraphSimQueryResponse::~VspGraphSimQueryResponse() {
  // @@protoc_insertion_point(destructor:ce.net.VspGraphSimQueryResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void VspGraphSimQueryResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void VspGraphSimQueryResponse::ArenaDtor(void* object) {
  VspGraphSimQueryResponse* _this = reinterpret_cast< VspGraphSimQueryResponse* >(object);
  (void)_this;
}
void VspGraphSimQueryResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VspGraphSimQueryResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const VspGraphSimQueryResponse& VspGraphSimQueryResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_VspGraphSimQueryResponse_FFS_5fVSP_2fVSP_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void VspGraphSimQueryResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.VspGraphSimQueryResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  contents_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VspGraphSimQueryResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .ce.net.VspGraphSyncNode contents = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_contents(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* VspGraphSimQueryResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.VspGraphSimQueryResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .ce.net.VspGraphSyncNode contents = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_contents_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_contents(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.VspGraphSimQueryResponse)
  return target;
}

size_t VspGraphSimQueryResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.VspGraphSimQueryResponse)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.VspGraphSyncNode contents = 1;
  total_size += 1UL * this->_internal_contents_size();
  for (const auto& msg : this->contents_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VspGraphSimQueryResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.VspGraphSimQueryResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const VspGraphSimQueryResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<VspGraphSimQueryResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.VspGraphSimQueryResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.VspGraphSimQueryResponse)
    MergeFrom(*source);
  }
}

void VspGraphSimQueryResponse::MergeFrom(const VspGraphSimQueryResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.VspGraphSimQueryResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  contents_.MergeFrom(from.contents_);
}

void VspGraphSimQueryResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.VspGraphSimQueryResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VspGraphSimQueryResponse::CopyFrom(const VspGraphSimQueryResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.VspGraphSimQueryResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VspGraphSimQueryResponse::IsInitialized() const {
  return true;
}

void VspGraphSimQueryResponse::InternalSwap(VspGraphSimQueryResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  contents_.InternalSwap(&other->contents_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VspGraphSimQueryResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace net
}  // namespace ce
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::ce::net::VspGraphSyncNode* Arena::CreateMaybeMessage< ::ce::net::VspGraphSyncNode >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::VspGraphSyncNode >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::VspGraphSyncRequest* Arena::CreateMaybeMessage< ::ce::net::VspGraphSyncRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::VspGraphSyncRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::VspGraphSyncResponse* Arena::CreateMaybeMessage< ::ce::net::VspGraphSyncResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::VspGraphSyncResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::VspGraphSimSyncRequest* Arena::CreateMaybeMessage< ::ce::net::VspGraphSimSyncRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::VspGraphSimSyncRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::VspGraphSimSyncResponse* Arena::CreateMaybeMessage< ::ce::net::VspGraphSimSyncResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::VspGraphSimSyncResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::VspGraphSimQueryRequest* Arena::CreateMaybeMessage< ::ce::net::VspGraphSimQueryRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::VspGraphSimQueryRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::VspGraphSimQueryResponse* Arena::CreateMaybeMessage< ::ce::net::VspGraphSimQueryResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::VspGraphSimQueryResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
