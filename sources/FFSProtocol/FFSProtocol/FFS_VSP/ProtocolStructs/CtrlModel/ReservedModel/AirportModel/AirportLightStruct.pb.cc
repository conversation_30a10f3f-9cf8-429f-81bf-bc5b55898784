// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FFS_VSP/ProtocolStructs/CtrlModel/ReservedModel/AirportModel/AirportLightStruct.proto

#include "FFS_VSP/ProtocolStructs/CtrlModel/ReservedModel/AirportModel/AirportLightStruct.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_EnvironmentLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SMGCSLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto;
namespace ce {
namespace net {
class RunwayLightsStructDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RunwayLightsStruct> _instance;
} _RunwayLightsStruct_default_instance_;
class SMGCSLightsStructDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SMGCSLightsStruct> _instance;
} _SMGCSLightsStruct_default_instance_;
class EnvironmentLightsStructDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<EnvironmentLightsStruct> _instance;
} _EnvironmentLightsStruct_default_instance_;
class AirportLightsStructDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<AirportLightsStruct> _instance;
} _AirportLightsStruct_default_instance_;
}  // namespace net
}  // namespace ce
static void InitDefaultsscc_info_AirportLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_AirportLightsStruct_default_instance_;
    new (ptr) ::ce::net::AirportLightsStruct();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_AirportLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_AirportLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto}, {
      &scc_info_SMGCSLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base,
      &scc_info_EnvironmentLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base,}};

static void InitDefaultsscc_info_EnvironmentLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_EnvironmentLightsStruct_default_instance_;
    new (ptr) ::ce::net::EnvironmentLightsStruct();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_EnvironmentLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_EnvironmentLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto}, {}};

static void InitDefaultsscc_info_RunwayLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_RunwayLightsStruct_default_instance_;
    new (ptr) ::ce::net::RunwayLightsStruct();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RunwayLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_RunwayLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto}, {}};

static void InitDefaultsscc_info_SMGCSLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::_SMGCSLightsStruct_default_instance_;
    new (ptr) ::ce::net::SMGCSLightsStruct();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SMGCSLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_SMGCSLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, approachlights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, edgelights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, endlights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, thresholdlights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, centerlinelights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, tdzlights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, strobelights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, vlalights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, reilights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, lahsolights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, thlights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, relights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::RunwayLightsStruct, rilights_intensity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::SMGCSLightsStruct, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::SMGCSLightsStruct, smgcs_route_seg_lights_),
  PROTOBUF_FIELD_OFFSET(::ce::net::SMGCSLightsStruct, smgcs_route_seg_stopbar_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::EnvironmentLightsStruct, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::EnvironmentLightsStruct, majorarterieslights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::EnvironmentLightsStruct, smallroadlights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::EnvironmentLightsStruct, residentlights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::EnvironmentLightsStruct, trafficlights_intensity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, ap_rotating_beacon_),
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, taxilights_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, stop_bar_),
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, random_intensity_),
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, smgcs_enable_),
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, smgcs_route_sel_),
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, smgcs_lights_),
  PROTOBUF_FIELD_OFFSET(::ce::net::AirportLightsStruct, environment_lights_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::ce::net::RunwayLightsStruct)},
  { 18, -1, sizeof(::ce::net::SMGCSLightsStruct)},
  { 25, -1, sizeof(::ce::net::EnvironmentLightsStruct)},
  { 34, -1, sizeof(::ce::net::AirportLightsStruct)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_RunwayLightsStruct_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_SMGCSLightsStruct_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_EnvironmentLightsStruct_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::_AirportLightsStruct_default_instance_),
};

const char descriptor_table_protodef_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\nUFFS_VSP/ProtocolStructs/CtrlModel/Rese"
  "rvedModel/AirportModel/AirportLightStruc"
  "t.proto\022\006ce.net\032\037FFS_VSP/VSP_GeneralStru"
  "ct.proto\"\233\006\n\022RunwayLightsStruct\022=\n\030appro"
  "achlights_intensity\030\001 \001(\0162\033.ce.net.EnumL"
  "ightsIntensity\0229\n\024edgelights_intensity\030\002"
  " \001(\0162\033.ce.net.EnumLightsIntensity\0228\n\023end"
  "lights_intensity\030\003 \001(\0162\033.ce.net.EnumLigh"
  "tsIntensity\022>\n\031thresholdlights_intensity"
  "\030\004 \001(\0162\033.ce.net.EnumLightsIntensity\022\?\n\032c"
  "enterlinelights_intensity\030\005 \001(\0162\033.ce.net"
  ".EnumLightsIntensity\0228\n\023tdzlights_intens"
  "ity\030\006 \001(\0162\033.ce.net.EnumLightsIntensity\022;"
  "\n\026strobelights_intensity\030\007 \001(\0162\033.ce.net."
  "EnumLightsIntensity\0228\n\023vlalights_intensi"
  "ty\030\010 \001(\0162\033.ce.net.EnumLightsIntensity\0228\n"
  "\023reilights_intensity\030\t \001(\0162\033.ce.net.Enum"
  "LightsIntensity\022:\n\025lahsolights_intensity"
  "\030\n \001(\0162\033.ce.net.EnumLightsIntensity\0227\n\022t"
  "hlights_intensity\030\013 \001(\0162\033.ce.net.EnumLig"
  "htsIntensity\0227\n\022relights_intensity\030\014 \001(\016"
  "2\033.ce.net.EnumLightsIntensity\0227\n\022rilight"
  "s_intensity\030\r \001(\0162\033.ce.net.EnumLightsInt"
  "ensity\"\216\001\n\021SMGCSLightsStruct\022;\n\026smgcs_ro"
  "ute_seg_lights\030\001 \001(\0162\033.ce.net.EnumLights"
  "Intensity\022<\n\027smgcs_route_seg_stopbar\030\002 \001"
  "(\0162\033.ce.net.EnumLightsIntensity\"\232\002\n\027Envi"
  "ronmentLightsStruct\022B\n\035majorarteriesligh"
  "ts_intensity\030\001 \001(\0162\033.ce.net.EnumLightsIn"
  "tensity\022>\n\031smallroadlights_intensity\030\002 \001"
  "(\0162\033.ce.net.EnumLightsIntensity\022=\n\030resid"
  "entlights_intensity\030\003 \001(\0162\033.ce.net.EnumL"
  "ightsIntensity\022<\n\027trafficlights_intensit"
  "y\030\004 \001(\0162\033.ce.net.EnumLightsIntensity\"\265\002\n"
  "\023AirportLightsStruct\022\032\n\022ap_rotating_beac"
  "on\030\001 \001(\010\0229\n\024taxilights_intensity\030\002 \001(\0162\033"
  ".ce.net.EnumLightsIntensity\022\020\n\010stop_bar\030"
  "\003 \001(\010\022\030\n\020random_intensity\030\004 \001(\010\022\024\n\014smgcs"
  "_enable\030\005 \001(\010\022\027\n\017smgcs_route_sel\030\006 \001(\r\022/"
  "\n\014smgcs_lights\030\007 \003(\0132\031.ce.net.SMGCSLight"
  "sStruct\022;\n\022environment_lights\030\010 \001(\0132\037.ce"
  ".net.EnvironmentLightsStructb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto_deps[1] = {
  &::descriptor_table_FFS_5fVSP_2fVSP_5fGeneralStruct_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto_sccs[4] = {
  &scc_info_AirportLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base,
  &scc_info_EnvironmentLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base,
  &scc_info_RunwayLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base,
  &scc_info_SMGCSLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto = {
  false, false, descriptor_table_protodef_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto, "FFS_VSP/ProtocolStructs/CtrlModel/ReservedModel/AirportModel/AirportLightStruct.proto", 1676,
  &descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto_once, descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto_sccs, descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto_deps, 4, 1,
  schemas, file_default_instances, TableStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto::offsets,
  file_level_metadata_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto, 4, file_level_enum_descriptors_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto, file_level_service_descriptors_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto)), true);
namespace ce {
namespace net {

// ===================================================================

class RunwayLightsStruct::_Internal {
 public:
};

RunwayLightsStruct::RunwayLightsStruct(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.RunwayLightsStruct)
}
RunwayLightsStruct::RunwayLightsStruct(const RunwayLightsStruct& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&approachlights_intensity_, &from.approachlights_intensity_,
    static_cast<size_t>(reinterpret_cast<char*>(&rilights_intensity_) -
    reinterpret_cast<char*>(&approachlights_intensity_)) + sizeof(rilights_intensity_));
  // @@protoc_insertion_point(copy_constructor:ce.net.RunwayLightsStruct)
}

void RunwayLightsStruct::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&approachlights_intensity_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&rilights_intensity_) -
      reinterpret_cast<char*>(&approachlights_intensity_)) + sizeof(rilights_intensity_));
}

RunwayLightsStruct::~RunwayLightsStruct() {
  // @@protoc_insertion_point(destructor:ce.net.RunwayLightsStruct)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RunwayLightsStruct::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RunwayLightsStruct::ArenaDtor(void* object) {
  RunwayLightsStruct* _this = reinterpret_cast< RunwayLightsStruct* >(object);
  (void)_this;
}
void RunwayLightsStruct::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RunwayLightsStruct::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RunwayLightsStruct& RunwayLightsStruct::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RunwayLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base);
  return *internal_default_instance();
}


void RunwayLightsStruct::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.RunwayLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&approachlights_intensity_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&rilights_intensity_) -
      reinterpret_cast<char*>(&approachlights_intensity_)) + sizeof(rilights_intensity_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RunwayLightsStruct::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .ce.net.EnumLightsIntensity approachlights_intensity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_approachlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity edgelights_intensity = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_edgelights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity endlights_intensity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_endlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity thresholdlights_intensity = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_thresholdlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity centerlinelights_intensity = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_centerlinelights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity tdzlights_intensity = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_tdzlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity strobelights_intensity = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_strobelights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity vlalights_intensity = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_vlalights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity reilights_intensity = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_reilights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity lahsolights_intensity = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_lahsolights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity thlights_intensity = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_thlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity relights_intensity = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_relights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity rilights_intensity = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_rilights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RunwayLightsStruct::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.RunwayLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .ce.net.EnumLightsIntensity approachlights_intensity = 1;
  if (this->approachlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_approachlights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity edgelights_intensity = 2;
  if (this->edgelights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_edgelights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity endlights_intensity = 3;
  if (this->endlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_endlights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity thresholdlights_intensity = 4;
  if (this->thresholdlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_thresholdlights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity centerlinelights_intensity = 5;
  if (this->centerlinelights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_centerlinelights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity tdzlights_intensity = 6;
  if (this->tdzlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_tdzlights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity strobelights_intensity = 7;
  if (this->strobelights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_strobelights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity vlalights_intensity = 8;
  if (this->vlalights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_vlalights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity reilights_intensity = 9;
  if (this->reilights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      9, this->_internal_reilights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity lahsolights_intensity = 10;
  if (this->lahsolights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      10, this->_internal_lahsolights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity thlights_intensity = 11;
  if (this->thlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      11, this->_internal_thlights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity relights_intensity = 12;
  if (this->relights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      12, this->_internal_relights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity rilights_intensity = 13;
  if (this->rilights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      13, this->_internal_rilights_intensity(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.RunwayLightsStruct)
  return target;
}

size_t RunwayLightsStruct::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.RunwayLightsStruct)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .ce.net.EnumLightsIntensity approachlights_intensity = 1;
  if (this->approachlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_approachlights_intensity());
  }

  // .ce.net.EnumLightsIntensity edgelights_intensity = 2;
  if (this->edgelights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_edgelights_intensity());
  }

  // .ce.net.EnumLightsIntensity endlights_intensity = 3;
  if (this->endlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_endlights_intensity());
  }

  // .ce.net.EnumLightsIntensity thresholdlights_intensity = 4;
  if (this->thresholdlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_thresholdlights_intensity());
  }

  // .ce.net.EnumLightsIntensity centerlinelights_intensity = 5;
  if (this->centerlinelights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_centerlinelights_intensity());
  }

  // .ce.net.EnumLightsIntensity tdzlights_intensity = 6;
  if (this->tdzlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_tdzlights_intensity());
  }

  // .ce.net.EnumLightsIntensity strobelights_intensity = 7;
  if (this->strobelights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_strobelights_intensity());
  }

  // .ce.net.EnumLightsIntensity vlalights_intensity = 8;
  if (this->vlalights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_vlalights_intensity());
  }

  // .ce.net.EnumLightsIntensity reilights_intensity = 9;
  if (this->reilights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_reilights_intensity());
  }

  // .ce.net.EnumLightsIntensity lahsolights_intensity = 10;
  if (this->lahsolights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_lahsolights_intensity());
  }

  // .ce.net.EnumLightsIntensity thlights_intensity = 11;
  if (this->thlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_thlights_intensity());
  }

  // .ce.net.EnumLightsIntensity relights_intensity = 12;
  if (this->relights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_relights_intensity());
  }

  // .ce.net.EnumLightsIntensity rilights_intensity = 13;
  if (this->rilights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_rilights_intensity());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RunwayLightsStruct::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.RunwayLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  const RunwayLightsStruct* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RunwayLightsStruct>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.RunwayLightsStruct)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.RunwayLightsStruct)
    MergeFrom(*source);
  }
}

void RunwayLightsStruct::MergeFrom(const RunwayLightsStruct& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.RunwayLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.approachlights_intensity() != 0) {
    _internal_set_approachlights_intensity(from._internal_approachlights_intensity());
  }
  if (from.edgelights_intensity() != 0) {
    _internal_set_edgelights_intensity(from._internal_edgelights_intensity());
  }
  if (from.endlights_intensity() != 0) {
    _internal_set_endlights_intensity(from._internal_endlights_intensity());
  }
  if (from.thresholdlights_intensity() != 0) {
    _internal_set_thresholdlights_intensity(from._internal_thresholdlights_intensity());
  }
  if (from.centerlinelights_intensity() != 0) {
    _internal_set_centerlinelights_intensity(from._internal_centerlinelights_intensity());
  }
  if (from.tdzlights_intensity() != 0) {
    _internal_set_tdzlights_intensity(from._internal_tdzlights_intensity());
  }
  if (from.strobelights_intensity() != 0) {
    _internal_set_strobelights_intensity(from._internal_strobelights_intensity());
  }
  if (from.vlalights_intensity() != 0) {
    _internal_set_vlalights_intensity(from._internal_vlalights_intensity());
  }
  if (from.reilights_intensity() != 0) {
    _internal_set_reilights_intensity(from._internal_reilights_intensity());
  }
  if (from.lahsolights_intensity() != 0) {
    _internal_set_lahsolights_intensity(from._internal_lahsolights_intensity());
  }
  if (from.thlights_intensity() != 0) {
    _internal_set_thlights_intensity(from._internal_thlights_intensity());
  }
  if (from.relights_intensity() != 0) {
    _internal_set_relights_intensity(from._internal_relights_intensity());
  }
  if (from.rilights_intensity() != 0) {
    _internal_set_rilights_intensity(from._internal_rilights_intensity());
  }
}

void RunwayLightsStruct::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.RunwayLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RunwayLightsStruct::CopyFrom(const RunwayLightsStruct& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.RunwayLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RunwayLightsStruct::IsInitialized() const {
  return true;
}

void RunwayLightsStruct::InternalSwap(RunwayLightsStruct* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RunwayLightsStruct, rilights_intensity_)
      + sizeof(RunwayLightsStruct::rilights_intensity_)
      - PROTOBUF_FIELD_OFFSET(RunwayLightsStruct, approachlights_intensity_)>(
          reinterpret_cast<char*>(&approachlights_intensity_),
          reinterpret_cast<char*>(&other->approachlights_intensity_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RunwayLightsStruct::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class SMGCSLightsStruct::_Internal {
 public:
};

SMGCSLightsStruct::SMGCSLightsStruct(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.SMGCSLightsStruct)
}
SMGCSLightsStruct::SMGCSLightsStruct(const SMGCSLightsStruct& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&smgcs_route_seg_lights_, &from.smgcs_route_seg_lights_,
    static_cast<size_t>(reinterpret_cast<char*>(&smgcs_route_seg_stopbar_) -
    reinterpret_cast<char*>(&smgcs_route_seg_lights_)) + sizeof(smgcs_route_seg_stopbar_));
  // @@protoc_insertion_point(copy_constructor:ce.net.SMGCSLightsStruct)
}

void SMGCSLightsStruct::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&smgcs_route_seg_lights_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&smgcs_route_seg_stopbar_) -
      reinterpret_cast<char*>(&smgcs_route_seg_lights_)) + sizeof(smgcs_route_seg_stopbar_));
}

SMGCSLightsStruct::~SMGCSLightsStruct() {
  // @@protoc_insertion_point(destructor:ce.net.SMGCSLightsStruct)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void SMGCSLightsStruct::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void SMGCSLightsStruct::ArenaDtor(void* object) {
  SMGCSLightsStruct* _this = reinterpret_cast< SMGCSLightsStruct* >(object);
  (void)_this;
}
void SMGCSLightsStruct::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SMGCSLightsStruct::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SMGCSLightsStruct& SMGCSLightsStruct::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SMGCSLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base);
  return *internal_default_instance();
}


void SMGCSLightsStruct::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.SMGCSLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&smgcs_route_seg_lights_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&smgcs_route_seg_stopbar_) -
      reinterpret_cast<char*>(&smgcs_route_seg_lights_)) + sizeof(smgcs_route_seg_stopbar_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SMGCSLightsStruct::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .ce.net.EnumLightsIntensity smgcs_route_seg_lights = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_smgcs_route_seg_lights(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity smgcs_route_seg_stopbar = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_smgcs_route_seg_stopbar(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SMGCSLightsStruct::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.SMGCSLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .ce.net.EnumLightsIntensity smgcs_route_seg_lights = 1;
  if (this->smgcs_route_seg_lights() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_smgcs_route_seg_lights(), target);
  }

  // .ce.net.EnumLightsIntensity smgcs_route_seg_stopbar = 2;
  if (this->smgcs_route_seg_stopbar() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_smgcs_route_seg_stopbar(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.SMGCSLightsStruct)
  return target;
}

size_t SMGCSLightsStruct::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.SMGCSLightsStruct)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .ce.net.EnumLightsIntensity smgcs_route_seg_lights = 1;
  if (this->smgcs_route_seg_lights() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_smgcs_route_seg_lights());
  }

  // .ce.net.EnumLightsIntensity smgcs_route_seg_stopbar = 2;
  if (this->smgcs_route_seg_stopbar() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_smgcs_route_seg_stopbar());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SMGCSLightsStruct::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.SMGCSLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  const SMGCSLightsStruct* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SMGCSLightsStruct>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.SMGCSLightsStruct)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.SMGCSLightsStruct)
    MergeFrom(*source);
  }
}

void SMGCSLightsStruct::MergeFrom(const SMGCSLightsStruct& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.SMGCSLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.smgcs_route_seg_lights() != 0) {
    _internal_set_smgcs_route_seg_lights(from._internal_smgcs_route_seg_lights());
  }
  if (from.smgcs_route_seg_stopbar() != 0) {
    _internal_set_smgcs_route_seg_stopbar(from._internal_smgcs_route_seg_stopbar());
  }
}

void SMGCSLightsStruct::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.SMGCSLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SMGCSLightsStruct::CopyFrom(const SMGCSLightsStruct& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.SMGCSLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SMGCSLightsStruct::IsInitialized() const {
  return true;
}

void SMGCSLightsStruct::InternalSwap(SMGCSLightsStruct* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SMGCSLightsStruct, smgcs_route_seg_stopbar_)
      + sizeof(SMGCSLightsStruct::smgcs_route_seg_stopbar_)
      - PROTOBUF_FIELD_OFFSET(SMGCSLightsStruct, smgcs_route_seg_lights_)>(
          reinterpret_cast<char*>(&smgcs_route_seg_lights_),
          reinterpret_cast<char*>(&other->smgcs_route_seg_lights_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SMGCSLightsStruct::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class EnvironmentLightsStruct::_Internal {
 public:
};

EnvironmentLightsStruct::EnvironmentLightsStruct(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.EnvironmentLightsStruct)
}
EnvironmentLightsStruct::EnvironmentLightsStruct(const EnvironmentLightsStruct& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&majorarterieslights_intensity_, &from.majorarterieslights_intensity_,
    static_cast<size_t>(reinterpret_cast<char*>(&trafficlights_intensity_) -
    reinterpret_cast<char*>(&majorarterieslights_intensity_)) + sizeof(trafficlights_intensity_));
  // @@protoc_insertion_point(copy_constructor:ce.net.EnvironmentLightsStruct)
}

void EnvironmentLightsStruct::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&majorarterieslights_intensity_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&trafficlights_intensity_) -
      reinterpret_cast<char*>(&majorarterieslights_intensity_)) + sizeof(trafficlights_intensity_));
}

EnvironmentLightsStruct::~EnvironmentLightsStruct() {
  // @@protoc_insertion_point(destructor:ce.net.EnvironmentLightsStruct)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void EnvironmentLightsStruct::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void EnvironmentLightsStruct::ArenaDtor(void* object) {
  EnvironmentLightsStruct* _this = reinterpret_cast< EnvironmentLightsStruct* >(object);
  (void)_this;
}
void EnvironmentLightsStruct::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EnvironmentLightsStruct::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const EnvironmentLightsStruct& EnvironmentLightsStruct::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_EnvironmentLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base);
  return *internal_default_instance();
}


void EnvironmentLightsStruct::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.EnvironmentLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&majorarterieslights_intensity_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&trafficlights_intensity_) -
      reinterpret_cast<char*>(&majorarterieslights_intensity_)) + sizeof(trafficlights_intensity_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EnvironmentLightsStruct::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .ce.net.EnumLightsIntensity majorarterieslights_intensity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_majorarterieslights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity smallroadlights_intensity = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_smallroadlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity residentlights_intensity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_residentlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity trafficlights_intensity = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_trafficlights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EnvironmentLightsStruct::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.EnvironmentLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .ce.net.EnumLightsIntensity majorarterieslights_intensity = 1;
  if (this->majorarterieslights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_majorarterieslights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity smallroadlights_intensity = 2;
  if (this->smallroadlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_smallroadlights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity residentlights_intensity = 3;
  if (this->residentlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_residentlights_intensity(), target);
  }

  // .ce.net.EnumLightsIntensity trafficlights_intensity = 4;
  if (this->trafficlights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_trafficlights_intensity(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.EnvironmentLightsStruct)
  return target;
}

size_t EnvironmentLightsStruct::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.EnvironmentLightsStruct)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .ce.net.EnumLightsIntensity majorarterieslights_intensity = 1;
  if (this->majorarterieslights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_majorarterieslights_intensity());
  }

  // .ce.net.EnumLightsIntensity smallroadlights_intensity = 2;
  if (this->smallroadlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_smallroadlights_intensity());
  }

  // .ce.net.EnumLightsIntensity residentlights_intensity = 3;
  if (this->residentlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_residentlights_intensity());
  }

  // .ce.net.EnumLightsIntensity trafficlights_intensity = 4;
  if (this->trafficlights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_trafficlights_intensity());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EnvironmentLightsStruct::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.EnvironmentLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  const EnvironmentLightsStruct* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<EnvironmentLightsStruct>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.EnvironmentLightsStruct)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.EnvironmentLightsStruct)
    MergeFrom(*source);
  }
}

void EnvironmentLightsStruct::MergeFrom(const EnvironmentLightsStruct& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.EnvironmentLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.majorarterieslights_intensity() != 0) {
    _internal_set_majorarterieslights_intensity(from._internal_majorarterieslights_intensity());
  }
  if (from.smallroadlights_intensity() != 0) {
    _internal_set_smallroadlights_intensity(from._internal_smallroadlights_intensity());
  }
  if (from.residentlights_intensity() != 0) {
    _internal_set_residentlights_intensity(from._internal_residentlights_intensity());
  }
  if (from.trafficlights_intensity() != 0) {
    _internal_set_trafficlights_intensity(from._internal_trafficlights_intensity());
  }
}

void EnvironmentLightsStruct::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.EnvironmentLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EnvironmentLightsStruct::CopyFrom(const EnvironmentLightsStruct& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.EnvironmentLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnvironmentLightsStruct::IsInitialized() const {
  return true;
}

void EnvironmentLightsStruct::InternalSwap(EnvironmentLightsStruct* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EnvironmentLightsStruct, trafficlights_intensity_)
      + sizeof(EnvironmentLightsStruct::trafficlights_intensity_)
      - PROTOBUF_FIELD_OFFSET(EnvironmentLightsStruct, majorarterieslights_intensity_)>(
          reinterpret_cast<char*>(&majorarterieslights_intensity_),
          reinterpret_cast<char*>(&other->majorarterieslights_intensity_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EnvironmentLightsStruct::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class AirportLightsStruct::_Internal {
 public:
  static const ::ce::net::EnvironmentLightsStruct& environment_lights(const AirportLightsStruct* msg);
};

const ::ce::net::EnvironmentLightsStruct&
AirportLightsStruct::_Internal::environment_lights(const AirportLightsStruct* msg) {
  return *msg->environment_lights_;
}
AirportLightsStruct::AirportLightsStruct(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  smgcs_lights_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.AirportLightsStruct)
}
AirportLightsStruct::AirportLightsStruct(const AirportLightsStruct& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      smgcs_lights_(from.smgcs_lights_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_environment_lights()) {
    environment_lights_ = new ::ce::net::EnvironmentLightsStruct(*from.environment_lights_);
  } else {
    environment_lights_ = nullptr;
  }
  ::memcpy(&taxilights_intensity_, &from.taxilights_intensity_,
    static_cast<size_t>(reinterpret_cast<char*>(&smgcs_route_sel_) -
    reinterpret_cast<char*>(&taxilights_intensity_)) + sizeof(smgcs_route_sel_));
  // @@protoc_insertion_point(copy_constructor:ce.net.AirportLightsStruct)
}

void AirportLightsStruct::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_AirportLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&environment_lights_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&smgcs_route_sel_) -
      reinterpret_cast<char*>(&environment_lights_)) + sizeof(smgcs_route_sel_));
}

AirportLightsStruct::~AirportLightsStruct() {
  // @@protoc_insertion_point(destructor:ce.net.AirportLightsStruct)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void AirportLightsStruct::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete environment_lights_;
}

void AirportLightsStruct::ArenaDtor(void* object) {
  AirportLightsStruct* _this = reinterpret_cast< AirportLightsStruct* >(object);
  (void)_this;
}
void AirportLightsStruct::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AirportLightsStruct::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const AirportLightsStruct& AirportLightsStruct::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AirportLightsStruct_FFS_5fVSP_2fProtocolStructs_2fCtrlModel_2fReservedModel_2fAirportModel_2fAirportLightStruct_2eproto.base);
  return *internal_default_instance();
}


void AirportLightsStruct::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.AirportLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  smgcs_lights_.Clear();
  if (GetArena() == nullptr && environment_lights_ != nullptr) {
    delete environment_lights_;
  }
  environment_lights_ = nullptr;
  ::memset(&taxilights_intensity_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&smgcs_route_sel_) -
      reinterpret_cast<char*>(&taxilights_intensity_)) + sizeof(smgcs_route_sel_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AirportLightsStruct::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // bool ap_rotating_beacon = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ap_rotating_beacon_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .ce.net.EnumLightsIntensity taxilights_intensity = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_taxilights_intensity(static_cast<::ce::net::EnumLightsIntensity>(val));
        } else goto handle_unusual;
        continue;
      // bool stop_bar = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          stop_bar_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool random_intensity = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          random_intensity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool smgcs_enable = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          smgcs_enable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 smgcs_route_sel = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          smgcs_route_sel_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .ce.net.SMGCSLightsStruct smgcs_lights = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_smgcs_lights(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else goto handle_unusual;
        continue;
      // .ce.net.EnvironmentLightsStruct environment_lights = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_environment_lights(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* AirportLightsStruct::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.AirportLightsStruct)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool ap_rotating_beacon = 1;
  if (this->ap_rotating_beacon() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_ap_rotating_beacon(), target);
  }

  // .ce.net.EnumLightsIntensity taxilights_intensity = 2;
  if (this->taxilights_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_taxilights_intensity(), target);
  }

  // bool stop_bar = 3;
  if (this->stop_bar() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_stop_bar(), target);
  }

  // bool random_intensity = 4;
  if (this->random_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_random_intensity(), target);
  }

  // bool smgcs_enable = 5;
  if (this->smgcs_enable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_smgcs_enable(), target);
  }

  // uint32 smgcs_route_sel = 6;
  if (this->smgcs_route_sel() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_smgcs_route_sel(), target);
  }

  // repeated .ce.net.SMGCSLightsStruct smgcs_lights = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_smgcs_lights_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_smgcs_lights(i), target, stream);
  }

  // .ce.net.EnvironmentLightsStruct environment_lights = 8;
  if (this->has_environment_lights()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::environment_lights(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.AirportLightsStruct)
  return target;
}

size_t AirportLightsStruct::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.AirportLightsStruct)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.SMGCSLightsStruct smgcs_lights = 7;
  total_size += 1UL * this->_internal_smgcs_lights_size();
  for (const auto& msg : this->smgcs_lights_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .ce.net.EnvironmentLightsStruct environment_lights = 8;
  if (this->has_environment_lights()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *environment_lights_);
  }

  // .ce.net.EnumLightsIntensity taxilights_intensity = 2;
  if (this->taxilights_intensity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_taxilights_intensity());
  }

  // bool ap_rotating_beacon = 1;
  if (this->ap_rotating_beacon() != 0) {
    total_size += 1 + 1;
  }

  // bool stop_bar = 3;
  if (this->stop_bar() != 0) {
    total_size += 1 + 1;
  }

  // bool random_intensity = 4;
  if (this->random_intensity() != 0) {
    total_size += 1 + 1;
  }

  // bool smgcs_enable = 5;
  if (this->smgcs_enable() != 0) {
    total_size += 1 + 1;
  }

  // uint32 smgcs_route_sel = 6;
  if (this->smgcs_route_sel() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_smgcs_route_sel());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AirportLightsStruct::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.AirportLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  const AirportLightsStruct* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<AirportLightsStruct>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.AirportLightsStruct)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.AirportLightsStruct)
    MergeFrom(*source);
  }
}

void AirportLightsStruct::MergeFrom(const AirportLightsStruct& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.AirportLightsStruct)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  smgcs_lights_.MergeFrom(from.smgcs_lights_);
  if (from.has_environment_lights()) {
    _internal_mutable_environment_lights()->::ce::net::EnvironmentLightsStruct::MergeFrom(from._internal_environment_lights());
  }
  if (from.taxilights_intensity() != 0) {
    _internal_set_taxilights_intensity(from._internal_taxilights_intensity());
  }
  if (from.ap_rotating_beacon() != 0) {
    _internal_set_ap_rotating_beacon(from._internal_ap_rotating_beacon());
  }
  if (from.stop_bar() != 0) {
    _internal_set_stop_bar(from._internal_stop_bar());
  }
  if (from.random_intensity() != 0) {
    _internal_set_random_intensity(from._internal_random_intensity());
  }
  if (from.smgcs_enable() != 0) {
    _internal_set_smgcs_enable(from._internal_smgcs_enable());
  }
  if (from.smgcs_route_sel() != 0) {
    _internal_set_smgcs_route_sel(from._internal_smgcs_route_sel());
  }
}

void AirportLightsStruct::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.AirportLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AirportLightsStruct::CopyFrom(const AirportLightsStruct& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.AirportLightsStruct)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AirportLightsStruct::IsInitialized() const {
  return true;
}

void AirportLightsStruct::InternalSwap(AirportLightsStruct* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  smgcs_lights_.InternalSwap(&other->smgcs_lights_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AirportLightsStruct, smgcs_route_sel_)
      + sizeof(AirportLightsStruct::smgcs_route_sel_)
      - PROTOBUF_FIELD_OFFSET(AirportLightsStruct, environment_lights_)>(
          reinterpret_cast<char*>(&environment_lights_),
          reinterpret_cast<char*>(&other->environment_lights_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AirportLightsStruct::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace net
}  // namespace ce
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::ce::net::RunwayLightsStruct* Arena::CreateMaybeMessage< ::ce::net::RunwayLightsStruct >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::RunwayLightsStruct >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::SMGCSLightsStruct* Arena::CreateMaybeMessage< ::ce::net::SMGCSLightsStruct >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::SMGCSLightsStruct >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::EnvironmentLightsStruct* Arena::CreateMaybeMessage< ::ce::net::EnvironmentLightsStruct >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::EnvironmentLightsStruct >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::AirportLightsStruct* Arena::CreateMaybeMessage< ::ce::net::AirportLightsStruct >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::AirportLightsStruct >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
