// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FFS_CC/CC_Ultra_Lights_Communication.proto

#include "FFS_CC/CC_Ultra_Lights_Communication.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fCC_2fCC_5fLights_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_AircraftLights_FFS_5fCC_2fCC_5fLights_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fCC_2fCC_5fLights_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_AirportLights_FFS_5fCC_2fCC_5fLights_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fCC_2fCC_5fLights_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_EnvironmentLights_FFS_5fCC_2fCC_5fLights_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fCC_2fCC_5fLights_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RunwayLights_FFS_5fCC_2fCC_5fLights_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto;
namespace ce {
namespace net {
namespace cc {
class ImageGeneratorSceneLightsDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSceneLights> _instance;
} _ImageGeneratorSceneLights_default_instance_;
class ImageGeneratorSceneLightsUltraRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSceneLightsUltraRequest> _instance;
} _ImageGeneratorSceneLightsUltraRequest_default_instance_;
class ImageGeneratorSceneLightsUltraResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSceneLightsUltraResponse> _instance;
} _ImageGeneratorSceneLightsUltraResponse_default_instance_;
class ImageGeneratorSceneLightsUltraSetRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSceneLightsUltraSetRequest> _instance;
} _ImageGeneratorSceneLightsUltraSetRequest_default_instance_;
class ImageGeneratorSceneLightsUltraSetResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSceneLightsUltraSetResponse> _instance;
} _ImageGeneratorSceneLightsUltraSetResponse_default_instance_;
class ImageGeneratorAircraftLightsUltraRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorAircraftLightsUltraRequest> _instance;
} _ImageGeneratorAircraftLightsUltraRequest_default_instance_;
class ImageGeneratorAircraftLightsUltraResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorAircraftLightsUltraResponse> _instance;
} _ImageGeneratorAircraftLightsUltraResponse_default_instance_;
class ImageGeneratorAircraftLightsUltraSetRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorAircraftLightsUltraSetRequest> _instance;
} _ImageGeneratorAircraftLightsUltraSetRequest_default_instance_;
class ImageGeneratorAircraftLightsUltraSetResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorAircraftLightsUltraSetResponse> _instance;
} _ImageGeneratorAircraftLightsUltraSetResponse_default_instance_;
class ImageGeneratorSMGCSLightsDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSMGCSLights> _instance;
} _ImageGeneratorSMGCSLights_default_instance_;
class ImageGeneratorSMGCSLightsUltraRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSMGCSLightsUltraRequest> _instance;
} _ImageGeneratorSMGCSLightsUltraRequest_default_instance_;
class ImageGeneratorSMGCSLightsUltraResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSMGCSLightsUltraResponse> _instance;
} _ImageGeneratorSMGCSLightsUltraResponse_default_instance_;
class ImageGeneratorSMGCSLightsSetRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSMGCSLightsSetRequest> _instance;
} _ImageGeneratorSMGCSLightsSetRequest_default_instance_;
class ImageGeneratorSMGCSLightsSetResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorSMGCSLightsSetResponse> _instance;
} _ImageGeneratorSMGCSLightsSetResponse_default_instance_;
}  // namespace cc
}  // namespace net
}  // namespace ce
static void InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorAircraftLightsUltraRequest_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ImageGeneratorAircraftLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {}};

static void InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorAircraftLightsUltraResponse_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorAircraftLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_AircraftLights_FFS_5fCC_2fCC_5fLights_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorAircraftLightsUltraSetRequest_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorAircraftLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_AircraftLights_FFS_5fCC_2fCC_5fLights_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorAircraftLightsUltraSetResponse_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorAircraftLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorAircraftLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_AircraftLights_FFS_5fCC_2fCC_5fLights_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSMGCSLights_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSMGCSLights();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {}};

static void InitDefaultsscc_info_ImageGeneratorSMGCSLightsSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSMGCSLightsSetRequest_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorSMGCSLightsSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorSMGCSLightsSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorSMGCSLightsSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSMGCSLightsSetResponse_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorSMGCSLightsSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorSMGCSLightsSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorSMGCSLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSMGCSLightsUltraRequest_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ImageGeneratorSMGCSLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ImageGeneratorSMGCSLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {}};

static void InitDefaultsscc_info_ImageGeneratorSMGCSLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSMGCSLightsUltraResponse_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorSMGCSLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorSMGCSLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSceneLights_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSceneLights();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, 0, InitDefaultsscc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_EnvironmentLights_FFS_5fCC_2fCC_5fLights_2eproto.base,
      &scc_info_AirportLights_FFS_5fCC_2fCC_5fLights_2eproto.base,
      &scc_info_RunwayLights_FFS_5fCC_2fCC_5fLights_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorSceneLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSceneLightsUltraRequest_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSceneLightsUltraRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ImageGeneratorSceneLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ImageGeneratorSceneLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {}};

static void InitDefaultsscc_info_ImageGeneratorSceneLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSceneLightsUltraResponse_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSceneLightsUltraResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorSceneLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorSceneLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorSceneLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSceneLightsUltraSetRequest_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorSceneLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorSceneLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorSceneLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorSceneLightsUltraSetResponse_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_ImageGeneratorSceneLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_ImageGeneratorSceneLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto}, {
      &scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto[14];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLights, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLights, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLights, icao_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLights, environment_lights_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLights, airport_lights_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLights, runway_lights_),
  0,
  1,
  2,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraRequest, icao_),
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse, err_code_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse, err_message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse, ultra_on_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse, content_),
  2,
  0,
  3,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest, ultra_on_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest, content_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse, err_code_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse, err_message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse, ultra_on_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse, content_),
  2,
  0,
  3,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest, model_),
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse, err_code_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse, err_message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse, ultra_on_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse, content_),
  2,
  0,
  3,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest, ultra_on_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest, model_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest, content_),
  2,
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse, err_code_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse, err_message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse, ultra_on_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse, content_),
  2,
  0,
  3,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLights, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLights, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLights, ultra_on_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLights, icao_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLights, routes_),
  1,
  0,
  ~0u,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest, icao_),
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse, err_code_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse, err_message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse, content_),
  2,
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest, content_),
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse, err_code_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse, err_message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse, content_),
  2,
  0,
  1,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 9, sizeof(::ce::net::cc::ImageGeneratorSceneLights)},
  { 13, 19, sizeof(::ce::net::cc::ImageGeneratorSceneLightsUltraRequest)},
  { 20, 29, sizeof(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse)},
  { 33, 40, sizeof(::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest)},
  { 42, 51, sizeof(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse)},
  { 55, 61, sizeof(::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest)},
  { 62, 71, sizeof(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse)},
  { 75, 83, sizeof(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest)},
  { 86, 95, sizeof(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse)},
  { 99, 107, sizeof(::ce::net::cc::ImageGeneratorSMGCSLights)},
  { 110, 116, sizeof(::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest)},
  { 117, 125, sizeof(::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse)},
  { 128, 134, sizeof(::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest)},
  { 135, 143, sizeof(::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSceneLights_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSceneLightsUltraRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSceneLightsUltraResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSceneLightsUltraSetRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSceneLightsUltraSetResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorAircraftLightsUltraRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorAircraftLightsUltraResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorAircraftLightsUltraSetRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorAircraftLightsUltraSetResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSMGCSLights_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSMGCSLightsUltraRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSMGCSLightsUltraResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSMGCSLightsSetRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorSMGCSLightsSetResponse_default_instance_),
};

const char descriptor_table_protodef_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n*FFS_CC/CC_Ultra_Lights_Communication.p"
  "roto\022\tce.net.cc\032\026FFS_CC/CC_Lights.proto\""
  "\274\001\n\031ImageGeneratorSceneLights\022\014\n\004icao\030\002 "
  "\002(\t\0225\n\022environment_lights\030\003 \002(\0132\031.ce.net"
  ".EnvironmentLights\022-\n\016airport_lights\030\004 \002"
  "(\0132\025.ce.net.AirportLights\022+\n\rrunway_ligh"
  "ts\030\005 \003(\0132\024.ce.net.RunwayLights\"5\n%ImageG"
  "eneratorSceneLightsUltraRequest\022\014\n\004icao\030"
  "\001 \001(\t\"\265\002\n&ImageGeneratorSceneLightsUltra"
  "Response\022d\n\010err_code\030\001 \002(\0162R.ce.net.cc.I"
  "mageGeneratorSceneLightsUltraResponse.En"
  "umSceneLightsUltra_OperateCode\022\023\n\013err_me"
  "ssage\030\002 \001(\t\022\020\n\010ultra_on\030\003 \002(\010\0225\n\007content"
  "\030\004 \001(\0132$.ce.net.cc.ImageGeneratorSceneLi"
  "ghts\"G\n EnumSceneLightsUltra_OperateCode"
  "\022\013\n\007Success\020\000\022\026\n\022FailedForWrongICAO\020\001\"s\n"
  "(ImageGeneratorSceneLightsUltraSetReques"
  "t\022\020\n\010ultra_on\030\001 \002(\010\0225\n\007content\030\002 \001(\0132$.c"
  "e.net.cc.ImageGeneratorSceneLights\"\273\002\n)I"
  "mageGeneratorSceneLightsUltraSetResponse"
  "\022g\n\010err_code\030\001 \002(\0162U.ce.net.cc.ImageGene"
  "ratorSceneLightsUltraSetResponse.EnumSce"
  "neLightsUltra_OperateCode\022\023\n\013err_message"
  "\030\002 \001(\t\022\020\n\010ultra_on\030\003 \002(\010\0225\n\007content\030\004 \002("
  "\0132$.ce.net.cc.ImageGeneratorSceneLights\""
  "G\n EnumSceneLightsUltra_OperateCode\022\013\n\007S"
  "uccess\020\000\022\026\n\022FailedForWrongICAO\020\001\"9\n(Imag"
  "eGeneratorAircraftLightsUltraRequest\022\r\n\005"
  "model\030\001 \001(\t\"\264\002\n)ImageGeneratorAircraftLi"
  "ghtsUltraResponse\022j\n\010err_code\030\001 \002(\0162X.ce"
  ".net.cc.ImageGeneratorAircraftLightsUltr"
  "aResponse.EnumAircraftLightsUltra_Operat"
  "eCode\022\023\n\013err_message\030\002 \001(\t\022\020\n\010ultra_on\030\003"
  " \002(\010\022\'\n\007content\030\004 \001(\0132\026.ce.net.AircraftL"
  "ights\"K\n#EnumAircraftLightsUltra_Operate"
  "Code\022\013\n\007Success\020\000\022\027\n\023FailedForWrongModel"
  "\020\001\"w\n+ImageGeneratorAircraftLightsUltraS"
  "etRequest\022\020\n\010ultra_on\030\001 \002(\010\022\r\n\005model\030\002 \001"
  "(\t\022\'\n\007content\030\003 \001(\0132\026.ce.net.AircraftLig"
  "hts\"\272\002\n,ImageGeneratorAircraftLightsUltr"
  "aSetResponse\022m\n\010err_code\030\001 \002(\0162[.ce.net."
  "cc.ImageGeneratorAircraftLightsUltraSetR"
  "esponse.EnumAircraftLightsUltra_OperateC"
  "ode\022\023\n\013err_message\030\002 \001(\t\022\020\n\010ultra_on\030\003 \002"
  "(\010\022\'\n\007content\030\004 \002(\0132\026.ce.net.AircraftLig"
  "hts\"K\n#EnumAircraftLightsUltra_OperateCo"
  "de\022\013\n\007Success\020\000\022\027\n\023FailedForWrongModel\020\001"
  "\"K\n\031ImageGeneratorSMGCSLights\022\020\n\010ultra_o"
  "n\030\001 \002(\010\022\014\n\004icao\030\002 \002(\t\022\016\n\006routes\030\003 \003(\t\"5\n"
  "%ImageGeneratorSMGCSLightsUltraRequest\022\014"
  "\n\004icao\030\001 \002(\t\"\206\001\n&ImageGeneratorSMGCSLigh"
  "tsUltraResponse\022\020\n\010err_code\030\001 \002(\r\022\023\n\013err"
  "_message\030\002 \001(\t\0225\n\007content\030\003 \002(\0132$.ce.net"
  ".cc.ImageGeneratorSMGCSLights\"\\\n#ImageGe"
  "neratorSMGCSLightsSetRequest\0225\n\007content\030"
  "\001 \002(\0132$.ce.net.cc.ImageGeneratorSMGCSLig"
  "hts\"\204\001\n$ImageGeneratorSMGCSLightsSetResp"
  "onse\022\020\n\010err_code\030\001 \002(\r\022\023\n\013err_message\030\002 "
  "\001(\t\0225\n\007content\030\003 \002(\0132$.ce.net.cc.ImageGe"
  "neratorSMGCSLights"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto_deps[1] = {
  &::descriptor_table_FFS_5fCC_2fCC_5fLights_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto_sccs[14] = {
  &scc_info_ImageGeneratorAircraftLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorAircraftLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorAircraftLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorAircraftLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSMGCSLightsSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSMGCSLightsSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSMGCSLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSMGCSLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSceneLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSceneLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSceneLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
  &scc_info_ImageGeneratorSceneLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto = {
  false, false, descriptor_table_protodef_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto, "FFS_CC/CC_Ultra_Lights_Communication.proto", 2378,
  &descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto_once, descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto_sccs, descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto_deps, 14, 1,
  schemas, file_default_instances, TableStruct_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto::offsets,
  file_level_metadata_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto, 14, file_level_enum_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto, file_level_service_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto)), true);
namespace ce {
namespace net {
namespace cc {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto);
  return file_level_enum_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto[0];
}
bool ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraResponse::Success;
constexpr ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraResponse::FailedForWrongICAO;
constexpr ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraResponse::EnumSceneLightsUltra_OperateCode_MIN;
constexpr ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraResponse::EnumSceneLightsUltra_OperateCode_MAX;
constexpr int ImageGeneratorSceneLightsUltraResponse::EnumSceneLightsUltra_OperateCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto);
  return file_level_enum_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto[1];
}
bool ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraSetResponse::Success;
constexpr ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraSetResponse::FailedForWrongICAO;
constexpr ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraSetResponse::EnumSceneLightsUltra_OperateCode_MIN;
constexpr ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode ImageGeneratorSceneLightsUltraSetResponse::EnumSceneLightsUltra_OperateCode_MAX;
constexpr int ImageGeneratorSceneLightsUltraSetResponse::EnumSceneLightsUltra_OperateCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto);
  return file_level_enum_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto[2];
}
bool ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraResponse::Success;
constexpr ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraResponse::FailedForWrongModel;
constexpr ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraResponse::EnumAircraftLightsUltra_OperateCode_MIN;
constexpr ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraResponse::EnumAircraftLightsUltra_OperateCode_MAX;
constexpr int ImageGeneratorAircraftLightsUltraResponse::EnumAircraftLightsUltra_OperateCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto);
  return file_level_enum_descriptors_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto[3];
}
bool ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraSetResponse::Success;
constexpr ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraSetResponse::FailedForWrongModel;
constexpr ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraSetResponse::EnumAircraftLightsUltra_OperateCode_MIN;
constexpr ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode ImageGeneratorAircraftLightsUltraSetResponse::EnumAircraftLightsUltra_OperateCode_MAX;
constexpr int ImageGeneratorAircraftLightsUltraSetResponse::EnumAircraftLightsUltra_OperateCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)

// ===================================================================

class ImageGeneratorSceneLights::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSceneLights>()._has_bits_);
  static void set_has_icao(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::ce::net::EnvironmentLights& environment_lights(const ImageGeneratorSceneLights* msg);
  static void set_has_environment_lights(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::ce::net::AirportLights& airport_lights(const ImageGeneratorSceneLights* msg);
  static void set_has_airport_lights(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

const ::ce::net::EnvironmentLights&
ImageGeneratorSceneLights::_Internal::environment_lights(const ImageGeneratorSceneLights* msg) {
  return *msg->environment_lights_;
}
const ::ce::net::AirportLights&
ImageGeneratorSceneLights::_Internal::airport_lights(const ImageGeneratorSceneLights* msg) {
  return *msg->airport_lights_;
}
void ImageGeneratorSceneLights::clear_environment_lights() {
  if (environment_lights_ != nullptr) environment_lights_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
void ImageGeneratorSceneLights::clear_airport_lights() {
  if (airport_lights_ != nullptr) airport_lights_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
void ImageGeneratorSceneLights::clear_runway_lights() {
  runway_lights_.Clear();
}
ImageGeneratorSceneLights::ImageGeneratorSceneLights(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  runway_lights_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSceneLights)
}
ImageGeneratorSceneLights::ImageGeneratorSceneLights(const ImageGeneratorSceneLights& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      runway_lights_(from.runway_lights_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_icao()) {
    icao_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icao(), 
      GetArena());
  }
  if (from._internal_has_environment_lights()) {
    environment_lights_ = new ::ce::net::EnvironmentLights(*from.environment_lights_);
  } else {
    environment_lights_ = nullptr;
  }
  if (from._internal_has_airport_lights()) {
    airport_lights_ = new ::ce::net::AirportLights(*from.airport_lights_);
  } else {
    airport_lights_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSceneLights)
}

void ImageGeneratorSceneLights::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&environment_lights_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&airport_lights_) -
      reinterpret_cast<char*>(&environment_lights_)) + sizeof(airport_lights_));
}

ImageGeneratorSceneLights::~ImageGeneratorSceneLights() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSceneLights)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSceneLights::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  icao_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete environment_lights_;
  if (this != internal_default_instance()) delete airport_lights_;
}

void ImageGeneratorSceneLights::ArenaDtor(void* object) {
  ImageGeneratorSceneLights* _this = reinterpret_cast< ImageGeneratorSceneLights* >(object);
  (void)_this;
}
void ImageGeneratorSceneLights::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSceneLights::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSceneLights& ImageGeneratorSceneLights::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSceneLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSceneLights::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSceneLights)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  runway_lights_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      icao_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(environment_lights_ != nullptr);
      environment_lights_->Clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(airport_lights_ != nullptr);
      airport_lights_->Clear();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSceneLights::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string icao = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_icao();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSceneLights.icao");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.EnvironmentLights environment_lights = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_environment_lights(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.AirportLights airport_lights = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_airport_lights(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .ce.net.RunwayLights runway_lights = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_runway_lights(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSceneLights::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSceneLights)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string icao = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_icao().data(), static_cast<int>(this->_internal_icao().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSceneLights.icao");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_icao(), target);
  }

  // required .ce.net.EnvironmentLights environment_lights = 3;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::environment_lights(this), target, stream);
  }

  // required .ce.net.AirportLights airport_lights = 4;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::airport_lights(this), target, stream);
  }

  // repeated .ce.net.RunwayLights runway_lights = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_runway_lights_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_runway_lights(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSceneLights)
  return target;
}

size_t ImageGeneratorSceneLights::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorSceneLights)
  size_t total_size = 0;

  if (_internal_has_icao()) {
    // required string icao = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icao());
  }

  if (_internal_has_environment_lights()) {
    // required .ce.net.EnvironmentLights environment_lights = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *environment_lights_);
  }

  if (_internal_has_airport_lights()) {
    // required .ce.net.AirportLights airport_lights = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *airport_lights_);
  }

  return total_size;
}
size_t ImageGeneratorSceneLights::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSceneLights)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string icao = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icao());

    // required .ce.net.EnvironmentLights environment_lights = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *environment_lights_);

    // required .ce.net.AirportLights airport_lights = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *airport_lights_);

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.RunwayLights runway_lights = 5;
  total_size += 1UL * this->_internal_runway_lights_size();
  for (const auto& msg : this->runway_lights_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSceneLights::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSceneLights)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSceneLights* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSceneLights>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSceneLights)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSceneLights)
    MergeFrom(*source);
  }
}

void ImageGeneratorSceneLights::MergeFrom(const ImageGeneratorSceneLights& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSceneLights)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  runway_lights_.MergeFrom(from.runway_lights_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_icao(from._internal_icao());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_environment_lights()->::ce::net::EnvironmentLights::MergeFrom(from._internal_environment_lights());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_airport_lights()->::ce::net::AirportLights::MergeFrom(from._internal_airport_lights());
    }
  }
}

void ImageGeneratorSceneLights::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSceneLights)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSceneLights::CopyFrom(const ImageGeneratorSceneLights& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSceneLights)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSceneLights::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(runway_lights_)) return false;
  if (_internal_has_environment_lights()) {
    if (!environment_lights_->IsInitialized()) return false;
  }
  if (_internal_has_airport_lights()) {
    if (!airport_lights_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorSceneLights::InternalSwap(ImageGeneratorSceneLights* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  runway_lights_.InternalSwap(&other->runway_lights_);
  icao_.Swap(&other->icao_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLights, airport_lights_)
      + sizeof(ImageGeneratorSceneLights::airport_lights_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLights, environment_lights_)>(
          reinterpret_cast<char*>(&environment_lights_),
          reinterpret_cast<char*>(&other->environment_lights_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSceneLights::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSceneLightsUltraRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSceneLightsUltraRequest>()._has_bits_);
  static void set_has_icao(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

ImageGeneratorSceneLightsUltraRequest::ImageGeneratorSceneLightsUltraRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
}
ImageGeneratorSceneLightsUltraRequest::ImageGeneratorSceneLightsUltraRequest(const ImageGeneratorSceneLightsUltraRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_icao()) {
    icao_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icao(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
}

void ImageGeneratorSceneLightsUltraRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSceneLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

ImageGeneratorSceneLightsUltraRequest::~ImageGeneratorSceneLightsUltraRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSceneLightsUltraRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  icao_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ImageGeneratorSceneLightsUltraRequest::ArenaDtor(void* object) {
  ImageGeneratorSceneLightsUltraRequest* _this = reinterpret_cast< ImageGeneratorSceneLightsUltraRequest* >(object);
  (void)_this;
}
void ImageGeneratorSceneLightsUltraRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSceneLightsUltraRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSceneLightsUltraRequest& ImageGeneratorSceneLightsUltraRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSceneLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSceneLightsUltraRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    icao_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSceneLightsUltraRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string icao = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_icao();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSceneLightsUltraRequest.icao");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSceneLightsUltraRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string icao = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_icao().data(), static_cast<int>(this->_internal_icao().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSceneLightsUltraRequest.icao");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_icao(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  return target;
}

size_t ImageGeneratorSceneLightsUltraRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string icao = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icao());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSceneLightsUltraRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSceneLightsUltraRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSceneLightsUltraRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
    MergeFrom(*source);
  }
}

void ImageGeneratorSceneLightsUltraRequest::MergeFrom(const ImageGeneratorSceneLightsUltraRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_icao()) {
    _internal_set_icao(from._internal_icao());
  }
}

void ImageGeneratorSceneLightsUltraRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSceneLightsUltraRequest::CopyFrom(const ImageGeneratorSceneLightsUltraRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSceneLightsUltraRequest::IsInitialized() const {
  return true;
}

void ImageGeneratorSceneLightsUltraRequest::InternalSwap(ImageGeneratorSceneLightsUltraRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  icao_.Swap(&other->icao_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSceneLightsUltraRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSceneLightsUltraResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSceneLightsUltraResponse>()._has_bits_);
  static void set_has_err_code(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_err_message(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_ultra_on(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::ce::net::cc::ImageGeneratorSceneLights& content(const ImageGeneratorSceneLightsUltraResponse* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x0000000c) ^ 0x0000000c) != 0;
  }
};

const ::ce::net::cc::ImageGeneratorSceneLights&
ImageGeneratorSceneLightsUltraResponse::_Internal::content(const ImageGeneratorSceneLightsUltraResponse* msg) {
  return *msg->content_;
}
ImageGeneratorSceneLightsUltraResponse::ImageGeneratorSceneLightsUltraResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
}
ImageGeneratorSceneLightsUltraResponse::ImageGeneratorSceneLightsUltraResponse(const ImageGeneratorSceneLightsUltraResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_err_message()) {
    err_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_err_message(), 
      GetArena());
  }
  if (from._internal_has_content()) {
    content_ = new ::ce::net::cc::ImageGeneratorSceneLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  ::memcpy(&err_code_, &from.err_code_,
    static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
    reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
}

void ImageGeneratorSceneLightsUltraResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSceneLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
      reinterpret_cast<char*>(&content_)) + sizeof(ultra_on_));
}

ImageGeneratorSceneLightsUltraResponse::~ImageGeneratorSceneLightsUltraResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSceneLightsUltraResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  err_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorSceneLightsUltraResponse::ArenaDtor(void* object) {
  ImageGeneratorSceneLightsUltraResponse* _this = reinterpret_cast< ImageGeneratorSceneLightsUltraResponse* >(object);
  (void)_this;
}
void ImageGeneratorSceneLightsUltraResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSceneLightsUltraResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSceneLightsUltraResponse& ImageGeneratorSceneLightsUltraResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSceneLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSceneLightsUltraResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      err_message_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(content_ != nullptr);
      content_->Clear();
    }
  }
  if (cached_has_bits & 0x0000000cu) {
    ::memset(&err_code_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&ultra_on_) -
        reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSceneLightsUltraResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.ImageGeneratorSceneLightsUltraResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode_IsValid(val))) {
            _internal_set_err_code(static_cast<::ce::net::cc::ImageGeneratorSceneLightsUltraResponse_EnumSceneLightsUltra_OperateCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string err_message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_err_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSceneLightsUltraResponse.err_message");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required bool ultra_on = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_ultra_on(&has_bits);
          ultra_on_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .ce.net.cc.ImageGeneratorSceneLights content = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSceneLightsUltraResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.ImageGeneratorSceneLightsUltraResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_err_code(), target);
  }

  // optional string err_message = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_err_message().data(), static_cast<int>(this->_internal_err_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSceneLightsUltraResponse.err_message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_err_message(), target);
  }

  // required bool ultra_on = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_ultra_on(), target);
  }

  // optional .ce.net.cc.ImageGeneratorSceneLights content = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  return target;
}

size_t ImageGeneratorSceneLightsUltraResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  size_t total_size = 0;

  if (_internal_has_err_code()) {
    // required .ce.net.cc.ImageGeneratorSceneLightsUltraResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());
  }

  if (_internal_has_ultra_on()) {
    // required bool ultra_on = 3;
    total_size += 1 + 1;
  }

  return total_size;
}
size_t ImageGeneratorSceneLightsUltraResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x0000000c) ^ 0x0000000c) == 0) {  // All required fields are present.
    // required .ce.net.cc.ImageGeneratorSceneLightsUltraResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());

    // required bool ultra_on = 3;
    total_size += 1 + 1;

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string err_message = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_err_message());
    }

    // optional .ce.net.cc.ImageGeneratorSceneLights content = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *content_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSceneLightsUltraResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSceneLightsUltraResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSceneLightsUltraResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
    MergeFrom(*source);
  }
}

void ImageGeneratorSceneLightsUltraResponse::MergeFrom(const ImageGeneratorSceneLightsUltraResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_err_message(from._internal_err_message());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_content()->::ce::net::cc::ImageGeneratorSceneLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000004u) {
      err_code_ = from.err_code_;
    }
    if (cached_has_bits & 0x00000008u) {
      ultra_on_ = from.ultra_on_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorSceneLightsUltraResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSceneLightsUltraResponse::CopyFrom(const ImageGeneratorSceneLightsUltraResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSceneLightsUltraResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorSceneLightsUltraResponse::InternalSwap(ImageGeneratorSceneLightsUltraResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  err_message_.Swap(&other->err_message_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLightsUltraResponse, ultra_on_)
      + sizeof(ImageGeneratorSceneLightsUltraResponse::ultra_on_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLightsUltraResponse, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSceneLightsUltraResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSceneLightsUltraSetRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSceneLightsUltraSetRequest>()._has_bits_);
  static void set_has_ultra_on(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::ce::net::cc::ImageGeneratorSceneLights& content(const ImageGeneratorSceneLightsUltraSetRequest* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000002) ^ 0x00000002) != 0;
  }
};

const ::ce::net::cc::ImageGeneratorSceneLights&
ImageGeneratorSceneLightsUltraSetRequest::_Internal::content(const ImageGeneratorSceneLightsUltraSetRequest* msg) {
  return *msg->content_;
}
ImageGeneratorSceneLightsUltraSetRequest::ImageGeneratorSceneLightsUltraSetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
}
ImageGeneratorSceneLightsUltraSetRequest::ImageGeneratorSceneLightsUltraSetRequest(const ImageGeneratorSceneLightsUltraSetRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_content()) {
    content_ = new ::ce::net::cc::ImageGeneratorSceneLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  ultra_on_ = from.ultra_on_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
}

void ImageGeneratorSceneLightsUltraSetRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSceneLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
      reinterpret_cast<char*>(&content_)) + sizeof(ultra_on_));
}

ImageGeneratorSceneLightsUltraSetRequest::~ImageGeneratorSceneLightsUltraSetRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSceneLightsUltraSetRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorSceneLightsUltraSetRequest::ArenaDtor(void* object) {
  ImageGeneratorSceneLightsUltraSetRequest* _this = reinterpret_cast< ImageGeneratorSceneLightsUltraSetRequest* >(object);
  (void)_this;
}
void ImageGeneratorSceneLightsUltraSetRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSceneLightsUltraSetRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSceneLightsUltraSetRequest& ImageGeneratorSceneLightsUltraSetRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSceneLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSceneLightsUltraSetRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(content_ != nullptr);
    content_->Clear();
  }
  ultra_on_ = false;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSceneLightsUltraSetRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required bool ultra_on = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_ultra_on(&has_bits);
          ultra_on_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .ce.net.cc.ImageGeneratorSceneLights content = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSceneLightsUltraSetRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required bool ultra_on = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_ultra_on(), target);
  }

  // optional .ce.net.cc.ImageGeneratorSceneLights content = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  return target;
}

size_t ImageGeneratorSceneLightsUltraSetRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  size_t total_size = 0;

  // required bool ultra_on = 1;
  if (_internal_has_ultra_on()) {
    total_size += 1 + 1;
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional .ce.net.cc.ImageGeneratorSceneLights content = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSceneLightsUltraSetRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSceneLightsUltraSetRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSceneLightsUltraSetRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
    MergeFrom(*source);
  }
}

void ImageGeneratorSceneLightsUltraSetRequest::MergeFrom(const ImageGeneratorSceneLightsUltraSetRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_content()->::ce::net::cc::ImageGeneratorSceneLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000002u) {
      ultra_on_ = from.ultra_on_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorSceneLightsUltraSetRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSceneLightsUltraSetRequest::CopyFrom(const ImageGeneratorSceneLightsUltraSetRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSceneLightsUltraSetRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorSceneLightsUltraSetRequest::InternalSwap(ImageGeneratorSceneLightsUltraSetRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLightsUltraSetRequest, ultra_on_)
      + sizeof(ImageGeneratorSceneLightsUltraSetRequest::ultra_on_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLightsUltraSetRequest, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSceneLightsUltraSetRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSceneLightsUltraSetResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSceneLightsUltraSetResponse>()._has_bits_);
  static void set_has_err_code(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_err_message(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_ultra_on(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::ce::net::cc::ImageGeneratorSceneLights& content(const ImageGeneratorSceneLightsUltraSetResponse* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x0000000e) ^ 0x0000000e) != 0;
  }
};

const ::ce::net::cc::ImageGeneratorSceneLights&
ImageGeneratorSceneLightsUltraSetResponse::_Internal::content(const ImageGeneratorSceneLightsUltraSetResponse* msg) {
  return *msg->content_;
}
ImageGeneratorSceneLightsUltraSetResponse::ImageGeneratorSceneLightsUltraSetResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
}
ImageGeneratorSceneLightsUltraSetResponse::ImageGeneratorSceneLightsUltraSetResponse(const ImageGeneratorSceneLightsUltraSetResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_err_message()) {
    err_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_err_message(), 
      GetArena());
  }
  if (from._internal_has_content()) {
    content_ = new ::ce::net::cc::ImageGeneratorSceneLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  ::memcpy(&err_code_, &from.err_code_,
    static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
    reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
}

void ImageGeneratorSceneLightsUltraSetResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSceneLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
      reinterpret_cast<char*>(&content_)) + sizeof(ultra_on_));
}

ImageGeneratorSceneLightsUltraSetResponse::~ImageGeneratorSceneLightsUltraSetResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSceneLightsUltraSetResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  err_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorSceneLightsUltraSetResponse::ArenaDtor(void* object) {
  ImageGeneratorSceneLightsUltraSetResponse* _this = reinterpret_cast< ImageGeneratorSceneLightsUltraSetResponse* >(object);
  (void)_this;
}
void ImageGeneratorSceneLightsUltraSetResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSceneLightsUltraSetResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSceneLightsUltraSetResponse& ImageGeneratorSceneLightsUltraSetResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSceneLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSceneLightsUltraSetResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      err_message_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(content_ != nullptr);
      content_->Clear();
    }
  }
  if (cached_has_bits & 0x0000000cu) {
    ::memset(&err_code_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&ultra_on_) -
        reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSceneLightsUltraSetResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode_IsValid(val))) {
            _internal_set_err_code(static_cast<::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse_EnumSceneLightsUltra_OperateCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string err_message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_err_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse.err_message");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required bool ultra_on = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_ultra_on(&has_bits);
          ultra_on_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.cc.ImageGeneratorSceneLights content = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSceneLightsUltraSetResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_err_code(), target);
  }

  // optional string err_message = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_err_message().data(), static_cast<int>(this->_internal_err_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse.err_message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_err_message(), target);
  }

  // required bool ultra_on = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_ultra_on(), target);
  }

  // required .ce.net.cc.ImageGeneratorSceneLights content = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  return target;
}

size_t ImageGeneratorSceneLightsUltraSetResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  size_t total_size = 0;

  if (_internal_has_content()) {
    // required .ce.net.cc.ImageGeneratorSceneLights content = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);
  }

  if (_internal_has_err_code()) {
    // required .ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());
  }

  if (_internal_has_ultra_on()) {
    // required bool ultra_on = 3;
    total_size += 1 + 1;
  }

  return total_size;
}
size_t ImageGeneratorSceneLightsUltraSetResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x0000000e) ^ 0x0000000e) == 0) {  // All required fields are present.
    // required .ce.net.cc.ImageGeneratorSceneLights content = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);

    // required .ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse.EnumSceneLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());

    // required bool ultra_on = 3;
    total_size += 1 + 1;

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string err_message = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_err_message());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSceneLightsUltraSetResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSceneLightsUltraSetResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSceneLightsUltraSetResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
    MergeFrom(*source);
  }
}

void ImageGeneratorSceneLightsUltraSetResponse::MergeFrom(const ImageGeneratorSceneLightsUltraSetResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_err_message(from._internal_err_message());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_content()->::ce::net::cc::ImageGeneratorSceneLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000004u) {
      err_code_ = from.err_code_;
    }
    if (cached_has_bits & 0x00000008u) {
      ultra_on_ = from.ultra_on_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorSceneLightsUltraSetResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSceneLightsUltraSetResponse::CopyFrom(const ImageGeneratorSceneLightsUltraSetResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSceneLightsUltraSetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSceneLightsUltraSetResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorSceneLightsUltraSetResponse::InternalSwap(ImageGeneratorSceneLightsUltraSetResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  err_message_.Swap(&other->err_message_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLightsUltraSetResponse, ultra_on_)
      + sizeof(ImageGeneratorSceneLightsUltraSetResponse::ultra_on_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorSceneLightsUltraSetResponse, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSceneLightsUltraSetResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorAircraftLightsUltraRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorAircraftLightsUltraRequest>()._has_bits_);
  static void set_has_model(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

ImageGeneratorAircraftLightsUltraRequest::ImageGeneratorAircraftLightsUltraRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
}
ImageGeneratorAircraftLightsUltraRequest::ImageGeneratorAircraftLightsUltraRequest(const ImageGeneratorAircraftLightsUltraRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_model()) {
    model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
}

void ImageGeneratorAircraftLightsUltraRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorAircraftLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

ImageGeneratorAircraftLightsUltraRequest::~ImageGeneratorAircraftLightsUltraRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorAircraftLightsUltraRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  model_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ImageGeneratorAircraftLightsUltraRequest::ArenaDtor(void* object) {
  ImageGeneratorAircraftLightsUltraRequest* _this = reinterpret_cast< ImageGeneratorAircraftLightsUltraRequest* >(object);
  (void)_this;
}
void ImageGeneratorAircraftLightsUltraRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorAircraftLightsUltraRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorAircraftLightsUltraRequest& ImageGeneratorAircraftLightsUltraRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorAircraftLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorAircraftLightsUltraRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    model_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorAircraftLightsUltraRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // optional string model = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_model();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorAircraftLightsUltraRequest.model");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorAircraftLightsUltraRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string model = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_model().data(), static_cast<int>(this->_internal_model().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorAircraftLightsUltraRequest.model");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_model(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  return target;
}

size_t ImageGeneratorAircraftLightsUltraRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string model = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorAircraftLightsUltraRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorAircraftLightsUltraRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorAircraftLightsUltraRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
    MergeFrom(*source);
  }
}

void ImageGeneratorAircraftLightsUltraRequest::MergeFrom(const ImageGeneratorAircraftLightsUltraRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_model()) {
    _internal_set_model(from._internal_model());
  }
}

void ImageGeneratorAircraftLightsUltraRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorAircraftLightsUltraRequest::CopyFrom(const ImageGeneratorAircraftLightsUltraRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorAircraftLightsUltraRequest::IsInitialized() const {
  return true;
}

void ImageGeneratorAircraftLightsUltraRequest::InternalSwap(ImageGeneratorAircraftLightsUltraRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  model_.Swap(&other->model_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorAircraftLightsUltraRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorAircraftLightsUltraResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorAircraftLightsUltraResponse>()._has_bits_);
  static void set_has_err_code(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_err_message(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_ultra_on(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::ce::net::AircraftLights& content(const ImageGeneratorAircraftLightsUltraResponse* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x0000000c) ^ 0x0000000c) != 0;
  }
};

const ::ce::net::AircraftLights&
ImageGeneratorAircraftLightsUltraResponse::_Internal::content(const ImageGeneratorAircraftLightsUltraResponse* msg) {
  return *msg->content_;
}
void ImageGeneratorAircraftLightsUltraResponse::clear_content() {
  if (content_ != nullptr) content_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
ImageGeneratorAircraftLightsUltraResponse::ImageGeneratorAircraftLightsUltraResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
}
ImageGeneratorAircraftLightsUltraResponse::ImageGeneratorAircraftLightsUltraResponse(const ImageGeneratorAircraftLightsUltraResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_err_message()) {
    err_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_err_message(), 
      GetArena());
  }
  if (from._internal_has_content()) {
    content_ = new ::ce::net::AircraftLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  ::memcpy(&err_code_, &from.err_code_,
    static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
    reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
}

void ImageGeneratorAircraftLightsUltraResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorAircraftLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
      reinterpret_cast<char*>(&content_)) + sizeof(ultra_on_));
}

ImageGeneratorAircraftLightsUltraResponse::~ImageGeneratorAircraftLightsUltraResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorAircraftLightsUltraResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  err_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorAircraftLightsUltraResponse::ArenaDtor(void* object) {
  ImageGeneratorAircraftLightsUltraResponse* _this = reinterpret_cast< ImageGeneratorAircraftLightsUltraResponse* >(object);
  (void)_this;
}
void ImageGeneratorAircraftLightsUltraResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorAircraftLightsUltraResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorAircraftLightsUltraResponse& ImageGeneratorAircraftLightsUltraResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorAircraftLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorAircraftLightsUltraResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      err_message_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(content_ != nullptr);
      content_->Clear();
    }
  }
  if (cached_has_bits & 0x0000000cu) {
    ::memset(&err_code_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&ultra_on_) -
        reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorAircraftLightsUltraResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.ImageGeneratorAircraftLightsUltraResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode_IsValid(val))) {
            _internal_set_err_code(static_cast<::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse_EnumAircraftLightsUltra_OperateCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string err_message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_err_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorAircraftLightsUltraResponse.err_message");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required bool ultra_on = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_ultra_on(&has_bits);
          ultra_on_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .ce.net.AircraftLights content = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorAircraftLightsUltraResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.ImageGeneratorAircraftLightsUltraResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_err_code(), target);
  }

  // optional string err_message = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_err_message().data(), static_cast<int>(this->_internal_err_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorAircraftLightsUltraResponse.err_message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_err_message(), target);
  }

  // required bool ultra_on = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_ultra_on(), target);
  }

  // optional .ce.net.AircraftLights content = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  return target;
}

size_t ImageGeneratorAircraftLightsUltraResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  size_t total_size = 0;

  if (_internal_has_err_code()) {
    // required .ce.net.cc.ImageGeneratorAircraftLightsUltraResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());
  }

  if (_internal_has_ultra_on()) {
    // required bool ultra_on = 3;
    total_size += 1 + 1;
  }

  return total_size;
}
size_t ImageGeneratorAircraftLightsUltraResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x0000000c) ^ 0x0000000c) == 0) {  // All required fields are present.
    // required .ce.net.cc.ImageGeneratorAircraftLightsUltraResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());

    // required bool ultra_on = 3;
    total_size += 1 + 1;

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string err_message = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_err_message());
    }

    // optional .ce.net.AircraftLights content = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *content_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorAircraftLightsUltraResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorAircraftLightsUltraResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorAircraftLightsUltraResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
    MergeFrom(*source);
  }
}

void ImageGeneratorAircraftLightsUltraResponse::MergeFrom(const ImageGeneratorAircraftLightsUltraResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_err_message(from._internal_err_message());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_content()->::ce::net::AircraftLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000004u) {
      err_code_ = from.err_code_;
    }
    if (cached_has_bits & 0x00000008u) {
      ultra_on_ = from.ultra_on_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorAircraftLightsUltraResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorAircraftLightsUltraResponse::CopyFrom(const ImageGeneratorAircraftLightsUltraResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorAircraftLightsUltraResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorAircraftLightsUltraResponse::InternalSwap(ImageGeneratorAircraftLightsUltraResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  err_message_.Swap(&other->err_message_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorAircraftLightsUltraResponse, ultra_on_)
      + sizeof(ImageGeneratorAircraftLightsUltraResponse::ultra_on_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorAircraftLightsUltraResponse, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorAircraftLightsUltraResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorAircraftLightsUltraSetRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorAircraftLightsUltraSetRequest>()._has_bits_);
  static void set_has_ultra_on(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_model(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::ce::net::AircraftLights& content(const ImageGeneratorAircraftLightsUltraSetRequest* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000004) ^ 0x00000004) != 0;
  }
};

const ::ce::net::AircraftLights&
ImageGeneratorAircraftLightsUltraSetRequest::_Internal::content(const ImageGeneratorAircraftLightsUltraSetRequest* msg) {
  return *msg->content_;
}
void ImageGeneratorAircraftLightsUltraSetRequest::clear_content() {
  if (content_ != nullptr) content_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
ImageGeneratorAircraftLightsUltraSetRequest::ImageGeneratorAircraftLightsUltraSetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
}
ImageGeneratorAircraftLightsUltraSetRequest::ImageGeneratorAircraftLightsUltraSetRequest(const ImageGeneratorAircraftLightsUltraSetRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_model()) {
    model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model(), 
      GetArena());
  }
  if (from._internal_has_content()) {
    content_ = new ::ce::net::AircraftLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  ultra_on_ = from.ultra_on_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
}

void ImageGeneratorAircraftLightsUltraSetRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorAircraftLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
      reinterpret_cast<char*>(&content_)) + sizeof(ultra_on_));
}

ImageGeneratorAircraftLightsUltraSetRequest::~ImageGeneratorAircraftLightsUltraSetRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorAircraftLightsUltraSetRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  model_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorAircraftLightsUltraSetRequest::ArenaDtor(void* object) {
  ImageGeneratorAircraftLightsUltraSetRequest* _this = reinterpret_cast< ImageGeneratorAircraftLightsUltraSetRequest* >(object);
  (void)_this;
}
void ImageGeneratorAircraftLightsUltraSetRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorAircraftLightsUltraSetRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorAircraftLightsUltraSetRequest& ImageGeneratorAircraftLightsUltraSetRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorAircraftLightsUltraSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorAircraftLightsUltraSetRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      model_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(content_ != nullptr);
      content_->Clear();
    }
  }
  ultra_on_ = false;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorAircraftLightsUltraSetRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required bool ultra_on = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_ultra_on(&has_bits);
          ultra_on_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string model = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_model();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest.model");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional .ce.net.AircraftLights content = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorAircraftLightsUltraSetRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required bool ultra_on = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_ultra_on(), target);
  }

  // optional string model = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_model().data(), static_cast<int>(this->_internal_model().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest.model");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_model(), target);
  }

  // optional .ce.net.AircraftLights content = 3;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  return target;
}

size_t ImageGeneratorAircraftLightsUltraSetRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  size_t total_size = 0;

  // required bool ultra_on = 1;
  if (_internal_has_ultra_on()) {
    total_size += 1 + 1;
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string model = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_model());
    }

    // optional .ce.net.AircraftLights content = 3;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *content_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorAircraftLightsUltraSetRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorAircraftLightsUltraSetRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorAircraftLightsUltraSetRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
    MergeFrom(*source);
  }
}

void ImageGeneratorAircraftLightsUltraSetRequest::MergeFrom(const ImageGeneratorAircraftLightsUltraSetRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_model(from._internal_model());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_content()->::ce::net::AircraftLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000004u) {
      ultra_on_ = from.ultra_on_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorAircraftLightsUltraSetRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorAircraftLightsUltraSetRequest::CopyFrom(const ImageGeneratorAircraftLightsUltraSetRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorAircraftLightsUltraSetRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorAircraftLightsUltraSetRequest::InternalSwap(ImageGeneratorAircraftLightsUltraSetRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  model_.Swap(&other->model_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorAircraftLightsUltraSetRequest, ultra_on_)
      + sizeof(ImageGeneratorAircraftLightsUltraSetRequest::ultra_on_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorAircraftLightsUltraSetRequest, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorAircraftLightsUltraSetRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorAircraftLightsUltraSetResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorAircraftLightsUltraSetResponse>()._has_bits_);
  static void set_has_err_code(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_err_message(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_ultra_on(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::ce::net::AircraftLights& content(const ImageGeneratorAircraftLightsUltraSetResponse* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x0000000e) ^ 0x0000000e) != 0;
  }
};

const ::ce::net::AircraftLights&
ImageGeneratorAircraftLightsUltraSetResponse::_Internal::content(const ImageGeneratorAircraftLightsUltraSetResponse* msg) {
  return *msg->content_;
}
void ImageGeneratorAircraftLightsUltraSetResponse::clear_content() {
  if (content_ != nullptr) content_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
ImageGeneratorAircraftLightsUltraSetResponse::ImageGeneratorAircraftLightsUltraSetResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
}
ImageGeneratorAircraftLightsUltraSetResponse::ImageGeneratorAircraftLightsUltraSetResponse(const ImageGeneratorAircraftLightsUltraSetResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_err_message()) {
    err_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_err_message(), 
      GetArena());
  }
  if (from._internal_has_content()) {
    content_ = new ::ce::net::AircraftLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  ::memcpy(&err_code_, &from.err_code_,
    static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
    reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
}

void ImageGeneratorAircraftLightsUltraSetResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorAircraftLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&ultra_on_) -
      reinterpret_cast<char*>(&content_)) + sizeof(ultra_on_));
}

ImageGeneratorAircraftLightsUltraSetResponse::~ImageGeneratorAircraftLightsUltraSetResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorAircraftLightsUltraSetResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  err_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorAircraftLightsUltraSetResponse::ArenaDtor(void* object) {
  ImageGeneratorAircraftLightsUltraSetResponse* _this = reinterpret_cast< ImageGeneratorAircraftLightsUltraSetResponse* >(object);
  (void)_this;
}
void ImageGeneratorAircraftLightsUltraSetResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorAircraftLightsUltraSetResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorAircraftLightsUltraSetResponse& ImageGeneratorAircraftLightsUltraSetResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorAircraftLightsUltraSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorAircraftLightsUltraSetResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      err_message_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(content_ != nullptr);
      content_->Clear();
    }
  }
  if (cached_has_bits & 0x0000000cu) {
    ::memset(&err_code_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&ultra_on_) -
        reinterpret_cast<char*>(&err_code_)) + sizeof(ultra_on_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorAircraftLightsUltraSetResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode_IsValid(val))) {
            _internal_set_err_code(static_cast<::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse_EnumAircraftLightsUltra_OperateCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string err_message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_err_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse.err_message");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required bool ultra_on = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          _Internal::set_has_ultra_on(&has_bits);
          ultra_on_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.AircraftLights content = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorAircraftLightsUltraSetResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_err_code(), target);
  }

  // optional string err_message = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_err_message().data(), static_cast<int>(this->_internal_err_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse.err_message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_err_message(), target);
  }

  // required bool ultra_on = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_ultra_on(), target);
  }

  // required .ce.net.AircraftLights content = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  return target;
}

size_t ImageGeneratorAircraftLightsUltraSetResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  size_t total_size = 0;

  if (_internal_has_content()) {
    // required .ce.net.AircraftLights content = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);
  }

  if (_internal_has_err_code()) {
    // required .ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());
  }

  if (_internal_has_ultra_on()) {
    // required bool ultra_on = 3;
    total_size += 1 + 1;
  }

  return total_size;
}
size_t ImageGeneratorAircraftLightsUltraSetResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x0000000e) ^ 0x0000000e) == 0) {  // All required fields are present.
    // required .ce.net.AircraftLights content = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);

    // required .ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse.EnumAircraftLightsUltra_OperateCode err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_err_code());

    // required bool ultra_on = 3;
    total_size += 1 + 1;

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string err_message = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_err_message());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorAircraftLightsUltraSetResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorAircraftLightsUltraSetResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorAircraftLightsUltraSetResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
    MergeFrom(*source);
  }
}

void ImageGeneratorAircraftLightsUltraSetResponse::MergeFrom(const ImageGeneratorAircraftLightsUltraSetResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_err_message(from._internal_err_message());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_content()->::ce::net::AircraftLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000004u) {
      err_code_ = from.err_code_;
    }
    if (cached_has_bits & 0x00000008u) {
      ultra_on_ = from.ultra_on_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorAircraftLightsUltraSetResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorAircraftLightsUltraSetResponse::CopyFrom(const ImageGeneratorAircraftLightsUltraSetResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorAircraftLightsUltraSetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorAircraftLightsUltraSetResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorAircraftLightsUltraSetResponse::InternalSwap(ImageGeneratorAircraftLightsUltraSetResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  err_message_.Swap(&other->err_message_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorAircraftLightsUltraSetResponse, ultra_on_)
      + sizeof(ImageGeneratorAircraftLightsUltraSetResponse::ultra_on_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorAircraftLightsUltraSetResponse, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorAircraftLightsUltraSetResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSMGCSLights::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSMGCSLights>()._has_bits_);
  static void set_has_ultra_on(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_icao(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

ImageGeneratorSMGCSLights::ImageGeneratorSMGCSLights(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  routes_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSMGCSLights)
}
ImageGeneratorSMGCSLights::ImageGeneratorSMGCSLights(const ImageGeneratorSMGCSLights& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      routes_(from.routes_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_icao()) {
    icao_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icao(), 
      GetArena());
  }
  ultra_on_ = from.ultra_on_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSMGCSLights)
}

void ImageGeneratorSMGCSLights::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ultra_on_ = false;
}

ImageGeneratorSMGCSLights::~ImageGeneratorSMGCSLights() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSMGCSLights)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSMGCSLights::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  icao_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ImageGeneratorSMGCSLights::ArenaDtor(void* object) {
  ImageGeneratorSMGCSLights* _this = reinterpret_cast< ImageGeneratorSMGCSLights* >(object);
  (void)_this;
}
void ImageGeneratorSMGCSLights::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSMGCSLights::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSMGCSLights& ImageGeneratorSMGCSLights::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSMGCSLights_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSMGCSLights::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSMGCSLights)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  routes_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    icao_.ClearNonDefaultToEmpty();
  }
  ultra_on_ = false;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSMGCSLights::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required bool ultra_on = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_ultra_on(&has_bits);
          ultra_on_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string icao = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_icao();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSMGCSLights.icao");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string routes = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_routes();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            #ifndef NDEBUG
            ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSMGCSLights.routes");
            #endif  // !NDEBUG
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSMGCSLights::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSMGCSLights)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required bool ultra_on = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_ultra_on(), target);
  }

  // required string icao = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_icao().data(), static_cast<int>(this->_internal_icao().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSMGCSLights.icao");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_icao(), target);
  }

  // repeated string routes = 3;
  for (int i = 0, n = this->_internal_routes_size(); i < n; i++) {
    const auto& s = this->_internal_routes(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSMGCSLights.routes");
    target = stream->WriteString(3, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSMGCSLights)
  return target;
}

size_t ImageGeneratorSMGCSLights::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorSMGCSLights)
  size_t total_size = 0;

  if (_internal_has_icao()) {
    // required string icao = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icao());
  }

  if (_internal_has_ultra_on()) {
    // required bool ultra_on = 1;
    total_size += 1 + 1;
  }

  return total_size;
}
size_t ImageGeneratorSMGCSLights::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSMGCSLights)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required string icao = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icao());

    // required bool ultra_on = 1;
    total_size += 1 + 1;

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string routes = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(routes_.size());
  for (int i = 0, n = routes_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      routes_.Get(i));
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSMGCSLights::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLights)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSMGCSLights* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSMGCSLights>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSMGCSLights)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSMGCSLights)
    MergeFrom(*source);
  }
}

void ImageGeneratorSMGCSLights::MergeFrom(const ImageGeneratorSMGCSLights& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLights)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  routes_.MergeFrom(from.routes_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_icao(from._internal_icao());
    }
    if (cached_has_bits & 0x00000002u) {
      ultra_on_ = from.ultra_on_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorSMGCSLights::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLights)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSMGCSLights::CopyFrom(const ImageGeneratorSMGCSLights& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLights)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSMGCSLights::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void ImageGeneratorSMGCSLights::InternalSwap(ImageGeneratorSMGCSLights* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  routes_.InternalSwap(&other->routes_);
  icao_.Swap(&other->icao_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(ultra_on_, other->ultra_on_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSMGCSLights::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSMGCSLightsUltraRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSMGCSLightsUltraRequest>()._has_bits_);
  static void set_has_icao(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

ImageGeneratorSMGCSLightsUltraRequest::ImageGeneratorSMGCSLightsUltraRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
}
ImageGeneratorSMGCSLightsUltraRequest::ImageGeneratorSMGCSLightsUltraRequest(const ImageGeneratorSMGCSLightsUltraRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_icao()) {
    icao_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icao(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
}

void ImageGeneratorSMGCSLightsUltraRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSMGCSLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  icao_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

ImageGeneratorSMGCSLightsUltraRequest::~ImageGeneratorSMGCSLightsUltraRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSMGCSLightsUltraRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  icao_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ImageGeneratorSMGCSLightsUltraRequest::ArenaDtor(void* object) {
  ImageGeneratorSMGCSLightsUltraRequest* _this = reinterpret_cast< ImageGeneratorSMGCSLightsUltraRequest* >(object);
  (void)_this;
}
void ImageGeneratorSMGCSLightsUltraRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSMGCSLightsUltraRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSMGCSLightsUltraRequest& ImageGeneratorSMGCSLightsUltraRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSMGCSLightsUltraRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSMGCSLightsUltraRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    icao_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSMGCSLightsUltraRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string icao = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_icao();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest.icao");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSMGCSLightsUltraRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string icao = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_icao().data(), static_cast<int>(this->_internal_icao().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest.icao");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_icao(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  return target;
}

size_t ImageGeneratorSMGCSLightsUltraRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  size_t total_size = 0;

  // required string icao = 1;
  if (_internal_has_icao()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icao());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSMGCSLightsUltraRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSMGCSLightsUltraRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSMGCSLightsUltraRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
    MergeFrom(*source);
  }
}

void ImageGeneratorSMGCSLightsUltraRequest::MergeFrom(const ImageGeneratorSMGCSLightsUltraRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_icao()) {
    _internal_set_icao(from._internal_icao());
  }
}

void ImageGeneratorSMGCSLightsUltraRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSMGCSLightsUltraRequest::CopyFrom(const ImageGeneratorSMGCSLightsUltraRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSMGCSLightsUltraRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void ImageGeneratorSMGCSLightsUltraRequest::InternalSwap(ImageGeneratorSMGCSLightsUltraRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  icao_.Swap(&other->icao_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSMGCSLightsUltraRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSMGCSLightsUltraResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSMGCSLightsUltraResponse>()._has_bits_);
  static void set_has_err_code(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_err_message(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::ce::net::cc::ImageGeneratorSMGCSLights& content(const ImageGeneratorSMGCSLightsUltraResponse* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000006) ^ 0x00000006) != 0;
  }
};

const ::ce::net::cc::ImageGeneratorSMGCSLights&
ImageGeneratorSMGCSLightsUltraResponse::_Internal::content(const ImageGeneratorSMGCSLightsUltraResponse* msg) {
  return *msg->content_;
}
ImageGeneratorSMGCSLightsUltraResponse::ImageGeneratorSMGCSLightsUltraResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
}
ImageGeneratorSMGCSLightsUltraResponse::ImageGeneratorSMGCSLightsUltraResponse(const ImageGeneratorSMGCSLightsUltraResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_err_message()) {
    err_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_err_message(), 
      GetArena());
  }
  if (from._internal_has_content()) {
    content_ = new ::ce::net::cc::ImageGeneratorSMGCSLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  err_code_ = from.err_code_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
}

void ImageGeneratorSMGCSLightsUltraResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSMGCSLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&err_code_) -
      reinterpret_cast<char*>(&content_)) + sizeof(err_code_));
}

ImageGeneratorSMGCSLightsUltraResponse::~ImageGeneratorSMGCSLightsUltraResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSMGCSLightsUltraResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  err_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorSMGCSLightsUltraResponse::ArenaDtor(void* object) {
  ImageGeneratorSMGCSLightsUltraResponse* _this = reinterpret_cast< ImageGeneratorSMGCSLightsUltraResponse* >(object);
  (void)_this;
}
void ImageGeneratorSMGCSLightsUltraResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSMGCSLightsUltraResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSMGCSLightsUltraResponse& ImageGeneratorSMGCSLightsUltraResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSMGCSLightsUltraResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSMGCSLightsUltraResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      err_message_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(content_ != nullptr);
      content_->Clear();
    }
  }
  err_code_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSMGCSLightsUltraResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required uint32 err_code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_err_code(&has_bits);
          err_code_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string err_message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_err_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse.err_message");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSMGCSLightsUltraResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required uint32 err_code = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_err_code(), target);
  }

  // optional string err_message = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_err_message().data(), static_cast<int>(this->_internal_err_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse.err_message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_err_message(), target);
  }

  // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  return target;
}

size_t ImageGeneratorSMGCSLightsUltraResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  size_t total_size = 0;

  if (_internal_has_content()) {
    // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);
  }

  if (_internal_has_err_code()) {
    // required uint32 err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_err_code());
  }

  return total_size;
}
size_t ImageGeneratorSMGCSLightsUltraResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000006) ^ 0x00000006) == 0) {  // All required fields are present.
    // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);

    // required uint32 err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_err_code());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string err_message = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_err_message());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSMGCSLightsUltraResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSMGCSLightsUltraResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSMGCSLightsUltraResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
    MergeFrom(*source);
  }
}

void ImageGeneratorSMGCSLightsUltraResponse::MergeFrom(const ImageGeneratorSMGCSLightsUltraResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_err_message(from._internal_err_message());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_content()->::ce::net::cc::ImageGeneratorSMGCSLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000004u) {
      err_code_ = from.err_code_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorSMGCSLightsUltraResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSMGCSLightsUltraResponse::CopyFrom(const ImageGeneratorSMGCSLightsUltraResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsUltraResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSMGCSLightsUltraResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorSMGCSLightsUltraResponse::InternalSwap(ImageGeneratorSMGCSLightsUltraResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  err_message_.Swap(&other->err_message_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorSMGCSLightsUltraResponse, err_code_)
      + sizeof(ImageGeneratorSMGCSLightsUltraResponse::err_code_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorSMGCSLightsUltraResponse, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSMGCSLightsUltraResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSMGCSLightsSetRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSMGCSLightsSetRequest>()._has_bits_);
  static const ::ce::net::cc::ImageGeneratorSMGCSLights& content(const ImageGeneratorSMGCSLightsSetRequest* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

const ::ce::net::cc::ImageGeneratorSMGCSLights&
ImageGeneratorSMGCSLightsSetRequest::_Internal::content(const ImageGeneratorSMGCSLightsSetRequest* msg) {
  return *msg->content_;
}
ImageGeneratorSMGCSLightsSetRequest::ImageGeneratorSMGCSLightsSetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
}
ImageGeneratorSMGCSLightsSetRequest::ImageGeneratorSMGCSLightsSetRequest(const ImageGeneratorSMGCSLightsSetRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_content()) {
    content_ = new ::ce::net::cc::ImageGeneratorSMGCSLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
}

void ImageGeneratorSMGCSLightsSetRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSMGCSLightsSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  content_ = nullptr;
}

ImageGeneratorSMGCSLightsSetRequest::~ImageGeneratorSMGCSLightsSetRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSMGCSLightsSetRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorSMGCSLightsSetRequest::ArenaDtor(void* object) {
  ImageGeneratorSMGCSLightsSetRequest* _this = reinterpret_cast< ImageGeneratorSMGCSLightsSetRequest* >(object);
  (void)_this;
}
void ImageGeneratorSMGCSLightsSetRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSMGCSLightsSetRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSMGCSLightsSetRequest& ImageGeneratorSMGCSLightsSetRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSMGCSLightsSetRequest_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSMGCSLightsSetRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(content_ != nullptr);
    content_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSMGCSLightsSetRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.ImageGeneratorSMGCSLights content = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSMGCSLightsSetRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.ImageGeneratorSMGCSLights content = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  return target;
}

size_t ImageGeneratorSMGCSLightsSetRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  size_t total_size = 0;

  // required .ce.net.cc.ImageGeneratorSMGCSLights content = 1;
  if (_internal_has_content()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSMGCSLightsSetRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSMGCSLightsSetRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSMGCSLightsSetRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
    MergeFrom(*source);
  }
}

void ImageGeneratorSMGCSLightsSetRequest::MergeFrom(const ImageGeneratorSMGCSLightsSetRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_content()) {
    _internal_mutable_content()->::ce::net::cc::ImageGeneratorSMGCSLights::MergeFrom(from._internal_content());
  }
}

void ImageGeneratorSMGCSLightsSetRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSMGCSLightsSetRequest::CopyFrom(const ImageGeneratorSMGCSLightsSetRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSMGCSLightsSetRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorSMGCSLightsSetRequest::InternalSwap(ImageGeneratorSMGCSLightsSetRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(content_, other->content_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSMGCSLightsSetRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorSMGCSLightsSetResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorSMGCSLightsSetResponse>()._has_bits_);
  static void set_has_err_code(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_err_message(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::ce::net::cc::ImageGeneratorSMGCSLights& content(const ImageGeneratorSMGCSLightsSetResponse* msg);
  static void set_has_content(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000006) ^ 0x00000006) != 0;
  }
};

const ::ce::net::cc::ImageGeneratorSMGCSLights&
ImageGeneratorSMGCSLightsSetResponse::_Internal::content(const ImageGeneratorSMGCSLightsSetResponse* msg) {
  return *msg->content_;
}
ImageGeneratorSMGCSLightsSetResponse::ImageGeneratorSMGCSLightsSetResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
}
ImageGeneratorSMGCSLightsSetResponse::ImageGeneratorSMGCSLightsSetResponse(const ImageGeneratorSMGCSLightsSetResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_err_message()) {
    err_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_err_message(), 
      GetArena());
  }
  if (from._internal_has_content()) {
    content_ = new ::ce::net::cc::ImageGeneratorSMGCSLights(*from.content_);
  } else {
    content_ = nullptr;
  }
  err_code_ = from.err_code_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
}

void ImageGeneratorSMGCSLightsSetResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ImageGeneratorSMGCSLightsSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  err_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&content_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&err_code_) -
      reinterpret_cast<char*>(&content_)) + sizeof(err_code_));
}

ImageGeneratorSMGCSLightsSetResponse::~ImageGeneratorSMGCSLightsSetResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorSMGCSLightsSetResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  err_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete content_;
}

void ImageGeneratorSMGCSLightsSetResponse::ArenaDtor(void* object) {
  ImageGeneratorSMGCSLightsSetResponse* _this = reinterpret_cast< ImageGeneratorSMGCSLightsSetResponse* >(object);
  (void)_this;
}
void ImageGeneratorSMGCSLightsSetResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorSMGCSLightsSetResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorSMGCSLightsSetResponse& ImageGeneratorSMGCSLightsSetResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorSMGCSLightsSetResponse_FFS_5fCC_2fCC_5fUltra_5fLights_5fCommunication_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorSMGCSLightsSetResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      err_message_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(content_ != nullptr);
      content_->Clear();
    }
  }
  err_code_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorSMGCSLightsSetResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required uint32 err_code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_err_code(&has_bits);
          err_code_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // optional string err_message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_err_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ImageGeneratorSMGCSLightsSetResponse.err_message");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_content(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorSMGCSLightsSetResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required uint32 err_code = 1;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_err_code(), target);
  }

  // optional string err_message = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_err_message().data(), static_cast<int>(this->_internal_err_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ImageGeneratorSMGCSLightsSetResponse.err_message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_err_message(), target);
  }

  // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::content(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  return target;
}

size_t ImageGeneratorSMGCSLightsSetResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  size_t total_size = 0;

  if (_internal_has_content()) {
    // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);
  }

  if (_internal_has_err_code()) {
    // required uint32 err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_err_code());
  }

  return total_size;
}
size_t ImageGeneratorSMGCSLightsSetResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000006) ^ 0x00000006) == 0) {  // All required fields are present.
    // required .ce.net.cc.ImageGeneratorSMGCSLights content = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *content_);

    // required uint32 err_code = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_err_code());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string err_message = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_err_message());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorSMGCSLightsSetResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorSMGCSLightsSetResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorSMGCSLightsSetResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
    MergeFrom(*source);
  }
}

void ImageGeneratorSMGCSLightsSetResponse::MergeFrom(const ImageGeneratorSMGCSLightsSetResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_err_message(from._internal_err_message());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_content()->::ce::net::cc::ImageGeneratorSMGCSLights::MergeFrom(from._internal_content());
    }
    if (cached_has_bits & 0x00000004u) {
      err_code_ = from.err_code_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorSMGCSLightsSetResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorSMGCSLightsSetResponse::CopyFrom(const ImageGeneratorSMGCSLightsSetResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorSMGCSLightsSetResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorSMGCSLightsSetResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_content()) {
    if (!content_->IsInitialized()) return false;
  }
  return true;
}

void ImageGeneratorSMGCSLightsSetResponse::InternalSwap(ImageGeneratorSMGCSLightsSetResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  err_message_.Swap(&other->err_message_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorSMGCSLightsSetResponse, err_code_)
      + sizeof(ImageGeneratorSMGCSLightsSetResponse::err_code_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorSMGCSLightsSetResponse, content_)>(
          reinterpret_cast<char*>(&content_),
          reinterpret_cast<char*>(&other->content_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorSMGCSLightsSetResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace cc
}  // namespace net
}  // namespace ce
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSceneLights* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSceneLights >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSceneLights >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSceneLightsUltraRequest* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSceneLightsUltraRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSceneLightsUltraRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSceneLightsUltraResponse* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSceneLightsUltraResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSceneLightsUltraResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSceneLightsUltraSetResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorAircraftLightsUltraRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorAircraftLightsUltraResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorAircraftLightsUltraSetResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSMGCSLights* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSMGCSLights >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSMGCSLights >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSMGCSLightsUltraRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSMGCSLightsUltraResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSMGCSLightsSetRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorSMGCSLightsSetResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
