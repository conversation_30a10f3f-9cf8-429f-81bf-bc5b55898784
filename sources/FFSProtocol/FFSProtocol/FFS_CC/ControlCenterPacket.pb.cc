// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FFS_CC/ControlCenterPacket.proto

#include "FFS_CC/ControlCenterPacket.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
namespace ce {
namespace net {
namespace cc {
class ControlCenterPacketDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ControlCenterPacket> _instance;
} _ControlCenterPacket_default_instance_;
}  // namespace cc
}  // namespace net
}  // namespace ce
static void InitDefaultsscc_info_ControlCenterPacket_FFS_5fCC_2fControlCenterPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ControlCenterPacket_default_instance_;
    new (ptr) ::ce::net::cc::ControlCenterPacket();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ControlCenterPacket_FFS_5fCC_2fControlCenterPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ControlCenterPacket_FFS_5fCC_2fControlCenterPacket_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_FFS_5fCC_2fControlCenterPacket_2eproto[1];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_FFS_5fCC_2fControlCenterPacket_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_FFS_5fCC_2fControlCenterPacket_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_FFS_5fCC_2fControlCenterPacket_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ControlCenterPacket, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ControlCenterPacket, message_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ControlCenterPacket, content_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::ce::net::cc::ControlCenterPacket)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ControlCenterPacket_default_instance_),
};

const char descriptor_table_protodef_FFS_5fCC_2fControlCenterPacket_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n FFS_CC/ControlCenterPacket.proto\022\tce.n"
  "et.cc\"\244\013\n\023ControlCenterPacket\022=\n\007message"
  "\030\001 \001(\0162,.ce.net.cc.ControlCenterPacket.E"
  "numCCCommand\022\017\n\007content\030\002 \001(\014\"\274\n\n\rEnumCC"
  "Command\022\022\n\016UnknownCommand\020\000\022\030\n\023IGGeograp"
  "hicUltraRQ\020\320\017\022\030\n\023IGGeographicUltraRP\020\321\017\022"
  "\033\n\026IGGeographicUltraSetRQ\020\322\017\022\033\n\026IGGeogra"
  "phicUltraSetRP\020\323\017\022\027\n\022IGTimeofDayUltraRQ\020"
  "\324\017\022\027\n\022IGTimeofDayUltraRP\020\325\017\022\032\n\025IGTimeOfD"
  "ayUltraSetRQ\020\326\017\022\032\n\025IGTimeOfDayUltraSetRP"
  "\020\327\017\022\030\n\023IGVisibilityUltraRQ\020\334\017\022\030\n\023IGVisib"
  "ilityUltraRP\020\335\017\022\033\n\026IGVisibilityUltraSetR"
  "Q\020\336\017\022\033\n\026IGVisibilityUltraSetRP\020\337\017\022\023\n\016IGC"
  "loudUltraRQ\020\340\017\022\023\n\016IGCloudUltraRP\020\341\017\022\026\n\021I"
  "GCloudUltraSetRQ\020\342\017\022\026\n\021IGCloudUltraSetRP"
  "\020\343\017\022 \n\033IGGroundContaminantsUltraRQ\020\344\017\022 \n"
  "\033IGGroundContaminantsUltraRP\020\345\017\022#\n\036IGGro"
  "undContaminantsUltraSetRQ\020\346\017\022#\n\036IGGround"
  "ContaminantsUltraSetRP\020\347\017\022\025\n\020IGWeatherUl"
  "traRQ\020\350\017\022\025\n\020IGWeatherUltraRP\020\351\017\022\030\n\023IGWea"
  "therUltraSetRQ\020\352\017\022\030\n\023IGWeatherUltraSetRP"
  "\020\353\017\022\034\n\027IGAircraftLightsUltraRQ\020\354\017\022\034\n\027IGA"
  "ircraftLightsUltraRP\020\355\017\022\037\n\032IGAircraftLig"
  "htsUltraSetRQ\020\356\017\022\037\n\032IGAircraftLightsUltr"
  "aSetRP\020\357\017\022\034\n\027IGSceneLightsUltraSetRQ\020\360\017\022"
  "\034\n\027IGSceneLightsUltraSetRP\020\361\017\022\031\n\024IGScene"
  "LightsUltraRQ\020\362\017\022\031\n\024IGSceneLightsUltraRP"
  "\020\363\017\022\026\n\021IGPlaybackUltraRQ\020\202\020\022\026\n\021IGPlaybac"
  "kUltraFB\020\203\020\022\026\n\021IGPlaybackUltraST\020\204\020\022\027\n\022D"
  "BWAirportConfigRQ\020\226\020\022\027\n\022DBWAirportConfig"
  "FB\020\227\020\022\027\n\022DBWUpdateAirportST\020\230\020\022\027\n\022DBWDel"
  "eteAirportST\020\231\020\022\035\n\030DBWUpdateCommonAirpor"
  "tST\020\232\020\022\024\n\017IGUltraStatusRQ\020\240\020\022\024\n\017IGUltraS"
  "tatusRP\020\241\020\022\032\n\025IGAircraftGcsStatusRQ\020\242\020\022\032"
  "\n\025IGAircraftGcsStatusRP\020\243\020\022\031\n\024IGAirportG"
  "csStatusRQ\020\244\020\022\031\n\024IGAirportGcsStatusRP\020\245\020"
  "\022\030\n\023IGAirportICAOListRQ\020\246\020\022\030\n\023IGAirportI"
  "CAOListRP\020\247\020b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto_sccs[1] = {
  &scc_info_ControlCenterPacket_FFS_5fCC_2fControlCenterPacket_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto = {
  false, false, descriptor_table_protodef_FFS_5fCC_2fControlCenterPacket_2eproto, "FFS_CC/ControlCenterPacket.proto", 1500,
  &descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto_once, descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto_sccs, descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto_deps, 1, 0,
  schemas, file_default_instances, TableStruct_FFS_5fCC_2fControlCenterPacket_2eproto::offsets,
  file_level_metadata_FFS_5fCC_2fControlCenterPacket_2eproto, 1, file_level_enum_descriptors_FFS_5fCC_2fControlCenterPacket_2eproto, file_level_service_descriptors_FFS_5fCC_2fControlCenterPacket_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_FFS_5fCC_2fControlCenterPacket_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto)), true);
namespace ce {
namespace net {
namespace cc {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ControlCenterPacket_EnumCCCommand_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fCC_2fControlCenterPacket_2eproto);
  return file_level_enum_descriptors_FFS_5fCC_2fControlCenterPacket_2eproto[0];
}
bool ControlCenterPacket_EnumCCCommand_IsValid(int value) {
  switch (value) {
    case 0:
    case 2000:
    case 2001:
    case 2002:
    case 2003:
    case 2004:
    case 2005:
    case 2006:
    case 2007:
    case 2012:
    case 2013:
    case 2014:
    case 2015:
    case 2016:
    case 2017:
    case 2018:
    case 2019:
    case 2020:
    case 2021:
    case 2022:
    case 2023:
    case 2024:
    case 2025:
    case 2026:
    case 2027:
    case 2028:
    case 2029:
    case 2030:
    case 2031:
    case 2032:
    case 2033:
    case 2034:
    case 2035:
    case 2050:
    case 2051:
    case 2052:
    case 2070:
    case 2071:
    case 2072:
    case 2073:
    case 2074:
    case 2080:
    case 2081:
    case 2082:
    case 2083:
    case 2084:
    case 2085:
    case 2086:
    case 2087:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::UnknownCommand;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGeographicUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGeographicUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGeographicUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGeographicUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGTimeofDayUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGTimeofDayUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGTimeOfDayUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGTimeOfDayUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGVisibilityUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGVisibilityUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGVisibilityUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGVisibilityUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGCloudUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGCloudUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGCloudUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGCloudUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGroundContaminantsUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGroundContaminantsUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGroundContaminantsUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGGroundContaminantsUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGWeatherUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGWeatherUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGWeatherUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGWeatherUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAircraftLightsUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAircraftLightsUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAircraftLightsUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAircraftLightsUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGSceneLightsUltraSetRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGSceneLightsUltraSetRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGSceneLightsUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGSceneLightsUltraRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGPlaybackUltraRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGPlaybackUltraFB;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGPlaybackUltraST;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::DBWAirportConfigRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::DBWAirportConfigFB;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::DBWUpdateAirportST;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::DBWDeleteAirportST;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::DBWUpdateCommonAirportST;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGUltraStatusRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGUltraStatusRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAircraftGcsStatusRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAircraftGcsStatusRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAirportGcsStatusRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAirportGcsStatusRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAirportICAOListRQ;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::IGAirportICAOListRP;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::EnumCCCommand_MIN;
constexpr ControlCenterPacket_EnumCCCommand ControlCenterPacket::EnumCCCommand_MAX;
constexpr int ControlCenterPacket::EnumCCCommand_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)

// ===================================================================

class ControlCenterPacket::_Internal {
 public:
};

ControlCenterPacket::ControlCenterPacket(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ControlCenterPacket)
}
ControlCenterPacket::ControlCenterPacket(const ControlCenterPacket& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  content_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_content().empty()) {
    content_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_content(), 
      GetArena());
  }
  message_ = from.message_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ControlCenterPacket)
}

void ControlCenterPacket::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ControlCenterPacket_FFS_5fCC_2fControlCenterPacket_2eproto.base);
  content_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  message_ = 0;
}

ControlCenterPacket::~ControlCenterPacket() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ControlCenterPacket)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ControlCenterPacket::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  content_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ControlCenterPacket::ArenaDtor(void* object) {
  ControlCenterPacket* _this = reinterpret_cast< ControlCenterPacket* >(object);
  (void)_this;
}
void ControlCenterPacket::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ControlCenterPacket::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ControlCenterPacket& ControlCenterPacket::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ControlCenterPacket_FFS_5fCC_2fControlCenterPacket_2eproto.base);
  return *internal_default_instance();
}


void ControlCenterPacket::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ControlCenterPacket)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  content_.ClearToEmpty();
  message_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ControlCenterPacket::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .ce.net.cc.ControlCenterPacket.EnumCCCommand message = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_message(static_cast<::ce::net::cc::ControlCenterPacket_EnumCCCommand>(val));
        } else goto handle_unusual;
        continue;
      // bytes content = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_content();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ControlCenterPacket::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ControlCenterPacket)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .ce.net.cc.ControlCenterPacket.EnumCCCommand message = 1;
  if (this->message() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_message(), target);
  }

  // bytes content = 2;
  if (this->content().size() > 0) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_content(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ControlCenterPacket)
  return target;
}

size_t ControlCenterPacket::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ControlCenterPacket)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes content = 2;
  if (this->content().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_content());
  }

  // .ce.net.cc.ControlCenterPacket.EnumCCCommand message = 1;
  if (this->message() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_message());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ControlCenterPacket::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ControlCenterPacket)
  GOOGLE_DCHECK_NE(&from, this);
  const ControlCenterPacket* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ControlCenterPacket>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ControlCenterPacket)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ControlCenterPacket)
    MergeFrom(*source);
  }
}

void ControlCenterPacket::MergeFrom(const ControlCenterPacket& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ControlCenterPacket)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.content().size() > 0) {
    _internal_set_content(from._internal_content());
  }
  if (from.message() != 0) {
    _internal_set_message(from._internal_message());
  }
}

void ControlCenterPacket::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ControlCenterPacket)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ControlCenterPacket::CopyFrom(const ControlCenterPacket& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ControlCenterPacket)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ControlCenterPacket::IsInitialized() const {
  return true;
}

void ControlCenterPacket::InternalSwap(ControlCenterPacket* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  content_.Swap(&other->content_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(message_, other->message_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ControlCenterPacket::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace cc
}  // namespace net
}  // namespace ce
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::ce::net::cc::ControlCenterPacket* Arena::CreateMaybeMessage< ::ce::net::cc::ControlCenterPacket >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ControlCenterPacket >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
