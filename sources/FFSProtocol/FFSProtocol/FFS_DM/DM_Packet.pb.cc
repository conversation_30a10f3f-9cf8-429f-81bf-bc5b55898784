// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: FFS_DM/DM_Packet.proto

#include "FFS_DM/DM_Packet.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_CrossEngineEnum_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_RouteInfo_CrossEngineEnum_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fDM_2fDM_5fPacket_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_FFS_5fDM_2fDM_5fPacket_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto;
namespace ce {
namespace net {
namespace cc {
class DaemonInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonInfo> _instance;
} _DaemonInfo_default_instance_;
class ProcessInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ProcessInfo> _instance;
} _ProcessInfo_default_instance_;
class ImageGeneratorInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ImageGeneratorInfo> _instance;
} _ImageGeneratorInfo_default_instance_;
class DaemonGenericResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonGenericResponse> _instance;
} _DaemonGenericResponse_default_instance_;
class DaemonHeartBeatDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonHeartBeat> _instance;
} _DaemonHeartBeat_default_instance_;
class DaemonHandshakeRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonHandshakeRequest> _instance;
} _DaemonHandshakeRequest_default_instance_;
class DaemonHandshakeResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonHandshakeResponse> _instance;
} _DaemonHandshakeResponse_default_instance_;
class DaemonStatusRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonStatusRequest> _instance;
} _DaemonStatusRequest_default_instance_;
class DaemonStatusResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonStatusResponse> _instance;
} _DaemonStatusResponse_default_instance_;
class DaemonLaunchRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonLaunchRequest> _instance;
} _DaemonLaunchRequest_default_instance_;
class DaemonLaunchResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonLaunchResponse> _instance;
} _DaemonLaunchResponse_default_instance_;
class DaemonShutdownRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonShutdownRequest> _instance;
} _DaemonShutdownRequest_default_instance_;
class DaemonShutdownResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<DaemonShutdownResponse> _instance;
} _DaemonShutdownResponse_default_instance_;
class CCUploadFilesRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCUploadFilesRequest> _instance;
} _CCUploadFilesRequest_default_instance_;
class CCUploadFilesResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCUploadFilesResponse> _instance;
} _CCUploadFilesResponse_default_instance_;
class CCControlIGRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCControlIGRequest> _instance;
} _CCControlIGRequest_default_instance_;
class CCControlIGResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCControlIGResponse> _instance;
} _CCControlIGResponse_default_instance_;
class CCGetDaemonStateRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCGetDaemonStateRequest> _instance;
} _CCGetDaemonStateRequest_default_instance_;
class CCGetDaemonStateResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCGetDaemonStateResponse> _instance;
} _CCGetDaemonStateResponse_default_instance_;
class CCGetIGStateRequestDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCGetIGStateRequest> _instance;
} _CCGetIGStateRequest_default_instance_;
class CCGetIGStateResponseDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CCGetIGStateResponse> _instance;
} _CCGetIGStateResponse_default_instance_;
}  // namespace cc
}  // namespace net
}  // namespace ce
static void InitDefaultsscc_info_CCControlIGRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCControlIGRequest_default_instance_;
    new (ptr) ::ce::net::cc::CCControlIGRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCControlIGRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCControlIGRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_CCControlIGResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCControlIGResponse_default_instance_;
    new (ptr) ::ce::net::cc::CCControlIGResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCControlIGResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCControlIGResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_CCGetDaemonStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCGetDaemonStateRequest_default_instance_;
    new (ptr) ::ce::net::cc::CCGetDaemonStateRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCGetDaemonStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCGetDaemonStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_CCGetDaemonStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCGetDaemonStateResponse_default_instance_;
    new (ptr) ::ce::net::cc::CCGetDaemonStateResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCGetDaemonStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCGetDaemonStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_CCGetIGStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCGetIGStateRequest_default_instance_;
    new (ptr) ::ce::net::cc::CCGetIGStateRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCGetIGStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCGetIGStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_CCGetIGStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCGetIGStateResponse_default_instance_;
    new (ptr) ::ce::net::cc::CCGetIGStateResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCGetIGStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCGetIGStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_CCUploadFilesRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCUploadFilesRequest_default_instance_;
    new (ptr) ::ce::net::cc::CCUploadFilesRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCUploadFilesRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCUploadFilesRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_CCUploadFilesResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_CCUploadFilesResponse_default_instance_;
    new (ptr) ::ce::net::cc::CCUploadFilesResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CCUploadFilesResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CCUploadFilesResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_DaemonGenericResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonGenericResponse_default_instance_;
    new (ptr) ::ce::net::cc::DaemonGenericResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_DaemonGenericResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, 0, InitDefaultsscc_info_DaemonGenericResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {
      &scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,
      &scc_info_RouteInfo_CrossEngineEnum_2eproto.base,
      &scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_DaemonHandshakeRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonHandshakeRequest_default_instance_;
    new (ptr) ::ce::net::cc::DaemonHandshakeRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_DaemonHandshakeRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_DaemonHandshakeRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {
      &scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_DaemonHandshakeResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonHandshakeResponse_default_instance_;
    new (ptr) ::ce::net::cc::DaemonHandshakeResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_DaemonHandshakeResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_DaemonHandshakeResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {
      &scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_DaemonHeartBeat_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonHeartBeat_default_instance_;
    new (ptr) ::ce::net::cc::DaemonHeartBeat();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DaemonHeartBeat_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_DaemonHeartBeat_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonInfo_default_instance_;
    new (ptr) ::ce::net::cc::DaemonInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_DaemonLaunchRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonLaunchRequest_default_instance_;
    new (ptr) ::ce::net::cc::DaemonLaunchRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_DaemonLaunchRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_DaemonLaunchRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {
      &scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_DaemonLaunchResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonLaunchResponse_default_instance_;
    new (ptr) ::ce::net::cc::DaemonLaunchResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DaemonLaunchResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_DaemonLaunchResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_DaemonShutdownRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonShutdownRequest_default_instance_;
    new (ptr) ::ce::net::cc::DaemonShutdownRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_DaemonShutdownRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_DaemonShutdownRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {
      &scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_DaemonShutdownResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonShutdownResponse_default_instance_;
    new (ptr) ::ce::net::cc::DaemonShutdownResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DaemonShutdownResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_DaemonShutdownResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_DaemonStatusRequest_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonStatusRequest_default_instance_;
    new (ptr) ::ce::net::cc::DaemonStatusRequest();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_DaemonStatusRequest_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_DaemonStatusRequest_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_DaemonStatusResponse_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_DaemonStatusResponse_default_instance_;
    new (ptr) ::ce::net::cc::DaemonStatusResponse();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_DaemonStatusResponse_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_DaemonStatusResponse_FFS_5fDM_2fDM_5fPacket_2eproto}, {
      &scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,}};

static void InitDefaultsscc_info_ImageGeneratorInfo_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ImageGeneratorInfo_default_instance_;
    new (ptr) ::ce::net::cc::ImageGeneratorInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ImageGeneratorInfo_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ImageGeneratorInfo_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static void InitDefaultsscc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::ce::net::cc::_ProcessInfo_default_instance_;
    new (ptr) ::ce::net::cc::ProcessInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

Protocol_API ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_FFS_5fDM_2fDM_5fPacket_2eproto[21];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_FFS_5fDM_2fDM_5fPacket_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonInfo, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonInfo, machine_name_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonInfo, ip_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ProcessInfo, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ProcessInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ProcessInfo, name_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ProcessInfo, status_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ProcessInfo, path_),
  0,
  2,
  1,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorInfo, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorInfo, id_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorInfo, master_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::ImageGeneratorInfo, status_),
  0,
  1,
  2,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonGenericResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonGenericResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonGenericResponse, daemon_info_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonGenericResponse, route_info_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonGenericResponse, processes_info_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonGenericResponse, ref_count_),
  0,
  1,
  ~0u,
  2,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonHeartBeat, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonHandshakeRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonHandshakeRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonHandshakeRequest, daemon_info_),
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonHandshakeResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonHandshakeResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonHandshakeResponse, daemon_info_),
  0,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonStatusRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonStatusResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonStatusResponse, processes_info_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonLaunchRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonLaunchRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonLaunchRequest, process_info_),
  0,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonLaunchResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonShutdownRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonShutdownRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonShutdownRequest, process_info_),
  0,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::DaemonShutdownResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesRequest, state_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesRequest, folder_path_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesResponse, state_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCUploadFilesResponse, process_msg_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGRequest, dbw_path_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGRequest, config_path_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGRequest, state_),
  0,
  1,
  2,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGResponse, state_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCControlIGResponse, process_msg_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetDaemonStateRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetDaemonStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetDaemonStateRequest, state_),
  0,
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetDaemonStateResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetDaemonStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetDaemonStateResponse, dbw_folder_path_),
  0,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, pc_name_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, daemon_name_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, ig_name_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, ip_address_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, cpu_temp_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, gpu_temp_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, cpu_load_),
  PROTOBUF_FIELD_OFFSET(::ce::net::cc::CCGetIGStateResponse, gpu_load_),
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 7, sizeof(::ce::net::cc::DaemonInfo)},
  { 9, 17, sizeof(::ce::net::cc::ProcessInfo)},
  { 20, 28, sizeof(::ce::net::cc::ImageGeneratorInfo)},
  { 31, 40, sizeof(::ce::net::cc::DaemonGenericResponse)},
  { 44, -1, sizeof(::ce::net::cc::DaemonHeartBeat)},
  { 49, 55, sizeof(::ce::net::cc::DaemonHandshakeRequest)},
  { 56, 62, sizeof(::ce::net::cc::DaemonHandshakeResponse)},
  { 63, -1, sizeof(::ce::net::cc::DaemonStatusRequest)},
  { 68, -1, sizeof(::ce::net::cc::DaemonStatusResponse)},
  { 74, 80, sizeof(::ce::net::cc::DaemonLaunchRequest)},
  { 81, -1, sizeof(::ce::net::cc::DaemonLaunchResponse)},
  { 86, 92, sizeof(::ce::net::cc::DaemonShutdownRequest)},
  { 93, -1, sizeof(::ce::net::cc::DaemonShutdownResponse)},
  { 98, 105, sizeof(::ce::net::cc::CCUploadFilesRequest)},
  { 107, 114, sizeof(::ce::net::cc::CCUploadFilesResponse)},
  { 116, 124, sizeof(::ce::net::cc::CCControlIGRequest)},
  { 127, 134, sizeof(::ce::net::cc::CCControlIGResponse)},
  { 136, 142, sizeof(::ce::net::cc::CCGetDaemonStateRequest)},
  { 143, 149, sizeof(::ce::net::cc::CCGetDaemonStateResponse)},
  { 150, -1, sizeof(::ce::net::cc::CCGetIGStateRequest)},
  { 155, 168, sizeof(::ce::net::cc::CCGetIGStateResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ProcessInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_ImageGeneratorInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonGenericResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonHeartBeat_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonHandshakeRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonHandshakeResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonStatusRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonStatusResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonLaunchRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonLaunchResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonShutdownRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_DaemonShutdownResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCUploadFilesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCUploadFilesResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCControlIGRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCControlIGResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCGetDaemonStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCGetDaemonStateResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCGetIGStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ce::net::cc::_CCGetIGStateResponse_default_instance_),
};

const char descriptor_table_protodef_FFS_5fDM_2fDM_5fPacket_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\026FFS_DM/DM_Packet.proto\022\tce.net.cc\032\025Cro"
  "ssEngineEnum.proto\".\n\nDaemonInfo\022\024\n\014mach"
  "ine_name\030\001 \002(\t\022\n\n\002ip\030\002 \002(\t\"Y\n\013ProcessInf"
  "o\022\014\n\004name\030\001 \002(\t\022.\n\006status\030\002 \002(\0162\036.ce.net"
  ".cc.EnumExecutionStatus\022\014\n\004path\030\003 \001(\t\"`\n"
  "\022ImageGeneratorInfo\022\n\n\002id\030\001 \002(\005\022\016\n\006maste"
  "r\030\002 \002(\010\022.\n\006status\030\003 \002(\0162\036.ce.net.cc.Enum"
  "ExecutionStatus\"\260\001\n\025DaemonGenericRespons"
  "e\022*\n\013daemon_info\030\001 \002(\0132\025.ce.net.cc.Daemo"
  "nInfo\022%\n\nroute_info\030\002 \002(\0132\021.ce.net.Route"
  "Info\022.\n\016processes_info\030\003 \003(\0132\026.ce.net.cc"
  ".ProcessInfo\022\024\n\tref_count\030\004 \002(\005:\0011\"\021\n\017Da"
  "emonHeartBeat\"D\n\026DaemonHandshakeRequest\022"
  "*\n\013daemon_info\030\001 \002(\0132\025.ce.net.cc.DaemonI"
  "nfo\"E\n\027DaemonHandshakeResponse\022*\n\013daemon"
  "_info\030\001 \002(\0132\025.ce.net.cc.DaemonInfo\"\025\n\023Da"
  "emonStatusRequest\"F\n\024DaemonStatusRespons"
  "e\022.\n\016processes_info\030\001 \003(\0132\026.ce.net.cc.Pr"
  "ocessInfo\"C\n\023DaemonLaunchRequest\022,\n\014proc"
  "ess_info\030\001 \002(\0132\026.ce.net.cc.ProcessInfo\"\026"
  "\n\024DaemonLaunchResponse\"E\n\025DaemonShutdown"
  "Request\022,\n\014process_info\030\001 \002(\0132\026.ce.net.c"
  "c.ProcessInfo\"\030\n\026DaemonShutdownResponse\""
  "\260\001\n\024CCUploadFilesRequest\022H\n\005state\030\001 \002(\0162"
  "9.ce.net.cc.CCUploadFilesRequest.EnumFil"
  "eType_OperatorCode\022\023\n\013folder_path\030\002 \002(\t\""
  "9\n\031EnumFileType_OperatorCode\022\013\n\007Airport\020"
  "\000\022\017\n\013Calibration\020\001\"\324\001\n\025CCUploadFilesResp"
  "onse\022G\n\005state\030\001 \002(\01628.ce.net.cc.CCUpload"
  "FilesResponse.EnumUpload_OperatorCode\022\023\n"
  "\013process_msg\030\002 \001(\t\"]\n\027EnumUpload_Operato"
  "rCode\022\013\n\007CopySuc\020\000\022\022\n\016ImportCheckSuc\020\001\022\014"
  "\n\010CopyFail\020\002\022\023\n\017ImportCheckFail\020\003\"\355\001\n\022CC"
  "ControlIGRequest\022\020\n\010dbw_path\030\001 \002(\t\022\023\n\013co"
  "nfig_path\030\002 \002(\t\022N\n\005state\030\003 \002(\0162\?.ce.net."
  "cc.CCControlIGRequest.EnumControlIGReque"
  "st_OperatorCode\"`\n!EnumControlIGRequest_"
  "OperatorCode\022\013\n\007StartIG\020\000\022\013\n\007Restart\020\001\022\025"
  "\n\021SkipCheck_Restart\020\002\022\n\n\006StopIG\020\003\"\365\001\n\023CC"
  "ControlIGResponse\022L\n\005state\030\001 \002(\0162=.ce.ne"
  "t.cc.CCControlIGResponse.EnumControlIGRe"
  "sp_OperatorCode\022\023\n\013process_msg\030\002 \001(\t\"{\n\036"
  "EnumControlIGResp_OperatorCode\022\010\n\004None\020\000"
  "\022\010\n\004Copy\020\001\022\t\n\005Unzip\020\002\022\016\n\nStartClock\020\003\022\014\n"
  "\010StartVSD\020\004\022\022\n\016StartIGSuccess\020\005\022\010\n\004Fail\020"
  "\006\"\324\001\n\027CCGetDaemonStateRequest\022N\n\005state\030\001"
  " \002(\0162\?.ce.net.cc.CCGetDaemonStateRequest"
  ".EnumDaemonState_OperatorCode\"i\n\034EnumDae"
  "monState_OperatorCode\022\r\n\tUpdateLog\020\000\022\023\n\017"
  "UpdateScreenPic\020\001\022\020\n\014StopComputer\020\002\022\023\n\017R"
  "estartComputer\020\003\"3\n\030CCGetDaemonStateResp"
  "onse\022\027\n\017dbw_folder_path\030\001 \002(\t\"\025\n\023CCGetIG"
  "StateRequest\"\251\001\n\024CCGetIGStateResponse\022\017\n"
  "\007pc_name\030\001 \002(\t\022\023\n\013daemon_name\030\002 \002(\t\022\017\n\007i"
  "g_name\030\003 \002(\t\022\022\n\nip_address\030\004 \002(\t\022\020\n\010cpu_"
  "temp\030\006 \002(\t\022\020\n\010gpu_temp\030\007 \002(\t\022\020\n\010cpu_load"
  "\030\010 \002(\t\022\020\n\010gpu_load\030\t \002(\t*|\n\023EnumExecutio"
  "nStatus\022\021\n\rUnknownStatus\020\000\022\013\n\007Pending\020\001\022"
  "\014\n\010Starting\020\002\022\013\n\007Running\020\003\022\017\n\013Terminatin"
  "g\020\004\022\r\n\tSucceeded\020\005\022\n\n\006Failed\020\006"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto_deps[1] = {
  &::descriptor_table_CrossEngineEnum_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto_sccs[21] = {
  &scc_info_CCControlIGRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_CCControlIGResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_CCGetDaemonStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_CCGetDaemonStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_CCGetIGStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_CCGetIGStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_CCUploadFilesRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_CCUploadFilesResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonGenericResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonHandshakeRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonHandshakeResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonHeartBeat_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonLaunchRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonLaunchResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonShutdownRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonShutdownResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonStatusRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_DaemonStatusResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_ImageGeneratorInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,
  &scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto = {
  false, false, descriptor_table_protodef_FFS_5fDM_2fDM_5fPacket_2eproto, "FFS_DM/DM_Packet.proto", 2390,
  &descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto_once, descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto_sccs, descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto_deps, 21, 1,
  schemas, file_default_instances, TableStruct_FFS_5fDM_2fDM_5fPacket_2eproto::offsets,
  file_level_metadata_FFS_5fDM_2fDM_5fPacket_2eproto, 21, file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto, file_level_service_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_FFS_5fDM_2fDM_5fPacket_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto)), true);
namespace ce {
namespace net {
namespace cc {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CCUploadFilesRequest_EnumFileType_OperatorCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto);
  return file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto[0];
}
bool CCUploadFilesRequest_EnumFileType_OperatorCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr CCUploadFilesRequest_EnumFileType_OperatorCode CCUploadFilesRequest::Airport;
constexpr CCUploadFilesRequest_EnumFileType_OperatorCode CCUploadFilesRequest::Calibration;
constexpr CCUploadFilesRequest_EnumFileType_OperatorCode CCUploadFilesRequest::EnumFileType_OperatorCode_MIN;
constexpr CCUploadFilesRequest_EnumFileType_OperatorCode CCUploadFilesRequest::EnumFileType_OperatorCode_MAX;
constexpr int CCUploadFilesRequest::EnumFileType_OperatorCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CCUploadFilesResponse_EnumUpload_OperatorCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto);
  return file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto[1];
}
bool CCUploadFilesResponse_EnumUpload_OperatorCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr CCUploadFilesResponse_EnumUpload_OperatorCode CCUploadFilesResponse::CopySuc;
constexpr CCUploadFilesResponse_EnumUpload_OperatorCode CCUploadFilesResponse::ImportCheckSuc;
constexpr CCUploadFilesResponse_EnumUpload_OperatorCode CCUploadFilesResponse::CopyFail;
constexpr CCUploadFilesResponse_EnumUpload_OperatorCode CCUploadFilesResponse::ImportCheckFail;
constexpr CCUploadFilesResponse_EnumUpload_OperatorCode CCUploadFilesResponse::EnumUpload_OperatorCode_MIN;
constexpr CCUploadFilesResponse_EnumUpload_OperatorCode CCUploadFilesResponse::EnumUpload_OperatorCode_MAX;
constexpr int CCUploadFilesResponse::EnumUpload_OperatorCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CCControlIGRequest_EnumControlIGRequest_OperatorCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto);
  return file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto[2];
}
bool CCControlIGRequest_EnumControlIGRequest_OperatorCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr CCControlIGRequest_EnumControlIGRequest_OperatorCode CCControlIGRequest::StartIG;
constexpr CCControlIGRequest_EnumControlIGRequest_OperatorCode CCControlIGRequest::Restart;
constexpr CCControlIGRequest_EnumControlIGRequest_OperatorCode CCControlIGRequest::SkipCheck_Restart;
constexpr CCControlIGRequest_EnumControlIGRequest_OperatorCode CCControlIGRequest::StopIG;
constexpr CCControlIGRequest_EnumControlIGRequest_OperatorCode CCControlIGRequest::EnumControlIGRequest_OperatorCode_MIN;
constexpr CCControlIGRequest_EnumControlIGRequest_OperatorCode CCControlIGRequest::EnumControlIGRequest_OperatorCode_MAX;
constexpr int CCControlIGRequest::EnumControlIGRequest_OperatorCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CCControlIGResponse_EnumControlIGResp_OperatorCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto);
  return file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto[3];
}
bool CCControlIGResponse_EnumControlIGResp_OperatorCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::None;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::Copy;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::Unzip;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::StartClock;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::StartVSD;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::StartIGSuccess;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::Fail;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::EnumControlIGResp_OperatorCode_MIN;
constexpr CCControlIGResponse_EnumControlIGResp_OperatorCode CCControlIGResponse::EnumControlIGResp_OperatorCode_MAX;
constexpr int CCControlIGResponse::EnumControlIGResp_OperatorCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CCGetDaemonStateRequest_EnumDaemonState_OperatorCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto);
  return file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto[4];
}
bool CCGetDaemonStateRequest_EnumDaemonState_OperatorCode_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr CCGetDaemonStateRequest_EnumDaemonState_OperatorCode CCGetDaemonStateRequest::UpdateLog;
constexpr CCGetDaemonStateRequest_EnumDaemonState_OperatorCode CCGetDaemonStateRequest::UpdateScreenPic;
constexpr CCGetDaemonStateRequest_EnumDaemonState_OperatorCode CCGetDaemonStateRequest::StopComputer;
constexpr CCGetDaemonStateRequest_EnumDaemonState_OperatorCode CCGetDaemonStateRequest::RestartComputer;
constexpr CCGetDaemonStateRequest_EnumDaemonState_OperatorCode CCGetDaemonStateRequest::EnumDaemonState_OperatorCode_MIN;
constexpr CCGetDaemonStateRequest_EnumDaemonState_OperatorCode CCGetDaemonStateRequest::EnumDaemonState_OperatorCode_MAX;
constexpr int CCGetDaemonStateRequest::EnumDaemonState_OperatorCode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* EnumExecutionStatus_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_FFS_5fDM_2fDM_5fPacket_2eproto);
  return file_level_enum_descriptors_FFS_5fDM_2fDM_5fPacket_2eproto[5];
}
bool EnumExecutionStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class DaemonInfo::_Internal {
 public:
  using HasBits = decltype(std::declval<DaemonInfo>()._has_bits_);
  static void set_has_machine_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_ip(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

DaemonInfo::DaemonInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonInfo)
}
DaemonInfo::DaemonInfo(const DaemonInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  machine_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_machine_name()) {
    machine_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_machine_name(), 
      GetArena());
  }
  ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_ip()) {
    ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ip(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonInfo)
}

void DaemonInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  machine_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

DaemonInfo::~DaemonInfo() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  machine_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DaemonInfo::ArenaDtor(void* object) {
  DaemonInfo* _this = reinterpret_cast< DaemonInfo* >(object);
  (void)_this;
}
void DaemonInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonInfo& DaemonInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      machine_name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      ip_.ClearNonDefaultToEmpty();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string machine_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_machine_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.DaemonInfo.machine_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string ip = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.DaemonInfo.ip");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string machine_name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_machine_name().data(), static_cast<int>(this->_internal_machine_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.DaemonInfo.machine_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_machine_name(), target);
  }

  // required string ip = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_ip().data(), static_cast<int>(this->_internal_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.DaemonInfo.ip");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_ip(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonInfo)
  return target;
}

size_t DaemonInfo::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.DaemonInfo)
  size_t total_size = 0;

  if (_internal_has_machine_name()) {
    // required string machine_name = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_machine_name());
  }

  if (_internal_has_ip()) {
    // required string ip = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ip());
  }

  return total_size;
}
size_t DaemonInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonInfo)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required string machine_name = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_machine_name());

    // required string ip = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ip());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonInfo)
    MergeFrom(*source);
  }
}

void DaemonInfo::MergeFrom(const DaemonInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_machine_name(from._internal_machine_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_ip(from._internal_ip());
    }
  }
}

void DaemonInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonInfo::CopyFrom(const DaemonInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonInfo::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void DaemonInfo::InternalSwap(DaemonInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  machine_name_.Swap(&other->machine_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ip_.Swap(&other->ip_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ProcessInfo::_Internal {
 public:
  using HasBits = decltype(std::declval<ProcessInfo>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_status(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_path(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000005) ^ 0x00000005) != 0;
  }
};

ProcessInfo::ProcessInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ProcessInfo)
}
ProcessInfo::ProcessInfo(const ProcessInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_name()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArena());
  }
  path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_path()) {
    path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_path(), 
      GetArena());
  }
  status_ = from.status_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ProcessInfo)
}

void ProcessInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  status_ = 0;
}

ProcessInfo::~ProcessInfo() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ProcessInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ProcessInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ProcessInfo::ArenaDtor(void* object) {
  ProcessInfo* _this = reinterpret_cast< ProcessInfo* >(object);
  (void)_this;
}
void ProcessInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ProcessInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ProcessInfo& ProcessInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ProcessInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void ProcessInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ProcessInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      path_.ClearNonDefaultToEmpty();
    }
  }
  status_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ProcessInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ProcessInfo.name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.cc.EnumExecutionStatus status = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::EnumExecutionStatus_IsValid(val))) {
            _internal_set_status(static_cast<::ce::net::cc::EnumExecutionStatus>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string path = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.ProcessInfo.path");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ProcessInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ProcessInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ProcessInfo.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // required .ce.net.cc.EnumExecutionStatus status = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_status(), target);
  }

  // optional string path = 3;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_path().data(), static_cast<int>(this->_internal_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.ProcessInfo.path");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_path(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ProcessInfo)
  return target;
}

size_t ProcessInfo::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ProcessInfo)
  size_t total_size = 0;

  if (_internal_has_name()) {
    // required string name = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  if (_internal_has_status()) {
    // required .ce.net.cc.EnumExecutionStatus status = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status());
  }

  return total_size;
}
size_t ProcessInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ProcessInfo)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000005) ^ 0x00000005) == 0) {  // All required fields are present.
    // required string name = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());

    // required .ce.net.cc.EnumExecutionStatus status = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string path = 3;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000002u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_path());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ProcessInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ProcessInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const ProcessInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ProcessInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ProcessInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ProcessInfo)
    MergeFrom(*source);
  }
}

void ProcessInfo::MergeFrom(const ProcessInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ProcessInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_name(from._internal_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_path(from._internal_path());
    }
    if (cached_has_bits & 0x00000004u) {
      status_ = from.status_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ProcessInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ProcessInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ProcessInfo::CopyFrom(const ProcessInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ProcessInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProcessInfo::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void ProcessInfo::InternalSwap(ProcessInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  name_.Swap(&other->name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  path_.Swap(&other->path_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(status_, other->status_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ProcessInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ImageGeneratorInfo::_Internal {
 public:
  using HasBits = decltype(std::declval<ImageGeneratorInfo>()._has_bits_);
  static void set_has_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_master(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_status(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

ImageGeneratorInfo::ImageGeneratorInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.ImageGeneratorInfo)
}
ImageGeneratorInfo::ImageGeneratorInfo(const ImageGeneratorInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&status_) -
    reinterpret_cast<char*>(&id_)) + sizeof(status_));
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.ImageGeneratorInfo)
}

void ImageGeneratorInfo::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&status_) -
      reinterpret_cast<char*>(&id_)) + sizeof(status_));
}

ImageGeneratorInfo::~ImageGeneratorInfo() {
  // @@protoc_insertion_point(destructor:ce.net.cc.ImageGeneratorInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ImageGeneratorInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void ImageGeneratorInfo::ArenaDtor(void* object) {
  ImageGeneratorInfo* _this = reinterpret_cast< ImageGeneratorInfo* >(object);
  (void)_this;
}
void ImageGeneratorInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImageGeneratorInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ImageGeneratorInfo& ImageGeneratorInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ImageGeneratorInfo_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void ImageGeneratorInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.ImageGeneratorInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    ::memset(&id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&status_) -
        reinterpret_cast<char*>(&id_)) + sizeof(status_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImageGeneratorInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _Internal::set_has_id(&has_bits);
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required bool master = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _Internal::set_has_master(&has_bits);
          master_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.cc.EnumExecutionStatus status = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::EnumExecutionStatus_IsValid(val))) {
            _internal_set_status(static_cast<::ce::net::cc::EnumExecutionStatus>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(3, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ImageGeneratorInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.ImageGeneratorInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required int32 id = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // required bool master = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_master(), target);
  }

  // required .ce.net.cc.EnumExecutionStatus status = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_status(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.ImageGeneratorInfo)
  return target;
}

size_t ImageGeneratorInfo::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.ImageGeneratorInfo)
  size_t total_size = 0;

  if (_internal_has_id()) {
    // required int32 id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_id());
  }

  if (_internal_has_master()) {
    // required bool master = 2;
    total_size += 1 + 1;
  }

  if (_internal_has_status()) {
    // required .ce.net.cc.EnumExecutionStatus status = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status());
  }

  return total_size;
}
size_t ImageGeneratorInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.ImageGeneratorInfo)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required int32 id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_id());

    // required bool master = 2;
    total_size += 1 + 1;

    // required .ce.net.cc.EnumExecutionStatus status = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ImageGeneratorInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.ImageGeneratorInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const ImageGeneratorInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ImageGeneratorInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.ImageGeneratorInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.ImageGeneratorInfo)
    MergeFrom(*source);
  }
}

void ImageGeneratorInfo::MergeFrom(const ImageGeneratorInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.ImageGeneratorInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      id_ = from.id_;
    }
    if (cached_has_bits & 0x00000002u) {
      master_ = from.master_;
    }
    if (cached_has_bits & 0x00000004u) {
      status_ = from.status_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void ImageGeneratorInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.ImageGeneratorInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ImageGeneratorInfo::CopyFrom(const ImageGeneratorInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.ImageGeneratorInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImageGeneratorInfo::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void ImageGeneratorInfo::InternalSwap(ImageGeneratorInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ImageGeneratorInfo, status_)
      + sizeof(ImageGeneratorInfo::status_)
      - PROTOBUF_FIELD_OFFSET(ImageGeneratorInfo, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ImageGeneratorInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonGenericResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<DaemonGenericResponse>()._has_bits_);
  static const ::ce::net::cc::DaemonInfo& daemon_info(const DaemonGenericResponse* msg);
  static void set_has_daemon_info(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::ce::net::RouteInfo& route_info(const DaemonGenericResponse* msg);
  static void set_has_route_info(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_ref_count(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

const ::ce::net::cc::DaemonInfo&
DaemonGenericResponse::_Internal::daemon_info(const DaemonGenericResponse* msg) {
  return *msg->daemon_info_;
}
const ::ce::net::RouteInfo&
DaemonGenericResponse::_Internal::route_info(const DaemonGenericResponse* msg) {
  return *msg->route_info_;
}
void DaemonGenericResponse::clear_route_info() {
  if (route_info_ != nullptr) route_info_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
DaemonGenericResponse::DaemonGenericResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  processes_info_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonGenericResponse)
}
DaemonGenericResponse::DaemonGenericResponse(const DaemonGenericResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      processes_info_(from.processes_info_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_daemon_info()) {
    daemon_info_ = new ::ce::net::cc::DaemonInfo(*from.daemon_info_);
  } else {
    daemon_info_ = nullptr;
  }
  if (from._internal_has_route_info()) {
    route_info_ = new ::ce::net::RouteInfo(*from.route_info_);
  } else {
    route_info_ = nullptr;
  }
  ref_count_ = from.ref_count_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonGenericResponse)
}

void DaemonGenericResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DaemonGenericResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&daemon_info_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&route_info_) -
      reinterpret_cast<char*>(&daemon_info_)) + sizeof(route_info_));
  ref_count_ = 1;
}

DaemonGenericResponse::~DaemonGenericResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonGenericResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonGenericResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete daemon_info_;
  if (this != internal_default_instance()) delete route_info_;
}

void DaemonGenericResponse::ArenaDtor(void* object) {
  DaemonGenericResponse* _this = reinterpret_cast< DaemonGenericResponse* >(object);
  (void)_this;
}
void DaemonGenericResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonGenericResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonGenericResponse& DaemonGenericResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonGenericResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonGenericResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonGenericResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  processes_info_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(daemon_info_ != nullptr);
      daemon_info_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(route_info_ != nullptr);
      route_info_->Clear();
    }
    ref_count_ = 1;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonGenericResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.DaemonInfo daemon_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_daemon_info(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.RouteInfo route_info = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_route_info(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .ce.net.cc.ProcessInfo processes_info = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_processes_info(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      // required int32 ref_count = 4 [default = 1];
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          _Internal::set_has_ref_count(&has_bits);
          ref_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonGenericResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonGenericResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.DaemonInfo daemon_info = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::daemon_info(this), target, stream);
  }

  // required .ce.net.RouteInfo route_info = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::route_info(this), target, stream);
  }

  // repeated .ce.net.cc.ProcessInfo processes_info = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_processes_info_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_processes_info(i), target, stream);
  }

  // required int32 ref_count = 4 [default = 1];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_ref_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonGenericResponse)
  return target;
}

size_t DaemonGenericResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.DaemonGenericResponse)
  size_t total_size = 0;

  if (_internal_has_daemon_info()) {
    // required .ce.net.cc.DaemonInfo daemon_info = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *daemon_info_);
  }

  if (_internal_has_route_info()) {
    // required .ce.net.RouteInfo route_info = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *route_info_);
  }

  if (_internal_has_ref_count()) {
    // required int32 ref_count = 4 [default = 1];
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_ref_count());
  }

  return total_size;
}
size_t DaemonGenericResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonGenericResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required .ce.net.cc.DaemonInfo daemon_info = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *daemon_info_);

    // required .ce.net.RouteInfo route_info = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *route_info_);

    // required int32 ref_count = 4 [default = 1];
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_ref_count());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.cc.ProcessInfo processes_info = 3;
  total_size += 1UL * this->_internal_processes_info_size();
  for (const auto& msg : this->processes_info_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonGenericResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonGenericResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonGenericResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonGenericResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonGenericResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonGenericResponse)
    MergeFrom(*source);
  }
}

void DaemonGenericResponse::MergeFrom(const DaemonGenericResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonGenericResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  processes_info_.MergeFrom(from.processes_info_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_daemon_info()->::ce::net::cc::DaemonInfo::MergeFrom(from._internal_daemon_info());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_route_info()->::ce::net::RouteInfo::MergeFrom(from._internal_route_info());
    }
    if (cached_has_bits & 0x00000004u) {
      ref_count_ = from.ref_count_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void DaemonGenericResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonGenericResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonGenericResponse::CopyFrom(const DaemonGenericResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonGenericResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonGenericResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(processes_info_)) return false;
  if (_internal_has_daemon_info()) {
    if (!daemon_info_->IsInitialized()) return false;
  }
  return true;
}

void DaemonGenericResponse::InternalSwap(DaemonGenericResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  processes_info_.InternalSwap(&other->processes_info_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DaemonGenericResponse, route_info_)
      + sizeof(DaemonGenericResponse::route_info_)
      - PROTOBUF_FIELD_OFFSET(DaemonGenericResponse, daemon_info_)>(
          reinterpret_cast<char*>(&daemon_info_),
          reinterpret_cast<char*>(&other->daemon_info_));
  swap(ref_count_, other->ref_count_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonGenericResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonHeartBeat::_Internal {
 public:
};

DaemonHeartBeat::DaemonHeartBeat(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonHeartBeat)
}
DaemonHeartBeat::DaemonHeartBeat(const DaemonHeartBeat& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonHeartBeat)
}

void DaemonHeartBeat::SharedCtor() {
}

DaemonHeartBeat::~DaemonHeartBeat() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonHeartBeat)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonHeartBeat::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void DaemonHeartBeat::ArenaDtor(void* object) {
  DaemonHeartBeat* _this = reinterpret_cast< DaemonHeartBeat* >(object);
  (void)_this;
}
void DaemonHeartBeat::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonHeartBeat::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonHeartBeat& DaemonHeartBeat::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonHeartBeat_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonHeartBeat::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonHeartBeat)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonHeartBeat::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonHeartBeat::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonHeartBeat)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonHeartBeat)
  return target;
}

size_t DaemonHeartBeat::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonHeartBeat)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonHeartBeat::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonHeartBeat)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonHeartBeat* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonHeartBeat>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonHeartBeat)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonHeartBeat)
    MergeFrom(*source);
  }
}

void DaemonHeartBeat::MergeFrom(const DaemonHeartBeat& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonHeartBeat)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void DaemonHeartBeat::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonHeartBeat)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonHeartBeat::CopyFrom(const DaemonHeartBeat& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonHeartBeat)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonHeartBeat::IsInitialized() const {
  return true;
}

void DaemonHeartBeat::InternalSwap(DaemonHeartBeat* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonHeartBeat::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonHandshakeRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<DaemonHandshakeRequest>()._has_bits_);
  static const ::ce::net::cc::DaemonInfo& daemon_info(const DaemonHandshakeRequest* msg);
  static void set_has_daemon_info(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

const ::ce::net::cc::DaemonInfo&
DaemonHandshakeRequest::_Internal::daemon_info(const DaemonHandshakeRequest* msg) {
  return *msg->daemon_info_;
}
DaemonHandshakeRequest::DaemonHandshakeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonHandshakeRequest)
}
DaemonHandshakeRequest::DaemonHandshakeRequest(const DaemonHandshakeRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_daemon_info()) {
    daemon_info_ = new ::ce::net::cc::DaemonInfo(*from.daemon_info_);
  } else {
    daemon_info_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonHandshakeRequest)
}

void DaemonHandshakeRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DaemonHandshakeRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  daemon_info_ = nullptr;
}

DaemonHandshakeRequest::~DaemonHandshakeRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonHandshakeRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonHandshakeRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete daemon_info_;
}

void DaemonHandshakeRequest::ArenaDtor(void* object) {
  DaemonHandshakeRequest* _this = reinterpret_cast< DaemonHandshakeRequest* >(object);
  (void)_this;
}
void DaemonHandshakeRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonHandshakeRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonHandshakeRequest& DaemonHandshakeRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonHandshakeRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonHandshakeRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonHandshakeRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(daemon_info_ != nullptr);
    daemon_info_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonHandshakeRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.DaemonInfo daemon_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_daemon_info(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonHandshakeRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonHandshakeRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.DaemonInfo daemon_info = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::daemon_info(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonHandshakeRequest)
  return target;
}

size_t DaemonHandshakeRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonHandshakeRequest)
  size_t total_size = 0;

  // required .ce.net.cc.DaemonInfo daemon_info = 1;
  if (_internal_has_daemon_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *daemon_info_);
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonHandshakeRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonHandshakeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonHandshakeRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonHandshakeRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonHandshakeRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonHandshakeRequest)
    MergeFrom(*source);
  }
}

void DaemonHandshakeRequest::MergeFrom(const DaemonHandshakeRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonHandshakeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_daemon_info()) {
    _internal_mutable_daemon_info()->::ce::net::cc::DaemonInfo::MergeFrom(from._internal_daemon_info());
  }
}

void DaemonHandshakeRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonHandshakeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonHandshakeRequest::CopyFrom(const DaemonHandshakeRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonHandshakeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonHandshakeRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_daemon_info()) {
    if (!daemon_info_->IsInitialized()) return false;
  }
  return true;
}

void DaemonHandshakeRequest::InternalSwap(DaemonHandshakeRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(daemon_info_, other->daemon_info_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonHandshakeRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonHandshakeResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<DaemonHandshakeResponse>()._has_bits_);
  static const ::ce::net::cc::DaemonInfo& daemon_info(const DaemonHandshakeResponse* msg);
  static void set_has_daemon_info(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

const ::ce::net::cc::DaemonInfo&
DaemonHandshakeResponse::_Internal::daemon_info(const DaemonHandshakeResponse* msg) {
  return *msg->daemon_info_;
}
DaemonHandshakeResponse::DaemonHandshakeResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonHandshakeResponse)
}
DaemonHandshakeResponse::DaemonHandshakeResponse(const DaemonHandshakeResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_daemon_info()) {
    daemon_info_ = new ::ce::net::cc::DaemonInfo(*from.daemon_info_);
  } else {
    daemon_info_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonHandshakeResponse)
}

void DaemonHandshakeResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DaemonHandshakeResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  daemon_info_ = nullptr;
}

DaemonHandshakeResponse::~DaemonHandshakeResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonHandshakeResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonHandshakeResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete daemon_info_;
}

void DaemonHandshakeResponse::ArenaDtor(void* object) {
  DaemonHandshakeResponse* _this = reinterpret_cast< DaemonHandshakeResponse* >(object);
  (void)_this;
}
void DaemonHandshakeResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonHandshakeResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonHandshakeResponse& DaemonHandshakeResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonHandshakeResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonHandshakeResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonHandshakeResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(daemon_info_ != nullptr);
    daemon_info_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonHandshakeResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.DaemonInfo daemon_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_daemon_info(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonHandshakeResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonHandshakeResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.DaemonInfo daemon_info = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::daemon_info(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonHandshakeResponse)
  return target;
}

size_t DaemonHandshakeResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonHandshakeResponse)
  size_t total_size = 0;

  // required .ce.net.cc.DaemonInfo daemon_info = 1;
  if (_internal_has_daemon_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *daemon_info_);
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonHandshakeResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonHandshakeResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonHandshakeResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonHandshakeResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonHandshakeResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonHandshakeResponse)
    MergeFrom(*source);
  }
}

void DaemonHandshakeResponse::MergeFrom(const DaemonHandshakeResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonHandshakeResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_daemon_info()) {
    _internal_mutable_daemon_info()->::ce::net::cc::DaemonInfo::MergeFrom(from._internal_daemon_info());
  }
}

void DaemonHandshakeResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonHandshakeResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonHandshakeResponse::CopyFrom(const DaemonHandshakeResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonHandshakeResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonHandshakeResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_daemon_info()) {
    if (!daemon_info_->IsInitialized()) return false;
  }
  return true;
}

void DaemonHandshakeResponse::InternalSwap(DaemonHandshakeResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(daemon_info_, other->daemon_info_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonHandshakeResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonStatusRequest::_Internal {
 public:
};

DaemonStatusRequest::DaemonStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonStatusRequest)
}
DaemonStatusRequest::DaemonStatusRequest(const DaemonStatusRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonStatusRequest)
}

void DaemonStatusRequest::SharedCtor() {
}

DaemonStatusRequest::~DaemonStatusRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonStatusRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonStatusRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void DaemonStatusRequest::ArenaDtor(void* object) {
  DaemonStatusRequest* _this = reinterpret_cast< DaemonStatusRequest* >(object);
  (void)_this;
}
void DaemonStatusRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonStatusRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonStatusRequest& DaemonStatusRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonStatusRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonStatusRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonStatusRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonStatusRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonStatusRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonStatusRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonStatusRequest)
  return target;
}

size_t DaemonStatusRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonStatusRequest)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonStatusRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonStatusRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonStatusRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonStatusRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonStatusRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonStatusRequest)
    MergeFrom(*source);
  }
}

void DaemonStatusRequest::MergeFrom(const DaemonStatusRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonStatusRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void DaemonStatusRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonStatusRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonStatusRequest::CopyFrom(const DaemonStatusRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonStatusRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonStatusRequest::IsInitialized() const {
  return true;
}

void DaemonStatusRequest::InternalSwap(DaemonStatusRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonStatusRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonStatusResponse::_Internal {
 public:
};

DaemonStatusResponse::DaemonStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  processes_info_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonStatusResponse)
}
DaemonStatusResponse::DaemonStatusResponse(const DaemonStatusResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      processes_info_(from.processes_info_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonStatusResponse)
}

void DaemonStatusResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DaemonStatusResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
}

DaemonStatusResponse::~DaemonStatusResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonStatusResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonStatusResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void DaemonStatusResponse::ArenaDtor(void* object) {
  DaemonStatusResponse* _this = reinterpret_cast< DaemonStatusResponse* >(object);
  (void)_this;
}
void DaemonStatusResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonStatusResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonStatusResponse& DaemonStatusResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonStatusResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonStatusResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonStatusResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  processes_info_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonStatusResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .ce.net.cc.ProcessInfo processes_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_processes_info(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonStatusResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonStatusResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .ce.net.cc.ProcessInfo processes_info = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_processes_info_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_processes_info(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonStatusResponse)
  return target;
}

size_t DaemonStatusResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonStatusResponse)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ce.net.cc.ProcessInfo processes_info = 1;
  total_size += 1UL * this->_internal_processes_info_size();
  for (const auto& msg : this->processes_info_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonStatusResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonStatusResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonStatusResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonStatusResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonStatusResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonStatusResponse)
    MergeFrom(*source);
  }
}

void DaemonStatusResponse::MergeFrom(const DaemonStatusResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonStatusResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  processes_info_.MergeFrom(from.processes_info_);
}

void DaemonStatusResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonStatusResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonStatusResponse::CopyFrom(const DaemonStatusResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonStatusResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonStatusResponse::IsInitialized() const {
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(processes_info_)) return false;
  return true;
}

void DaemonStatusResponse::InternalSwap(DaemonStatusResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  processes_info_.InternalSwap(&other->processes_info_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonStatusResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonLaunchRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<DaemonLaunchRequest>()._has_bits_);
  static const ::ce::net::cc::ProcessInfo& process_info(const DaemonLaunchRequest* msg);
  static void set_has_process_info(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

const ::ce::net::cc::ProcessInfo&
DaemonLaunchRequest::_Internal::process_info(const DaemonLaunchRequest* msg) {
  return *msg->process_info_;
}
DaemonLaunchRequest::DaemonLaunchRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonLaunchRequest)
}
DaemonLaunchRequest::DaemonLaunchRequest(const DaemonLaunchRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_process_info()) {
    process_info_ = new ::ce::net::cc::ProcessInfo(*from.process_info_);
  } else {
    process_info_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonLaunchRequest)
}

void DaemonLaunchRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DaemonLaunchRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  process_info_ = nullptr;
}

DaemonLaunchRequest::~DaemonLaunchRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonLaunchRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonLaunchRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete process_info_;
}

void DaemonLaunchRequest::ArenaDtor(void* object) {
  DaemonLaunchRequest* _this = reinterpret_cast< DaemonLaunchRequest* >(object);
  (void)_this;
}
void DaemonLaunchRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonLaunchRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonLaunchRequest& DaemonLaunchRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonLaunchRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonLaunchRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonLaunchRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(process_info_ != nullptr);
    process_info_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonLaunchRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.ProcessInfo process_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_process_info(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonLaunchRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonLaunchRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.ProcessInfo process_info = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::process_info(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonLaunchRequest)
  return target;
}

size_t DaemonLaunchRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonLaunchRequest)
  size_t total_size = 0;

  // required .ce.net.cc.ProcessInfo process_info = 1;
  if (_internal_has_process_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *process_info_);
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonLaunchRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonLaunchRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonLaunchRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonLaunchRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonLaunchRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonLaunchRequest)
    MergeFrom(*source);
  }
}

void DaemonLaunchRequest::MergeFrom(const DaemonLaunchRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonLaunchRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_process_info()) {
    _internal_mutable_process_info()->::ce::net::cc::ProcessInfo::MergeFrom(from._internal_process_info());
  }
}

void DaemonLaunchRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonLaunchRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonLaunchRequest::CopyFrom(const DaemonLaunchRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonLaunchRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonLaunchRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_process_info()) {
    if (!process_info_->IsInitialized()) return false;
  }
  return true;
}

void DaemonLaunchRequest::InternalSwap(DaemonLaunchRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(process_info_, other->process_info_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonLaunchRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonLaunchResponse::_Internal {
 public:
};

DaemonLaunchResponse::DaemonLaunchResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonLaunchResponse)
}
DaemonLaunchResponse::DaemonLaunchResponse(const DaemonLaunchResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonLaunchResponse)
}

void DaemonLaunchResponse::SharedCtor() {
}

DaemonLaunchResponse::~DaemonLaunchResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonLaunchResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonLaunchResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void DaemonLaunchResponse::ArenaDtor(void* object) {
  DaemonLaunchResponse* _this = reinterpret_cast< DaemonLaunchResponse* >(object);
  (void)_this;
}
void DaemonLaunchResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonLaunchResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonLaunchResponse& DaemonLaunchResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonLaunchResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonLaunchResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonLaunchResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonLaunchResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonLaunchResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonLaunchResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonLaunchResponse)
  return target;
}

size_t DaemonLaunchResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonLaunchResponse)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonLaunchResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonLaunchResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonLaunchResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonLaunchResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonLaunchResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonLaunchResponse)
    MergeFrom(*source);
  }
}

void DaemonLaunchResponse::MergeFrom(const DaemonLaunchResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonLaunchResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void DaemonLaunchResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonLaunchResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonLaunchResponse::CopyFrom(const DaemonLaunchResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonLaunchResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonLaunchResponse::IsInitialized() const {
  return true;
}

void DaemonLaunchResponse::InternalSwap(DaemonLaunchResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonLaunchResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonShutdownRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<DaemonShutdownRequest>()._has_bits_);
  static const ::ce::net::cc::ProcessInfo& process_info(const DaemonShutdownRequest* msg);
  static void set_has_process_info(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

const ::ce::net::cc::ProcessInfo&
DaemonShutdownRequest::_Internal::process_info(const DaemonShutdownRequest* msg) {
  return *msg->process_info_;
}
DaemonShutdownRequest::DaemonShutdownRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonShutdownRequest)
}
DaemonShutdownRequest::DaemonShutdownRequest(const DaemonShutdownRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_process_info()) {
    process_info_ = new ::ce::net::cc::ProcessInfo(*from.process_info_);
  } else {
    process_info_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonShutdownRequest)
}

void DaemonShutdownRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_DaemonShutdownRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  process_info_ = nullptr;
}

DaemonShutdownRequest::~DaemonShutdownRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonShutdownRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonShutdownRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete process_info_;
}

void DaemonShutdownRequest::ArenaDtor(void* object) {
  DaemonShutdownRequest* _this = reinterpret_cast< DaemonShutdownRequest* >(object);
  (void)_this;
}
void DaemonShutdownRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonShutdownRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonShutdownRequest& DaemonShutdownRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonShutdownRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonShutdownRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonShutdownRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(process_info_ != nullptr);
    process_info_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonShutdownRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.ProcessInfo process_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_process_info(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonShutdownRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonShutdownRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.ProcessInfo process_info = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::process_info(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonShutdownRequest)
  return target;
}

size_t DaemonShutdownRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonShutdownRequest)
  size_t total_size = 0;

  // required .ce.net.cc.ProcessInfo process_info = 1;
  if (_internal_has_process_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *process_info_);
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonShutdownRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonShutdownRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonShutdownRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonShutdownRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonShutdownRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonShutdownRequest)
    MergeFrom(*source);
  }
}

void DaemonShutdownRequest::MergeFrom(const DaemonShutdownRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonShutdownRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_process_info()) {
    _internal_mutable_process_info()->::ce::net::cc::ProcessInfo::MergeFrom(from._internal_process_info());
  }
}

void DaemonShutdownRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonShutdownRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonShutdownRequest::CopyFrom(const DaemonShutdownRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonShutdownRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonShutdownRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_process_info()) {
    if (!process_info_->IsInitialized()) return false;
  }
  return true;
}

void DaemonShutdownRequest::InternalSwap(DaemonShutdownRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(process_info_, other->process_info_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonShutdownRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class DaemonShutdownResponse::_Internal {
 public:
};

DaemonShutdownResponse::DaemonShutdownResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.DaemonShutdownResponse)
}
DaemonShutdownResponse::DaemonShutdownResponse(const DaemonShutdownResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.DaemonShutdownResponse)
}

void DaemonShutdownResponse::SharedCtor() {
}

DaemonShutdownResponse::~DaemonShutdownResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.DaemonShutdownResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void DaemonShutdownResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void DaemonShutdownResponse::ArenaDtor(void* object) {
  DaemonShutdownResponse* _this = reinterpret_cast< DaemonShutdownResponse* >(object);
  (void)_this;
}
void DaemonShutdownResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DaemonShutdownResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const DaemonShutdownResponse& DaemonShutdownResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_DaemonShutdownResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void DaemonShutdownResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.DaemonShutdownResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DaemonShutdownResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* DaemonShutdownResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.DaemonShutdownResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.DaemonShutdownResponse)
  return target;
}

size_t DaemonShutdownResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.DaemonShutdownResponse)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DaemonShutdownResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.DaemonShutdownResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const DaemonShutdownResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<DaemonShutdownResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.DaemonShutdownResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.DaemonShutdownResponse)
    MergeFrom(*source);
  }
}

void DaemonShutdownResponse::MergeFrom(const DaemonShutdownResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.DaemonShutdownResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void DaemonShutdownResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.DaemonShutdownResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DaemonShutdownResponse::CopyFrom(const DaemonShutdownResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.DaemonShutdownResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DaemonShutdownResponse::IsInitialized() const {
  return true;
}

void DaemonShutdownResponse::InternalSwap(DaemonShutdownResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DaemonShutdownResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCUploadFilesRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<CCUploadFilesRequest>()._has_bits_);
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_folder_path(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

CCUploadFilesRequest::CCUploadFilesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCUploadFilesRequest)
}
CCUploadFilesRequest::CCUploadFilesRequest(const CCUploadFilesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  folder_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_folder_path()) {
    folder_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_folder_path(), 
      GetArena());
  }
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCUploadFilesRequest)
}

void CCUploadFilesRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CCUploadFilesRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  folder_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  state_ = 0;
}

CCUploadFilesRequest::~CCUploadFilesRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCUploadFilesRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCUploadFilesRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  folder_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CCUploadFilesRequest::ArenaDtor(void* object) {
  CCUploadFilesRequest* _this = reinterpret_cast< CCUploadFilesRequest* >(object);
  (void)_this;
}
void CCUploadFilesRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCUploadFilesRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCUploadFilesRequest& CCUploadFilesRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCUploadFilesRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCUploadFilesRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCUploadFilesRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    folder_path_.ClearNonDefaultToEmpty();
  }
  state_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCUploadFilesRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.CCUploadFilesRequest.EnumFileType_OperatorCode state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::CCUploadFilesRequest_EnumFileType_OperatorCode_IsValid(val))) {
            _internal_set_state(static_cast<::ce::net::cc::CCUploadFilesRequest_EnumFileType_OperatorCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // required string folder_path = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_folder_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCUploadFilesRequest.folder_path");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCUploadFilesRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCUploadFilesRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.CCUploadFilesRequest.EnumFileType_OperatorCode state = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_state(), target);
  }

  // required string folder_path = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_folder_path().data(), static_cast<int>(this->_internal_folder_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCUploadFilesRequest.folder_path");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_folder_path(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCUploadFilesRequest)
  return target;
}

size_t CCUploadFilesRequest::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.CCUploadFilesRequest)
  size_t total_size = 0;

  if (_internal_has_folder_path()) {
    // required string folder_path = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folder_path());
  }

  if (_internal_has_state()) {
    // required .ce.net.cc.CCUploadFilesRequest.EnumFileType_OperatorCode state = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return total_size;
}
size_t CCUploadFilesRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCUploadFilesRequest)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required string folder_path = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_folder_path());

    // required .ce.net.cc.CCUploadFilesRequest.EnumFileType_OperatorCode state = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCUploadFilesRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCUploadFilesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CCUploadFilesRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCUploadFilesRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCUploadFilesRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCUploadFilesRequest)
    MergeFrom(*source);
  }
}

void CCUploadFilesRequest::MergeFrom(const CCUploadFilesRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCUploadFilesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_folder_path(from._internal_folder_path());
    }
    if (cached_has_bits & 0x00000002u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void CCUploadFilesRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCUploadFilesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCUploadFilesRequest::CopyFrom(const CCUploadFilesRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCUploadFilesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCUploadFilesRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void CCUploadFilesRequest::InternalSwap(CCUploadFilesRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  folder_path_.Swap(&other->folder_path_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(state_, other->state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CCUploadFilesRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCUploadFilesResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<CCUploadFilesResponse>()._has_bits_);
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_process_msg(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000002) ^ 0x00000002) != 0;
  }
};

CCUploadFilesResponse::CCUploadFilesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCUploadFilesResponse)
}
CCUploadFilesResponse::CCUploadFilesResponse(const CCUploadFilesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  process_msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_process_msg()) {
    process_msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_process_msg(), 
      GetArena());
  }
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCUploadFilesResponse)
}

void CCUploadFilesResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CCUploadFilesResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  process_msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  state_ = 0;
}

CCUploadFilesResponse::~CCUploadFilesResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCUploadFilesResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCUploadFilesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  process_msg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CCUploadFilesResponse::ArenaDtor(void* object) {
  CCUploadFilesResponse* _this = reinterpret_cast< CCUploadFilesResponse* >(object);
  (void)_this;
}
void CCUploadFilesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCUploadFilesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCUploadFilesResponse& CCUploadFilesResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCUploadFilesResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCUploadFilesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCUploadFilesResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    process_msg_.ClearNonDefaultToEmpty();
  }
  state_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCUploadFilesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.CCUploadFilesResponse.EnumUpload_OperatorCode state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::CCUploadFilesResponse_EnumUpload_OperatorCode_IsValid(val))) {
            _internal_set_state(static_cast<::ce::net::cc::CCUploadFilesResponse_EnumUpload_OperatorCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string process_msg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_process_msg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCUploadFilesResponse.process_msg");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCUploadFilesResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCUploadFilesResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.CCUploadFilesResponse.EnumUpload_OperatorCode state = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_state(), target);
  }

  // optional string process_msg = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_process_msg().data(), static_cast<int>(this->_internal_process_msg().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCUploadFilesResponse.process_msg");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_process_msg(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCUploadFilesResponse)
  return target;
}

size_t CCUploadFilesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCUploadFilesResponse)
  size_t total_size = 0;

  // required .ce.net.cc.CCUploadFilesResponse.EnumUpload_OperatorCode state = 1;
  if (_internal_has_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string process_msg = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_process_msg());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCUploadFilesResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCUploadFilesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CCUploadFilesResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCUploadFilesResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCUploadFilesResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCUploadFilesResponse)
    MergeFrom(*source);
  }
}

void CCUploadFilesResponse::MergeFrom(const CCUploadFilesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCUploadFilesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_process_msg(from._internal_process_msg());
    }
    if (cached_has_bits & 0x00000002u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void CCUploadFilesResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCUploadFilesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCUploadFilesResponse::CopyFrom(const CCUploadFilesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCUploadFilesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCUploadFilesResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void CCUploadFilesResponse::InternalSwap(CCUploadFilesResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  process_msg_.Swap(&other->process_msg_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(state_, other->state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CCUploadFilesResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCControlIGRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<CCControlIGRequest>()._has_bits_);
  static void set_has_dbw_path(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_config_path(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

CCControlIGRequest::CCControlIGRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCControlIGRequest)
}
CCControlIGRequest::CCControlIGRequest(const CCControlIGRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  dbw_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_dbw_path()) {
    dbw_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_dbw_path(), 
      GetArena());
  }
  config_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_config_path()) {
    config_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_config_path(), 
      GetArena());
  }
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCControlIGRequest)
}

void CCControlIGRequest::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CCControlIGRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  dbw_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  config_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  state_ = 0;
}

CCControlIGRequest::~CCControlIGRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCControlIGRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCControlIGRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  dbw_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  config_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CCControlIGRequest::ArenaDtor(void* object) {
  CCControlIGRequest* _this = reinterpret_cast< CCControlIGRequest* >(object);
  (void)_this;
}
void CCControlIGRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCControlIGRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCControlIGRequest& CCControlIGRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCControlIGRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCControlIGRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCControlIGRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      dbw_path_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      config_path_.ClearNonDefaultToEmpty();
    }
  }
  state_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCControlIGRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string dbw_path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_dbw_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCControlIGRequest.dbw_path");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string config_path = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_config_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCControlIGRequest.config_path");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required .ce.net.cc.CCControlIGRequest.EnumControlIGRequest_OperatorCode state = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::CCControlIGRequest_EnumControlIGRequest_OperatorCode_IsValid(val))) {
            _internal_set_state(static_cast<::ce::net::cc::CCControlIGRequest_EnumControlIGRequest_OperatorCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(3, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCControlIGRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCControlIGRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string dbw_path = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_dbw_path().data(), static_cast<int>(this->_internal_dbw_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCControlIGRequest.dbw_path");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_dbw_path(), target);
  }

  // required string config_path = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_config_path().data(), static_cast<int>(this->_internal_config_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCControlIGRequest.config_path");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_config_path(), target);
  }

  // required .ce.net.cc.CCControlIGRequest.EnumControlIGRequest_OperatorCode state = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCControlIGRequest)
  return target;
}

size_t CCControlIGRequest::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.CCControlIGRequest)
  size_t total_size = 0;

  if (_internal_has_dbw_path()) {
    // required string dbw_path = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dbw_path());
  }

  if (_internal_has_config_path()) {
    // required string config_path = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_config_path());
  }

  if (_internal_has_state()) {
    // required .ce.net.cc.CCControlIGRequest.EnumControlIGRequest_OperatorCode state = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return total_size;
}
size_t CCControlIGRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCControlIGRequest)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string dbw_path = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dbw_path());

    // required string config_path = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_config_path());

    // required .ce.net.cc.CCControlIGRequest.EnumControlIGRequest_OperatorCode state = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCControlIGRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCControlIGRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CCControlIGRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCControlIGRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCControlIGRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCControlIGRequest)
    MergeFrom(*source);
  }
}

void CCControlIGRequest::MergeFrom(const CCControlIGRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCControlIGRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_dbw_path(from._internal_dbw_path());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_config_path(from._internal_config_path());
    }
    if (cached_has_bits & 0x00000004u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void CCControlIGRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCControlIGRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCControlIGRequest::CopyFrom(const CCControlIGRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCControlIGRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCControlIGRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void CCControlIGRequest::InternalSwap(CCControlIGRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  dbw_path_.Swap(&other->dbw_path_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  config_path_.Swap(&other->config_path_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(state_, other->state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CCControlIGRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCControlIGResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<CCControlIGResponse>()._has_bits_);
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_process_msg(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000002) ^ 0x00000002) != 0;
  }
};

CCControlIGResponse::CCControlIGResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCControlIGResponse)
}
CCControlIGResponse::CCControlIGResponse(const CCControlIGResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  process_msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_process_msg()) {
    process_msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_process_msg(), 
      GetArena());
  }
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCControlIGResponse)
}

void CCControlIGResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CCControlIGResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  process_msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  state_ = 0;
}

CCControlIGResponse::~CCControlIGResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCControlIGResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCControlIGResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  process_msg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CCControlIGResponse::ArenaDtor(void* object) {
  CCControlIGResponse* _this = reinterpret_cast< CCControlIGResponse* >(object);
  (void)_this;
}
void CCControlIGResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCControlIGResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCControlIGResponse& CCControlIGResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCControlIGResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCControlIGResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCControlIGResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    process_msg_.ClearNonDefaultToEmpty();
  }
  state_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCControlIGResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.CCControlIGResponse.EnumControlIGResp_OperatorCode state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::CCControlIGResponse_EnumControlIGResp_OperatorCode_IsValid(val))) {
            _internal_set_state(static_cast<::ce::net::cc::CCControlIGResponse_EnumControlIGResp_OperatorCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      // optional string process_msg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_process_msg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCControlIGResponse.process_msg");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCControlIGResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCControlIGResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.CCControlIGResponse.EnumControlIGResp_OperatorCode state = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_state(), target);
  }

  // optional string process_msg = 2;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_process_msg().data(), static_cast<int>(this->_internal_process_msg().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCControlIGResponse.process_msg");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_process_msg(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCControlIGResponse)
  return target;
}

size_t CCControlIGResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCControlIGResponse)
  size_t total_size = 0;

  // required .ce.net.cc.CCControlIGResponse.EnumControlIGResp_OperatorCode state = 1;
  if (_internal_has_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional string process_msg = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_process_msg());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCControlIGResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCControlIGResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CCControlIGResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCControlIGResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCControlIGResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCControlIGResponse)
    MergeFrom(*source);
  }
}

void CCControlIGResponse::MergeFrom(const CCControlIGResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCControlIGResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_process_msg(from._internal_process_msg());
    }
    if (cached_has_bits & 0x00000002u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void CCControlIGResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCControlIGResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCControlIGResponse::CopyFrom(const CCControlIGResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCControlIGResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCControlIGResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void CCControlIGResponse::InternalSwap(CCControlIGResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  process_msg_.Swap(&other->process_msg_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(state_, other->state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CCControlIGResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCGetDaemonStateRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<CCGetDaemonStateRequest>()._has_bits_);
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

CCGetDaemonStateRequest::CCGetDaemonStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCGetDaemonStateRequest)
}
CCGetDaemonStateRequest::CCGetDaemonStateRequest(const CCGetDaemonStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCGetDaemonStateRequest)
}

void CCGetDaemonStateRequest::SharedCtor() {
  state_ = 0;
}

CCGetDaemonStateRequest::~CCGetDaemonStateRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCGetDaemonStateRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCGetDaemonStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void CCGetDaemonStateRequest::ArenaDtor(void* object) {
  CCGetDaemonStateRequest* _this = reinterpret_cast< CCGetDaemonStateRequest* >(object);
  (void)_this;
}
void CCGetDaemonStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCGetDaemonStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCGetDaemonStateRequest& CCGetDaemonStateRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCGetDaemonStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCGetDaemonStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCGetDaemonStateRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  state_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCGetDaemonStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required .ce.net.cc.CCGetDaemonStateRequest.EnumDaemonState_OperatorCode state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::ce::net::cc::CCGetDaemonStateRequest_EnumDaemonState_OperatorCode_IsValid(val))) {
            _internal_set_state(static_cast<::ce::net::cc::CCGetDaemonStateRequest_EnumDaemonState_OperatorCode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCGetDaemonStateRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCGetDaemonStateRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .ce.net.cc.CCGetDaemonStateRequest.EnumDaemonState_OperatorCode state = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCGetDaemonStateRequest)
  return target;
}

size_t CCGetDaemonStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCGetDaemonStateRequest)
  size_t total_size = 0;

  // required .ce.net.cc.CCGetDaemonStateRequest.EnumDaemonState_OperatorCode state = 1;
  if (_internal_has_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCGetDaemonStateRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCGetDaemonStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CCGetDaemonStateRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCGetDaemonStateRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCGetDaemonStateRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCGetDaemonStateRequest)
    MergeFrom(*source);
  }
}

void CCGetDaemonStateRequest::MergeFrom(const CCGetDaemonStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCGetDaemonStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_state()) {
    _internal_set_state(from._internal_state());
  }
}

void CCGetDaemonStateRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCGetDaemonStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCGetDaemonStateRequest::CopyFrom(const CCGetDaemonStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCGetDaemonStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCGetDaemonStateRequest::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void CCGetDaemonStateRequest::InternalSwap(CCGetDaemonStateRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(state_, other->state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CCGetDaemonStateRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCGetDaemonStateResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<CCGetDaemonStateResponse>()._has_bits_);
  static void set_has_dbw_folder_path(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000001) ^ 0x00000001) != 0;
  }
};

CCGetDaemonStateResponse::CCGetDaemonStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCGetDaemonStateResponse)
}
CCGetDaemonStateResponse::CCGetDaemonStateResponse(const CCGetDaemonStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  dbw_folder_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_dbw_folder_path()) {
    dbw_folder_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_dbw_folder_path(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCGetDaemonStateResponse)
}

void CCGetDaemonStateResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CCGetDaemonStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  dbw_folder_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

CCGetDaemonStateResponse::~CCGetDaemonStateResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCGetDaemonStateResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCGetDaemonStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  dbw_folder_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CCGetDaemonStateResponse::ArenaDtor(void* object) {
  CCGetDaemonStateResponse* _this = reinterpret_cast< CCGetDaemonStateResponse* >(object);
  (void)_this;
}
void CCGetDaemonStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCGetDaemonStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCGetDaemonStateResponse& CCGetDaemonStateResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCGetDaemonStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCGetDaemonStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCGetDaemonStateResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    dbw_folder_path_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCGetDaemonStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string dbw_folder_path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_dbw_folder_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetDaemonStateResponse.dbw_folder_path");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCGetDaemonStateResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCGetDaemonStateResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string dbw_folder_path = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_dbw_folder_path().data(), static_cast<int>(this->_internal_dbw_folder_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetDaemonStateResponse.dbw_folder_path");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_dbw_folder_path(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCGetDaemonStateResponse)
  return target;
}

size_t CCGetDaemonStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCGetDaemonStateResponse)
  size_t total_size = 0;

  // required string dbw_folder_path = 1;
  if (_internal_has_dbw_folder_path()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dbw_folder_path());
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCGetDaemonStateResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCGetDaemonStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CCGetDaemonStateResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCGetDaemonStateResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCGetDaemonStateResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCGetDaemonStateResponse)
    MergeFrom(*source);
  }
}

void CCGetDaemonStateResponse::MergeFrom(const CCGetDaemonStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCGetDaemonStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_dbw_folder_path()) {
    _internal_set_dbw_folder_path(from._internal_dbw_folder_path());
  }
}

void CCGetDaemonStateResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCGetDaemonStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCGetDaemonStateResponse::CopyFrom(const CCGetDaemonStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCGetDaemonStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCGetDaemonStateResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void CCGetDaemonStateResponse::InternalSwap(CCGetDaemonStateResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  dbw_folder_path_.Swap(&other->dbw_folder_path_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata CCGetDaemonStateResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCGetIGStateRequest::_Internal {
 public:
};

CCGetIGStateRequest::CCGetIGStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCGetIGStateRequest)
}
CCGetIGStateRequest::CCGetIGStateRequest(const CCGetIGStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCGetIGStateRequest)
}

void CCGetIGStateRequest::SharedCtor() {
}

CCGetIGStateRequest::~CCGetIGStateRequest() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCGetIGStateRequest)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCGetIGStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void CCGetIGStateRequest::ArenaDtor(void* object) {
  CCGetIGStateRequest* _this = reinterpret_cast< CCGetIGStateRequest* >(object);
  (void)_this;
}
void CCGetIGStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCGetIGStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCGetIGStateRequest& CCGetIGStateRequest::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCGetIGStateRequest_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCGetIGStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCGetIGStateRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCGetIGStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCGetIGStateRequest::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCGetIGStateRequest)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCGetIGStateRequest)
  return target;
}

size_t CCGetIGStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCGetIGStateRequest)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCGetIGStateRequest::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCGetIGStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CCGetIGStateRequest* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCGetIGStateRequest>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCGetIGStateRequest)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCGetIGStateRequest)
    MergeFrom(*source);
  }
}

void CCGetIGStateRequest::MergeFrom(const CCGetIGStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCGetIGStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void CCGetIGStateRequest::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCGetIGStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCGetIGStateRequest::CopyFrom(const CCGetIGStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCGetIGStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCGetIGStateRequest::IsInitialized() const {
  return true;
}

void CCGetIGStateRequest::InternalSwap(CCGetIGStateRequest* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CCGetIGStateRequest::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class CCGetIGStateResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<CCGetIGStateResponse>()._has_bits_);
  static void set_has_pc_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_daemon_name(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_ig_name(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_ip_address(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_cpu_temp(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_gpu_temp(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_cpu_load(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_gpu_load(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x000000ff) ^ 0x000000ff) != 0;
  }
};

CCGetIGStateResponse::CCGetIGStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:ce.net.cc.CCGetIGStateResponse)
}
CCGetIGStateResponse::CCGetIGStateResponse(const CCGetIGStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  pc_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_pc_name()) {
    pc_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_pc_name(), 
      GetArena());
  }
  daemon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_daemon_name()) {
    daemon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_daemon_name(), 
      GetArena());
  }
  ig_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_ig_name()) {
    ig_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ig_name(), 
      GetArena());
  }
  ip_address_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_ip_address()) {
    ip_address_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ip_address(), 
      GetArena());
  }
  cpu_temp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_cpu_temp()) {
    cpu_temp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cpu_temp(), 
      GetArena());
  }
  gpu_temp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_gpu_temp()) {
    gpu_temp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_gpu_temp(), 
      GetArena());
  }
  cpu_load_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_cpu_load()) {
    cpu_load_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cpu_load(), 
      GetArena());
  }
  gpu_load_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (from._internal_has_gpu_load()) {
    gpu_load_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_gpu_load(), 
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:ce.net.cc.CCGetIGStateResponse)
}

void CCGetIGStateResponse::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CCGetIGStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  pc_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  daemon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ig_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ip_address_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cpu_temp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  gpu_temp_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cpu_load_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  gpu_load_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

CCGetIGStateResponse::~CCGetIGStateResponse() {
  // @@protoc_insertion_point(destructor:ce.net.cc.CCGetIGStateResponse)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CCGetIGStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  pc_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  daemon_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ig_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ip_address_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cpu_temp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  gpu_temp_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cpu_load_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  gpu_load_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CCGetIGStateResponse::ArenaDtor(void* object) {
  CCGetIGStateResponse* _this = reinterpret_cast< CCGetIGStateResponse* >(object);
  (void)_this;
}
void CCGetIGStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CCGetIGStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CCGetIGStateResponse& CCGetIGStateResponse::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CCGetIGStateResponse_FFS_5fDM_2fDM_5fPacket_2eproto.base);
  return *internal_default_instance();
}


void CCGetIGStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:ce.net.cc.CCGetIGStateResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      pc_name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      daemon_name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000004u) {
      ig_name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000008u) {
      ip_address_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000010u) {
      cpu_temp_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000020u) {
      gpu_temp_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000040u) {
      cpu_load_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000080u) {
      gpu_load_.ClearNonDefaultToEmpty();
    }
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CCGetIGStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // required string pc_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_pc_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.pc_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string daemon_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_daemon_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.daemon_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string ig_name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_ig_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.ig_name");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string ip_address = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_ip_address();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.ip_address");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string cpu_temp = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_cpu_temp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.cpu_temp");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string gpu_temp = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_gpu_temp();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.gpu_temp");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string cpu_load = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_cpu_load();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.cpu_load");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // required string gpu_load = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_gpu_load();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ce.net.cc.CCGetIGStateResponse.gpu_load");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CCGetIGStateResponse::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ce.net.cc.CCGetIGStateResponse)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string pc_name = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_pc_name().data(), static_cast<int>(this->_internal_pc_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.pc_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_pc_name(), target);
  }

  // required string daemon_name = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_daemon_name().data(), static_cast<int>(this->_internal_daemon_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.daemon_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_daemon_name(), target);
  }

  // required string ig_name = 3;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_ig_name().data(), static_cast<int>(this->_internal_ig_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.ig_name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_ig_name(), target);
  }

  // required string ip_address = 4;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_ip_address().data(), static_cast<int>(this->_internal_ip_address().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.ip_address");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_ip_address(), target);
  }

  // required string cpu_temp = 6;
  if (cached_has_bits & 0x00000010u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_cpu_temp().data(), static_cast<int>(this->_internal_cpu_temp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.cpu_temp");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_cpu_temp(), target);
  }

  // required string gpu_temp = 7;
  if (cached_has_bits & 0x00000020u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_gpu_temp().data(), static_cast<int>(this->_internal_gpu_temp().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.gpu_temp");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_gpu_temp(), target);
  }

  // required string cpu_load = 8;
  if (cached_has_bits & 0x00000040u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_cpu_load().data(), static_cast<int>(this->_internal_cpu_load().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.cpu_load");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_cpu_load(), target);
  }

  // required string gpu_load = 9;
  if (cached_has_bits & 0x00000080u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_gpu_load().data(), static_cast<int>(this->_internal_gpu_load().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "ce.net.cc.CCGetIGStateResponse.gpu_load");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_gpu_load(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ce.net.cc.CCGetIGStateResponse)
  return target;
}

size_t CCGetIGStateResponse::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:ce.net.cc.CCGetIGStateResponse)
  size_t total_size = 0;

  if (_internal_has_pc_name()) {
    // required string pc_name = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_pc_name());
  }

  if (_internal_has_daemon_name()) {
    // required string daemon_name = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_daemon_name());
  }

  if (_internal_has_ig_name()) {
    // required string ig_name = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ig_name());
  }

  if (_internal_has_ip_address()) {
    // required string ip_address = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ip_address());
  }

  if (_internal_has_cpu_temp()) {
    // required string cpu_temp = 6;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cpu_temp());
  }

  if (_internal_has_gpu_temp()) {
    // required string gpu_temp = 7;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_gpu_temp());
  }

  if (_internal_has_cpu_load()) {
    // required string cpu_load = 8;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cpu_load());
  }

  if (_internal_has_gpu_load()) {
    // required string gpu_load = 9;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_gpu_load());
  }

  return total_size;
}
size_t CCGetIGStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ce.net.cc.CCGetIGStateResponse)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x000000ff) ^ 0x000000ff) == 0) {  // All required fields are present.
    // required string pc_name = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_pc_name());

    // required string daemon_name = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_daemon_name());

    // required string ig_name = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ig_name());

    // required string ip_address = 4;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ip_address());

    // required string cpu_temp = 6;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cpu_temp());

    // required string gpu_temp = 7;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_gpu_temp());

    // required string cpu_load = 8;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cpu_load());

    // required string gpu_load = 9;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_gpu_load());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CCGetIGStateResponse::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ce.net.cc.CCGetIGStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CCGetIGStateResponse* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CCGetIGStateResponse>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ce.net.cc.CCGetIGStateResponse)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ce.net.cc.CCGetIGStateResponse)
    MergeFrom(*source);
  }
}

void CCGetIGStateResponse::MergeFrom(const CCGetIGStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ce.net.cc.CCGetIGStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_pc_name(from._internal_pc_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_daemon_name(from._internal_daemon_name());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_set_ig_name(from._internal_ig_name());
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_set_ip_address(from._internal_ip_address());
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_set_cpu_temp(from._internal_cpu_temp());
    }
    if (cached_has_bits & 0x00000020u) {
      _internal_set_gpu_temp(from._internal_gpu_temp());
    }
    if (cached_has_bits & 0x00000040u) {
      _internal_set_cpu_load(from._internal_cpu_load());
    }
    if (cached_has_bits & 0x00000080u) {
      _internal_set_gpu_load(from._internal_gpu_load());
    }
  }
}

void CCGetIGStateResponse::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ce.net.cc.CCGetIGStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CCGetIGStateResponse::CopyFrom(const CCGetIGStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ce.net.cc.CCGetIGStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CCGetIGStateResponse::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void CCGetIGStateResponse::InternalSwap(CCGetIGStateResponse* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  pc_name_.Swap(&other->pc_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  daemon_name_.Swap(&other->daemon_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ig_name_.Swap(&other->ig_name_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ip_address_.Swap(&other->ip_address_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  cpu_temp_.Swap(&other->cpu_temp_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  gpu_temp_.Swap(&other->gpu_temp_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  cpu_load_.Swap(&other->cpu_load_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  gpu_load_.Swap(&other->gpu_load_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata CCGetIGStateResponse::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace cc
}  // namespace net
}  // namespace ce
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonInfo* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ProcessInfo* Arena::CreateMaybeMessage< ::ce::net::cc::ProcessInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ProcessInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::ImageGeneratorInfo* Arena::CreateMaybeMessage< ::ce::net::cc::ImageGeneratorInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::ImageGeneratorInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonGenericResponse* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonGenericResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonGenericResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonHeartBeat* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonHeartBeat >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonHeartBeat >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonHandshakeRequest* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonHandshakeRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonHandshakeRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonHandshakeResponse* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonHandshakeResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonHandshakeResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonStatusRequest* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonStatusRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonStatusRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonStatusResponse* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonStatusResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonStatusResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonLaunchRequest* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonLaunchRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonLaunchRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonLaunchResponse* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonLaunchResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonLaunchResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonShutdownRequest* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonShutdownRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonShutdownRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::DaemonShutdownResponse* Arena::CreateMaybeMessage< ::ce::net::cc::DaemonShutdownResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::DaemonShutdownResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCUploadFilesRequest* Arena::CreateMaybeMessage< ::ce::net::cc::CCUploadFilesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCUploadFilesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCUploadFilesResponse* Arena::CreateMaybeMessage< ::ce::net::cc::CCUploadFilesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCUploadFilesResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCControlIGRequest* Arena::CreateMaybeMessage< ::ce::net::cc::CCControlIGRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCControlIGRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCControlIGResponse* Arena::CreateMaybeMessage< ::ce::net::cc::CCControlIGResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCControlIGResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCGetDaemonStateRequest* Arena::CreateMaybeMessage< ::ce::net::cc::CCGetDaemonStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCGetDaemonStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCGetDaemonStateResponse* Arena::CreateMaybeMessage< ::ce::net::cc::CCGetDaemonStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCGetDaemonStateResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCGetIGStateRequest* Arena::CreateMaybeMessage< ::ce::net::cc::CCGetIGStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCGetIGStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::ce::net::cc::CCGetIGStateResponse* Arena::CreateMaybeMessage< ::ce::net::cc::CCGetIGStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ce::net::cc::CCGetIGStateResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
