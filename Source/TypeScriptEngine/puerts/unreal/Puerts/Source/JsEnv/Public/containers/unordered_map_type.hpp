#pragma once
#include <TypeInfo.hpp>
#include <unordered_map>
namespace PUERTS_NAMESPACE
{

template <typename KeyType, typename ValueType>
struct ScriptTypeName<std::unordered_map<KeyType, ValueType>>
{
    static constexpr auto value()
    {
        auto key_name = ScriptTypeName<KeyType>::value();
        auto value_name = ScriptTypeName<ValueType>::value();
        return internal::Literal("CUnorderedMap<") + key_name + internal::Literal(", ") + value_name + internal::Literal(">");
    }
};

template <typename KeyType, typename ValueType>
struct is_objecttype<std::unordered_map<KeyType, ValueType>> : public std::true_type
{
};

namespace v8_impl
{
template<typename KeyType, typename ValueType>
class StdUnorderedMapExtension
{
    using MapType = std::unordered_map<KeyType, ValueType>;

public:
    static int32_t Num(const MapType& data)
    {
        return data.size();
    }

    static bool Empty(const MapType& data)
    {
        return data.empty();
    }

    static void Add(MapType& data, const KeyType& key, const ValueType& value)
    {
        data.insert(std::make_pair(key, value));
    }

    static int32_t GetMaxIndex(const MapType& data)
    {
        return data.size() - 1;
    }

    static bool Contains(const MapType& data, const KeyType& key)
    {
        return data.find(key) != data.end();
    }

    static bool IsValidIndex(MapType& data, int32_t index)
    {
        return index < data.size() && index >= 0;
    }

    static const KeyType& GetKey(const MapType& data, int32_t index)
    {
        auto it = data.begin();
        std::advance(it, index);
        return it->first;
    }

    static ValueType Get(const MapType& data, const KeyType& key)
    {
        auto it = data.find(key);
        if (it != data.end())
        {
            return it->second;
        }
        throw std::out_of_range("Key not found in map");
    }

    static const ValueType& GetRef(MapType& data, const KeyType& key)
    {
        auto it = data.find(key);
        if (it != data.end())
        {
            return it->second;
        }
        throw std::out_of_range("Key not found in map");
    }

    static void Remove(MapType& data, const KeyType& key)
    {
        auto it = data.find(key);
        if (it != data.end())
        {
            data.erase(it);
        }
    }
};

template <typename KeyType, typename ValueType>
struct Converter<std::unordered_map<KeyType, ValueType>*>
{
    using CLS = std::unordered_map<KeyType, ValueType>;

    static v8::Local<v8::Value> toScript(v8::Local<v8::Context> context, CLS* value)          
    {                                                                                         
        if (value != nullptr)                                                                 
        {                                                
            return ::PUERTS_NAMESPACE::DataTransfer::FindOrAddCData(                          
                context->GetIsolate(), context, DynamicTypeId<CLS>::get(value), value, true); 
        }                                                                                     
        else                                                                                  
        {                                                                                     
            return v8::Undefined(context->GetIsolate());                                      
        }                                                                                     
    }

    static CLS* toCpp(v8::Local<v8::Context> context, const v8::Local<v8::Value>& value)      
    {
        return ::PUERTS_NAMESPACE::DataTransfer::GetPointerFast<CLS>(value.As<v8::Object>());
    }

    static bool accept(v8::Local<v8::Context> context, const v8::Local<v8::Value>& value)     
    {                                                                                         
        return ::PUERTS_NAMESPACE::DataTransfer::IsInstanceOf(                                
            context->GetIsolate(), StaticTypeId<CLS>::get(), value.As<v8::Object>());         
    }                                                                                         
};

}}
#define RegisterUnorderedMapWrapper(MAP_TYPE, REGISTER_TYPE)                                                                                                                                                                                              \
    {                                                                                                                                                                                                                                          \
        using KeyType =  MAP_TYPE::key_type;                                                                                                                                                       \
        using ValueType2 =  MAP_TYPE::mapped_type;                                                                                                                                                       \
        using ExtensionType = puerts::v8_impl::StdUnorderedMapExtension<KeyType, ValueType2>;                                                                                                                                                                       \
        auto classBuilder = ::PUERTS_NAMESPACE::DefineClass<MAP_TYPE>(REGISTER_TYPE);                                                                                                                                                        \
        if (FindCppTypeClassByName(classBuilder.className_) == nullptr)                                                                                                                                                                                \
        {                                                                                                                                                                                                                                              \
			classBuilder.template Constructor<>()                                                                                                                                                                                                       \
            .Method("Num", MakeExtension(&ExtensionType::Num))                                                                                                                                                  \
            .Method("Add", MakeExtension(&ExtensionType::Add))                                                                                                                                   \
            .Method("Get", MakeExtension(&ExtensionType::Get))                                                                                                                                   \
            .Method("GetRef", MakeExtension(&ExtensionType::GetRef))                                                                                                                                   \
            .Method("Set", MakeExtension(&ExtensionType::Add))                                                                                                                                               \
            .Method("Remove", MakeExtension(&ExtensionType::Remove))                                                                                                                                         \
            .Method("GetMaxIndex", MakeExtension(&ExtensionType::GetMaxIndex))                                                                                                                                         \
            .Method("IsValidIndex", MakeExtension(&ExtensionType::IsValidIndex))                                                                                                                                                  \
            .Method("GetKey", MakeExtension(&ExtensionType::GetKey))                                                                                                                                               \
            .Method("Contains", MakeExtension(&ExtensionType::Contains))                                                                                                                                               \
            .Method("Empty", MakeExtension(&ExtensionType::Empty))                                                                                                                                               \
            .Register();                                                                                                                                                                                                                       \
        }                                                                                                                                                                                                                                              \
        ::PUERTS_NAMESPACE::ContainerRegistry::Instance().RegisterClassConstructor<MAP_TYPE>(); \
     }
