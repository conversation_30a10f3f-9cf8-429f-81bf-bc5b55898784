#include "EnginePrefix.h"
#include "Runtime/Interface/CrossEngine.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetStreaming.h"
#include "resourceasset.h"
#include "Texture/Texture2D.h"
#include "TerrainResource.h"
#include "CrossImage/lodepng.h"
#include "clipp/clipp.h"
#include "bc7decomp.h"
#include <filesystem>

ICrossEngine* StartUpCrossEngine(std::string projectPath, std::string engineResource)
{
    auto crossEngine = CreateCrossEngine();
    cross::InitInfo initInfo = {projectPath.c_str(), engineResource.c_str(), nullptr};
    crossEngine->Init(initInfo);
    return crossEngine;
}

std::vector<UInt32> LoadNDAFile(const std::string& path, UInt32& width, UInt32& height)
{
    using namespace cross;

    auto relativeCurrentPath = path;
    if (PathHelper::IsAbsoluteFilePath(path))
    {
        relativeCurrentPath = PathHelper::GetRelativePath(PathHelper::GetCurrentDirectoryPath());
    }

    const auto fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
    if (fileSystem->HaveFile(relativeCurrentPath))
    {
        auto errorCode{ resource::ResourceLoadError::Succeeded };
        ResourcePtr resource(gResourceAssetMgr.LoadNDAFile(relativeCurrentPath.c_str(), errorCode));

        if (errorCode == resource::ResourceLoadError::Succeeded)
        {
            auto textureRes = TypeCast<resource::Texture2D>(resource);
            auto data = reinterpret_cast<UInt32*>(textureRes->GetTextureData()->GetImageData(0));
            auto textureInfo = textureRes->GetTextureData()->GetTextureInfo();
            width = textureInfo.Width;
            height = textureInfo.Height;

            std::vector<UInt32> textureData;      
            textureData.resize(width * height);

            switch (textureInfo.Format)
            {
                case TextureFormat::RGBA32:
                    std::memcpy(textureData.data(), data, textureData.size() * 4U);
                    break;
                case TextureFormat::BC7:
                {
                    const auto blocksX = width / 4U;
                    const auto blocksY = height / 4U;
                    for (UInt32 by = 0; by < blocksY; by++)
                    {
                        for (UInt32 bx = 0; bx < blocksX; bx++)
                        {
                            UInt32 unpackedPixels[16];
                            const auto block = &data[(by * blocksX + bx) * 4U];
                            bc7decomp::unpack_bc7(block, (bc7decomp::color_rgba*)unpackedPixels);
                            std::memcpy(&textureData[(by * 4U + 0U) * width + bx * 4U], &unpackedPixels[0], sizeof(UInt32) * 4U);
                            std::memcpy(&textureData[(by * 4U + 1U) * width + bx * 4U], &unpackedPixels[4], sizeof(UInt32) * 4U);
                            std::memcpy(&textureData[(by * 4U + 2U) * width + bx * 4U], &unpackedPixels[8], sizeof(UInt32) * 4U);
                            std::memcpy(&textureData[(by * 4U + 3U) * width + bx * 4U], &unpackedPixels[12], sizeof(UInt32) * 4U);
                        }
                    }

                    break;
                }
                default:
                    Assert(false);
            }      

            return textureData;
        }
    }

    return {};
}

void SavePNGFile(const std::string& path, UInt32 width, UInt32 height, UInt32 bitDepth, const UInt32* data)
{
    lodepng::encode(path, reinterpret_cast<const UInt8*>(data), width, height, LCT_RGBA, bitDepth);
}

void CreateDirectories(std::string path)
{
    using namespace cross;

    PathHelper::Normalize(path);
    if (PathHelper::IsDirectoryExist(path))
    {
        std::filesystem::remove_all(path);
    }
    std::filesystem::create_directories(path);
}

// example: Exporter -p C:\Repo\ProjectFFS -e C:\Repo\CrossEngine\Resource -i Contents\CHN_Terrain\CHN_Terrain.nda 23.0548095703125 23.744888305664062 112.8900146484375 113.7030029296875
int main(int argc, char* argv[])
{
    using namespace cross;
    using namespace clipp;

    std::string projectDir;
    std::string terrainNda;
    std::string engineResource;
    std::string outputDir{""};
    Double2 latRange;
    Double2 lonRange;

    auto cli =
        (
            required("-p") & value("project path", projectDir),
            required("-e") & value("engine resource path", engineResource),
            required("-i") & value("terrain nda", terrainNda),
            option("-o") & value("output nda path", outputDir),
            value("latitude0", latRange.x),
            value("latitude1", latRange.y),
            value("longitude0", lonRange.x),
            value("longitude1", lonRange.y)
            );

    if (parse(argc, argv, cli))
    {
        auto crossEngine = StartUpCrossEngine(projectDir, engineResource);

        auto terrainResourcePtr = TypeCast<resource::TerrainResource>(gAssetStreamingManager->LoadSynchronously(terrainNda));
        Assert(terrainResourcePtr);

        const TerrainInfo& terrainInfo = terrainResourcePtr->mTerrainInfo;
        const auto updateDir = PathHelper::GetParentPath(projectDir + "/" + terrainInfo.mRootDataPath) + "/Exported";
        if (outputDir.size() == 0)
            outputDir = updateDir;
        auto heightmapOutputPath = outputDir + "/" + PathHelper::GetParentPath(terrainInfo.mHeightmapPrefix);
        auto albedoTextureOutputPath = outputDir + "/" + PathHelper::GetParentPath(terrainInfo.mAlbedoTexturePrefix);

        CreateDirectories(heightmapOutputPath);
        CreateDirectories(albedoTextureOutputPath);

        const auto gridDimX = terrainInfo.mGridSizeX * terrainInfo.mBlockSize * terrainInfo.mTileSize;
        const auto gridDimY = terrainInfo.mGridSizeY * terrainInfo.mBlockSize * terrainInfo.mTileSize;
        const auto latX = static_cast<UInt32>(std::floor((latRange.x + 90.0) / 180.0 * gridDimY));
        const auto latY = static_cast<UInt32>(std::ceil((latRange.y + 90.0) / 180.0 * gridDimY));
        const auto lonX = static_cast<UInt32>(std::floor((lonRange.x + 180.0) / 360.0 * gridDimX));
        const auto lonY = static_cast<UInt32>(std::ceil((lonRange.y + 180.0) / 360.0 * gridDimX));

        for (UInt32 i = lonX / terrainInfo.mTileSize, ie = (lonY + terrainInfo.mTileSize - 1U) / terrainInfo.mTileSize; i <= ie; i++)
        {
            const auto blockX = i / terrainInfo.mBlockSize;
            const auto tileX = i % terrainInfo.mBlockSize;
            for (UInt32 j = latX / terrainInfo.mTileSize, je = (latY + terrainInfo.mTileSize - 1U) / terrainInfo.mTileSize; j <= je; j++)
            {
                const auto blockY = j / terrainInfo.mBlockSize;
                const auto tileY = j % terrainInfo.mBlockSize;

                const auto tileIndex = TileIndex(blockX, blockY, 0U, tileX, tileY);
                const auto heightmapNDAPath = TileIndex::GetNdaPath(terrainInfo.mRootDataPath, terrainInfo.mHeightmapPrefix, tileIndex);
                const auto albedoTextureNDAPath = TileIndex::GetNdaPath(terrainInfo.mRootDataPath, terrainInfo.mAlbedoTexturePrefix, tileIndex);           

                UInt32 hmWidth, hmHeight;
                UInt32 atWidth, atHeight;
                const auto heightmap = LoadNDAFile(heightmapNDAPath, hmWidth, hmHeight);
                const auto albedoTexture = LoadNDAFile(albedoTextureNDAPath, atWidth, atHeight);

                if (!heightmap.empty())
                {
                    const auto heightmapFinalPath = heightmapOutputPath + "/" + PathHelper::GetBaseFileName(heightmapNDAPath) + ".png";
                    SavePNGFile(heightmapFinalPath, hmWidth, hmHeight, 8U, heightmap.data());
                }

                if (!albedoTexture.empty())
                {
                    const auto albedoTextureFinalPath = albedoTextureOutputPath + "/" + PathHelper::GetBaseFileName(albedoTextureNDAPath) + ".png";
                    SavePNGFile(albedoTextureFinalPath, atWidth, atHeight, 8U, albedoTexture.data());
                }
            }
        }

        DestroyCrossEngine(crossEngine);
    }
    else
    {
        std::cout << "Usage:\n" << usage_lines(cli, argv[0]) << '\n';
        std::cout << "Example:\n"
            << argv[0] << " -p C:\\Repo\\ProjectFFS -e C:\\Repo\\CrossEngine\\Resource -i Contents\\CHN_Terrain\\CHN_Terrain.nda 23.0548095703125 23.744888305664062 112.8900146484375 113.7030029296875" << std::endl;
    }

    return 0;
}
