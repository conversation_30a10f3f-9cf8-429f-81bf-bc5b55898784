#pragma once
#include "EnginePrefix.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/GameSystemBase.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "PhysicsEngine/PhysicsQuery.h"
#include "PhysicsEngine/PhysicsControllerManager.h"
#include "Resource/MeshAssetDataResource.h"
#include "Runtime/GameWorld/PhysicsCustomData.h"

namespace cross
{
    using CollisionCallback = std::function<void(ecs::EntityID)>;
    struct PhysicsBoxCollision;

    enum class CEMeta(Puerts) CollisionEventType
    {
        OnCollisionEnter = 1 << 0,
        OnCollisionStay = 1 << 1,
        OnCollisionExit = 1 << 2,
        OnTriggerEnter = 1 << 3,
        OnTriggerExit = 1 << 4,
    };
    //////////////////////////////////////////////////////////////////////////
    //physics component
    //////////////////////////////////////////////////////////////////////////
    struct PhysicsComponentG final : ecs::IComponent
    {
        CEComponentInternal(SystemType = PhysicsSystemG) 

         CEFunction(Reflect)
        static ENGINE_API ecs::ComponentDesc* GetDesc();

    protected:
        CEProperty()
        bool mEnable = true;
        CEProperty()
        bool mIsDynamic = false;
        CEProperty()
        bool mEnableGravity = false;
        CEProperty()
        bool mIsTrigger = false; //If a entity is a trigger, no force will be applied to it. And it will not affect other actor. Only callbacks will effect
        CEProperty()
        bool mIsKinematic = false;   // If a entity is a kinematic, it must be a dynamic first. Kinematic moved by setting position instead of physics simulation. 
        CEProperty()
        bool mUseMeshCollision = false;
        CEProperty()
        bool mStartAsleep = false;
        CEProperty()
        float mLinearDamping = 0.01f;
        CEProperty()
        float mMass = 0;
        CEProperty()
        float mMaxDepenetrationVelocity = 0.0f;
        CEProperty()
        Float3 mMassSpaceInertiaTensorMultiplier { 1.0f, 1.0f, 1.0f };
        CEProperty()
        CollisionType mCollisionType = CollisionType::NoCollision;
        CEProperty()
        CollisionMask mCollisionMask = CollisionMask::None();

        std::vector<CollisionCallback> mCollisionEnterCBs;
        std::vector<CollisionCallback> mCollisionStayCBs;
        std::vector<CollisionCallback> mCollisionExitCBs;
        std::vector<CollisionCallback> mTriggerEnterCBs;
        std::vector<CollisionCallback> mTriggerExitCBs;
        CEProperty()
        MaterialType mMaterialType = MaterialType::None;
        CEProperty()
        bool mEnableCollisionEvent = false; //Set to true to fill mCollisionInfo when collision appears

        PhysicsActor* mRigidActor = nullptr;

        std::unique_ptr<PhysicsCollision> mCollision;

        //Extra collisions will be added to physics scene together with collisions from StaticMesh
        CEProperty()
        std::unique_ptr<PhysicsSimpleCollision> mExtraCollision;

        CE_Serialize_Deserialize;

        friend class PhysicsSystemG;

    public:
        CEFunction(AdditionalDeserialize)
        void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);
    };

    //////////////////////////////////////////////////////////////////////////
    // Events
    /////////////////////////////////////////////////////////////////////////
    struct RigidCreatedEventData
    {
        ecs::EntityID mEntity;
        PhysicsActor* mNewRigidActor = nullptr;
    };
    using RigidCreatedEvent = SystemEvent<RigidCreatedEventData>;

    //////////////////////////////////////////////////////////////////////////
    /// PhysicsSystem
    /////////////////////////////////////////////////////////////////////////
    class ENGINE_API PhysicsSystemG final : public GameSystemBase, public SystemEventManager<RigidCreatedEvent>
    {
        CESystemInternal(ComponentType = PhysicsComponentG)

    public:
        using GameSystemBase::SetEnable;

        using PhysicsComponentHandle = ecs::ComponentHandle<PhysicsComponentG>;
        DEFINE_COMPONENT_READER_WRITER(PhysicsComponentG, PhysicsComponentReader, PhysicsComponentWriter)

        CEFunction(Reflect)
        static PhysicsSystemG* CreateInstance();

        virtual void Release() override;

        virtual void OnEndFrame(FrameParam* frameParam) override;

        CEFunction(Reflect)
        virtual void OnBuildPreUpdateTasks(FrameParam* frameParam) override;

        CEFunction(Reflect)
        virtual void OnBuildPostUpdateTasks(FrameParam* frameParam) override;

        virtual void NotifyAddRenderSystemToRenderWorld() override;

        //void UpdateTerrainCollision(const PhysicsComponentWriter& phyComp, const std::vector<PhysicsGeometryTerrain>& collisionResList, const Float3& scale);
        PhysicsShape* AddTerrainCollision(const PhysicsComponentWriter& phyComp, const PhysicsGeometryTerrain& collision);
        void RemoveShape(const PhysicsComponentWriter& phyComp, PhysicsShape* shape);

        void OnShapeChanged(const PhysicsComponentWriter& phyComp);
        void OnTransformChanged(const PhysicsComponentWriter& phyComp, const TRS_A& transform);

        void AddPhysicsActorToPhysicsScene(PhysicsActor* actor);
        void RemovePhysicsActorFromPhysicsScene(PhysicsActor* actor);

        void SetDebugViewOption(const PhysicsSceneDebugViewOption& option);
        static PhysicsComponentG CreatePhysicsComp(CollisionType type, MaterialType materialType);
        PhysicsSimpleCollision* GetPhysicsSimpleCollision(const PhysicsComponentReader& component);

    public:
        void OnFirstUpdate(FrameParam* frameParam) override;

        CEFunction(Editor, Script)
        void InitPhysics(const PhysicsComponentWriter& component);


        //UInt32 RayCast(const TRSVector3Type& origin, const Float3& unitDir, float maxDistance, CollisionMask mask, HitFlag flag = HitFlag::Default, UInt32 maxHit = 1, const PhysicsActor* self = nullptr,
        //               PhysicsHitResult* outResults = nullptr);

        //UInt32 Sweep(PhysicsGeometryBase* geometry, const TRSVector3Type& position, const TRSQuaternionType& rotation, const TRSVector3Type& scale, const Float3& unitDir, float maxDistance, CollisionMask mask,
        //             HitFlag flag = HitFlag::Default,
        //             UInt32 maxHit = 1,
        //             const PhysicsActor* self = nullptr, PhysicsHitResult* outResults = nullptr);

        //UInt32 Overlap(PhysicsGeometryBase* geometry, const TRSVector3Type& position, const TRSQuaternionType& rotation, const TRSVector3Type& scale, CollisionMask mask, UInt32 maxHit = 1, const PhysicsActor* self = nullptr,
        //               PhysicsHitResult* outResults = nullptr);

        CEFunction(Editor, Script)
        void SetCollisionEventEnable(const PhysicsComponentWriter& component, bool enable, bool reportContactPoints = false);

        CEFunction(Editor, Script)
        bool GetCollisionEventEnable(const PhysicsComponentReader& component);

        void RegisterCollisionCallback(const PhysicsComponentWriter& component);

        bool HasCollisionCallback(const PhysicsComponentReader& component,CollisionEventType eventType) const;
        void RegisterCollisionEnterCallback(const PhysicsComponentWriter& component, CollisionCallback entityCollisionCallback);
        void CollisionEnterEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo);

        void RegisterCollisionStayCallback(const PhysicsComponentWriter& component, CollisionCallback stayCollisionCallback);
        void CollisionStayEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo);

        void RegisterCollisionExitCallback(const PhysicsComponentWriter& component, CollisionCallback entityCollisionCallback);
        void CollisionExitEvent(const PhysicsComponentReader& component, ecs::EntityID collidedEntityID, const CollisionInfo& collisionInfo);
        
        void RegisterTriggerEnterCallback(const PhysicsComponentWriter& component, CollisionCallback entityCollisionCallback);
        void TriggerEnterEvent(const PhysicsComponentReader& component, ecs::EntityID triggerEntityID, const CollisionInfo& collisionInfo);

        void RegisterTriggerExitCallback(const PhysicsComponentWriter& component, CollisionCallback entityCollisionCallback);
        void TriggerExitEvent(const PhysicsComponentReader& component, ecs::EntityID triggerEntityID, const CollisionInfo& collisionInfo);
        CEFunction(Editor, Script)
        void AddForce(const PhysicsComponentWriter& component, const Float3A& force);
        CEFunction(Script)
        void AddImpulse(const PhysicsComponentWriter& component, const Float3A& impulse);
        CEFunction(Script)
        void AddLinearVelocity(const PhysicsComponentWriter& component, const Float3A& velocity);
        CEFunction(Script)
        void AddLinearAcceleration(const PhysicsComponentWriter& component, const Float3A& acceleration);

        CEFunction(Editor, Script)
        void ClearForce(const PhysicsComponentWriter& component);
        CEFunction(Editor, Script)
        void ClearImpulse(const PhysicsComponentWriter& component);
        CEFunction(Editor, Script)
        void ClearLinearVelocity(const PhysicsComponentWriter& component);
        CEFunction(Editor, Script)
        void ClearLinearAcceleration(const PhysicsComponentWriter& component);

        CEFunction(Editor, Script)
        void AddTorque(const PhysicsComponentWriter& component, const Float3& torque);
        CEFunction(Editor, Script)
        void AddImpulseTorque(const PhysicsComponentWriter& component, const Float3& impulse);
        CEFunction(Editor, Script)
        void AddAngularVelocity(const PhysicsComponentWriter& component, const Float3& velocity);
        CEFunction(Editor, Script)
        void AddAngularAcceleration(const PhysicsComponentWriter& component, const Float3& acceleration);
        CEFunction(Editor, Script)
        void ClearTorque(const PhysicsComponentWriter& component);
        CEFunction(Editor, Script)
        void ClearImpulseTorque(const PhysicsComponentWriter& component);
        CEFunction(Editor, Script)
        void ClearAngularVelocity(const PhysicsComponentWriter& component);
        CEFunction(Editor, Script)
        void ClearAngularAcceleration(const PhysicsComponentWriter& component);

        CEFunction(Editor, Script)
        Float3 GetAngularVelocity(const PhysicsComponentReader& component) const;
        CEFunction(Editor, Script)
        Float3 GetLinearVelocity(const PhysicsComponentReader& component) const;

        // Geometry from StaticMesh
        const PhysicsCollision* GetPhysicsGeometry(const PhysicsComponentReader& comp) const;
        const std::vector<PhysicsGeometryBox>& GetBoxGeometry(const PhysicsComponentReader& comp) const { return GetPhysicsGeometry(comp)->mBoxGeometry; }
        const std::vector<PhysicsGeometrySphere>& GetSphereGeometry(const PhysicsComponentReader& comp) const { return GetPhysicsGeometry(comp)->mSphereGeometry; }
        const std::vector<PhysicsGeometryCapsule>& GetCapsuleGeometry(const PhysicsComponentReader& comp) const { return GetPhysicsGeometry(comp)->mCapsuleGeometry; }
        const std::vector<PhysicsGeometryPlane>& GetPlaneGeometry(const PhysicsComponentReader& comp) const { return GetPhysicsGeometry(comp)->mPlaneGeometry; }
        const std::vector<PhysicsGeometryConvex>& GetConvexGeometry(const PhysicsComponentReader& comp) const { return GetPhysicsGeometry(comp)->mConvexGeometry; }
        const std::vector<PhysicsGeometryMesh>& GetMeshGeometry(const PhysicsComponentReader& comp) const { return GetPhysicsGeometry(comp)->mMeshGeometry; }

        // Geometry extra added
        const PhysicsSimpleCollision* GetExtraShape(const PhysicsComponentReader& comp) const;
        PhysicsSimpleCollision* GetExtraShape(const PhysicsComponentReader& comp);
        CEFunction(Editor, Script)
        void AddExtraBoxShape(const PhysicsComponentWriter& comp, const PhysicsGeometryBox& boxGeo);
        CEFunction(Editor, Script)
        void AddExtraSphereShape(const PhysicsComponentWriter& comp, const PhysicsGeometrySphere& sphereGeo);
        CEFunction(Editor, Script)
        void AddExtraCapsuleShape(const PhysicsComponentWriter& comp, const PhysicsGeometryCapsule& capsuleGeo);
        void AddExtraConvexShape(const PhysicsComponentWriter& comp, const PhysicsGeometryConvex& convexGeo);
        CEFunction(Editor, Script)
        float UpdateMassAndInertia(const PhysicsComponentWriter& comp, float density);
        
        float SetMassAndUpdateInertia(const PhysicsComponentWriter& comp, float mass);

        CEFunction(Editor)
        std::vector<PhysicsGeometryBox> GetExtraBoxes(const PhysicsComponentReader& component) const;
        CEFunction(Editor)
        void SetExtraBoxes(const PhysicsComponentWriter& component, const std::vector<PhysicsGeometryBox>& boxes);

        CEFunction(Editor)
        std::vector<PhysicsGeometrySphere> GetExtraSpheres(const PhysicsComponentReader& component) const;
        CEFunction(Editor)
        void SetExtraSpheres(const PhysicsComponentWriter& component, const std::vector<PhysicsGeometrySphere>& spheres);

        CEFunction(Editor)
        std::vector<PhysicsGeometryCapsule> GetExtraCapsules(const PhysicsComponentReader& component) const;
        CEFunction(Editor)
        void SetExtraCapsules(const PhysicsComponentWriter& component, const std::vector<PhysicsGeometryCapsule>& capsules);
        CEFunction(Editor)
        PhysicsSimpleCollision GetExtraCollision(const PhysicsComponentReader& component) const;
        CEFunction(Editor)
        void SetExtraCollision(const PhysicsComponentWriter& component, const cross::PhysicsSimpleCollision& inValue);

    public:
        static SerializeNode SerializePhysicsComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
        static void DeserializePhysicsComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
        static void PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    public:
        CEFunction(Editor, Script)
        bool GetEnable(const PhysicsComponentReader& comp) { return comp->mEnable; }
        CEFunction(Editor, Script)
        void SetEnable(const PhysicsComponentWriter& comp, bool enable);

        CEFunction(Editor, Script)
        float GetMass(const PhysicsComponentReader& comp) const { return comp->mMass; }
        CEFunction(Editor, Script)
        void SetMass(const PhysicsComponentWriter& comp, float mass);

        CEFunction(Editor, Script)
        void SetStartAsleep(const PhysicsComponentWriter& comp, bool startupAsleep) { comp->mStartAsleep = startupAsleep; }
        CEFunction(Editor, Script)
        bool GetStartAsleep(const PhysicsComponentReader& comp) const { return comp->mStartAsleep; }
        
        CEFunction(Editor, Script)
        void SetIsDynamic(const PhysicsComponentWriter& comp, bool enable);
        CEFunction(Editor, Script)
        bool GetIsDynamic(const PhysicsComponentReader& comp) const { return comp->mIsDynamic; }
        CEFunction(Editor, Script)
        void SetEnableGravity(const PhysicsComponentWriter& comp, bool enable);
        CEFunction(Editor, Script)
        bool GetEnableGravity(const PhysicsComponentReader& comp);
        CEFunction(Editor, Script)
        bool GetIsTrigger(const PhysicsComponentReader& comp) const;
        CEFunction(Editor, Script)
        void SetIsTrigger(const PhysicsComponentWriter& comp, bool isTrigger);
        CEFunction(Editor, Script)
        bool GetIsKinematic(const PhysicsComponentReader& comp) const;
        CEFunction(Editor, Script)
        void SetIsKinematic(const PhysicsComponentWriter& comp, bool isKinematic);
        CEFunction(Editor, Script)
        bool GetUseMeshCollision(const PhysicsComponentReader& comp) const;
        CEFunction(Editor, Script)
        void SetUseMeshCollision(const PhysicsComponentWriter& comp, bool useMeshCollision);
        CEFunction(Editor, Script)
        float GetMaxDepenetrationVelocity(const PhysicsComponentReader& comp);
        CEFunction(Editor, Script)
        void SetMaxDepenetrationVelocity(const PhysicsComponentWriter& comp, float velocity);

        CEFunction(Editor, Script)
        const Float3& GetMassSpaceInertiaTensorMultiplier(const PhysicsComponentReader& comp) const
        {
            return comp->mMassSpaceInertiaTensorMultiplier;
        }
        CEFunction(Editor, Script)
        void SetMassSpaceInertiaTensorMultiplier(const PhysicsComponentWriter& comp, const Float3& multiplier); 

        CEFunction(Editor, Script)
        CollisionType GetCollisionType(const PhysicsComponentReader& comp) const;
        CEFunction(Editor, Script)
        void SetCollisionType(const PhysicsComponentWriter& comp, CollisionType type);

        CEFunction(Editor)
        CollisionMask GetCollisionMask(const PhysicsComponentReader& comp) const;
        CEFunction(Editor)
        void SetCollisionMask(const PhysicsComponentWriter& comp, CollisionMask mask);


        CEFunction(Editor, Script) MaterialType GetMaterialType(const PhysicsComponentReader& comp) const {
            return comp->mMaterialType;
        }

        CEFunction(Editor, Script) void SetMaterialType(const PhysicsComponentWriter& comp, MaterialType materialType) {
            comp->mMaterialType = materialType;
        }

        PhysicsScene* GetPhysicsScene() const { return mScene; }

        const PhysicsActor* GetPhysicsActor(const PhysicsComponentReader& comp) const
        {
            return comp->mRigidActor;
        }
        PhysicsActor* GetPhysicsActor(const PhysicsComponentWriter& comp) const
        {
            return comp->mRigidActor;
        }
        CEFunction(Editor, Script, Reflect)
        float GetLinearDamping(const PhysicsComponentReader& comp) const;
        CEFunction(Editor, Script, Reflect)
        void SetLinearDamping(const PhysicsComponentWriter& comp, float linerDamping) const;

        virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

        PhysicsControllerManager* GetControllerManager() { return mControllerManager; }

    public:
        CEFunction(Editor)
        PhysicsHitResultDebug RayCastForDebug(const TRSVector3Type& start, const TRSVector3Type& direction);
    protected:
        PhysicsSystemG();

        virtual ~PhysicsSystemG();

        virtual RenderSystemBase* GetRenderSystem() override { return nullptr; }

    protected:
        //Notice this function may call multiple times. Remember to judge condition
        void DrawDebugVisualization(FrameAllocator* curAllocator, Float3 tile);
        void StepPhysicsScene(float elapsedTime);
        void ApplyMass(const PhysicsComponentWriter& comp);
        void ResetActor(const PhysicsComponentWriter& comp);
    private:
        PhysicsScene* mScene = nullptr;
        PhysicsControllerManager* mControllerManager = nullptr;
        float mDeltaTimeRemain = 0;
        bool mSimulated = false;
        PhysicsActor* mSyncingActorFromPhyWorldToCEWorld = nullptr;
        PhysicsSceneDebugViewOption mDebugViewOpt;

        //debug visualization ib cache
        struct VisualPoint
        {
            Float3 pos;
            UInt32 color;
#ifdef CE_USE_DOUBLE_TRANSFORM
            Float3 tile;
#endif
        };
        struct VisualLine
        {
            VisualPoint p0, p1;
        };
        std::vector<UInt32> mVisualLinesIB;
        std::vector<VisualLine> mVisualLinesVB;

        struct VisualTrianVertex
        {
            Float3 pos;
            UInt32 color;
            Float3 normal;
        };
        std::vector<UInt32> mVisualTrianIB;
        std::vector<VisualTrianVertex> mVisualTrianVB;
    };
}
