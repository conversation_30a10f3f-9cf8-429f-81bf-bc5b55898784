#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"

namespace cross {

struct ComponentDesc;

enum class CEMeta(Reflect, Editor, Cli, WorkflowType, Puerts) TOD4TimeState
{
    Dawn = 0,
    Day,
    Dusk,
    Night
};

struct CEMeta(<PERSON><PERSON>, <PERSON>uer<PERSON>) TODLightConfig
{
    CEProperty(Editor, Script)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bKeyFrame = true))
    UInt32 Year{2020};
    CEProperty(Editor, Script)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bKeyFrame = true))
    UInt32 Month{1};
    CEProperty(Editor, Script)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bKeyFrame = true))
    UInt32 Day{1};
    CEProperty(Editor, Script)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bKeyFrame = true))
    UInt32 Hour{0};
    CEProperty(Editor, Script)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bKeyFrame = true))
    UInt32 Minute{0};
    CEProperty(Editor, Script)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", bKeyFrame = true))
    UInt32 Second{0};
    CE_Serialize_Deserialize;

    std::tm AsStdTM(SInt32 inTimeZone = 0) const
    {
        std::tm info;

        info.tm_year = Year - 1900;
        info.tm_mon = Month - 1;
        info.tm_mday = Day;
        info.tm_hour = Hour - inTimeZone;
        info.tm_min = Minute;
        info.tm_sec = Second;
        info.tm_isdst = -1;
        return info;
    }
    bool InSameMinute(const TODLightConfig& target) const
    {
        return (Year == target.Year && Month == target.Month && Day == target.Day && Hour == target.Hour && Minute == target.Minute);
    }
    bool InSameSecond(const TODLightConfig& target)
    {
        return (Year == target.Year && Month == target.Month && Day == target.Day && Hour == target.Hour && Minute == target.Minute && Second == target.Second);
    }

    CEProperty(Editor)
    bool Equal(const TODLightConfig& target, SInt32 inTimeZone = 0, SInt32 inTimeZoneTarget = 0) const;
};

struct CEMeta(Puerts) TODConfigChangedEventData
{
    ecs::EntityID mEntityID{ecs::EntityID::InvalidHandle()};
    CEProperty(ScriptReadWrite)
    TODLightConfig config;
    CEProperty(ScriptReadWrite)
    SInt32 timezone;
    CEProperty(ScriptReadWrite)
    bool RefreshForce{false};
};


using TODConfigChangedEvent = SystemEvent<TODConfigChangedEventData>;

struct TODLightComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = TODLightSystemG)
    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

protected:
    /*
     *   TOD configuration for light
     */
    CEProperty(Editor, Script)
    TODLightConfig config;

    CE_Serialize_Deserialize;

    friend class TODLightSystemG;
};




inline UInt32 FloatTimeUnitToUInt32(float inTimeUnit)
{
    return static_cast<UInt32>(std::max(0.0f, inTimeUnit));
}

class ENGINE_API TODLightSystemG final : public GameSystemBase, public SystemEventManager<TODConfigChangedEvent>
{
    CESystemInternal(ComponentType = TODLightSystemG)
public:
    using TODLightCompHandle = ecs::ComponentHandle<TODLightComponentG>;
    DEFINE_COMPONENT_READER_WRITER(TODLightComponentG, TODLightCompReader, TODLightCompWriter)

    CEFunction(Reflect)
    static TODLightSystemG* CreateInstance();

    virtual void Release() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override;

    virtual RenderSystemBase* GetRenderSystem() override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

public:
    CEFunction(Editor, Script, Reflect)
    TODLightConfig GetTODLightConfig(const TODLightCompReader& comp) const
    {
        return comp->config;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightConfig(const TODLightCompWriter& comp, const TODLightConfig& inConfig)
    {
        comp->config = inConfig;
    }

    CEFunction(Editor, Script, Reflect)
    UInt32 GetTODLightYear(const TODLightCompReader& comp) const
    {
        return comp->config.Year;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightYear(const TODLightCompWriter& comp, UInt32 inYear)
    {
        comp->config.Year = inYear;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightYearFloat(const TODLightCompWriter& comp, float inYear)
    {
        comp->config.Year = FloatTimeUnitToUInt32(inYear);
    }

    CEFunction(Editor, Script, Reflect)
    UInt32 GetTODLightMonth(const TODLightCompReader& comp) const
    {
        return comp->config.Month;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightMonth(const TODLightCompWriter& comp, UInt32 inMonth)
    {
        comp->config.Month = inMonth;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightMonthFloat(const TODLightCompWriter& comp, float inMonth)
    {
        comp->config.Month = FloatTimeUnitToUInt32(inMonth);
    }

    CEFunction(Editor, Script, Reflect)
    UInt32 GetTODLightDay(const TODLightCompReader& comp) const
    {
        return comp->config.Day;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightDay(const TODLightCompWriter& comp, UInt32 inDay)
    {
        comp->config.Day = inDay;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightDayFloat(const TODLightCompWriter& comp, float inDay)
    {
        comp->config.Day = FloatTimeUnitToUInt32(inDay);
    }

    CEFunction(Editor, Script, Reflect)
    UInt32 GetTODLightHour(const TODLightCompReader& comp) const
    {
        return comp->config.Hour;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightHour(const TODLightCompWriter& comp, UInt32 inHour)
    {
        comp->config.Hour = inHour;
    }
    CEFunction(Editor, Script, Reflect)
    void SetTODLightHourFloat(const TODLightCompWriter& comp, float inHour)
    {
        comp->config.Hour = FloatTimeUnitToUInt32(inHour);
    }

    CEFunction(Editor, Script, Reflect)
    UInt32 GetTODLightMinute(const TODLightCompReader& comp) const
    {
        return comp->config.Minute;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightMinute(const TODLightCompWriter& comp, UInt32 inMinute)
    {
        comp->config.Minute = inMinute;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightMinuteFloat(const TODLightCompWriter& comp, float inMinute)
    {
        comp->config.Minute = FloatTimeUnitToUInt32(inMinute);
    }

    CEFunction(Editor, Script, Reflect)
    UInt32 GetTODLightSecond(const TODLightCompReader& comp) const
    {
        return comp->config.Second;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightSecond(const TODLightCompWriter& comp, UInt32 inSecond)
    {
        comp->config.Second = inSecond;
    }

    CEFunction(Editor, Script, Reflect)
    void SetTODLightSecondFloat(const TODLightCompWriter& comp, float inSecond)
    {
        comp->config.Second = FloatTimeUnitToUInt32(inSecond);
    }

    void RefreshAllTODLightComponents(bool refreshForce = false);

    CEFunction(Editor, Script)
    void OnChangeConfig(const TODLightCompReader& comp, bool refreshForce = false);

    CEFunction(Editor, Script)
    void SyncAllTODLightComponents(const TODLightCompReader& comp);

    void SetTimeZoneFromLongitude(float Longitude);

    CEFunction(Editor, Script)
    void SetTODLightTimeZone(const TODLightCompWriter& comp, SInt32 inTimeZone)
    {
        mCurrentTimeZone = inTimeZone;
    }

    CEFunction(Editor, Script)
    void SetTODLightTimeZoneFloat(const TODLightCompWriter& comp, float inTimeZone)
    {
        mCurrentTimeZone = static_cast<SInt32>(inTimeZone);
    }

    CEFunction(Editor, Script)
    SInt32 GetTODLightTimeZone(const TODLightCompReader& comp) const
    {
        return mCurrentTimeZone;
    }

    /*
     *   return the Sun position at any time
     *   ret:
     *       x: Longitude
     *       y: Latitude
     */
    Double2 GetSunPosition(const TODLightConfig& inConfig);

    /*
     *   return the Moon position at any time
     *   ret:
     *       x: Longitude
     *       y: Latitude
     *       z: Distance To Earth
     */
    Double3 GetMoonPosition(const TODLightConfig& inConfig);

public:
    static SerializeNode SerializeTODLightComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializeTODLightComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializeTODLightComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);


    static cross::TODLightConfig GetConcreteTOD4Moment(const cross::TODLightConfig& inConfig, cross::TOD4TimeState, double latitude, double longitude);

    cross::TODLightConfig GetConcreteTOD4Moment(const cross::TODLightConfig& inConfig, cross::TOD4TimeState state);

    /*
     *  return Julian date
     */
    static double GetJulianDate(const TODLightConfig& inConfig, SInt32 inTimeZone);

    float ComputeSolarElevationAngle(const TODLightConfig& inConfig, Double3 GroundPosition);

    CEFunction(Editor)
    float ComputeSolarElevationAngleAtCameraPosition(const TODLightCompReader& comp);

    float ComputeSolarElevationAngleAtCameraPosition(const TODLightConfig& config);

    CEFunction(Editor)
    float ComputeSolarElevationAngleAtAnyPosition(const TODLightCompReader& comp, Double3 GroundPosition);

    /*
     * Set global time of day
     */
    void SetTimeOfDay(TOD4TimeState state, double latitude, double longitude);

    void SetTimeOfDay(TOD4TimeState state);
    bool GetLongtitudeAndLatitude(float& longitude, float& latitude);


    void SetTimeOfDay(const TODLightCompHandle& handle, TOD4TimeState state);
    void SetTimeOfDay(const TODLightCompHandle& handle, TOD4TimeState state, double latitude, double longitude);
    void SetTimeOfDay(const TODLightCompHandle& todComp, UInt32 Year, UInt32 Month, UInt32 Day, UInt32 Hour, UInt32 Minute, UInt32 Second);


protected:
    TODLightSystemG();
    virtual ~TODLightSystemG();
    virtual void OnFirstUpdate(FrameParam* frameParam) override;

private:
    /*
     *   Return the day offset to the base day
     *   Base day is at noon UTC on 1 January 2000, at Julian Day 2451545
     */
    double GetDaysOffset(const TODLightConfig& inConfig) const;

    /*
     *   return the mean Anomaly
     *   refer to https://www.aa.quae.nl/en/reken/hemelpositie.html
     */
    double GetMeanAnomaly(double inDayOffset) const;

    /*
     *   return the Ecliptic Longitude
     *   refer to https://www.aa.quae.nl/en/reken/hemelpositie.html
     */
    double GetEclipticLongitude(double inMeanAnomaly) const;

    /*
     *   return the Right Ascension and Declination
     *   refer to https://www.aa.quae.nl/en/reken/hemelpositie.html
     */
    Double2 GetRightAscensionAndDeclination(double inEclipticLongitude);

    /*
     *   return the side real time
     *   refer to https://www.aa.quae.nl/en/reken/hemelpositie.html
     */
    double GetSideRealTime(double inDayOffset);

    /*
     *   Declare internal terminal cmds
     */
    void DeclareTerminalCommands();

private:
    bool mIsRenderObjectOwner{false};

    // Default Time Zone: Beijing Time UTC+8
    SInt32 mCurrentTimeZone{8};
};

}   // namespace cross


//namespace cegf {
//    namespace evt {
//    template<class System, class Event>
//    class TODConfigChangedEventDispatcher : public GOEventDispatcherBase
//    {
//    public:
//        TODConfigChangedEventDispatcher(cross::GameWorld* inWorld)
//            : GOEventDispatcherBase(inWorld)
//        {}
//
//        void NotifyEvent(const cross::SystemEventBase& event, UInt32& flag) override { LOG_INFO("Found");
//        }
//
//    protected:
//        virtual void SubscribeInternal() override
//        {
//            mWorld->GetGameSystem<System>()->SubscribeEvent<Event>(this);
//        }
//
//        virtual void UnSubscribeInternal() override {
//            mWorld->GetGameSystem<System>()->Unsubscribe<Event>(this);
//        }
//
//        virtual CallbackSignature GetEventCallerID() const override { return {"OnTODConfigChanged"}; }
//    };
//}
//}