#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameContainer.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/LightProbeCache.h"
#include "RenderEngine/LightProbeSystemR.h"

namespace cross {
class LightProbeSystemR;

struct LightProbeComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = LightProbeSystemG);

    CEFunction(Reflect)
    ENGINE_API static ecs::ComponentDesc* GetDesc();

protected:
    bool mEnable{true};
    bool mDirty{true};
    bool mDebugGIOnly{false};

    friend class LightProbeSystemG;
};

class ENGINE_API LightProbeSystemG : public GameSystemBase
{
    CESystemInternal(ComponentType = LightProbeComponentG)
public:
    using LightProbeComponentHandle = ecs::ComponentHandle<LightProbeComponentG>;
    DEFINE_COMPONENT_READER_WRITER(LightProbeComponentG, LightProbeComponentReader, LightProbeComponentWriter);

    CEFunction(Reflect)
    static LightProbeSystemG* CreateInstance();

    virtual void Release() override;

    virtual RenderSystemBase* GetRenderSystem() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override
    {
        mIsRenderObjectOwner = false;
    };

protected:
    LightProbeSystemG();

    virtual ~LightProbeSystemG();

public:
#define LIGHT_PROBE_COMPONENT_PROPERTY_SYNC_R(PROP, TYPE)                                                                                                                                                                                      \
    CEFunction(Editor, Reflect)                                                                                                                                                                                                                \
    inline void SetLightProbe##PROP(const LightProbeComponentWriter& comp, const TYPE& val)                                                                                                                                                    \
    {                                                                                                                                                                                                                                          \
        comp->m##PROP = val;                                                                                                                                                                                                                   \
        DispatchRenderingCommandWithToken([renderSystem = mLightProbeSystemR, eID = comp.GetEntityID(), val]() { renderSystem->SetLightProbe##PROP(eID, val); });                                                                            \
    }                                                                                                                                                                                                                                          \
    CEFunction(Editor)                                                                                                                                                                                                                         \
    inline const TYPE GetLightProbe##PROP(const LightProbeComponentReader& comp) const                                                                                                                                                         \
    {                                                                                                                                                                                                                                          \
        return comp->m##PROP;                                                                                                                                                                                                                  \
    }
    LIGHT_PROBE_COMPONENT_PROPERTY_SYNC_R(Enable, bool);
    LIGHT_PROBE_COMPONENT_PROPERTY_SYNC_R(Dirty, bool);
    LIGHT_PROBE_COMPONENT_PROPERTY_SYNC_R(DebugGIOnly, bool);

public:
    bool SetLightProbeCache(LightProbeCache* lightProbeCache)
    {
        mLightProbeCache = lightProbeCache;
        DispatchRenderingCommandWithToken([renderSystem = mLightProbeSystemR, cache = lightProbeCache]() { renderSystem->SetLightProbeCache(cache); });
        return true;
    }

public:
    static SerializeNode SerializeModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);

    static void DeserializeModelComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);

    static void PostDeserializeModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

private:
    LightProbeSystemR* mLightProbeSystemR{nullptr};
    bool mIsRenderObjectOwner{true};

private:
    LightProbeCache* mLightProbeCache{nullptr};
};
}   // namespace cross
