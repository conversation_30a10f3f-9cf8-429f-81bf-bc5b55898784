#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/GameSystemBase.h"
#include "Resource/Texture/Texture.h"
#include "RenderEngine/CloudSystemR.h"
#include "RenderEngine/CloudSetting.h"

namespace cross {

enum CloudDataSource
{
    FromComponent = 0,
    FromWeatherSystem
};

struct ENGINE_API CloudComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = CloudSystemG)

    CEFunction(Reflect)
    static ecs::ComponentDesc* GetDesc();

public:
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Struct", ToolTips = "Cloud"))
    CloudSetting CloudSettings;

    MaterialPtr CloudMtl; // loading cloud material override to CloudMtl

private:
    CE_Serialize_Deserialize;
    friend class CloudSystemG;
};

class ENGINE_API CloudSystemG : public GameSystemBase
{
    CESystemInternal(ComponentType = CloudComponentG)
public:
    using CloudCompHandle = ecs::ComponentHandle<CloudComponentG>;
    DEFINE_COMPONENT_READER_WRITER(CloudComponentG, CloudCompReader, CloudCompWriter)

    CEFunction(Reflect)
    static CloudSystemG* CreateInstance();

    virtual void Release() override { delete this; }

    virtual RenderSystemBase* GetRenderSystem() override { return mCloudSystemR; }

    virtual void NotifyAddRenderSystemToRenderWorld() override{};

    static SerializeNode SerializeCloudComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializeCloudComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializeCloudComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    static void UpdateDeserializeCloudComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

protected:
    void UpdateSettingToRendering(const CloudCompWriter& comp);


    void CleanTextureMapCount(std::map<std::string, std::pair<int, TexturePtr>>& texMap);
    void CheckAndLoadTexture(std::map<std::string, std::pair<int, TexturePtr>>& texMap, const std::string& texPath, TexturePtr texture = TexturePtr(nullptr));
    void CleanTextureMap(std::map<std::string, std::pair<int, TexturePtr>>& texMap);

    CloudData PackCloudDataToRender(const CloudSetting&);

    ThunderstormToRender PackThunderstormDataToRender(const CloudThunderstormSetting&);
    //CloudLayerDataToRender PackCloudDataToRender(const CloudLayerSetting&);

    // reload new cloud sdf textures, and hold cloud texturePtr in mCloudTextures
    //void ReloadCloudTextures(const std::vector<CloudLayerSetting>&);
    // reload new storm sdf textures, and hold storm texturePtr in mThunderstormTextures
    //void ReloadStormTextures(const std::vector<CloudThunderstormInfo>&);

public:

    // Thunderstorm's initial direction is Z direct to north
    static auto GetDirectionFromPos(Float3 pos)
    {
        Float3 posUp = pos.Normalized();
        Float3 north = {0, -1, 0}; // Maybe due to the thunderstorm slicing or UV error, should fix later
        Float3 right = north.Cross(posUp);
        // forward(Z) should point to north, and that is right
        Float3 forward = posUp.Cross(right);
        return std::make_tuple(posUp, right, forward);
    }

    void SetPropertyCloudSettings(const CloudCompWriter& comp, const CloudSetting& setting);

    inline CloudSetting GetPropertyCloudSettings(const CloudCompReader& comp) const { return comp->CloudSettings; }

    void SetOverrideCloudLayer(const ecs::EntityID & entity, const CloudLayerPositionInfo& msg, int layer);

    void SetCloudLayerParam(const ecs::EntityID& entity, const CloudLayerPresetSetting& data, int layer);

    void SetVolcanicAsh(const bool isVolcanicAsh);

    void SetFogTop(const float fogTop);

    void SetLightning(bool enableLightning, const Float3 pos = Float3(0.f, 0.f, 0.f), const Float3 tilePos = Float3(0.f, 0.f, 0.f));

    // Override weather system storm data, and load textures if need
    void SetStormOverrideData(const ecs::EntityID& entity, const std::vector<CloudThunderstormInfo>& overrideCloudInfos);

    void SetCloudLightsEntity(FrameStdVector<ecs::EntityID>& entityIDs);

    // override cloud material
    void SetCloudMaterialOverride(const CloudCompWriter& comp, const MaterialPtr& mat);

protected:
    CloudSystemG();
    virtual ~CloudSystemG() {}

public:
    CEFunction(Editor)
    void GetCloudComponent(const CloudCompReader& inHandle, cross::CloudComponentG& outValue) const;

    CEFunction(Editor)
    void SetCloudComponent(const CloudCompWriter& inHandle, cross::CloudComponentG& inValue);

private:
    static void LoadToUpdateCloudMtl(const CloudCompWriter& component, const std::string& mtlPath);
    // Cloud Data
    
    //std::mutex                                               mChangedMutex;
    //std::set<ecs::EntityID>                                 mChangedEntity;

    //std::vector<ecs::EntityID> mOrderedCloudEntity;                          // this is ordered with mCloudLayerSettings



    //std::vector<CloudLayerSetting> mCloudLayerSettings;                 // data from component
    //std::vector<CloudLayerSetting> mCloudLayerSettingsOverride;         // data from weather system
    //std::map<std::string, std::pair<int, TexturePtr>> mCloudTextures;   // <texture name, <referece count, gpu texture pointer>>
    //bool bUseOverridedCloudData{false};                                 
    //bool bDirtySwitchCloudSource{true};                                 // dirty flag for switching weather storm data source (weather system or component)
    //bool bDirtyComponentCloud{true};                                    // dirty flag for storm data from component
    //bool bDirtyOverrideCloud{true};                                     // dirty flag for storm data from weather system
    float mFogTop{0.f};
    static inline float mBottomBleed{0.f};
    static inline float mTopBleed{0.f};


    //bool bEnableInteractiveTranslation{false};


    // Thunder Storm Data
    //std::vector<CloudThunderstormInfo> mThunderstormInfos;                    // data from component
    //std::vector<CloudThunderstormInfo> mThunderstormInfosOverride;            // data from weather system
    //std::map<std::string, std::pair<int, TexturePtr>> mThunderstormTextures;  // <texture name, <reference count, gpu texture pointer>>
    //bool bUseOverridedStormData{true};
    //bool bDirtySwitchStormSource{true};                                       // dirty flag for switching weather storm data source (weather system or component) 
    //bool bDirtyComponentStorm{true};                                          // dirty flag for storm data from component
    //bool bDirtyOverrideStorm{true};                                           // dirty flag for storm data from weather system

    //CloudProjectType mCloudProjectType{CloudProjectType::WGS84};

    CloudSystemR* mCloudSystemR{nullptr};
};
}   // namespace cross