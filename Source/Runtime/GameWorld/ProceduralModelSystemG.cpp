#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "Threading/RenderingThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/ProceduralModelSystemR.h"
#include "RenderEngine/RenderMaterial.h"
#include "Resource/Resource.h"
#include "Resource/AssetStreaming.h"
#include "Resource/ResourceManager.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/ProceduralModelSystemG.h"

namespace cross {
ecs::ComponentDesc* ProceduralModelComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::ProceduralModelComponentG>({false, true, true}, ecs::ComponentDesc::DefaultComponentSerializeFunc);
}

ProceduralModelSystemG* cross::ProceduralModelSystemG::CreateInstance()
{
    return new ProceduralModelSystemG();
}

ProceduralModelSystemG::ProceduralModelSystemG() {
    mRenderMeshSystem = ProceduralModelSystemR::CreateInstance();
}

ProceduralModelSystemG::~ProceduralModelSystemG() {
    if (mIsRenderObjectOwner)
    {
        mRenderMeshSystem->Release();
    }
    mRenderMeshSystem = nullptr;
}

void ProceduralModelSystemG::Release()
{
    delete this;
}

void ProceduralModelSystemG::OnBeginFrame(FrameParam* frameParam) {
    mModelChangeList.BeginFrame(frameParam, FRAME_STAGE_GAME);
}

void ProceduralModelSystemG::OnEndFrame(FrameParam* frameParam) {
}

RenderSystemBase* ProceduralModelSystemG::GetRenderSystem()
{
    return mRenderMeshSystem;
}

void ProceduralModelSystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
}

ProceduralModelComponentG ProceduralModelSystemG::CreateProceduralModelComponent()
{
    ProceduralModelComponentG cmp;
    return cmp;
}

const ProceduralModelComponentG::ProceduralModel& ProceduralModelSystemG::GetModel(const ProceduralModelComponentReader& modelH, UInt32 modelIndex) const
{
    Assert(modelIndex < modelH->mModels.size());
    return modelH->mModels[modelIndex];
}

BoundingBox ProceduralModelSystemG::GetCurrentBoundingBox(const ProceduralModelComponentReader& modelH) const
{
    BoundingBox box{BoundingBox::Flags::MergeIdentity};
    for (auto& model : modelH->mModels)
    {
        box += model.mBox;
    }
    return box;
}

void ProceduralModelSystemG::SetModelVisibility(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, bool visible) {

    if (modelIndex >= modelH->mModels.size())
        return;
    auto& model = modelH->mModels[modelIndex];
    model.mVisible = visible;
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex, visible]() {
        renderSystem->SetModelVisibility(entity, modelIndex, visible);
    });
}

void ProceduralModelSystemG::SetModelMaterial(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MaterialInterfacePtr matrial) {

    if (modelIndex >= modelH->mModels.size())
        return;
    auto& model = modelH->mModels[modelIndex];
    model.mMaterial = matrial;
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex, material = TYPE_CAST(MaterialR*, model.mMaterial->GetRenderMaterial())]() {
        renderSystem->SetModelMaterial(entity, modelIndex, material);
    });
}

void ProceduralModelSystemG::CreateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialPtr matrial)
{
    CreateModel(modelH, modelIndex, mesh, box, TypeCast<resource::MaterialInterface>(matrial));
}

void ProceduralModelSystemG::UpdateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialPtr matrial)
{
    UpdateModel(modelH, modelIndex, mesh, box, TypeCast<resource::MaterialInterface>(matrial));
}

void ProceduralModelSystemG::CreateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialInterfacePtr matrial)
{
    if (modelIndex >= modelH->mModels.size())
    {
        modelH->mModels.resize(modelIndex + 1);
    }
    auto& model = modelH->mModels[modelIndex];
    model.mMeshData = mesh;
    model.mBox = box;
    model.mMaterial = matrial;
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex, mesh = model.mMeshData, material = TYPE_CAST(MaterialR*, model.mMaterial->GetRenderMaterial())]() {
        renderSystem->CreateModel(entity, modelIndex, mesh, material);
    });
}

void ProceduralModelSystemG::UpdateModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex, MeshAssetDataPtr mesh, const BoundingBox& box, MaterialInterfacePtr matrial)
{
    auto& model = modelH->mModels[modelIndex];
    model.mMeshData = mesh;
    model.mBox = box;
    model.mMaterial = matrial;
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());

    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex, mesh = model.mMeshData, material = TYPE_CAST(MaterialR*, model.mMaterial->GetRenderMaterial())]() {
        renderSystem->UpdateModel(entity, modelIndex, mesh, material);
    });
}

void ProceduralModelSystemG::DeleteModel(const ProceduralModelComponentWriter& modelH, UInt32 modelIndex) {
    if (modelH->mModels.size() <= modelIndex)
        return;
    modelH->mModels.erase(modelH->mModels.begin() + modelIndex);
    mModelChangeList.EmplaceChangeData(modelH.GetEntityID());
    DispatchRenderingCommandWithToken([renderSystem = mRenderMeshSystem, entity = modelH.GetEntityID(), modelIndex]() {
        renderSystem->DeleteModel(entity, modelIndex);
    });
}
}   // namespace cross
