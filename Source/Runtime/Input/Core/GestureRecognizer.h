#pragma once
#include "CrossBase/Math/CrossMath.h"

#include "InputKeys.h"

namespace cross
{

class PlatformUser;

// CEGestureRecognizer: A class to detect different kinds of gesture and record it in PlatformUser
// NOTE: Different from UE, we store 3D coordinate of a point as Float3, while UE use FVector, which is the alias of Double3
class CEGestureRecognizer
{
public:
    // Constructors and deconstructor
    CEGestureRecognizer() = default;
    virtual ~CEGestureRecognizer() = default;

    // Attempt to detect touch gestures
    void DetectGestures(const Float3 (&inTouches)[input::CEKeys::NUM_TOUCH_KEYS], PlatformUser* inUser, float deltaTime);

    // Save the distance between the anchor points
    void SetAnchorDistanceSquared(const Float2& firstPoint, const Float2& secondPoint);

protected:
    // Internal processing of gestures
    void HandleGestureEvent(PlatformUser* inUser, const input::CEKey& inGesture, bool isStarted, bool isEnded);

    // A mapping of a gesture to it's current value (how far swiped, pinch amount, etc)
    mutable CEHashMap<input::CEKey, float> mCurrentGestureValues;

    // Special gesture tracking values
    Float2 mAnchorPoints[input::CEKeys::NUM_TOUCH_KEYS];

    // Pinch related members
    bool mIsReadyForPinch{false};
    // Special pinch tracking values
    Float2 mLastPinchStartPoint{0.0f, 0.0f};
    Float2 mLastPinchEndPoint{0.0f, 0.0f};
    float mAnchorDistanceSquared{0.0f};

    // Rotate related members
    float mStartAngle{0.0f};

    // Flick related members
    bool mIsReadyForFlick{false};
    Float2 mFlickCurrent{0.0f, 0.0f};
    float mFlickTime{0.0f};

    SInt32 mPreviousTouchCount{0};
};

}  // namespace cross
