#include "Streaming/StreamingCalculationTask.h"
#include "Streaming/StreamingManager.h"
#include "Streaming/StreamingUtils.h"
#include "NativeGraphicsInterface/Statistics.h"

namespace cross
{

bool StreamingCalculationTask::AllowPerRenderAssetLODBiasChanges() const
{
    const std::vector<StreamingViewInfo>& ViewInfos = StreamingData.GetViewInfos();
    for (int ViewIndex = 0; ViewIndex < ViewInfos.size(); ++ViewIndex)
    {
        const StreamingViewInfo& ViewInfo = ViewInfos[ViewIndex];
        if (ViewInfo.BoostFactor > StreamingManager->Settings.PerTextureBiasViewBoostThreshold)
        {
            return false;
        }
    }
    return true;
}

void StreamingCalculationTask::TryDropMaxResolutions(std::vector<int>& PrioritizedRenderAssets, SInt64& MemoryBudgeted, const SInt64 InMemoryBudget)
{
    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;
    const StreamingSettings& Settings = StreamingManager->Settings;

    // When using LOD bias per texture/mesh, we first reduce the maximum resolutions (if used) in order to fit.
    for (int NumDroppedLODs = 0; NumDroppedLODs < Settings.GlobalLODBias && MemoryBudgeted > InMemoryBudget && !IsAborted(); ++NumDroppedLODs)
    {
        const SInt64 PreviousMemoryBudgeted = MemoryBudgeted;

        // Heuristic: Only consider dropping max resolution for a mesh if it has reasonable impact on memory reduction.
        // Currently, reasonable impact is defined as MemDeltaOfDroppingOneLOD >= MinTextureMemDelta in this pass.
        SInt64 MinTextureMemDelta = std::numeric_limits<SInt64>::max();

        for (int PriorityIndex = static_cast<int>(PrioritizedRenderAssets.size() - 1); PriorityIndex >= 0 && MemoryBudgeted > InMemoryBudget && !IsAborted(); --PriorityIndex)
        {
            int AssetIndex = PrioritizedRenderAssets[PriorityIndex];
            if (AssetIndex == INDEX_NONE)
                continue;

            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
            const int MinAllowedLODs = std::max(StreamingRenderAsset.MinAllowedLODs, StreamingRenderAsset.NumForcedLODs);
            if (StreamingRenderAsset.BudgetedLODs <= MinAllowedLODs)
            {
                // Don't try this one again.
                PrioritizedRenderAssets[PriorityIndex] = INDEX_NONE;
                continue;
            }

            // If the texture/mesh requires a high resolution LOD, consider dropping it.
            // When considering dropping the first LOD, only textures/meshes using the first LOD will drop their resolution,
            // But when considering dropping the second LOD, textures/meshes using their first and second LODs will loose it.
            if (StreamingRenderAsset.MaxAllowedLODs + StreamingRenderAsset.BudgetLODBias - NumDroppedLODs <= StreamingRenderAsset.BudgetedLODs)
            {
                const int NumLODsToDrop = NumDroppedLODs + 1 - StreamingRenderAsset.BudgetLODBias;

                if (Settings.bPrioritizeMeshLODRetention)
                {
                    const bool bIsTexture = StreamingRenderAsset.IsTexture();
                    const SInt64 MemDeltaFromMaxResDrop = StreamingRenderAsset.GetDropMaxResMemDelta(NumLODsToDrop);

                    if (!MemDeltaFromMaxResDrop || (!bIsTexture && MemDeltaFromMaxResDrop < MinTextureMemDelta && MinTextureMemDelta != std::numeric_limits<SInt64>::max()))
                    {
                        continue;
                    }

                    MinTextureMemDelta = bIsTexture ? std::min(MinTextureMemDelta, MemDeltaFromMaxResDrop) : MinTextureMemDelta;
                }

                MemoryBudgeted -= StreamingRenderAsset.DropMaxResolution(NumLODsToDrop);
            }
        }

        // Break when memory does not change anymore
        if (PreviousMemoryBudgeted == MemoryBudgeted)
        {
            break;
        }
    }
}

void StreamingCalculationTask::TryDropLODs(std::vector<int>& PrioritizedRenderAssets, SInt64& MemoryBudgeted, const SInt64 InMemoryBudget)
{
    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;
    const StreamingSettings& Settings = StreamingManager->Settings;

    while (MemoryBudgeted > InMemoryBudget && !IsAborted())
    {
        const SInt64 PreviousMemoryBudgeted = MemoryBudgeted;

        // Heuristic: only start considering dropping mesh LODs if it has reasonable impact on memory reduction.
        SInt64 MinTextureMemDelta = std::numeric_limits<SInt64>::max();

        // Drop from the lowest priority first (starting with last elements)
        for (int PriorityIndex = static_cast<int>(PrioritizedRenderAssets.size() - 1); PriorityIndex >= 0 && MemoryBudgeted > InMemoryBudget && !IsAborted(); --PriorityIndex)
        {
            int AssetIndex = PrioritizedRenderAssets[PriorityIndex];
            if (AssetIndex == INDEX_NONE)
                continue;

            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
            const int MinAllowedLODs = std::max(StreamingRenderAsset.MinAllowedLODs, StreamingRenderAsset.NumForcedLODs);
            if (StreamingRenderAsset.BudgetedLODs <= MinAllowedLODs)
            {
                // Don't try this one again.
                PrioritizedRenderAssets[PriorityIndex] = INDEX_NONE;
                continue;
            }

            const bool bIsTexture = StreamingRenderAsset.IsTexture();
            const bool bIsMesh = !bIsTexture;
            if (Settings.bPrioritizeMeshLODRetention && bIsMesh)
            {
                const SInt64 PredictedMemDelta = StreamingRenderAsset.GetDropOneLODMemDelta();
                if (PredictedMemDelta < MinTextureMemDelta && MinTextureMemDelta != std::numeric_limits<SInt64>::max())
                {
                    continue;
                }
            }

            // If this texture/mesh has already missing LODs for its normal quality, don't drop more than required..
            // TODO:
            //if (StreamingRenderAsset.NumMissingLODs > 0)
            //{
            //    --StreamingRenderAsset.NumMissingLODs;
            //    continue;
            //}

            const SInt64 MemDelta = StreamingRenderAsset.DropOneLOD();
            MemoryBudgeted -= MemDelta;
            LOG_DEBUG("Streaming: {} Drop LOD {} -> {}, Mem {:.3f} / {:.3f}",
                StreamingRenderAsset.RenderAsset->GetFullName(), StreamingRenderAsset.BudgetedLODs + 1, StreamingRenderAsset.BudgetedLODs, MemoryBudgeted / 1024.0 / 1024.0, InMemoryBudget / 1024.0 / 1024.0);
            if (Settings.bPrioritizeMeshLODRetention && bIsTexture && MemDelta > 0)
            {
                MinTextureMemDelta = std::min(MinTextureMemDelta, MemDelta);
            }
        }

        // Break when memory does not change anymore
        if (PreviousMemoryBudgeted == MemoryBudgeted)
        {
            break;
        }
    }
}

void StreamingCalculationTask::TryKeepLODs(std::vector<int>& PrioritizedRenderAssets, SInt64& MemoryBudgeted, const SInt64 InMemoryBudget)
{
    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;
    bool bBudgetIsChanging = true;

    while (MemoryBudgeted < InMemoryBudget && bBudgetIsChanging && !IsAborted())
    {
        bBudgetIsChanging = false;

        // Keep from highest priority first.
        for (int PriorityIndex = 0; PriorityIndex < PrioritizedRenderAssets.size() && MemoryBudgeted < InMemoryBudget && !IsAborted(); ++PriorityIndex)
        {
            int AssetIndex = PrioritizedRenderAssets[PriorityIndex];
            if (AssetIndex == INDEX_NONE)
                continue;

            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
            SInt64 TakenMemory = StreamingRenderAsset.KeepOneLOD();

            if (TakenMemory > 0)
            {
                if (MemoryBudgeted + TakenMemory <= InMemoryBudget)
                {
                    MemoryBudgeted += TakenMemory;
                    bBudgetIsChanging = true;
                }
                else   // Cancel keeping this LOD
                {
                    StreamingRenderAsset.DropOneLOD();
                    PrioritizedRenderAssets[PriorityIndex] = INDEX_NONE;   // Don't try this one again.
                }
            }
            else   // No other LODs to keep.
            {
                PrioritizedRenderAssets[PriorityIndex] = INDEX_NONE;   // Don't try this one again.
            }
        }
    }
}

void StreamingCalculationTask::UpdateBudgetedLODs()
{
    Assert(threading::TaskSystem::IsInAsyncThread());

    //*************************************
    // Update Budget
    //*************************************

    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;
    const StreamingSettings& Settings = StreamingManager->Settings;

    std::vector<int> PrioritizedRenderAssets;
    //std::vector<int> PrioritizedMeshes;

    int NumAssets = 0;
    int NumMeshes = 0;

    SInt64 MemoryBudgeted = 0;
    SInt64 MeshMemoryBudgeted = 0;
    SInt64 MemoryUsedByNonTextures = 0;
    SInt64 MemoryUsed = 0;

    for (StreamingResource& StreamingRenderAsset : StreamingRenderAssets)
    {
        if (IsAborted())
            break;

        const SInt64 AssetMemBudgeted = StreamingRenderAsset.UpdateRetentionPriority(Settings.bPrioritizeMeshLODRetention);
        const int AssetMemUsed = StreamingRenderAsset.GetSize(StreamingRenderAsset.ResidentLODs);
        MemoryUsed += AssetMemUsed;

        if (StreamingRenderAsset.IsTexture())
        {
            MemoryBudgeted += AssetMemBudgeted;
            ++NumAssets;
        }
        else
        {
            MeshMemoryBudgeted += AssetMemBudgeted;
            MemoryUsedByNonTextures += AssetMemUsed;
            ++NumMeshes;
        }
    }

    //*************************************
    // Update Effective Budget
    //*************************************

    bool bResetLODBias = false;

    if (PerfectWantedLODsBudgetResetThresold - MemoryBudgeted - MeshMemoryBudgeted > TempMemoryBudget + MemoryMargin)
    {
        // Reset the budget tradeoffs if the required pool size shrinked significantly.
        PerfectWantedLODsBudgetResetThresold = MemoryBudgeted;
        bResetLODBias = true;
    }
    else if (MemoryBudgeted + MeshMemoryBudgeted > PerfectWantedLODsBudgetResetThresold)
    {
        // Keep increasing the threshold since higher requirements incurs bigger tradeoffs.
        PerfectWantedLODsBudgetResetThresold = MemoryBudgeted + MeshMemoryBudgeted;
    }

    const SInt64 NonStreamingRenderAssetMemory = AllocatedMemory - MemoryUsed + MemoryUsedByNonTextures;
    SInt64 AvailableMemoryForStreaming = PoolSize - NonStreamingRenderAssetMemory - MemoryMargin;

    // If the platform defines a max VRAM usage, check if the pool size must be reduced,
    // but also check if it would be safe to some of the NonStreamingRenderAssetMemory from the pool size computation.
    // The later helps significantly in low budget settings, where NonStreamingRenderAssetMemory would take too much of the pool.
    const auto& deviceStats = GetNGIDevice().GetGpuResourceStatistics()->mDeviceResources;
    int PoolSizeVRAMPercentage = StreamingManager->Settings.PoolSizeVRAMPercentage;
    if (PoolSizeVRAMPercentage > 0 && TotalGraphicsMemory > 0)
    {
        const SInt64 UsableVRAM = std::max<SInt64>(TotalGraphicsMemory * PoolSizeVRAMPercentage / 100, TotalGraphicsMemory - Settings.VRAMPercentageClamp * 1024ll * 1024ll);
        const SInt64 UsedVRAM = static_cast<SInt64>(deviceStats.used * 1024ll * 1024ll);
        const SInt64 AvailableVRAMForStreaming = std::min<SInt64>(UsableVRAM - UsedVRAM - MemoryMargin, PoolSize);
        if (Settings.bLimitPoolSizeToVRAM || AvailableVRAMForStreaming > AvailableMemoryForStreaming)
        {
            AvailableMemoryForStreaming = AvailableVRAMForStreaming;
        }
    }

    // TODO: Temp code
    SInt64 poolSize = static_cast<SInt64>(StreamingManager->Settings.VRAMPoolSize) * 1024 * 1024;
    if (poolSize <= 0)
        return;
    AvailableMemoryForStreaming = poolSize;

    // Update EffectiveStreamingPoolSize, trying to stabilize it independently of temp memory, allocator overhead and non-streaming resources normal variation.
    // It's hard to know how much temp memory and allocator overhead is actually in StreamingMemorySize as it is platform specific.
    // We handle it by not using all memory available. If temp memory and memory margin values are effectively bigger than the actual used values, the pool will stabilize.
    if (AvailableMemoryForStreaming < MemoryBudget)
    {
        // Reduce size immediately to avoid taking more memory.
        MemoryBudget = std::max<SInt64>(AvailableMemoryForStreaming, 0);
    }
    else if (AvailableMemoryForStreaming - MemoryBudget > TempMemoryBudget + MemoryMargin)
    {
        // Increase size considering that the variation does not come from temp memory or allocator overhead (or other recurring cause).
        // It's unclear how much temp memory is actually in there, but the value will decrease if temp memory increases.
        MemoryBudget = AvailableMemoryForStreaming;
        bResetLODBias = true;
    }

    const SInt64 PrevMeshMemoryBudget = MeshMemoryBudget;
    MeshMemoryBudget = Settings.MeshPoolSize * 1024 * 1024;
    //const bool bUseSeparatePoolForMeshes = MeshMemoryBudget >= 0;

    if (true/*!bUseSeparatePoolForMeshes*/)
    {
        NumAssets += NumMeshes;
        NumMeshes = 0;
        MemoryBudgeted += MeshMemoryBudgeted;
        MeshMemoryBudgeted = 0;
    }
    else if (PrevMeshMemoryBudget < MeshMemoryBudget)
    {
        bResetLODBias = true;
    }

    //*******************************************
    // Reset per LOD bias if not required anymore.
    //*******************************************

    // When using LOD per texture/mesh, the BudgetLODBias gets reset when the required resolution does not get affected anymore by the BudgetLODBias.
    // This allows texture/mesh to reset their bias when the viewpoint gets far enough, or the primitive is not visible anymore.
    if (Settings.bUsePerTextureBias)
    {
        for (StreamingResource& StreamingRenderAsset : StreamingRenderAssets)
        {
            if (IsAborted())
                break;

            if (StreamingRenderAsset.BudgetLODBias > 0 && (bResetLODBias || (Settings.bFullyLoadMeshes && StreamingRenderAsset.IsMesh()) ||
                                                           std::max<int>(StreamingRenderAsset.VisibleWantedLODs, StreamingRenderAsset.HiddenWantedLODs + StreamingRenderAsset.NumMissingLODs) < StreamingRenderAsset.MaxAllowedLODs))
            {
                StreamingRenderAsset.BudgetLODBias = 0;
            }
        }
    }

    //*************************************
    // Drop LODs
    //*************************************

    // If the budget is taking too much, drop some LODs.
    if ((MemoryBudgeted > MemoryBudget/* || (bUseSeparatePoolForMeshes && MeshMemoryBudgeted > MeshMemoryBudget)*/) && !IsAborted())
    {
        //*************************************
        // Get texture/mesh list in order of reduction
        //*************************************

        PrioritizedRenderAssets.clear();
        PrioritizedRenderAssets.reserve(NumAssets);
        //PrioritizedMeshes.clear();
        //PrioritizedMeshes.reserve(NumMeshes);

        for (int AssetIndex = 0; AssetIndex < StreamingRenderAssets.size() && !IsAborted(); ++AssetIndex)
        {
            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
            // Only consider non deleted textures/meshes (can change any time).
            if (!StreamingRenderAsset.RenderAsset)
                continue;

            // Ignore textures/meshes for which we are not allowed to reduce resolution.
            if (!StreamingRenderAsset.IsMaxResolutionAffectedByGlobalBias() || (Settings.bFullyLoadMeshes && StreamingRenderAsset.IsMesh()))
            {
                continue;
            }

            // Ignore texture/mesh that can't drop any LODs
            const int MinAllowedLODs = std::max(StreamingRenderAsset.MinAllowedLODs, StreamingRenderAsset.NumForcedLODs);
            if (StreamingRenderAsset.BudgetedLODs > MinAllowedLODs)
            {
                //if (bUseSeparatePoolForMeshes && StreamingRenderAsset.IsMesh())
                //{
                //    PrioritizedMeshes.emplace_back(AssetIndex);
                //}
                //else
                {
                    PrioritizedRenderAssets.emplace_back(AssetIndex);
                }
            }
        }

        // Sort texture/mesh, having those that should be dropped first.
        std::sort(PrioritizedRenderAssets.begin(), PrioritizedRenderAssets.end(), CompareStreamingAssetByRetentionPriority(StreamingRenderAssets));
        //std::sort(PrioritizedMeshes.begin(), PrioritizedMeshes.end(), CompareStreamingAssetByRetentionPriority(StreamingRenderAssets));

        if (Settings.bUsePerTextureBias && AllowPerRenderAssetLODBiasChanges())
        {
            //*************************************
            // Drop Max Resolution until in budget.
            //*************************************

            TryDropMaxResolutions(PrioritizedRenderAssets, MemoryBudgeted, MemoryBudget);
            //if (bUseSeparatePoolForMeshes)
            //{
            //    TryDropMaxResolutions(PrioritizedMeshes, MeshMemoryBudgeted, MeshMemoryBudget);
            //}
        }

        //*************************************
        // Drop WantedLOD until in budget.
        //*************************************

        TryDropLODs(PrioritizedRenderAssets, MemoryBudgeted, MemoryBudget);
        //if (bUseSeparatePoolForMeshes)
        //{
        //    TryDropLODs(PrioritizedMeshes, MeshMemoryBudgeted, MeshMemoryBudget);
        //}
    }

    //*************************************
    // Keep LODs
    //*************************************

    // If there is some room left, try to keep as much as long as it won't bust budget.
    // This will run even after sacrificing to fit in budget since some small unwanted LODs could still be kept.
#if 0
    if ((MemoryBudgeted < MemoryBudget/* || (bUseSeparatePoolForMeshes && MeshMemoryBudgeted < MeshMemoryBudget)*/) && !IsAborted())
    {
        PrioritizedRenderAssets.clear();
        PrioritizedRenderAssets.reserve(NumAssets);
        //PrioritizedMeshes.clear();
        //PrioritizedMeshes.reserve(NumMeshes);

        const SInt64 MaxAllowedDelta = MemoryBudget - MemoryBudgeted;
        //const SInt64 MaxAllowedMeshDelta = MeshMemoryBudget - MeshMemoryBudgeted;

        for (int AssetIndex = 0; AssetIndex < StreamingRenderAssets.size() && !IsAborted(); ++AssetIndex)
        {
            StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
            // Only consider non deleted textures/meshes (can change any time).
            if (!StreamingRenderAsset.RenderAsset)
                continue;

            // Only consider textures/meshes that won't bust budget nor generate new I/O requests
            if (StreamingRenderAsset.BudgetedLODs < StreamingRenderAsset.ResidentLODs)
            {
                const int Delta = StreamingRenderAsset.GetSize(StreamingRenderAsset.BudgetedLODs + 1) - StreamingRenderAsset.GetSize(StreamingRenderAsset.BudgetedLODs);
                //const bool bUseMeshVariant = bUseSeparatePoolForMeshes && StreamingRenderAsset.IsMesh();
                const SInt64 MaxDelta = MaxAllowedDelta/*bUseMeshVariant ? MaxAllowedMeshDelta : MaxAllowedDelta*/;
                std::vector<int>& AssetIndcies = PrioritizedRenderAssets /*bUseMeshVariant ? PrioritizedMeshes : PrioritizedRenderAssets*/;

                if (Delta <= MaxDelta)
                {
                    AssetIndcies.emplace_back(AssetIndex);
                }
            }
        }

        // Sort texture/mesh, having those that should be dropped first.
        std::sort(PrioritizedRenderAssets.begin(), PrioritizedRenderAssets.end(), CompareStreamingAssetByRetentionPriority(StreamingRenderAssets));
        //std::sort(PrioritizedMeshes.begin(), PrioritizedMeshes.end(), CompareStreamingAssetByRetentionPriority(StreamingRenderAssets));

        TryKeepLODs(PrioritizedRenderAssets, MemoryBudgeted, MemoryBudget);
        //if (bUseSeparatePoolForMeshes)
        //{
        //    TryKeepLODs(PrioritizedMeshes, MeshMemoryBudgeted, MeshMemoryBudget);
        //}
    }
#endif

    //*************************************
    // Handle drop LODs debug option
    //*************************************
#if !CROSSENGINE_RELEASE
    if (Settings.DropLODs > 0)
    {
        for (StreamingResource& StreamingRenderAsset : StreamingRenderAssets)
        {
            if (IsAborted())
                break;

            if (Settings.DropLODs == 1)
            {
                StreamingRenderAsset.BudgetedLODs = std::min<SInt8>(StreamingRenderAsset.BudgetedLODs, static_cast<SInt8>(StreamingRenderAsset.GetPerfectWantedLODs()));
            }
            else
            {
                StreamingRenderAsset.BudgetedLODs = std::min<SInt8>(StreamingRenderAsset.BudgetedLODs, StreamingRenderAsset.VisibleWantedLODs);
            }
        }
    }
#endif
}

void StreamingCalculationTask::UpdateLoadAndCancelationRequests()
{
    Assert(threading::TaskSystem::IsInAsyncThread());

    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;
    const StreamingSettings& Settings = StreamingManager->Settings;

    LoadRequests.clear();
    CancelationRequests.clear();

    SInt64 StreamOutMemoryBudget = TempMemoryBudget;
    SInt64 StreamInMemoryBudget = TempMemoryBudget;

    std::vector<int> PrioritizedRenderAssets;
    PrioritizedRenderAssets.reserve(StreamingRenderAssets.size());
    for (int AssetIndex = 0; AssetIndex < StreamingRenderAssets.size() && !IsAborted(); ++AssetIndex)
    {
        StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
        const bool bWasMissingTooManyLODs = StreamingRenderAsset.IsMissingTooManyLODs();

        // If we need to change the number of resident LODs.
        if (StreamingRenderAsset.UpdateLoadOrderPriority(Settings))
        {
            // If there is no pending update, kick one if the budget allows it.
            if (StreamingRenderAsset.RequestedLODs == StreamingRenderAsset.ResidentLODs)
            {
                PrioritizedRenderAssets.emplace_back(AssetIndex);
            }
            // Otherwise, if the update is trying to load too many, too few, or unload required LODs, (try to) cancel it.
            else if (
                // If marked as missing too many LODs, a high priority request was created so be more aggressive on canceling it.
                StreamingRenderAsset.RequestedLODs > std::max<int>(StreamingRenderAsset.ResidentLODs, StreamingRenderAsset.WantedLODs + (bWasMissingTooManyLODs ? 0 : 1)) ||
                // If too many missing LODs, cancel existing request if it is not loading enough so a high priority one can be created.
                // Otherwise, only cancel if it is trying to unload resident LODs.
                StreamingRenderAsset.RequestedLODs < (StreamingRenderAsset.IsMissingTooManyLODs() ? StreamingRenderAsset.WantedLODs : std::min<int>(StreamingRenderAsset.ResidentLODs, StreamingRenderAsset.WantedLODs)))
            {
                CancelationRequests.emplace_back(AssetIndex);
            }
        }

        // Reduce the stream in/out budgets from pending updates.
        const SInt64 TempMemoryUsed = StreamingRenderAsset.GetSize(StreamingRenderAsset.RequestedLODs);
        if (StreamingRenderAsset.RequestedLODs < StreamingRenderAsset.ResidentLODs)
        {
            // Here we assume that the stream out complete before new stream in requests start, so it doesn't affect stream in budget.
            StreamOutMemoryBudget -= TempMemoryUsed;
        }
        else if (StreamingRenderAsset.RequestedLODs > StreamingRenderAsset.ResidentLODs)
        {
            // If there is a pending stream in, remove the temporary memory from both stream in and stream out budget.
            // When the request was made, there were possibly stream out issued at the same time to free memory in case of budget limit.
            StreamInMemoryBudget -= TempMemoryUsed;
            StreamOutMemoryBudget -= TempMemoryUsed;
        }
    }

    std::sort(PrioritizedRenderAssets.begin(), PrioritizedRenderAssets.end(), CompareStreamingAssetByLoadOrderPriority(StreamingRenderAssets));

    // If possible, free as much memory with stream out operations, as will be required with new stream in requests.
    // This prevents becoming overbudget momentarily, when we are already at the budget limit.
    std::vector<int> StreamOutRequests;
    std::vector<int> StreamInRequests;

    // Now fill in the LoadRequest and CancelationRequests
    for (int PriorityIndex = 0; PriorityIndex < PrioritizedRenderAssets.size() && !IsAborted(); ++PriorityIndex)
    {
        int AssetIndex = PrioritizedRenderAssets[PriorityIndex];
        StreamingResource& StreamingRenderAsset = StreamingRenderAssets[AssetIndex];
        // This assumes that the assets are streamed through a copy of all LODs.
        // Even though this is only the case for non partially resident textures,
        // we still use this metric to limit the number of pending streaming requests.
        const SInt64 TempMemoryRequired = StreamingRenderAsset.GetSize(StreamingRenderAsset.WantedLODs);

        // Check whether the budget allows the update, with the exception of always allowing a single update of any size (otherwise completion might never happen).
        if (StreamingRenderAsset.WantedLODs < StreamingRenderAsset.ResidentLODs && (/*TempMemoryRequired <= StreamOutMemoryBudget */true || !StreamOutRequests.size()))
        {
            StreamOutRequests.emplace_back(AssetIndex);
            StreamOutMemoryBudget -= TempMemoryRequired;
        }
        else if (StreamingRenderAsset.WantedLODs > StreamingRenderAsset.ResidentLODs && (/*TempMemoryRequired <= StreamInMemoryBudget */true|| !StreamInRequests.size()))
        {
            StreamInRequests.emplace_back(AssetIndex);
            StreamInMemoryBudget -= TempMemoryRequired;
        }
    }

    // Process stream out requests first since they execute faster, freeing the memory for the stream in requests.
    LoadRequests.insert(LoadRequests.end(), StreamOutRequests.begin(), StreamOutRequests.end());
    LoadRequests.insert(LoadRequests.end(), StreamInRequests.begin(), StreamInRequests.end());
}

void StreamingCalculationTask::UpdatePendingStreamingStatus()
{
    Assert(threading::TaskSystem::IsInAsyncThread());

    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;
    const bool bIsStreamingPaused = StreamingManager->bPauseRenderAssetStreaming;

    PendingUpdateDirties.clear();

    for (int AssetIndex = 0; AssetIndex < StreamingRenderAssets.size() && !IsAborted(); ++AssetIndex)
    {
        const StreamingResource& StreamingTexture = StreamingRenderAssets[AssetIndex];
        if (StreamingTexture.bHasUpdatePending != StreamingTexture.HasUpdatePending(bIsStreamingPaused, HasAnyView()))
        {
            // The texture/mesh state are only updated on the gamethread, where we can make sure the UStreamableRenderAsset is in sync.
            PendingUpdateDirties.emplace_back(AssetIndex);
        }
    }
}

void StreamingCalculationTask::DoWork()
{
    SCOPED_CPU_TIMING(GroupStreaming, "StreamingCalculationTask::DoWork");
    //DECLARE_SCOPE_CYCLE_COUNTER(TEXT("StreamingCalculationTask::DoWork"), STAT_StreamingCalculationTask_DoWork, STATGROUP_StreamingDetails);

    // While the async task is runnning, the StreamingRenderAssets are guarantied not to be reallocated.
    // 2 things can happen : a texture can be removed, in which case the texture will be set to null
    // or some members can be updated following calls to UpdateDynamicData().
    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;
    const StreamingSettings& Settings = StreamingManager->Settings;

    StreamingData.ComputeViewInfoExtras(Settings);

    // Update the distance and size for each bounds.
    StreamingData.UpdateBoundSizes(Settings, StreamingRenderAssets);

    {
        UInt64 StartTime = time::CurrentMs();

        for (StreamingResource& StreamingRenderAsset : StreamingRenderAssets)
        {
            if (IsAborted())
                break;

            StreamingData.UpdatePerfectWantedLODs(StreamingRenderAsset, Settings);
            StreamingRenderAsset.DynamicBoostFactor = 1.f;   // Reset after every computation.
        }

        double ElapsedMSTime = (time::CurrentMs() - StartTime) / 1000.0;

        // use of a custom stat to have the elapsed time as a global stat and not a stat split accross multiple threads
        //CSV_CUSTOM_STAT(TextureStreaming, RenderAssetStreamingUpdate, float(ElapsedMSTime), ECsvCustomStatOp::Set);
    }

    // According to budget, make relevant sacrifices and keep possible unwanted LODs
    UpdateBudgetedLODs();

    // Update load requests.
    UpdateLoadAndCancelationRequests();

    // Update bHasStreamingUpdatePending
    UpdatePendingStreamingStatus();

    StreamingData.OnTaskDone();

    if (StreamingManager->Settings.EnableStats)
    {
        UpdateStats();
    }
    if (StreamingManager->Settings.EnableCSVStats)
    {
        UpdateCSVOnlyStats();
    }
}

void StreamingCalculationTask::UpdateStats()
{
    Assert(threading::TaskSystem::IsInAsyncThread());

    if (!StreamingManager->Settings.EnableStats)
    {
        return;
    }

    StreamingStats& Stats = StreamingManager->GatheredStats;
    StreamingSettings& Settings = StreamingManager->Settings;
    std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;

    Stats.RenderAssetPool = PoolSize;
    // Stats.StreamingPool = MemoryBudget;
    Stats.UsedStreamingPool = 0;

    Stats.SafetyPool = MemoryMargin;
    Stats.TemporaryPool = TempMemoryBudget;
    Stats.StreamingPool = MemoryBudget;
    Stats.NonStreamingLODs = AllocatedMemory;

    Stats.RequiredPool = 0;
    Stats.VisibleLODs = 0;
    Stats.HiddenLODs = 0;

    Stats.ForcedLODs = 0;
    Stats.UnkownRefLODs = 0;

    Stats.CachedLODs = 0;

    Stats.WantedLODs = 0;
    Stats.PendingRequests = 0;

    Stats.OverBudget = 0;

    Stats.NumStreamedMeshes = 0;
    Stats.AvgNumStreamedLODs = 0.f;
    Stats.AvgNumResidentLODs = 0.f;
    Stats.AvgNumEvictedLODs = 0.f;
    Stats.StreamedMeshMem = 0;
    Stats.ResidentMeshMem = 0;
    Stats.EvictedMeshMem = 0;
    int TotalNumStreamedLODs = 0;
    int TotalNumResidentLODs = 0;
    int TotalNumEvictedLODs = 0;

    for (StreamingResource& StreamingRenderAsset : StreamingRenderAssets)
    {
        if (IsAborted())
            break;
        if (!StreamingRenderAsset.RenderAsset)
            continue;

        const SInt64 ResidentSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.ResidentLODs);
        const SInt64 RequiredSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.GetPerfectWantedLODs());
        const SInt64 BudgetedSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.BudgetedLODs);
        const SInt64 MaxSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.MaxAllowedLODs);
        const SInt64 VisibleWantedSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.VisibleWantedLODs);

        // How much the streamer would use if there was no limit.
        Stats.RequiredPool += RequiredSize;

        // How much the streamer actually use.
        Stats.UsedStreamingPool += std::min<SInt64>(RequiredSize, BudgetedSize);

        // Remove from the non streaming budget what is actually taken by streaming.
        Stats.NonStreamingLODs -= ResidentSize * StreamingRenderAsset.IsTexture();

        // All persistent LOD bias bigger than the expected is considered overbudget.
        const int OverBudgetBias = std::max<int>(0, StreamingRenderAsset.BudgetLODBias - Settings.GlobalLODBias);
        Stats.OverBudget += StreamingRenderAsset.GetSize(StreamingRenderAsset.MaxAllowedLODs + OverBudgetBias) - MaxSize;

        const SInt64 UsedSize = std::min<SInt64>(std::min<SInt64>(RequiredSize, BudgetedSize), ResidentSize);

        Stats.WantedLODs += UsedSize;
        Stats.CachedLODs += std::max<SInt64>(ResidentSize - UsedSize, 0);

        if (StreamingManager->mResourceRequestLODMapInFlight.contains(StreamingRenderAsset.RenderAsset))
        {
            Stats.VisibleLODs += UsedSize;
        }
        else
        {
            Stats.HiddenLODs += StreamingRenderAsset.GetSize(StreamingRenderAsset.HiddenWantedLODs);
        }

        //if (StreamingUtils::IsEditor() && StreamingRenderAsset.bForceFullyLoadHeuristic)
        //{
        //    Stats.ForcedLODs += UsedSize;
        //}
        //else if (StreamingRenderAsset.bUseUnkownRefHeuristic)
        //{
        //    Stats.UnkownRefLODs += UsedSize;
        //}
        //else
        //{
        //    if (VisibleWantedSize >= UsedSize)
        //    {
        //        Stats.VisibleLODs += UsedSize;
        //    }
        //    else   // VisibleWantedSize < UsedSize
        //    {
        //        Stats.VisibleLODs += VisibleWantedSize;

        //        // Forced LODs are not the same as hidden LODs as they are loaded because the user wants them absolutly
        //        if (StreamingRenderAsset.bForceFullyLoadHeuristic/* || (StreamingRenderAsset.IsTexture() && StreamingRenderAsset.LODGroup == TEXTUREGROUP_HierarchicalLOD && Settings.HLODStrategy > 0)*/)
        //        {
        //            Stats.ForcedLODs += UsedSize - VisibleWantedSize;
        //        }
        //        else
        //        {
        //            Stats.HiddenLODs += UsedSize - VisibleWantedSize;
        //        }
        //    }
        //}

        if (StreamingRenderAsset.RequestedLODs > StreamingRenderAsset.ResidentLODs)
        {
            Stats.PendingRequests += StreamingRenderAsset.GetSize(StreamingRenderAsset.RequestedLODs) - ResidentSize;
        }

        if (StreamingRenderAsset.IsMesh())
        {
            const int NumStreamedLODs = StreamingRenderAsset.MaxAllowedLODs - StreamingRenderAsset.MinAllowedLODs;
            const int NumResidentLODs = StreamingRenderAsset.ResidentLODs;
            const int NumEvictedLODs = StreamingRenderAsset.MaxAllowedLODs - NumResidentLODs;
            const SInt64 TotalSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.MaxAllowedLODs);
            const SInt64 StreamedSize = TotalSize - StreamingRenderAsset.GetSize(StreamingRenderAsset.MinAllowedLODs);
            const SInt64 EvictedSize = TotalSize - ResidentSize;

            ++Stats.NumStreamedMeshes;
            TotalNumStreamedLODs += NumStreamedLODs;
            TotalNumResidentLODs += NumResidentLODs;
            TotalNumEvictedLODs += NumEvictedLODs;
            Stats.StreamedMeshMem += StreamedSize;
            Stats.ResidentMeshMem += ResidentSize;
            Stats.EvictedMeshMem += EvictedSize;
        }
    }

    if (Stats.NumStreamedMeshes > 0)
    {
        Stats.AvgNumStreamedLODs = (float)TotalNumStreamedLODs / Stats.NumStreamedMeshes;
        Stats.AvgNumResidentLODs = (float)TotalNumResidentLODs / Stats.NumStreamedMeshes;
        Stats.AvgNumEvictedLODs = (float)TotalNumEvictedLODs / Stats.NumStreamedMeshes;
    }

    Stats.OverBudget += std::max<SInt64>(Stats.RequiredPool - Stats.StreamingPool, 0);
    Stats.Timestamp = time::CurrentMs() / 1000.0;
}

void StreamingCalculationTask::UpdateCSVOnlyStats()
{
    Assert(threading::TaskSystem::IsInAsyncThread());

    const std::vector<StreamingResource>& StreamingRenderAssets = StreamingManager->AsyncUnsafeStreamingRenderAssets;

    StreamingStats& Stats = StreamingManager->GatheredStats;

    Stats.RenderAssetPool = PoolSize;

    Stats.SafetyPool = MemoryMargin;
    Stats.TemporaryPool = TempMemoryBudget;
    Stats.StreamingPool = MemoryBudget;
    Stats.NonStreamingLODs = AllocatedMemory;

    Stats.RequiredPool = 0;
    Stats.CachedLODs = 0;
    Stats.WantedLODs = 0;
    Stats.PendingRequests = 0;

    Stats.NumStreamedMeshes = 0;
    Stats.AvgNumStreamedLODs = 0.f;
    Stats.AvgNumResidentLODs = 0.f;
    Stats.AvgNumEvictedLODs = 0.f;
    Stats.StreamedMeshMem = 0;
    Stats.ResidentMeshMem = 0;
    Stats.EvictedMeshMem = 0;
    int TotalNumStreamedLODs = 0;
    int TotalNumResidentLODs = 0;
    int TotalNumEvictedLODs = 0;

    for (const StreamingResource& StreamingRenderAsset : StreamingRenderAssets)
    {
        if (IsAborted())
            break;
        if (!StreamingRenderAsset.RenderAsset)
            continue;

        const SInt64 ResidentSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.ResidentLODs);
        const SInt64 RequiredSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.GetPerfectWantedLODs());
        const SInt64 BudgetedSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.BudgetedLODs);

        // How much the streamer would use if there was no limit.
        Stats.RequiredPool += RequiredSize;

        // Remove from the non streaming budget what is actually taken by streaming.
        Stats.NonStreamingLODs -= ResidentSize * StreamingRenderAsset.IsTexture();

        const SInt64 UsedSize = std::min<SInt64>(std::min<SInt64>(RequiredSize, BudgetedSize), ResidentSize);

        Stats.WantedLODs += UsedSize;
        Stats.CachedLODs += std::max<SInt64>(ResidentSize - UsedSize, 0);

        if (StreamingRenderAsset.RequestedLODs > StreamingRenderAsset.ResidentLODs)
        {
            Stats.PendingRequests += StreamingRenderAsset.GetSize(StreamingRenderAsset.RequestedLODs) - ResidentSize;
        }

        if (StreamingRenderAsset.IsMesh())
        {
            const int NumStreamedLODs = StreamingRenderAsset.MaxAllowedLODs - StreamingRenderAsset.MinAllowedLODs;
            const int NumResidentLODs = StreamingRenderAsset.ResidentLODs;
            const int NumEvictedLODs = StreamingRenderAsset.MaxAllowedLODs - NumResidentLODs;
            const SInt64 TotalSize = StreamingRenderAsset.GetSize(StreamingRenderAsset.MaxAllowedLODs);
            const SInt64 StreamedSize = TotalSize - StreamingRenderAsset.GetSize(StreamingRenderAsset.MinAllowedLODs);
            const SInt64 EvictedSize = TotalSize - ResidentSize;

            ++Stats.NumStreamedMeshes;
            TotalNumStreamedLODs += NumStreamedLODs;
            TotalNumResidentLODs += NumResidentLODs;
            TotalNumEvictedLODs += NumEvictedLODs;
            Stats.StreamedMeshMem += StreamedSize;
            Stats.ResidentMeshMem += ResidentSize;
            Stats.EvictedMeshMem += EvictedSize;
        }
    }

    if (Stats.NumStreamedMeshes > 0)
    {
        Stats.AvgNumStreamedLODs = (float)TotalNumStreamedLODs / Stats.NumStreamedMeshes;
        Stats.AvgNumResidentLODs = (float)TotalNumResidentLODs / Stats.NumStreamedMeshes;
        Stats.AvgNumEvictedLODs = (float)TotalNumEvictedLODs / Stats.NumStreamedMeshes;
    }
}

} // namespace cross
