#include "EnginePrefix.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_ParameterLink.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraphDefine.h"

namespace cross::anim 
{

REGISTER_LINK_TYPE_NAME("ParamImplLink<bool>", AnimGraph_BoolLinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<int>", AnimGraph_IntLinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<float>", AnimGraph_FloatLinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<string>", AnimGraph_StringLinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<vector2>", AnimGraph_Vector2LinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<vector3>", AnimGraph_Vector3LinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<vector4>", AnimGraph_Vector4LinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<transform>", AnimGraph_TransformLinkImpl)
REGISTER_LINK_TYPE_NAME("ParamImplLink<void*>", AnimGraph_CustomizedLinkImpl)

}   // namespace cross::anim