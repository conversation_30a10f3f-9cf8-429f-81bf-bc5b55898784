#include "EnginePrefix.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_TransformBoneNode.h"
#include "Runtime/Animation/Animator/Animator.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_NodeUtils.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraphDefine.h"

#include "Runtime/GameWorld/TransformSystemG.h"

namespace cross::anim {
REGISTER_NODE_TYPE(AnimGraph_TransformBoneNode)

void ConvertRootSpaceToBoneSpace(NodeTransform& inOutBone, const NodeTransform& rootTransfrom, const NodeTransform& originBoneTM, const NodeTransform& parentBoneTM, BoneControlSpace Space)
{
    switch (Space)
    {
    case BoneControlSpace::WorldSpace:
        inOutBone = inOutBone * rootTransfrom;
        break;
    case BoneControlSpace::RootSpace:
        break;
    case BoneControlSpace::BoneSpace:
    {
        inOutBone = NodeTransform::GetRelativeTransform(originBoneTM, inOutBone);
    }
    break;
    case BoneControlSpace::ParentBoneSpace:
    {
        inOutBone = NodeTransform::GetRelativeTransform(parentBoneTM, inOutBone);
    }
    break;
    }
}

void ConvertBoneSpaceToRootSpace(NodeTransform& inOutBone, const NodeTransform& rootTransfrom, const NodeTransform& originBoneTM, const NodeTransform& parentBoneTM, BoneControlSpace Space)
{
    switch (Space)
    {
    case BoneControlSpace::WorldSpace:
        inOutBone = NodeTransform::GetRelativeTransform(rootTransfrom, inOutBone);
        break;
    case BoneControlSpace::RootSpace:
        break;
    case BoneControlSpace::BoneSpace:
    {
        inOutBone = inOutBone * originBoneTM;
    }
    break;
    case BoneControlSpace::ParentBoneSpace:
    {
        inOutBone = inOutBone * parentBoneTM;
    }
    break;
    }
}

AnimGraph_TransformBoneNode::AnimGraph_TransformBoneNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, const CENameMap<CEName, size_t>& inLinkNameToIndexMap,
                                                         const CENameMap<CEName, size_t>& inParamNameToIndexMap)
    : AnimGraph_BaseNode(inAnimGraph, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap)
    , mTranslationLink(Float3::Zero())
    , mRotationLink(Float3::Zero())
    , mScaleLink(Float3::One())
{
    ExtractMember(mBoneName, inNodeJson, "BoneName");

    ExtractMember(mTranslationSpace, inNodeJson["Translation"], "Space");
    ExtractMember(mTranslationMode, inNodeJson["Translation"], "Mode");
    ExtractMember(mRotationSpace, inNodeJson["Rotation"], "Space");
    ExtractMember(mRotationMode, inNodeJson["Rotation"], "Mode");
    ExtractMember(mScaleSpace, inNodeJson["Scale"], "Space");
    ExtractMember(mScaleMode, inNodeJson["Scale"], "Mode");
}

void AnimGraph_TransformBoneNode::Initialize(const AnimInitContext& inContext)
{
    mAnimator = inContext.AnimatorPtr;

    AnimGraph_BaseNode::Initialize(inContext);
}

void AnimGraph_TransformBoneNode::Update(const AnimUpdateContext& inContext)
{
    AnimGraph_BaseNode::Update(inContext);
    if (PoseLink())
    {
        PoseLink()->Update(inContext);
    }
}

void AnimGraph_TransformBoneNode::EvaluateRootSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext)
{
    LOG_INFO("EvaluateRootSpace");
}

void AnimGraph_TransformBoneNode::EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext)
{
    if (PoseLink())
    {
        PoseLink()->Evaluate(outPose, inContext);
    }

    PoseBoneHandle poseBoneHandle = PoseBoneHandle::InvalidHandle();
    PoseBoneHandle poseParentBoneHandle = PoseBoneHandle::InvalidHandle();
    if (mBoneHandle)
        poseBoneHandle = outPose.GetPoseIndexFromFilteredBoneIndex(mBoneHandle);
    if (mParentBoneHandle)
        poseParentBoneHandle = outPose.GetPoseIndexFromFilteredBoneIndex(mParentBoneHandle);

    if (!poseBoneHandle)
        return;


    auto transSys = mAnimator->GetWorld()->GetGameSystem<TransformSystemG>();
    auto transCmp = mAnimator->GetWorld()->GetComponent<WorldTransformComponentG>(mAnimator->GetOwner());
    auto worldTransform = transSys->GetWorldTransformT(transCmp.Read());


    Float3 pos = mTranslationLink;
    Float3 rot = mRotationLink;
    Float3 scale = mScaleLink;
    Quaternion quat = Quaternion::EulerToQuaternion(rot.ToRadian());

    // make sure this bone's transform is updated
    outPose.ConvertAllBoneToLocalSpace();

    NodeTransform originBoneTM = outPose.GetRootSpaceTransform(poseBoneHandle);
    NodeTransform parentBoneTM = poseParentBoneHandle ? outPose.GetRootSpaceTransform(poseParentBoneHandle) : originBoneTM;

    NodeTransform newBoneTM = originBoneTM;

    NodeTransform rootTransform = {Float3(worldTransform.mScale), Quaternion(worldTransform.mRotation), Float3(worldTransform.mTranslation)};

    if (mTranslationMode != BoneModificationMode::Ignore)
    {
        ConvertRootSpaceToBoneSpace(newBoneTM, rootTransform, originBoneTM, parentBoneTM, mTranslationSpace);
        if (mTranslationMode == BoneModificationMode::Additive)
        {
            newBoneTM.SetTranslation(newBoneTM.GetTranslation() + pos);
        }
        else
        {
            newBoneTM.SetTranslation(pos);
        }
        ConvertBoneSpaceToRootSpace(newBoneTM, rootTransform, originBoneTM, parentBoneTM, mTranslationSpace);
    }
    if (mRotationMode != BoneModificationMode::Ignore)
    {
        ConvertRootSpaceToBoneSpace(newBoneTM, rootTransform, originBoneTM, parentBoneTM, mRotationSpace);

        if (mRotationMode == BoneModificationMode::Additive)
        {
            newBoneTM.SetRotation(newBoneTM.GetRotation() * quat);
        }
        else
        {
            newBoneTM.SetRotation(quat);
        }
        ConvertBoneSpaceToRootSpace(newBoneTM, rootTransform, originBoneTM, parentBoneTM, mRotationSpace);
    }
    if (mScaleMode != BoneModificationMode::Ignore)
    {
        ConvertRootSpaceToBoneSpace(newBoneTM, rootTransform, originBoneTM, parentBoneTM, mScaleSpace);

        if (mScaleMode == BoneModificationMode::Additive)
        {
            newBoneTM.SetScale(newBoneTM.GetScale() * scale);
        }
        else
        {
            newBoneTM.SetScale(scale);
        }
        ConvertBoneSpaceToRootSpace(newBoneTM, rootTransform, originBoneTM, parentBoneTM, mScaleSpace);
    }

    outPose.SetRootSpaceTransform(poseBoneHandle, newBoneTM);

    // update childern transform
    outPose.ConvertAllBoneToLocalSpace();
}

void AnimGraph_TransformBoneNode::ResetNodeInternal()
{
    const auto& refSkeleton = mAnimator->GetSkeleton()->GetReferenceSkeleton();
    mBoneHandle = refSkeleton.FindRawBoneIndex(mBoneName);
    mParentBoneHandle = mBoneHandle ? refSkeleton.GetRawBoneParentIndex(mBoneHandle) : SkBoneHandle::InvalidHandle();
}

AnimGraph_LocalPoseLink* AnimGraph_TransformBoneNode::PoseLink()
{
    return mPoseLinks.size() > 0 ? TYPE_CAST(AnimGraph_LocalPoseLink*, mPoseLinks[0]) : nullptr;
}

AnimGraph_BaseNode* AnimGraph_TransformBoneNode::Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, const CENameMap<CEName, size_t>& inLinkNameToIndexMap,
                                                         const CENameMap<CEName, size_t>& inParamNameToIndexMap)
{
    return new AnimGraph_TransformBoneNode(inOwner, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap);
}

bool AnimGraph_TransformBoneNode::PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks)
{
    if (!AnimGraph_BaseNode::PostAssemble(inNodeJson, inParamLinks))
        return false;
    if (inParamLinks[0] != nullptr)
        mTranslationLink = ParamLinkUtils::GetParamLinkInstance<AnimGraph_Vec3ParamLink>(inParamLinks[0]);
    if (inParamLinks[1] != nullptr)
        mRotationLink = ParamLinkUtils::GetParamLinkInstance<AnimGraph_Vec3ParamLink>(inParamLinks[1]);
    if (inParamLinks[2] != nullptr)
        mScaleLink = ParamLinkUtils::GetParamLinkInstance<AnimGraph_Vec3ParamLink>(inParamLinks[2]);
    return true;
}
}   // namespace cross::anim