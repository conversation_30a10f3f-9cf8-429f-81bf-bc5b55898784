#pragma once
#include "CEAnimation/AnimBase.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_LocalPoseLink.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_RootPoseLink.h"

namespace cross::anim {

/**
 * This is the base class for the 'source version' of all skeletal control animation graph nodes
 */
class AnimGraph_BasicIKNode : public AnimGraph_BaseNode
{
public:
    AnimGraph_BasicIKNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    //// ~AnimGraph_BaseNode Interface Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void EvaluateRootSpace(RootSpacePose& outPose, AnimExtractContext<time::TrackUnWrapperH>& inContext) override;

    virtual void EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<time::TrackUnWrapperH>& inContext) override;

    virtual void GatherDebugData(GraphNodeDebugData& debugData) const override;

    virtual bool IsSourceLinkValid(AnimGraph_BaseLink const* inLink) const override;

protected:
    virtual void ResetNodeInternal() override;

    virtual bool PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks) override;

    ////
    //// ~AnimGraph_BaseNode Interface End~

private:
    inline AnimGraph_RootPoseLink* PoseLink()
    {
        Assert(mPoseLinks.size() > 0);
        return TYPE_CAST(AnimGraph_RootPoseLink*, mPoseLinks[0]);
    }

    void Solve(RootSpacePose& outPose, AnimExtractContext<time::TrackUnWrapperH>& inContext);

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

protected:
    // Ik bone name
    CEName mBoneName;

    // The effector
    AnimGraph_Vec3ParamLink mEffectorPositionLink;

    bool mAllowStretch;

private:
    const Animator* mAnimator{nullptr};

    SkBoneHandle mIKBoneHandle;
    SkBoneHandle mFixedBoneHandle;
    SkBoneHandle mMiddleBoneHandle;

    std::unordered_set<SkBoneHandle> mIKBoneChilds;
    std::unordered_set<SkBoneHandle> mFixedBoneChilds;
    std::unordered_set<SkBoneHandle> mMiddleBoneChilds;

    friend class AnimAssembler;
};

}   // namespace cross::anim
