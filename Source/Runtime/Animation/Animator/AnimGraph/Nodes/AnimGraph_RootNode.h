#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_LocalPoseLink.h"

namespace cross::anim {

// Root node of an animation tree (sink)
class AnimGraph_RootNode : public AnimGraph_BaseNode
{
public:
    AnimGraph_RootNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
        : AnimGraph_BaseNode(inAnimGraph, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap)
    {}

    //// ~AnimGraph_BaseNode Interface Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

    virtual void EvaluateRootSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

    virtual void GatherDebugData(GraphNodeDebugData& debugData) const override;

    ////
    //// ~AnimGraph_BaseNode Interface End~

private:
    inline AnimGraph_LocalPoseLink* PoseLink()
    {
        return mPoseLinks.size() > 0 ? TYPE_CAST(AnimGraph_LocalPoseLink*, mPoseLinks[0]) : nullptr;
    }

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    friend class AnimAssembler;
};

}   // namespace cross::anim
