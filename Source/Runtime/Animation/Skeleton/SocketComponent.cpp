#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/Animation/Skeleton/SocketComponent.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "Runtime/GameWorld/SkeltSocketSystemG.h"

namespace cross
{
	ecs::ComponentDesc* cross::SkeltSocketComponentG::GetDesc()
	{
        return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::SkeltSocketComponentG>({ false, true, true },
            &SkeltSocketSystemG::SerializeSkeltSocketComponent,
            &SkeltSocketSystemG::DeserializeSkeltSocketComponent,
            &SkeltSocketSystemG::PostDeserializeSkeltSocketComponent);
	}

}