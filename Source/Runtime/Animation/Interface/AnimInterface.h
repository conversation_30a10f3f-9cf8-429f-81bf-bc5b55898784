#pragma once

#include <memory>
#include "CECommon/GameWorld/IGameWorld.h"
#include "CECommon/Animation/Curve/FloatCurve.h"
#include "CrossBase/Math/CrossMath.h"
#include "CEAnimation/AnimBase.h"
#include "Resource/Animation/Skeleton/SkeletonResource.h"
#include "Resource/Animation/Sequence/AnimSequenceRes.h"
#include "Resource/Animation/Composite/AnimCompositeRes.h"
#include "Resource/Animation/Composite/AnimatrixRes.h"
#include "Resource/Animation/BlendSpace/AnimBlendSpaceRes.h"


namespace cross::editor {

enum class CEMeta(Editor) MsaCompatibleType
{
    Success = 0,
    CompatibleFailed,
    RunSkeltNoSkelt,
    AssetNoSkelt,
};

enum class CEMeta(Editor) MsaHierItemType
{
    SkeletalMeshAsset = 0,
    StaticMeshAsset,

    SkeletonAsset,

    AnimSequenceAsset,
    AnimCompositeAsset,
    AnimatrixAsset,
    AnimBlendSpaceAsset,
    AnimMotionMatchAsset,

    RefSkeltBone,
    RunSkeltBone,

    SkeletonPhysics,

    UnKnown
};

struct BoneData
{
    CEMeta(Editor)
    cross::anim::CEName Name{""};
    CEMeta(Editor)
    int BoneIndex{0};
    CEMeta(Editor)
    int ParentIndex{-1};
    
    CEMeta(Editor)
    std::vector<int> ChildrenIndices;

    CEMeta(Editor)
    MsaHierItemType BoneType;

    CEMeta(Editor)
    NodeTransform LocalSpaceTransform;

    CEMeta(Editor)
    NodeTransform RootSpaceTransform;
};

}   // namespace cross::editor

namespace cross {
// Get bone num from RefSkeleton of Skeleton, Skeletal Mesh, AnimSequence.
CEMeta(Editor) int GetRefSkBoneNumFromAsset(const char* assetPath);
// Get single bone data from RefSkeleton of Skeleton, Skeletal Mesh, AnimSequence.
CEMeta(Editor) bool GetRefSkBoneDataFromAsset(const char* assetPath, int boneIndex, cross::editor::BoneData& outRefSkBoneData);
// Get single bone transform from skeleton pose in skeleton component
CEMeta(Editor) bool GetBoneTransformFromSkeletonComp(cross::IGameWorld* world, UInt64 entity, int boneIndex, cross::editor::BoneData& outBoneData);

// Check skeleton compatible with Skeletal Mesh, AnimSequence.
// Output MissedBoneIndex if not compatible.
CEMeta(Editor) cross::editor::MsaCompatibleType IsSkeletonCompatible(const char* runSkeltPath, const char* assetPath, 
    cross::editor::BoneData& outRunSkMissedBone, cross::editor::BoneData& outResRefSkMissedBone);

// Reset skeleton pose in skeleton component to bind pose
CEMeta(Editor) void ResetSkPoseToRefPose(cross::IGameWorld* world, UInt64 entity);

CEMeta(Editor) void DrawBoneWithParent(cross::IGameWorld* world, const cross::editor::BoneData& inCurBone, const cross::editor::BoneData& inParentBone, bool isSelected);
CEMeta(Editor) void DrawBoneWithoutParent(cross::IGameWorld* world, const cross::editor::BoneData& inCurBone, bool isSelected);

}   // namespace cross

