#pragma once

#include "Resource/Resource.h"
#include "CEAnimation/Composite/AnimSegment.h"
#include "CEAnimation/Skeleton/Skeleton.h"
#include "Runtime/Animation/Notify/AnimNotifyTrackModifier.h"
#include "Runtime/Animation/Composite/AnimComposite.h"
#include "Runtime/Animation/Composite/Animatrix.h"

namespace cross::anim
{
#if CROSSENGINE_EDITOR

    using OnAnimAssetLengthModified = std::function<void(struct AnimTrack const*, float preLength)>;

    /** track modifiers, handles injecting of the Sequence & Composite & Matrix into animation editor modes */
    class ENGINE_API AnimTrackModifier
    {
    private:
        /* runtime skeleton which attached to modified track */
        const Skeleton* const mRunSkeleton{nullptr};
        /* track which is modified */
        AnimTrack& mTrack;
        // holding all anim seg resource ref here shared with above anim track's, which got const ref for runtime struct
        SlotTrackRes& mTrackRes;
        // holding all anim reference created by modifier, release after modifier de-constructor 
        std::vector<AnimSeqPtr> mAddedAnimSeqs;

    public:
        AnimTrackModifier(AnimTrack& inTrack, SlotTrackRes& inTrackRes, Skeleton const* inSkeleton)
            : mTrack(inTrack)
            , mTrackRes(inTrackRes)
            , mRunSkeleton(inSkeleton)
        {
            AnimTrack tTrack = mTrack;

            mTrackRes.SegmentsDesc.reserve(64);
            mTrackRes.SectionsDesc.reserve(64);

            // forbid vector scale-out, which lead to reference failed
            tTrack.AnimSegments.clear();
            SInt32 segIndex = 0;
            std::for_each(mTrack.AnimSegments.begin(), mTrack.AnimSegments.end(), [&tTrack, &segIndex, this](auto& elem) { 
                tTrack.AnimSegments.emplace_back(elem.SequencePtr, mTrackRes.SegmentsDesc[segIndex++], elem.StartPos);
            });

            // re-assignment 
            mTrack = std::move(tTrack);
        }
    
        ~AnimTrackModifier()  
        {}

        // 
        void RegisterLengthModifiedCallback(OnAnimAssetLengthModified inLengthModifiedFunc);

        /* applying and reverting the modifier for the given Animation Track */
        void ApplyToAnimationTrack(struct AnimTrack& outTrack);
        void RevertFromAnimationTrack(struct AnimTrack const& inTrack);
        
        /* insert a particular segment after 'index' pos in track
        *  index is negative while track is empty
        */
        bool InsertSegment(SInt32 preIndex, AnimSeqPtr segAnimAsset, SlotTrackResSegment const& segAnimRes);
        
        /* remove a particular segment by 'index' pos in track */
        bool RemoveSegment(SInt32 index);
        
        /* return current segments count. Const so it can't be changed recklessly */
        inline size_t GetSegmentsCount() const { return mTrack.AnimSegments.size(); }
        
        /* modify a particular segment by 'index' pos in track */
        template<typename TLambda = std::function<void(AnimSegment*, std::vector<AnimSeqPtr>& allInstances, SlotTrackRes& TrackRes)>>
        void ModifySegment(SInt32 index, TLambda&& lambda)
        {
            auto segment = GetSegment(index);
            float preTrackLength = mTrack.GetRunLength();

            if (segment != nullptr)
                lambda(segment, mAddedAnimSeqs, mTrackRes);

            // refresh segments track start pos after modify property
            RefreshSegmentsPositionInTrack();

            if (mOnLengthModifiedFunc != nullptr && 
                (std::abs)(mTrack.GetRunLength() - preTrackLength) > 0.001f)
                mOnLengthModifiedFunc(&mTrack, preTrackLength);

            RefreshTrackRes();
        }

    protected:
        inline AnimSegment* GetSegment(SInt32 index) { return &mTrack.AnimSegments[index]; }

        void RefreshSegmentsPositionInTrack();

        void RefreshTrackRes();

    private:
        OnAnimAssetLengthModified mOnLengthModifiedFunc = nullptr;
    };

    class AnimCompositeModifier
    {
    public:
        AnimCompositeModifier(AnimCmpPtr inShellPtr, Skeleton const* inSkelt)
            : ModifierForAnim(inShellPtr->mTrack, inShellPtr->mCompositeResPtr->mSlotTrack, inSkelt)
        {}

    public:
        AnimTrackModifier ModifierForAnim;
    };

    class AnimatrixModifier
    {
    public:
        AnimatrixModifier(AnimatrixPtr inShellPtr, Skeleton const* inSkelt);

    public:
        std::map<std::string, AnimTrackModifier> ModifierForActivatedTracks;
        std::vector<std::string> TrackNames;
    };

#endif
}
