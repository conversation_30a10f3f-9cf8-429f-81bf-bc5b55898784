#include "EnginePrefix.h"
#include "MotionMatchConfig.h"

namespace cross::anim
{


    bool WeightControl::ParseConfig(const DeserializeNode& nodeJson, WeightControl& weight)
    {
        auto SafeExtract = [&nodeJson](float& value, const std::string& key)
        {
            value = nodeJson.HasMember(key) ? nodeJson[key].AsFloat() : value;
        };

        SafeExtract(weight.mVelocity_weight, "BodyVelocityWeight");
        SafeExtract(weight.mRotVelocity_weight, "BodyRotVelocityWeight");

        auto ExtractFloatOrArray = [&nodeJson](std::vector<float>& result, const std::string& key)
        {
            if (!nodeJson.HasMember(key)) return;

            if (nodeJson[key].IsArray())
            {
                auto keyJson = nodeJson[key];
                for (int i = 0; i < keyJson.Size(); i++)
                {
                    result.push_back(keyJson[i].AsFloat());
                }
            }
            else
            {
                result.push_back(nodeJson[key].AsFloat());
            }
        };

        ExtractFloatOrArray(weight.mPosePos_weights, "PoseTranslationWeight");
        ExtractFloatOrArray(weight.mPoseVelo_weights, "PoseVelocityWeight");

        ExtractFloatOrArray(weight.mTrajPos_weights, "TrajTranslationWeight");
        ExtractFloatOrArray(weight.mTrajAngle_weights, "TrajAngleWeight");

        return true;
    }

    WeightControl WeightControl::Combine(const WeightControl& lhs, const WeightControl& rhs, float QualityVsResponsive)
    {
        float TrajRatio = 2.f * QualityVsResponsive;
        float PoseRatio = 2.f * (1.f - QualityVsResponsive);

        WeightControl result;
        result.mVelocity_weight = lhs.mVelocity_weight * rhs.mVelocity_weight;
        result.mRotVelocity_weight = lhs.mRotVelocity_weight * rhs.mRotVelocity_weight;

        auto VectorMultiply = [](std::vector<float>& result, const std::vector<float>& lhs, const std::vector<float>& rhs, float ratio)
        {
            int maxLen = static_cast<int>(std::max<size_t>(lhs.size(), rhs.size()));
            result.resize(maxLen);
            for (int i = 0; i < maxLen; i++)
            {
                auto lhs_value = lhs.empty() ? 1.0f : lhs[std::min<int>(i, static_cast<int>(lhs.size()) - 1)];
                auto rhs_value = rhs.empty() ? 1.0f : rhs[std::min<int>(i, static_cast<int>(rhs.size()) - 1)];
                result[i] = lhs_value * rhs_value  * ratio;
            }
        };

        VectorMultiply(result.mPosePos_weights, lhs.mPosePos_weights, rhs.mPosePos_weights, PoseRatio);
        VectorMultiply(result.mPoseVelo_weights, lhs.mPoseVelo_weights, rhs.mPoseVelo_weights, PoseRatio);

        VectorMultiply(result.mTrajPos_weights, lhs.mTrajPos_weights, rhs.mTrajPos_weights, TrajRatio);
        VectorMultiply(result.mTrajAngle_weights, lhs.mTrajAngle_weights, rhs.mTrajAngle_weights, TrajRatio);
        return result;
    }

    void WeightControl::Serialize(SerializeNode& inNodeJson)
    {
        inNodeJson["BodyVelocityWeight"] = mVelocity_weight;
        inNodeJson["BodyRotVelocityWeight"] = mRotVelocity_weight;

        auto PushVector = [&inNodeJson](const std::vector<float>& values, const std::string& key)
        {
            SerializeNode nodes = SerializeNode::EmptyArray();
            for (auto& v : values)
            {
                nodes.PushBack(v);
            }
            inNodeJson[key] = std::move(nodes);
        };

        PushVector(mPosePos_weights, "PoseTranslationWeight");
        PushVector(mPoseVelo_weights, "PoseVelocityWeight");

        PushVector(mTrajPos_weights, "TrajTranslationWeight");
        PushVector(mTrajAngle_weights, "TrajAngleWeight");
    }

    bool TrajGenConfig::ParseConfig(const DeserializeNode& nodeJson, TrajGenConfig& config)
    {
        Assert(nodeJson.HasMember("TrajTimes") && nodeJson.HasMember("MoveResponse") && nodeJson.HasMember("TurnResponse") && nodeJson.HasMember("PoseInterval"));

        const auto& times = nodeJson["TrajTimes"];

        if (times.IsArray())
        {
            config.mTrajTimes.resize(times.Size());

            for (int i = 0; i < times.Size(); i++)
            {
                config.mTrajTimes[i] = (times[i].AsFloat());
            }
        }

        config.MoveResponse = nodeJson["MoveResponse"].AsFloat();
        config.TurnResponse = nodeJson["TurnResponse"].AsFloat();
        config.PoseInterval = nodeJson["PoseInterval"].AsFloat();

        return true;
    }

    void TrajGenConfig::Serialize(SerializeNode& inNodeJson)
    {
        SerializeNode times = SerializeNode::EmptyArray();
        
        for (auto& time : mTrajTimes)
        {
            times.PushBack(time);
        }
        inNodeJson["TrajTimes"] = std::move(times);

        inNodeJson["MoveResponse"] = MoveResponse;
        inNodeJson["TurnResponse"] = TurnResponse;
        inNodeJson["PoseInterval"] = PoseInterval;
    }

}

