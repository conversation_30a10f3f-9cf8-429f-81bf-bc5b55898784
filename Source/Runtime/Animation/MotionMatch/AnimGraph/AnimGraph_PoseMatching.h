#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "Runtime/Animation/Composite/AnimComposite.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_PlayCompositeNode.h"

#include "../MMBase/MotionMatchConfig.h"
#include "../MMBase/MotionDataAsset.h"
#include "Runtime/Animation/Sequence/LinearReductionSeq.h"
#include "Runtime/Animation/Sequence/UniformSampleSeq.h"

namespace cross::anim
{
    class MotionRecorder;
    class PoseMatchCompositeInstance : public AnimCompositeInstance
    {
    public:
        void PlayAt(float time) {
            mPrevPos = mCurPos = { time };
            mDeltaTime = {0.f};
            mBlend.Reset();
            AnimCompositeInstance::Play();
        }
        AnimCmpPtr GetSequence() const { return mAnimCompositePtr; }
    protected:
        // Should be removed after anim factory async complete  
        static auto CreateAnimSeq_Sequence(AnimSeqResPtr const& seqResPtr, const Skeleton* skeltPtr) -> std::pair<AnimSequence*, FactoryErrorCode::Type> 
        {
            const auto* streamingAnim = seqResPtr->GetStreamingAnimation();

            // create required sequence here
            AnimSequence* seqPtr = nullptr;

            if (streamingAnim->CprType == StreamingAnimCompressType::LinearKeyReduction)
            {
                seqPtr = new anim::LinearReductionSeq(seqResPtr);
            }
            else if (streamingAnim->CprType == StreamingAnimCompressType::UniformlySampled)
            {
                seqPtr = new anim::UniformSampleSeq(seqResPtr);
            }
            else
            {
                Assert(false);
                return {nullptr, FactoryErrorCode::NoValidResource};
            }

            if (skeltPtr != nullptr && seqPtr != nullptr && skeltPtr->IsCompatibleAnimSeq(seqPtr) == false)
            {
                delete seqPtr;
                return {nullptr, FactoryErrorCode::FailedCompatiblilty};
            }

            return {seqPtr, FactoryErrorCode::Success};
        }

        PoseMatchCompositeInstance(AnimComposite* inCompositeShell, bool allowLooping = true, float playRate = 1.0f) : AnimCompositeInstance(inCompositeShell, false, allowLooping, playRate) {}
        template< typename ... Args>
        static std::shared_ptr<PoseMatchCompositeInstance> Create(ResourcePtr resource, const Skeleton* skeltPtr, const std::tuple<Args...> & args)
        {
            class MakeSharedEnabler : public PoseMatchCompositeInstance
            {
            public:
                MakeSharedEnabler(AnimComposite* inCompositeShell, bool allowLooping = true, float playRate = 1.0f) :PoseMatchCompositeInstance(inCompositeShell, allowLooping, playRate) {}
            };

            AnimComposite* composite_shell = nullptr;

            if (resource->GetClassID() == ClassID(AnimSequenceRes))
            {
                auto result = CreateAnimSeq_Sequence(TypeCast<AnimSequenceRes>(resource), skeltPtr);

                if (result.second != FactoryErrorCode::Success)
                {
                    return nullptr;
                }

                AnimSeqPtr seqPtr(TYPE_CAST(IAnimSequence*, result.first));

                // create a new Raw AnimComposite Ptr from AnimSeqResPtr and AnimSeqPtr.
                // will hold ref count of seqResPtr here when create a dynamic AnimComposite.
                composite_shell = new AnimComposite(TypeCast<AnimSequenceRes>(resource), seqPtr, {0.f}, seqPtr->GetRunLength());
            }

            bool allowLooping = std::get<0>(args);
            float playRate = std::get<1>(args);

            return std::make_shared<MakeSharedEnabler>(composite_shell, allowLooping, playRate);
        }

    protected:
        friend class AnimFactory;
       
        TrackUnWrapperH mPrevPos{0.f};
        
        TrackUnWrapperH mCurPos{0.f};
    };

    // it (inherit AnimCmpInstanceBase) seems only way that instance's cursor can be adjust freely
    class AnimGraph_PlayPoseMatch : public AnimGraph_PlayCompositeNode
    {
    public:
        AnimGraph_PlayPoseMatch(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
            const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

        virtual void Initialize(const AnimInitContext& inContext) override;

        virtual void Update(const AnimUpdateContext& inContext) override;

        virtual void EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

    protected:
        int SearchForClosestPose();

    public:
        static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson,
            const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

    private:
        WeightControl mUserWeights;

        MotionDataAssetPtr mAnimationAnalyzer;


        MotionMatchingMathPosePtr mCurrentPose;

        std::vector<PoseBoneHandle> mInterestPoseBones;
        MotionRecorder* mPoseRecorded = nullptr;
        bool mPoseRecoredInited = false;

        bool mSearch = false;
    };
}
