#pragma once
#include "Resource/Resource.h"
#include "CEAnimation/AnimBase.h"
#include "CEAnimation/Composite/AnimSegment.h"
#include "CEAnimation/Skeleton/Skeleton.h"
#include "Runtime/Animation/Interface/AnimInterface.h"

namespace cross::anim
{
#if CROSSENGINE_EDITOR

    template<class T, typename Enable = void> 
    class AnimMarkerTrackModifier;

    // modifications to a track holding markers those got no length but position
    template<class T> 
    class  AnimMarkerTrackModifier<T, typename std::enable_if_t<std::is_pointer_v<std::remove_cv_t<T>>>>
    {
    public:    
        AnimMarkerTrackModifier(std::vector<T>& inMarkers)
            : mMarkers(inMarkers)
        {}

        inline T const Get(UInt32 inMarkerIndex) const
        {
            Assert(mMarkers.size() > inMarkerIndex);
            return &mMarkers[inMarkerIndex];
        }

        inline bool Add(T inMarker) 
        {
            mMarkers.push_back(inMarker);
            Sort();
            return true;
        }
        
        template<class TLambda = std::function<bool(T)>> 
        bool Remove(TLambda&& lambda) 
        {
            auto itr = std::find_if(mMarkers.begin(), mMarkers.end(), [&lambda](auto elem) { 
                return lambda(elem);
            });

            if (itr != mMarkers.end()) 
            {
                mMarkers.erase(itr);
                return true;
            }
            
            return false;
        }

        inline bool RemoveIndex(UInt32 inMarkerIndex) 
        {
            mMarkers.erase(mMarkers.begin() + inMarkerIndex);
            return true;
        }

        template<class TLambda = std::function<bool(T)>> 
        void ModifyMarker(SInt32 inMarkerIndex, TLambda&& lambda)
        {
            Assert(inMarkerIndex < mMarkers.size());
            auto marker = Get(inMarkerIndex);
            if (marker)
                lambda(marker);
            Sort();
        }

        template<class CondTLambda = std::function<bool(T)>, class ModifyTLambda = std::function<bool(T)>> 
        bool ModifyMarkerByCond(CondTLambda&& condLambda, ModifyTLambda&& modifyLambda)
        {
            auto itr = std::find_if(mMarkers.begin(), mMarkers.end(), [&condLambda](auto elem) {
                return condLambda(elem);
            });
            if (itr != mMarkers.end())
            {
                modifyLambda(*itr);
                Sort();
                return true;
            }

            return false;
        }

        inline size_t Count() const { return mMarkers.size(); }

    protected:
        inline T Get(UInt32 inMarkerIndex) 
        {
            Assert(mMarkers.size() > inMarkerIndex);
            return mMarkers[inMarkerIndex];
        }

        inline void Sort()
        {
            auto sort_function = [](const T lhs, const T rhs) { return *lhs < *rhs; };
            std::sort(mMarkers.begin(), mMarkers.end(), sort_function);
        }

    protected:
        std::vector<T>& mMarkers;
    };

    template<class T>
    class  AnimMarkerTrackModifier<T, typename std::enable_if_t<!std::is_pointer_v<std::remove_cv_t<T>>>>
    {
    public:
        AnimMarkerTrackModifier(std::vector<T>& inMarkers)
            : mMarkers(inMarkers)
        {}

        inline T const& Get(UInt32 inMarkerIndex) const
        {
            Assert(mMarkers.size() > inMarkerIndex);
            return &mMarkers[inMarkerIndex];
        }

        inline bool Add(T inMarker)
        {
            mMarkers.push_back(inMarker);
            Sort();
            return true;
        }

        template<class TLambda = std::function<bool(T&)>> 
        bool Remove(TLambda&& lambda)
        {
            auto itr = std::find_if(mMarkers.begin(), mMarkers.end(), [&lambda](auto& elem) 
                { return lambda(elem); });

            if (itr != mMarkers.end())
            {
                mMarkers.erase(itr);
                return true;
            }

            return false;
        }

        inline bool RemoveIndex(UInt32 inMarkerIndex)
        {
            mMarkers.erase(mMarkers.begin() + inMarkerIndex);
            return true;
        }

        template<class TLambda = std::function<bool(T&)>> 
        void ModifyMarker(SInt32 inMarkerIndex, TLambda&& lambda)
        {
            Assert(inMarkerIndex < mMarkers.size());
            auto& marker = Get(inMarkerIndex);
            lambda(marker);
            Sort();
        }

        template<class CondTLambda = std::function<bool(T&)>, class ModifyTLambda = std::function<bool(T&)>> 
        bool ModifyMarkerByCond(CondTLambda&& condLambda, ModifyTLambda&& modifyLambda)
        {
            auto itr = std::find_if(mMarkers.begin(), mMarkers.end(), [&condLambda](auto elem) {
                return condLambda(elem);
            });
            if (itr != mMarkers.end())
            {
                modifyLambda(*itr);
                Sort();
                return true;
            }

            return false;
        }

        inline size_t Count() const
        {
            return mMarkers.size();
        }

    protected:
        inline T& Get(UInt32 inMarkerIndex)
        {
            Assert(mMarkers.size() > inMarkerIndex);
            return mMarkers[inMarkerIndex];
        }

        inline void Sort()
        {
            auto sort_function = [](const T& lhs, const T& rhs) { return lhs < rhs; };
            std::sort(mMarkers.begin(), mMarkers.end(), sort_function);
        }

    protected:
        std::vector<T>& mMarkers;
    };

    // anim notify marker track modifier
    class  AnimNotifyTrackModifier : public AnimMarkerTrackModifier<AnimNotifyEvent*>
    {
    private:
        AnimNotifyTrack& mNotifyTrack;

    public:       
        AnimNotifyTrackModifier(AnimNotifyTrack& inTrack)
            : AnimMarkerTrackModifier(inTrack.Notifies) 
            , mNotifyTrack(inTrack)
        {}

        inline AnimNotifyTrackModifier& operator=(const AnimNotifyTrackModifier& other) noexcept 
        {
            mNotifyTrack = other.mNotifyTrack;
            return *this;
        }

        bool Add(DeserializeNode const& inNode)
        {
            AnimNotifyEvent* ptr = CreateNotifyEvent(inNode);
            return AnimMarkerTrackModifier::Add(ptr);
        }

    protected: 
        ENGINE_API AnimNotifyEvent* CreateNotifyEvent(DeserializeNode const& inNode); 
    };

    // anim sync marker track modifier
    class AnimSyncTrackModifier : public AnimMarkerTrackModifier<sync::AnimSyncMarker>
    {
    public:
        AnimSyncTrackModifier(StreamingAnimation& inStreamingAnim)
            : AnimMarkerTrackModifier(inStreamingAnim.SyncMarkerTrack.MarkerTrack)
        {}
    };

#endif
}
