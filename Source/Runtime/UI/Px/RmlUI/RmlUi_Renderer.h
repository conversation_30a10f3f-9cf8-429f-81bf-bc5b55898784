#pragma once

#include "../Canvas.h"
#include <RmlUi/Core/RenderInterface.h>
#include <RmlUi/Core/Types.h>
#include <Resource/Resource.h>
#include <bitset>

namespace cross
{
    struct Canvas;
}

namespace cross::rmlui
{
    class RenderInterface_CE : public Rml::RenderInterface
    {
    private:
        struct Geometry
        {
            Geometry(Rml::Span<const Rml::Vertex> const vertices, Rml::Span<const int> const indices
                , MaterialPtr const material, Float2 const & boundingSize);

            Rml::Span<const Rml::Vertex> vertices;
            Rml::Span<const int> indices;
            MaterialPtr material;
            Float2 boundingSize;
        };

    public:
        RenderInterface_CE();
        ~RenderInterface_CE();

        // -- Inherited from Rml::RenderInterface --
        Rml::CompiledGeometryHandle CompileGeometry(Rml::Span<const Rml::Vertex> vertices, Rml::Span<const int> indices) override;
        void RenderGeometry(Rml::CompiledGeometryHandle handle, Rml::Vector2f translation, Rml::TextureHandle texture) override;
        void ReleaseGeometry(Rml::CompiledGeometryHandle handle) override;

        Rml::TextureHandle LoadTexture(Rml::Vector2i& texture_dimensions, const Rml::String& source) override;
        Rml::TextureHandle GenerateTexture(Rml::Span<const Rml::byte> source_data, Rml::Vector2i source_dimensions) override;
        Rml::TextureHandle GenerateTextureWhite();
        void ReleaseTexture(Rml::TextureHandle texture_handle) override;

        void EnableScissorRegion(bool enable) override;
        void SetScissorRegion(Rml::Rectanglei region) override;

        void EnableClipMask(bool enable) override;
        void RenderToClipMask(Rml::ClipMaskOperation mask_operation, Rml::CompiledGeometryHandle geometry, Rml::Vector2f translation) override;

        void SetTransform(const Rml::Matrix4f* transform) override;

        Rml::LayerHandle PushLayer() override;
        void CompositeLayers(Rml::LayerHandle source, Rml::LayerHandle destination, Rml::BlendMode blend_mode, Rml::Span<const Rml::CompiledFilterHandle> filters) override;
        void PopLayer() override;

        Rml::TextureHandle SaveLayerAsTexture() override;

        Rml::CompiledFilterHandle SaveLayerAsMaskImage() override;

        Rml::CompiledFilterHandle CompileFilter(const Rml::String& name, const Rml::Dictionary& parameters) override;
        void ReleaseFilter(Rml::CompiledFilterHandle filter) override;

        Rml::CompiledShaderHandle CompileShader(const Rml::String& name, const Rml::Dictionary& parameters) override;
        void RenderShader(Rml::CompiledShaderHandle shader_handle, Rml::CompiledGeometryHandle geometry_handle, Rml::Vector2f translation, Rml::TextureHandle texture) override;
        void ReleaseShader(Rml::CompiledShaderHandle effect_handle) override;

    public:
        void SetCurrentCanvas(cross::Canvas * const canvas);

    private:
        static cross::CanvasItem * CreateItem(Canvas * const canvas
            , Geometry const * const geometry, TexturePtr const texture
            , Rml::Vector2f const& translation, Rml::Matrix4f const * const transform
            , UInt8 const stencilRef);

    private:
        cross::Canvas * mCurrentCanvas = nullptr;

        int mGeneratedTextureIndex = 0;
        std::vector<TexturePtr> mTextures; //HERE always increase
        Rml::TextureHandle mTextureNull = std::numeric_limits<Rml::TextureHandle>::max();

        UInt8 mStencilRef{0};

        bool mHasTransform = false;
        Rml::Matrix4f mTransform = Rml::Matrix4f::Identity();
    };
}
