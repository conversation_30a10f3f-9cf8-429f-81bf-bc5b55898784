#include "EnginePrefix.h"
#include "PxUtil.h"
#include "imageio.h"

namespace cross::px {

ResourceFuturePtr<resource::Texture> LoadTexture(const char* url, UInt32& width, UInt32& height)
{
    imageio::image local_image;
    imageio::load_image(url, local_image);

    width = local_image.get_width();
    height = local_image.get_height();

    auto extension = PathHelper::GetExtension(url);
    if (extension == "png" || extension == "jpg")
    {
        if (width == 0 || height == 0)
        {
            auto texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously("EngineResource/Texture/DefaultTexture.nda"));
            width = texture->GetWidth();
            height = texture->GetHeight();
            return texture;
        }
        else
        {
            UInt32 size = local_image.get_total_pixels() * sizeof(imageio::color_rgba);
            UInt32 pitch = local_image.get_pitch() * sizeof(imageio::color_rgba);
            auto texture = gResourceMgr.CreateResourceAs<resource::Texture2D>(TextureFormat::RGBA32, ColorSpace::Linear, width, height, 1, url);
            texture->UploadImage(0, reinterpret_cast<const UInt8*>(local_image.get_pixels().data()), size, pitch);
            return TypeCast<resource::Texture>(texture);
        }
    }
    else
    {
        auto texture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(url));
        width = texture->GetWidth();
        height = texture->GetHeight();
        return texture;
    }
}

std::string PathResolve(const char* url)
{
    std::string uri(url);
    if (uri.find("http") == std::string::npos && 
        uri.find("https") == std::string::npos && 
        uri.find("file:") == std::string::npos)
    {
        uri = PathHelper::GetAbsolutePath(uri);
        if (!PathHelper::IsFileExist(uri))
            uri = "";
    }
    return uri;
}

}