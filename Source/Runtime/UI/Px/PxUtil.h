#pragma once

#include "Runtime/Interface/CrossEngineImp.h"
#include "Resource/Texture/Texture.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Resource.h"

namespace cross::px {

ResourceFuturePtr<resource::Texture> LoadTexture(const char* url, UInt32& width, UInt32& height);

std::string PathResolve(const char* url);

}