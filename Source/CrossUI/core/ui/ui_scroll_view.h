#pragma once

#include "ui_control.h"

namespace oui {

class CROSS_UI_API UIScrollView : public UIElement {
public:
    float content_height = 0.f;
    float scroll_y = 0.f;
    float2 start_scroll;
    bool render_scroll_bar = true;
    ui_rect rect;

    UIScrollView() {}
    ~UIScrollView() {}
};

CROSS_UI_API void ui_scroll_view(UIState& state, UIScrollView &view, ui_rect rect, int layer_index = 0);

}