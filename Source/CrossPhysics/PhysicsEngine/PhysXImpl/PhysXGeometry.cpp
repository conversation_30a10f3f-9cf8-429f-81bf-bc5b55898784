#include "EnginePrefix.h"
#include "PhysXGeometry.h"
#include "base/memoryhooker/Module.h"
namespace cross
{
	PhysXConvexMesh::~PhysXConvexMesh()
	{
		if (mPxConvexMesh)
			mPxConvexMesh->release();
		ALIGNED_FREE(mData);
	}

    PhysXTriangleMesh::~PhysXTriangleMesh()
    {
		if (mPxTriangleMesh)
			mPxTriangleMesh->release();
		ALIGNED_FREE(mData);
    }
    PhysXTerrainHeightMap::~PhysXTerrainHeightMap()
    {
        if (mPxHeightField)
            mPxHeightField->release();
        ALIGNED_FREE(mData);
    }
    }
