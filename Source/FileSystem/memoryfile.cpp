#include "EnginePrefix.h"

#include "memoryfile.h"

namespace cross
{
    namespace filesystem
    {
        MemoryFile::MemoryFile(SInt64 inSize) :
            mSize(inSize)
        {
            mBuffer = std::shared_ptr<char>(new char[mSize + 1]);
            mBuffer.get()[mSize] = 0;
        }

        MemoryFile::MemoryFile(const std::shared_ptr<char>& inBuffer, size_t inSize) :
            mSize(inSize)
        {
            mBuffer = inBuffer;
        }

        size_t MemoryFile::Read(char* outBuffer, size_t inReadLength)
        {
            if(IsEof())
            {
                return 0;
            } else
            {
                SInt64 readEndPos = mCurrentPos + inReadLength;
                if(readEndPos < mSize)
                {
                    memcpy(outBuffer, mBuffer.get() + mCurrentPos, inReadLength);
                    mCurrentPos += inReadLength;
                    return inReadLength;
                }
                else
                {
                    UInt64 realReadLength = mSize - mCurrentPos;
                    memcpy(outBuffer, mBuffer.get() + mCurrentPos, realReadLength);
                    mCurrentPos = mSize;
                    return realReadLength;
                }
            }
        }


		SInt64 MemoryFile::Seek(int inOffset, SeekOption inWhence)
        {
            switch(inWhence)
            {
                case SEEK_OPTION_BEGIN:
                {
                    if(inOffset < mSize && inOffset >= 0)
                    {
                        mCurrentPos = inOffset;
                        return 0;
                    }
                    else
                    {
                        return -1;
                    }
                         
                }
                break;
                case SEEK_OPTION_END:
                {
                    if(inOffset <= 0 && (inOffset + mSize) >= 0)
                    {
                        mCurrentPos = mSize + inOffset;
                    }
                    else
                    {
                        return -1;
                    }
                }
                break;
                case SEEK_OPTION_CURRENT:
                {
                    if(inOffset >= 0)
                    {
						SInt64 seekEndPos = mCurrentPos + inOffset;
                        if(seekEndPos > mSize)
                        {
                            return -1;
                        }
                        else
                        {
                            mCurrentPos = seekEndPos;
                            return 0;
                        }
                    }
                }
                break;
                default:
                    Assert(0);
                    return -1;
            }
			return -1;
        }

        bool MemoryFile::Close()
        {
            mBuffer.reset();
            return true;
        }
    
        void MemoryFile::SetPathName(const std::string& inVp)
        {
            mPath = std::shared_ptr<std::string>(new std::string(inVp));
        }

        const std::string& MemoryFile::GetPathName() const
        {
            static std::string empty("");
            return mPath ? *(mPath) : empty;
        }
    
        const std::string& MemoryFile::GetLoaderRootPath() const
        {
            return mLoaderRootPath;
        }
        
        void MemoryFile::SetLoaderRootPath(const std::string& inRootPath)
        {
            mLoaderRootPath = inRootPath;
        }
        
    }
}
