#include "EnginePrefix.h"


#if defined(__unix__)
#ifdef _FEATURES_H
#error Please make sure this file included before features.h
#endif

#if defined(ANDROID) || defined(__ANDROID__)
#if defined(__ANDROID_API__) && __ANDROID_API__ >= 24
#ifndef _LARGEFILE_SOURCE
#define _LARGEFILE_SOURCE
#endif

#ifndef _LARGEFILE64_SOURCE
#define _LARGEFILE64_SOURCE
#endif
#define _FILE_OFFSET_BITS 64
#endif
#else
#ifndef _LARGEFILE_SOURCE
#define _LARGEFILE_SOURCE
#endif

#ifndef _LARGEFILE64_SOURCE
#define _LARGEFILE64_SOURCE
#endif
#define _FILE_OFFSET_BITS 64
#endif

#include <sys/stat.h>
#include <unistd.h>


#ifndef O_BINARY
#define O_BINARY 0
#endif

#ifndef O_RANDOM
#define O_RANDOM 0
#endif

#ifndef S_IREAD
#define S_IREAD S_IRUSR
#endif

#ifndef S_IWRITE
#define S_IWRITE S_IWUSR
#endif

#ifndef S_IEXEC
#define S_IEXEC S_IXUSR
#endif

#elif defined(__APPLE__)
#ifdef _FEATURES_H
#error Please make sure this file included before features.h
#endif
#define _FILE_OFFSET_BITS 64

#include <sys/stat.h>
#include <unistd.h>

#ifndef O_BINARY
#define O_BINARY 0
#endif

#ifndef O_RANDOM
#define O_RANDOM 0
#endif

#ifndef S_IREAD
#define S_IREAD S_IRUSR
#endif

#ifndef S_IWRITE
#define S_IWRITE S_IWUSR
#endif

#ifndef S_IEXEC
#define S_IEXEC S_IXUSR
#endif

//no need to commit?
#define commit(f)

inline int mkdir(const char * path)
{
    return mkdir(path, S_IRWXU | (S_IRWXG & ~S_IWGRP) | (S_IRWXO | ~S_IWOTH));
}

#elif defined(WIN32)
#include <sys/stat.h>
#include <stdio.h>
#include <io.h>
#include <direct.h>
#include <corecrt_io.h>
#define lseek64(f, offset, dir) _lseeki64(f, offset, dir)
#define fstat64(f, s) _fstat64(f, s)
#define tell64(f) _telli64(f)
//#define access(path,mode) _access(path, mode)
#define commit(f) _commit(f)

typedef struct _stat64 Stat64;

#include <fcntl.h>

#define write _write
#define close _close
#define lseek _lseek
#define read  _read

#endif /* _WIN32 */

#if defined(_WIN64)
#    define CEFSTAT        _fstat64
#    define CEFSTAT_STRUCT struct _stat64
#    define CESEEK         _lseeki64
#elif defined(WIN32)
#    define CEFSTAT        fstat
#    define CEFSTAT_STRUCT struct stat
#    define CESEEK         lseek
#else
#    define CEFSTAT        fstat
#    define CEFSTAT_STRUCT struct stat
#    define CESEEK         lseek
#endif

#include "filesystem_io.h"
#include "io_system.h"

#if CROSSENGINE_IOS || CROSSENGINE_ANDROID || (CROSSENGINE_OSX && MACOS_ARM64)
#include <sys/stat.h>
#include <fcntl.h>
#endif

#if CROSSENGINE_OSX
#include <fcntl.h>
#endif

#define HUGE_WD_SIZE  1024 * 1024 * 100  // 100M
#define HUGE_FILE_SIZ 1024 * 1024 * 1024 // 1G

#include "filesystemutil.h"

namespace cross
{ namespace filesystem { namespace io {
        int OpenHandle(const char* path, int flag, int mode)
        {
#if CROSSENGINE_WIN
            UTF8ToWideCharConverter converter(path);
            int openResult = ::_wopen(converter.GetWideChar(), flag, mode);
#if 0
            if(openResult == -1)
            {
                errno_t err;
                _get_errno(&err);
                //char buf[256];
                char const* str = ::strerror(err);
                LOG_ERROR("OpenHandle Failed, Error Code({}): {}; Path: {}", err, str, path);
            }
#    endif
            return openResult;
#else
            return open(path, flag, mode);
#endif
        }

        Input* OpenerSys::OpenInput(const std::string& inPath)
        {
            int fileHandle = OpenHandle(inPath.c_str(), O_BINARY | O_RDONLY | O_RANDOM, S_IREAD);
            StreamSys* fileStream = OpenStream(fileHandle, inPath);

            if (fileStream)
                return new InputSys(fileStream);
            else
                return nullptr;
        }

        Output* OpenerSys::OpenOutput(const std::string& inPath)
        {
#if CROSSENGINE_WIN
            int mode = _S_IWRITE;
#else
            int mode = S_IRWXU;
#endif
            int fileHandle = OpenHandle(inPath.c_str(), O_BINARY | O_WRONLY | O_RANDOM | O_CREAT | O_TRUNC, mode);

            StreamSys* pStream = OpenStream(fileHandle, inPath);
            if (pStream)
                return new OutputSys(pStream);
            else
                return nullptr;
        }

        StreamSys* OpenerSys::OpenStream(int inHandle, const std::string& inPath)
        {
            if (inHandle == InValidHandle)
                return nullptr;

            CEFSTAT_STRUCT statInfo;
            if (CEFSTAT(inHandle, &statInfo) == 0)
            {
                StreamSys* pStreamSys = new StreamSys();
                pStreamSys->mHandle = inHandle;
                pStreamSys->mPath = inPath;
                pStreamSys->mOpener = this;
                pStreamSys->mSize = (size_t)statInfo.st_size;
                return pStreamSys;
            }
            else
            {
                return nullptr;
            }
        }

        StreamSys::~StreamSys()
        {
            if (mHandle != InValidHandle)
                close(mHandle);
            mHandle = InValidHandle;
        }

        size_t StreamSys::Tell() const
        {
            if (mHandle != InValidHandle)
                return (size_t)CESEEK(mHandle, 0, SEEK_CUR);
            else
                return 0;
        }

        SInt64 StreamSys::Seek(std::ptrdiff_t inOffset, int inFromWhere)
        {
            if (mHandle != InValidHandle)
                return CESEEK(mHandle, (long)inOffset, inFromWhere) < 0 ? -1 : 0;
            return -1;
        }

        size_t StreamSys::GetSize() const
        {
            return mSize;
        }

        bool StreamSys::IsEof() const
        {
            if (mHandle != InValidHandle)
            {
                size_t curPos = CESEEK(mHandle, 0, SEEK_CUR);
                return (curPos >= mSize) ? true : false;
            }
            else
            {
                return false;
            }
        }

        void StreamSys::Close()
        {
            if (mHandle != InValidHandle)
                close(mHandle);
            mHandle = InValidHandle;
        }

        bool StreamSys::IsClosed() const
        {
            return mHandle == InValidHandle;
        }

        const std::string& StreamSys::GetPathName() const
        {
            return mPath;
        }

        Opener* StreamSys::GetOpener() const
        {
            return mOpener;
        }

        bool StreamSys::GetFileDesc(int& outFd, size_t& outOffset) const
        {
            outFd = mHandle;
            outOffset = 0;
            return outFd != InValidHandle;
        }

        size_t StreamSys::Read(char* outBuffer, size_t inLength)
        {
            if (mHandle != InValidHandle)
                return (size_t)read(mHandle, outBuffer, (unsigned int)inLength);
            return 0;
        }

        size_t StreamSys::Write(const char* inBuffer, size_t inLength)
        {
            if (mHandle == InValidHandle)
                return 0;
            return (size_t)write(mHandle, inBuffer, (unsigned int)inLength);
        }

        size_t StreamSys::WriteHuge(const char* inSrc, size_t inLength)
        {
            if (mHandle == InValidHandle)
                return 0;
            size_t offset = 0;
            size_t wirte_size = std::min((size_t)HUGE_WD_SIZE, inLength - offset);
            while (wirte_size > 0)
            {
                auto succ_size = (size_t)write(mHandle, inSrc + offset, (unsigned int)wirte_size);
                if (succ_size != wirte_size)
                    break;
                offset += wirte_size;
                wirte_size = std::min((size_t)HUGE_WD_SIZE, inLength - offset);
            }
            return offset;
        }

        size_t OutputSys::Write(const char* inBuffer, size_t inLength)
        {
            if (inLength >= (size_t)HUGE_FILE_SIZ)
                return mStream->WriteHuge(inBuffer, inLength);
            else
                return mStream->Write(inBuffer, inLength);
        }
}}}

// for Unity Build, control define scope
#undef commit
#undef write
#undef close
#undef lseek
#undef read
