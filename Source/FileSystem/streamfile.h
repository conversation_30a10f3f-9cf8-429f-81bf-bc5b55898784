#ifndef CROSS_ENGINE_STREAMFILE_H
#define CROSS_ENGINE_STREAMFILE_H
#pragma warning(push)
#pragma warning(disable : 4100)
#include "ifile.h"
#include "filesystem_io.h"

namespace cross
{
    namespace filesystem
    {
        class StreamFile : public IFile
        {
        public:
            StreamFile() = default;
            virtual ~StreamFile() = default;
            virtual bool                  IsStream() const override { return true; }
            virtual bool                  Remap(UInt64 offset, size_t mappedBytes) override { return false; }
            virtual size_t                Read(char* outBuffer, size_t inLength) override;
            virtual size_t                Tell() const override;
            virtual SInt64                Seek(int inOffset, SeekOption inWhence) override;
            virtual bool                  IsEof() const override;
            virtual bool                  Close() override;
            virtual bool                  IsClosed() const override;
            virtual size_t                GetSize() const override;
            virtual char*                 GetBuffer() override;
            virtual std::shared_ptr<char> GetSharedBuffer() const override;
            virtual const std::string&    GetPathName() const override;
            virtual void                  SetPathName(const std::string& inPath) override;
            virtual bool                  GetFileDesc(int& outFd, size_t& outOffset) const override;
            virtual const std::string&    GetLoaderRootPath() const override;
            virtual void                  SetLoaderRootPath(const std::string& inRootPath) override;

            const io::InputPtr&           GetRealFile() const;
            void                          SetRealFile(const io::InputPtr& inRealFile, size_t inBegin, size_t inSize);
            void                          SetRealFile(const io::InputPtr& inRealFile) { SetRealFile(inRealFile, 0, 0);}
            
            
        private:
            size_t       mSize           = 0;
            size_t       mBegin          = 0;
            SInt64       mCurrentPos     = 0;            
            std::string  mPath           = "";
            std::string  mLoaderRootPath = "";
            io::InputPtr mRealFile       = nullptr;
        };
    
        using StreamFilePtr = std::shared_ptr<StreamFile>;
    }
}   // namespace cross
#pragma warning(pop)
#endif//CROSS_ENGINE_STREAMFILE_H
