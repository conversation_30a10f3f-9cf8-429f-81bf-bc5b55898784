#ifndef __FILESYSTEM_H__
#define __FILESYSTEM_H__

#include "ifile.h"
#include "filesystem_io.h"
#include <array>

#include "CEMetaMacros.h"
#include "discretefileloader.h"
#include "zippackage.h"
#include "filesystemutil.h"


namespace cross
{
    namespace filesystem
    {
        constexpr int FILESYSTEM_MAX_PATH = 260;
        constexpr int MaxLoaders = 8;
        class FILESYSTEM_API FileSystem : public IFileSystem
        {
        public:
            FileSystem();
            virtual ~FileSystem() {}

            // IFileSystem stuff start
            virtual IFilePtr            Open(const std::string& inPathName, bool isStreaming = false) override;
            virtual IFile*              MemoryMapping(const std::string& inPathName, UInt64 mappedBytes, CacheHint hint) override;
            virtual bool                ValidateFile(const std::string& inPathName) const override;
            //for index file loader
            virtual bool                Invalidate(const std::string& inPathName) const override;
            //for index file loader
            virtual UInt32              ValidateDir(const std::string& inPathName, bool inRecursize = false) const override;
            virtual bool                HaveFile(const std::string& inPathName) const override;
            virtual std::string         GetAbsoluteBackPath(const std::string& inPathName) const override;
            virtual bool                GetAbsolutePath(const std::string& inPathName, std::string& outFullPath) const override;
            virtual std::string         GetAbsolutePath(const std::string& inPathName) const override;
            virtual std::string         GetRelativeBackPath(const std::string& inPathName) const override;
            virtual bool                GetRelativePath(const std::string& inPathName, std::string& outFullPath) const override;
            virtual std::string         GetRelativePath(const std::string& inPathName) const override;
            virtual bool                Save(const std::string& inPathName, const char* inData, UInt64 inSize) override;
            virtual UInt32              RemoveFile(const std::string& inFileName) override;
            virtual UInt32              RemoveDirectory(const std::string& inDirName) override;
            virtual time_t              GetTimeStamp(const std::string& inPathName) const override;
            virtual void                ReloadAll() override;
            virtual void                UnloadAll() override;
            virtual void                ReloadByIndex(UInt32 inIndex) override;
            virtual void                UnloadByIndex(UInt32 inIndex) override;
            virtual void                ReloadByRootPath(const std::string& inRootPath) override;
            virtual void                UnloadByRootPath(const std::string& inRootPath) override;
            virtual UInt32              GetLoaderCount() const override;
            virtual const std::string&  GetWorkDir() const override;
            virtual void                SetWorkDir(const std::string& inWorkDir) override;
            virtual const std::string&  GetDocDir() const override;
            virtual void                SetDocDir(const std::string& inDocDir) override;
            virtual bool                IsDirecory(const std::string& inPathName) const override;
            virtual bool                CreateDirectory(const std::string& inAbsPath) override;
            virtual const FileLoaderPtr GetLoaderByIndex(UInt32 inIndex) const override;
            virtual IFilePtr            OpenForWrite(const std::string& inPathName) override;
            virtual const std::string&  GetDefaultAssetPath() override { return mDefaultAssetPath ;}
            // IFileSystem stuff end

            virtual bool Init(const FileSystemConfig& inConfig) override;
            virtual bool InitSimple(const char* inPath) override;
            virtual bool ReplaceString(std::string& str, const std::string& from, const std::string& to) override;
            bool IsInited() const { return mIsInited; }
            
            using OpenerMaps = std::unordered_map<std::string, io::Opener*>;
        private:
			void ReloadPostProcess();

            std::array<FileLoaderPtr, MaxLoaders> mFileLoaders;
            //OpenerMaps                 mExistOpeners;
            std::string                mWorkDir  = "";
            std::string                mDocumentDir = "";
            bool                       mIsInited    = false;
            UInt32                      mLoaderCount = 0;                                     
            std::string                mDefaultAssetPath = "";
        };


        class FILESYSTEM_API CEMeta(Puerts) FileSystemHelper
        {
        public:
            CEFunction(ScriptCallable)
            static std::string ReadTextFile(const std::string& file_name);

            CEFunction(ScriptCallable)
            static bool Save(const std::string& inPathName, const std::string& inData);

            CEFunction(ScriptCallable)
            static bool HaveFile(const std::string& inPathName);

            CEFunction(ScriptCallable)
            static std::string GetAbsolutePath(const std::string& inPathName);

            CEFunction(ScriptCallable)
            static std::string GetRelativePath(const std::string& inPathName);

            // TODO: The function below seems to have type conversion problem in its TypeScript generated version in NDK compilation
            // Only enable this function in Windows build for now
#if WIN32
            CEFunction(ScriptCallable)
            static UInt64 GetTimeStamp(const std::string& inPathName);
#endif
        };
    }
}

#endif//__FILESYSTEM_H__
