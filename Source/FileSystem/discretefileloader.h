#ifndef CROSS_ENGINE_DISCRETEFILELOADER_H
#define CROSS_ENGINE_DISCRETEFILELOADER_H

#include "fileloader.h"

namespace cross
{
    namespace filesystem
    {
        
        class DiscreteFileLoader : public FileLoader
        {
        public:
            DiscreteFileLoader() = default;
            virtual ~DiscreteFileLoader() = default ;
            virtual IFilePtr Open(const std::string& inVp, bool inTryStream) override;
            virtual IFile*  MemoryMapping(const std::string& inVp, UInt64 mappedBytes, CacheHint hint) override;
            virtual void     Reload() override;
            virtual void     Unload() override { mIsRootDirExist = false; }
            virtual bool     HaveFile(const std::string& inVp) const override;
            virtual bool     GetAbsolutePath(const std::string& inVp, std::string& outResult) const override;
            virtual bool     GetRelativePath(const std::string& inRealPath, std::string& outResult) const override;
            virtual time_t   GetTimeStamp(const std::string& inVp) const override;
            virtual bool     Save(const std::string& inVp, const char* data, UInt64 inSize) override;
            virtual bool     RemoveFile(const std::string& inVp) override;
            virtual bool     RemoveDirectory(const std::string& inVp) override;
            virtual bool     IsDiscreateLoader() const override { return true; }
            virtual void     SetKey(const KeyType& inKey) override;
            virtual IFilePtr OpenForWrite(const std::string& inVp) override;
        protected:
            virtual bool    DoInit(io::Opener* inOpener, std::string& inRootPath , UInt32 inDepth) override;
            virtual std::string GetRealPath(const std::string& inVp) const;
        private:
            std::string GetDirPathFromFile(const std::string& inFilePath);
            bool mIsRootDirExist = false;
        };

        class DiscreateFileLoaderCreator : public FileLoaderCreator
        {
        public:
            static DiscreateFileLoaderCreator& Instance();
            virtual const std::string& Tag() const override { return mTag; }
        protected:
            FileLoaderPtr CreateNewFileLoader() override { return std::make_shared<DiscreteFileLoader>(); }
        private:            
            std::string mTag = "discrete";

        };
    }
}

#endif//CROSS_ENGINE_DISCRETEFILELOADER_H
