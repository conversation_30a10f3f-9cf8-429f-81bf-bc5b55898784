#include "pch.h"
#include "QudricSimplifierMeshReductionUsePtr.h"
#include <fstream>
#include <sstream>
#include "Color.h"
#include "MeshUtilities.h"
#include "OverlappingCorners.h"
#include "CustomUtil.h"

QudricSimplifierMeshReductionUsePtr::QudricSimplifierMeshReductionUsePtr(/* args */)
{
}

QudricSimplifierMeshReductionUsePtr::~QudricSimplifierMeshReductionUsePtr()
{
}

template< uint32 NumTexCoords >
class TVertSimp
{
    typedef TVertSimp< NumTexCoords > VertType;
public:
    float           VertexWeight;
    uint32			MaterialIndex;
    UE::FVector			Position;
    UE::FVector			Normal;
    UE::FVector			Tangents[2];
    FLinearColor    Color;
    FVector2D		TexCoords[NumTexCoords];
    float			GetVertexWeight() const { return VertexWeight; }
    void			SetVertexWeight(float vertexWeight) { VertexWeight = vertexWeight; }
    uint32			GetMaterialIndex() const { return MaterialIndex; }
    UE::FVector& GetPos() { return Position; }
    const UE::FVector& GetPos() const { return Position; }
    float* GetAttributes() { return (float*)&Normal; }
    const float* GetAttributes() const { return (const float*)&Normal; }

    void		Correct()
    {
        Normal.Normalize();
        Tangents[0] -= (Tangents[0] * Normal) * Normal;
        Tangents[0].Normalize();
        Tangents[1] -= (Tangents[1] * Normal) * Normal;
        Tangents[1] -= (Tangents[1] * Tangents[0]) * Tangents[0];
        Tangents[1].Normalize();
        Color = Color.GetClamped();
    }

    bool		Equals(const VertType& a) const
    {
        if (MaterialIndex != a.MaterialIndex ||
            !PointsEqual(Position, a.Position) ||
            !NormalsEqual(Normal, a.Normal))
        {
            return false;
        }

        // UVs
        for (int32 UVIndex = 0; UVIndex < NumTexCoords; UVIndex++)
        {
            if (!UVsEqual(TexCoords[UVIndex], a.TexCoords[UVIndex]))
            {
                return false;
            }
        }

        return true;
    }

    bool		operator==(const VertType& a) const
    {
        if (MaterialIndex != a.MaterialIndex ||
            Position != a.Position ||
            Normal != a.Normal)
        {
            return false;
        }

        for (uint32 i = 0; i < NumTexCoords; i++)
        {
            if (TexCoords[i] != a.TexCoords[i])
            {
                return false;
            }
        }
        return true;
    }

    VertType	operator+(const VertType& a) const
    {
        VertType v;
        v.MaterialIndex = MaterialIndex;
        v.Position = Position + a.Position;
        v.Normal = Normal + a.Normal;

        for (uint32 i = 0; i < NumTexCoords; i++)
        {
            v.TexCoords[i] = TexCoords[i] + a.TexCoords[i];
        }
        return v;
    }

    VertType	operator-(const VertType& a) const
    {
        VertType v;
        v.MaterialIndex = MaterialIndex;
        v.Position = Position - a.Position;
        v.Normal = Normal - a.Normal;

        for (uint32 i = 0; i < NumTexCoords; i++)
        {
            v.TexCoords[i] = TexCoords[i] - a.TexCoords[i];
        }
        return v;
    }

    VertType	operator*(const float a) const
    {
        VertType v;
        v.MaterialIndex = MaterialIndex;
        v.Position = Position * a;
        v.Normal = Normal * a;

        for (uint32 i = 0; i < NumTexCoords; i++)
        {
            v.TexCoords[i] = TexCoords[i] * a;
        }
        return v;
    }

    VertType	operator/(const float a) const
    {
        float ia = 1.0f / a;
        return (*this) * ia;
    }
};

const int PositionOffset = 0;
const int NormalOffset = 3;
const int TangentOffset = 6;
const int BiTangentOffset = 9;
const int ColorOffset = 12;
const int UV1Offset = 16;
const int UV2Offset = 18;


static void CheckAndComputeTangents(
    float* srcVertex,
    int vertexCount,
    int* srcIndices,
    int indicesCount,
    const int attributesCount,
    int UVChannel)
{
    std::vector<UE::FVector> InVertices;
    std::vector<uint32> InIndices(srcIndices, srcIndices + 3 * indicesCount);
    std::vector<FVector2D> InUVs;
    std::vector<uint32> SmoothingGroupIndices;
    std::vector<UE::FVector> OutTangentX;
    std::vector<UE::FVector> OutTangentY;
    std::vector<UE::FVector> OutTangentZ;
    
    ExtractVertexAttribute(srcVertex, vertexCount, attributesCount, UVChannel, &InVertices, &InUVs, &OutTangentX, &OutTangentY, &OutTangentZ, nullptr, nullptr);

    //todo 光滑组
    SmoothingGroupIndices.resize(indicesCount, 0);
    /*InVertices.resize(vertexCount);
    memcpy(&InVertices[0], srcVertex, vertexCount * 3 * sizeof(float));*/

    //InIndices.resize(indicesCount);
    //memcpy(&InIndices[0], srcIndices, indicesCount * sizeof(int));



    //float THRESH_POINTS_ARE_SAME = 0.00002f;
    float ComparisonThreshold = 0.0;
    const FOverlappingCorners& OutOverlappingCorners = FOverlappingCorners(InVertices, InIndices, ComparisonThreshold);
    ComputeTangents(InVertices, InIndices, InUVs, SmoothingGroupIndices, OutOverlappingCorners, OutTangentX, OutTangentY, OutTangentZ, 0);

    //将数据转写回原来的数据
    //先实现 再优化
    for (int i = 0; i < vertexCount; i++)
    {
        int VertexAttribteStartIndex = i * attributesCount;
        int TargetPositionFloatStartOffset = VertexAttribteStartIndex + PositionOffset;
        int TargetNormalFloatStartOffset = VertexAttribteStartIndex + NormalOffset;
        int TargetTangentFloatStartOffset = VertexAttribteStartIndex + TangentOffset;
        int TargetBiTangentFloatStartOffset = VertexAttribteStartIndex + BiTangentOffset;
        int TargetColorFloatStartOffset = VertexAttribteStartIndex + ColorOffset;
        int TargetUVFloatStartOffset = VertexAttribteStartIndex + UV1Offset;

        srcVertex[TargetTangentFloatStartOffset] = OutTangentX[i].X;
        srcVertex[TargetTangentFloatStartOffset+1] = OutTangentX[i].Y;
        srcVertex[TargetTangentFloatStartOffset+2] = OutTangentX[i].Z;

        srcVertex[TargetNormalFloatStartOffset] = OutTangentZ[i].X;
        srcVertex[TargetNormalFloatStartOffset + 1] = OutTangentZ[i].Y;
        srcVertex[TargetNormalFloatStartOffset + 2] = OutTangentZ[i].Z;

        srcVertex[TargetBiTangentFloatStartOffset] = OutTangentY[i].X;
        srcVertex[TargetBiTangentFloatStartOffset + 1] = OutTangentY[i].Y;
        srcVertex[TargetBiTangentFloatStartOffset + 2] = OutTangentY[i].Z;
    }

}


void QudricSimplifierMeshReductionUsePtr::ReduceMeshDescription(
    float* srcVertex,
    int vertexCount,
    int* srcIndices,
    int indicesCount,
    int* materialGroup,
    float * FaceWeight,
    int* newVertexCount,
    int* newIndicesCount,
    float PercentTriangles,
    const int attributesCount,
    float PercentVertices,
    int UVChannel)
{

    const int allAttributesNum = attributesCount;
    Verts.clear();
    Indexes.clear();
    MaterialIndexes.clear();
    VertexWeight.clear();
    VertexCount.clear();
    VertsMap.clear();
    float SurfaceArea = 0.0f;
    int32 WedgeIndex = 0;
    

    CheckAndComputeTangents(srcVertex, vertexCount, srcIndices, indicesCount, attributesCount, UVChannel);

    //传入数据根据材料分组
    //重复点剔除,相应面的映射
    
    for (int i = 0; i < indicesCount * 3; i += 3)
    {
        for (int TriVert = 0; TriVert < 3; TriVert++)
        {
            std::vector<float>newVert;
            newVert.clear();
            int triIndex = srcIndices[i + TriVert];
           /* for (int j = 0; j < allAttributesNum; j++)
                newVert.push_back(srcVertex[triIndex * allAttributesNum + j]);*/
           std:: string newPosition = "";
            
            int dupindex = -1;
            for (int j = 0; j < allAttributesNum; j++)
                newPosition += std::to_string(srcVertex[triIndex * allAttributesNum + j]);
            auto it = VertsMap.find(newPosition);
            if (it != VertsMap.end())
            {
                dupindex = it->second;
            }
            //dupindex = vertsEqual(Verts, newVert, allAttributesNum);
            if (dupindex == -1)
            {
                srcIndices[i + TriVert] = Verts.size() / allAttributesNum;
                VertexWeight.push_back(FaceWeight[i/3]);
                VertexCount.push_back(1.0);
                VertsMap.insert(make_pair(newPosition
                        , Verts.size() / allAttributesNum));
                for (int j = 0; j < allAttributesNum; j++)
                    Verts.push_back(srcVertex[triIndex * allAttributesNum + j]);
                MaterialIndexes.push_back(materialGroup[i / 3]);
            }
            else
            {
                VertexCount[dupindex]+=1.0;
                VertexWeight[dupindex] += FaceWeight[i/3];
                srcIndices[i + TriVert] = dupindex;
            }

        }

        if (srcIndices[i] == srcIndices[i + 1] ||
            srcIndices[i + 1] == srcIndices[i + 2] ||
            srcIndices[i] == srcIndices[i + 2])
        {
            continue;
        }
        
        int firstCornerIndex = srcIndices[i];
        int secondCornerIndex = srcIndices[i + 1];
        int thirdCornerIndex = srcIndices[i + 2];

        Indexes.push_back(firstCornerIndex);
        Indexes.push_back(secondCornerIndex);
        Indexes.push_back(thirdCornerIndex);
        {
            int firstCornerFloatStartIndex = firstCornerIndex * allAttributesNum;
            int secondCornerFloatStartIndex = secondCornerIndex * allAttributesNum;
            int thirdCornerFloatStartIndex = thirdCornerIndex * allAttributesNum;
            FVector3f Edge01 = FVector3f(Verts[secondCornerFloatStartIndex],
                Verts[secondCornerFloatStartIndex + 1],
                Verts[secondCornerFloatStartIndex + 2]) -
                FVector3f(Verts[firstCornerFloatStartIndex],
                    Verts[firstCornerFloatStartIndex + 1],
                    Verts[firstCornerFloatStartIndex + 2]);

            FVector3f Edge20 = FVector3f(Verts[firstCornerFloatStartIndex],
                Verts[firstCornerFloatStartIndex + 1],
                Verts[firstCornerFloatStartIndex + 2]) -
                FVector3f(Verts[thirdCornerFloatStartIndex],
                    Verts[thirdCornerFloatStartIndex + 1],
                    Verts[thirdCornerFloatStartIndex + 2]);

            float TriArea = 0.5f * (Edge01 ^ Edge20).Size();
            SurfaceArea += TriArea;
        }

    }
    
   /* for (int i = 0; i < VertexWeight.size(); i++) 
    {
        VertexWeight[i] /= VertexCount[i];
    }*/
    const uint32 NumAttributes = allAttributesNum - 3;
    uint32 NumVerts = Verts.size() / allAttributesNum;
    uint32 NumIndexes = Indexes.size();
    uint32 NumTris = NumIndexes / 3;
    uint32 TargetNumTris = ceil(NumTris * PercentTriangles);
    //uint32 TargetNumVerts = ceil(NumVerts * PercentTriangles);
    uint32 TargetNumVerts = ceil(NumVerts * PercentVertices);
    if (TargetNumVerts >= NumVerts && TargetNumTris >= NumTris)
    {
        std::cout << "TargetNumVerts >= NumVerts && TargetNumTris >= NumTris" << std::endl;
        return;
    }
    
   
    //模型的处理,以及各种属性的缩放
    float TriangleSize = sqrt(SurfaceArea / NumTris);
    float CurrentSize(Fmath::Max(TriangleSize, THRESH_POINTS_ARE_SAME));
    float DesiredSize(0.25f);
    float Scale(1.0f);
    DesiredSize = DesiredSize / TriangleSize;
    float PositionScale = DesiredSize;
    //position归一化
    
    for (int i = 0; i < NumVerts; i++)
    {
        Verts[i * allAttributesNum] *= PositionScale;
        Verts[i * allAttributesNum + 1] *= PositionScale;
        Verts[i * allAttributesNum + 2] *= PositionScale;
    }

    //法线归一化
    float AttributeWeights[] =
    {
        16.0f, 16.0f, 16.0f,	// Normal
        0.1f, 0.1f, 0.1f,		// Tangent[0]
        0.1f, 0.1f, 0.1f,		// Tangent[1]
        0.1f, 0.1f, 0.1f, 0.1f,	// Color
        0.5f, 0.5f,				// TexCoord[0]
        0.5f, 0.5f,				// TexCoord[1]
    };
    float* ColorWeights = AttributeWeights + 3 + 3 + 3;
    float* TexCoordWeights = ColorWeights + 4;

    for (int i = 0; i < NumVerts; i++)
    {
        float dis = pow(Verts[i * allAttributesNum + 3], 2) + pow(Verts[i * allAttributesNum + 4], 2) + pow(Verts[i * allAttributesNum + 5], 2);
        if (dis > 0.000001)
        {
            dis = 1 / sqrt(dis);
            Verts[i * allAttributesNum + 3] *= dis;
            Verts[i * allAttributesNum + 4] *= dis;
            Verts[i * allAttributesNum + 5] *= dis;
        }
    }
    //UV权重设置
    float UVWeight = 0.5f;
    for (int32 UVIndex = 0; UVIndex < UVChannel; UVIndex++)
    {
        // Normalize UVWeights using min/max UV range.

        float XMin = +FLT_MAX;
        float XMax = -FLT_MAX;
        float YMin = +FLT_MAX;
        float YMax = -FLT_MAX;

        for (int32 VertexIndex = 0; VertexIndex < NumVerts; VertexIndex++)
        {
            int attributeUVXindex = VertexIndex * allAttributesNum + 3 + 3 + UVIndex * 2;
            int attributeUVYindex = VertexIndex * allAttributesNum + 3 + 3 + UVIndex * 2 + 1;
            XMin = Fmath::Min(XMin, Verts[attributeUVXindex]);
            XMax = Fmath::Max(XMax, Verts[attributeUVXindex]);
            YMin = Fmath::Min(YMin, Verts[attributeUVYindex]);
            YMax = Fmath::Max(YMax, Verts[attributeUVYindex]);
        }
        float XLength = (XMax > XMin) ? XMax - XMin : 0.f;
        float YLength = (YMax > YMin) ? YMax - YMin : 0.f;
        if(XLength > 1.f)
        {
            TexCoordWeights[2 * UVIndex + 0] /= XLength;
        }
        if (YLength > 1.f)
        {
            TexCoordWeights[2 * UVIndex + 1] /= YLength;
        }
    }
    
    std::vector<TVertSimp< NumTexCoords > >	UE4_Verts;
    for (int i = 0; i < NumVerts; i++)
    {
        TVertSimp< NumTexCoords > NewVert;
        NewVert.MaterialIndex = MaterialIndexes[i];
        int VertexAttribteStartIndex = i * allAttributesNum;
        int TargetPositionFloatStartOffset = VertexAttribteStartIndex + PositionOffset;
        int TargetNormalFloatStartOffset = VertexAttribteStartIndex + NormalOffset;
        int TargetTangentFloatStartOffset = VertexAttribteStartIndex + TangentOffset;
        int TargetBiTangentFloatStartOffset = VertexAttribteStartIndex + BiTangentOffset;
        int TargetColorFloatStartOffset = VertexAttribteStartIndex + ColorOffset;
        int TargetUVFloatStartOffset = VertexAttribteStartIndex + UV1Offset;

        NewVert.Position = UE::FVector(Verts[TargetPositionFloatStartOffset], Verts[TargetPositionFloatStartOffset + 1], Verts[TargetPositionFloatStartOffset + 2]);
        NewVert.Normal = UE::FVector(Verts[TargetNormalFloatStartOffset], Verts[TargetNormalFloatStartOffset + 1], Verts[TargetNormalFloatStartOffset + 2]);
        NewVert.Tangents[0] = UE::FVector(Verts[TargetTangentFloatStartOffset], Verts[TargetTangentFloatStartOffset + 1], Verts[TargetTangentFloatStartOffset + 2]);
        NewVert.Tangents[1] = UE::FVector(Verts[TargetBiTangentFloatStartOffset], Verts[TargetBiTangentFloatStartOffset + 1], Verts[TargetBiTangentFloatStartOffset + 2]);
        // Fix bad tangents
        //FVector ZeroVector = UE::FVector(0.0, 0.0, 0.0);
        NewVert.Tangents[0] = NewVert.Tangents[0].ContainsNaN() ? UE::FVector::ZeroVector : NewVert.Tangents[0];
        NewVert.Tangents[1] = NewVert.Tangents[1].ContainsNaN() ? UE::FVector::ZeroVector : NewVert.Tangents[1];
        NewVert.Normal = NewVert.Normal.ContainsNaN() ? UE::FVector::ZeroVector : NewVert.Normal;

        NewVert.Color = FLinearColor(Verts[TargetColorFloatStartOffset], Verts[TargetColorFloatStartOffset + 1],
            Verts[TargetColorFloatStartOffset + 2], Verts[TargetColorFloatStartOffset + 3]);

        NewVert.VertexWeight = VertexWeight[i];
        for (int UVIndex = 0; UVIndex < NumTexCoords; UVIndex++)
        {
            NewVert.TexCoords[UVIndex] = FVector2D(Verts[TargetUVFloatStartOffset + UVIndex * 2],
                Verts[TargetUVFloatStartOffset + UVIndex * 2 + 1]);
        }
        NewVert.Correct();
        UE4_Verts.push_back(NewVert);
    }
    //根据实际的情况而定，除去position 之外的大小
    const int without_pos_attribute_num = 17;
    TMeshSimplifier< TVertSimp< NumTexCoords >, without_pos_attribute_num >* MeshSimp = new TMeshSimplifier< TVertSimp< NumTexCoords >, without_pos_attribute_num >(UE4_Verts.data(), (int)(Verts.size() / (NumAttributes + 3)), Indexes.data(), Indexes.size(), FaceWeight);

    MeshSimp->SetAttributeWeights(AttributeWeights);
    //MeshSimp->SetBoundaryLocked();
    MeshSimp->InitCosts();

    //We need a minimum of 2 triangles, to see the object on both side. If we use one, we will end up with zero triangle when we will remove a shared edge
    int32 AbsoluteMinTris = 2;

    float MaxErrorSqr = MeshSimp->SimplifyMesh(MAX_FLT, TargetNumTris, TargetNumVerts);
    /*NumIndexes = MeshSimp->GetNumTris();
    NumVerts = MeshSimp->GetNumVerts();*/
    //std::cout << MeshSimp->GetNumVerts() << " " << MeshSimp->GetNumTris() << std::endl;
    MeshSimp->OutputMesh(UE4_Verts.data(), Indexes.data(), &NumVerts, &NumIndexes);
    delete MeshSimp;
    //NumTris = NumIndexes / 3;
    //Simplifier.printVertexAndIndices(1.0/PositionScale);
    PositionScale = 1 / PositionScale;
    (*newIndicesCount) = NumIndexes / 3;
    (*newVertexCount) = NumVerts;
    Verts.clear();
    for (uint32 i = 0; i < NumVerts; i += 1)
    {
        UE4_Verts[i].Position *= PositionScale;
       
        Verts.push_back(UE4_Verts[i].Position.X);
        Verts.push_back(UE4_Verts[i].Position.Y);
        Verts.push_back(UE4_Verts[i].Position.Z);
        float* allAttributePtr = UE4_Verts[i].GetAttributes();
        for (int j = 0; j < NumAttributes; j++)
            Verts.push_back(*(allAttributePtr+j));
    }
    MaterialIndexes.clear();
    
    for (uint32 i = 0; i < NumIndexes / 3; i++)
    {
        MaterialIndexes.push_back(UE4_Verts[Indexes[i * 3]].MaterialIndex);
    }
   
    //顶点缩放恢复
    //PositionScale = 1 / PositionScale;
    /*for (int i = 0; i < (*newVertexCount); i++)
    {
        Verts[i * allAttributesNum] *= PositionScale;
        Verts[i * allAttributesNum + 1] *= PositionScale;
        Verts[i * allAttributesNum + 2] *= PositionScale;
    }*/
    //Simplifier.printVertexAndIndices(1.0);

}
int* QudricSimplifierMeshReductionUsePtr::GetIndices()
{
    return (int*)Indexes.data();
}
float* QudricSimplifierMeshReductionUsePtr::GetVert()
{
    return (float*)Verts.data();
}
int* QudricSimplifierMeshReductionUsePtr::GetMaterialGroup()
{
    return (int*)MaterialIndexes.data();
}
