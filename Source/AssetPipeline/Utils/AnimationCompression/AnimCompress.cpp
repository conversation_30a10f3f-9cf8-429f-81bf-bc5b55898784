#include "AnimCompress.h"

namespace cross::editor {
void CompressAnimation_Lkr(AnimationDesc& anim, AnimCompressionSetting const& setting)
{
    for (UInt32 boneIdx = 0; boneIdx < anim.TracksForAllBones.size(); ++boneIdx)
    {
        auto& curTrack = anim.TracksForAllBones[boneIdx];
        auto redundantTranslationKeys = animcompress::LinearKeyReduction(curTrack.TranslationKeys.begin(),
                                                                         curTrack.TranslationKeys.end(),
                                                                         setting.TranslationThreshold,
                                                                         AnimationDesc::GetKeyTime<AnimationDesc::TranslationKey>,
                                                                         AnimationDesc::InterpTranslationKey,
                                                                         AnimationDesc::TranslationKeyDistance);
        animcompress::EraseRedundantAnimPropertyKeys(curTrack.TranslationKeys, redundantTranslationKeys);

        auto redundantRotationKeys = animcompress::LinearKeyReduction(
            curTrack.RotationKeys.begin(), curTrack.RotationKeys.end(), setting.RotationThreshold, AnimationDesc::GetKeyTime<AnimationDesc::RotationKey>, AnimationDesc::InterpRotationKey, AnimationDesc::RotationKeyDistance);
        animcompress::EraseRedundantAnimPropertyKeys(curTrack.RotationKeys, redundantRotationKeys);

        auto redundantScaleKeys =
            animcompress::LinearKeyReduction(curTrack.ScaleKeys.begin(), curTrack.ScaleKeys.end(), setting.ScaleThreshold, AnimationDesc::GetKeyTime<AnimationDesc::ScaleKey>, AnimationDesc::InterpScaleKey, AnimationDesc::ScaleKeyDistance);
        animcompress::EraseRedundantAnimPropertyKeys(curTrack.ScaleKeys, redundantScaleKeys);
    }
}

acl::compressed_tracks* CompressAnimation_Acl(acl::iallocator& allocator, const AnimationDesc& rawAnim, const SkeletonDesc& skeleton)
{
    const auto frameNum = rawAnim.GetFrameCount();
    const UInt32 boneTrackNum = static_cast<UInt32>(rawAnim.TracksForAllBones.size());
    /*
     * Create acl raw animation track_array
     */
    acl::track_array_qvvf aclRawTrackList(allocator, boneTrackNum);
    for (UInt32 boneIdx = 0; boneIdx < boneTrackNum; ++boneIdx)
    {
        const auto& curBoneTranslateKeys = rawAnim.TracksForAllBones[boneIdx].TranslationKeys;
        const auto& curBoneRotationKeys = rawAnim.TracksForAllBones[boneIdx].RotationKeys;
        const auto& curBoneScaleKeys = rawAnim.TracksForAllBones[boneIdx].ScaleKeys;

        acl::track_desc_transformf aclTrackDesc;
        aclTrackDesc.output_index = boneIdx;
        aclTrackDesc.parent_index = (boneIdx == 0) ? SK_BONE_INDEX_NONE : skeleton.Bones[boneIdx].ParentID;

        /*
         * Create acl track for current anim bone track
         */
        acl::track_qvvf aclBoneTrack = acl::track_qvvf::make_reserve(aclTrackDesc, allocator, frameNum, AnimationDesc::DefaultSampleRate());
        for (UInt32 frameIdx = 0; frameIdx < frameNum; ++frameIdx)
        {
            const auto& trans = !curBoneTranslateKeys.empty() ? curBoneTranslateKeys[frameIdx] : AnimationDesc::TranslationKey::Zero();
            const auto& rot = !curBoneRotationKeys.empty() ? curBoneRotationKeys[frameIdx] : AnimationDesc::RotationKey::Zero();
            const auto& scale = !curBoneScaleKeys.empty() ? curBoneScaleKeys[frameIdx] : AnimationDesc::ScaleKey::One();

            aclBoneTrack[frameIdx].translation = rtm::vector_set(trans.Translation.x, trans.Translation.y, trans.Translation.z);

            aclBoneTrack[frameIdx].rotation = rtm::quat_set(rot.Rotation.x, rot.Rotation.y, rot.Rotation.z, rot.Rotation.w);

            aclBoneTrack[frameIdx].scale = rtm::vector_set(scale.Scale.x, scale.Scale.y, scale.Scale.z);
        }

        aclRawTrackList[boneIdx] = std::move(aclBoneTrack);
    }

    /*
     * Create acl compression_settings
     */
    acl::compression_settings settings = acl::get_default_compression_settings();

    /*
    *   Set error metric manually.
    */
    acl::qvvf_transform_error_metric errorMetric;
    settings.error_metric = &errorMetric;

    /*
     * Compress animation
     */
    acl::output_stats outStatus;
    acl::compressed_tracks* outCompressedTracks = nullptr;
    acl::error_result errorResult = acl::compress_track_list(allocator, aclRawTrackList, settings, outCompressedTracks, outStatus);

    Assert(errorResult.empty() && errorResult.c_str());
    Assert(outCompressedTracks->is_valid(true).empty() && "Compressed clip is invalid");

    return outCompressedTracks;
}

float CompressAnimation_CheckAclError(acl::iallocator& allocator, acl::compressed_tracks* pCompressedAclAnim, const AnimationDesc& rawAnim)
{
    const float duration = rawAnim.Duration;
    const float sampleRate = AnimationDesc::DefaultSampleRate();
    const auto frameNum = rawAnim.GetFrameCount();

    // Initialize compress context
    animcompress::DecompressContext decprContext;
    decprContext.initialize(*pCompressedAclAnim);

    // alloc decompressed memory by bones num
    UInt32 boneNum = static_cast<UInt32>(rawAnim.TracksForAllBones.size());
    std::vector<Float4x4> rawPoseTransforms;
    rawPoseTransforms.resize(boneNum);

    animcompress::DecompressTransform* cprPoseTransforms = acl::allocate_type_array<animcompress::DecompressTransform>(allocator, boneNum);
    animcompress::MyTrackWriter cprPoseWriter(cprPoseTransforms, boneNum);

    // Check compressed animation though frames
    float vtxError = 0.0f;
    for (UInt32 frameIndex = 0; frameIndex < frameNum; ++frameIndex)
    {
        const float sampleTime = (std::min)(frameIndex / sampleRate, duration);

        // Grab raw animation though bones
        for (UInt32 boneIdx = 0; boneIdx < boneNum; ++boneIdx)
        {
            const auto& rot = !rawAnim.TracksForAllBones[boneIdx].RotationKeys.empty() ? rawAnim.TracksForAllBones[boneIdx].RotationKeys[frameIndex].Rotation : AnimationDesc::RotationKey::Zero().Rotation;
            const auto& trans = !rawAnim.TracksForAllBones[boneIdx].TranslationKeys.empty() ? rawAnim.TracksForAllBones[boneIdx].TranslationKeys[frameIndex].Translation : AnimationDesc::TranslationKey::Zero().Translation;
            const auto& scale = !rawAnim.TracksForAllBones[boneIdx].ScaleKeys.empty() ? rawAnim.TracksForAllBones[boneIdx].ScaleKeys[frameIndex].Scale : AnimationDesc::ScaleKey::One().Scale;

            rawPoseTransforms[boneIdx] = Float4x4::Compose(scale, rot, trans);
        }

        // Grab compressed animation though bones
        decprContext.seek(sampleTime, acl::sample_rounding_policy::nearest);
        decprContext.decompress_tracks(cprPoseWriter);

        // Compare
        for (UInt32 boneIdx = 0; boneIdx < boneNum; ++boneIdx)
        {
            // Grab compressed local matrix
            rtm::matrix3x4f cprObjMatrix = rtm::matrix_from_qvv(cprPoseTransforms[boneIdx]);
            rtm::vector4f aclVtx = rtm::vector_set(1.0f, 1.0f, 1.0f, 1.0f);
            aclVtx = rtm::matrix_mul_vector3(aclVtx, cprObjMatrix);

            // Grab raw local matrix
            Float4 rawVtx = Float4(1.0f, 1.0f, 1.0f, 1.0f);
            rawVtx = Float4x4::Transform(rawPoseTransforms[boneIdx], rawVtx);

            auto pData = reinterpret_cast<float*>(&aclVtx);
            Float3 cprVec3(pData[0], pData[1], pData[2]);
            Float3 rawVec3(rawVtx.x, rawVtx.y, rawVtx.z);
            float curVtxError = (rawVec3 - cprVec3).Length();

            vtxError = (std::max)(curVtxError, vtxError);
        }
    }

    acl::deallocate_type_array(allocator, cprPoseTransforms, boneNum);
    return vtxError;
}

}   // namespace cross::editor
