#pragma once
#include "CrossBase/Math/CrossMath.h"

namespace cross
{
    namespace AssetMath
    {
#if 1
        using Vector2f = cross::Float2;
        using Rangef = cross::Float2;
        using Vector3f = cross::Float3;
        using Vector4f = cross::Float4;
        using Quaternionf = cross::Quaternion;
        using Matrix4x4f =  cross::Float4x4;
	    using Vector4i = cross::Int4;
    
        //using Vec2f = data::Vec2f;
        //using Vec3f = data::Vec3f;
        //using Vec4f = data::Vec4f;
        //using Vec4i = data::Vec4i;
        //using AngleAxisf    = Eigen::AngleAxisf;
        //using Transform3f   = Eigen::Affine3f;
        //using Transform2f   = Eigen::Affine2f;
        //using Translation3f = Eigen::Translation3f;
        //using Translation2f = Eigen::Translation2f;
        //using Scale3f       = Eigen::AlignedScaling3f;
#endif
#if 0
        //using Quaternionf = Eigen::Quaternionf;
        //using Matrix4x4f = Eigen::Matrix4f;
        using Matrix3x3f = Eigen::Matrix3f;
        using Vector4i = Eigen::Vector4i;

        using AngleAxisf = Eigen::AngleAxisf;
        using Transform3f = Eigen::Affine3f;
        using Transform2f = Eigen::Affine2f;
        using Translation3f = Eigen::Translation3f;
        using Translation2f = Eigen::Translation2f;
        using Scale3f = Eigen::AlignedScaling3f;

        struct Vec2f;
        struct Vec3f;
        struct Vec4f;

        using Vector2f = Vec2f;
        using Rangef = Vec2f;
        using Vector3f = Vec3f;
        using Vector4f = Vec4f;



        struct Vec2f : public Float2
        {
        public:
            Vec2f() {}
            explicit Vec2f(float constant) : Vec2f(constant, constant) {}
            Vec2f(Vec2f const& v) : Vec2f(v.x(), v.y()) {}
            Vec2f(float _x, float _y) 
            { 
                Float2::x = _x;
                Float2::y = _y;
            }
            
            float x() const noexcept { return Float2::x; }
            float& x() noexcept { return Float2::x; }
            float y() const noexcept { return Float2::y; }
            float& y() noexcept { return Float2::y; }
            float const* Data() const noexcept { return (const float*)&(Float2::x); }

            static inline Vec2f Zero()
            {
                return Vec2f(0.0f, 0.0f);
            }

            static inline Vec2f Ones()
            {
                return Vec2f(1.0f, 1.0f);
            }

            inline float dot(const Vec2f& v) const
            {
                return (Float2::x * v.x() + Float2::y * v.y());
            }

            inline bool isApprox(const Vec2f& v, float uvEpsilon) const
            {
                Float2 c = *this;
                c = *this - v;
                return c.Length() < uvEpsilon;
            }
            inline bool isApprox(Vec2f& v, float uvEpsilon)
            {
                Float2 c = *this;
                c = *this - v;
                return c.Length() < uvEpsilon;
            }

            inline bool hasNaN() const
            {
                return Float2::IsNaN();
            }
            
        };
        
        struct Vec3f : public Float3
        {
        public:
            Vec3f() {}
            explicit Vec3f(float constant) : Vec3f(constant, constant, constant) {}
            Vec3f(Vec3f const& v) : Vec3f(v.x(), v.y(), v.z()) {}
            Vec3f(float _x, float _y,float _z) {
                Float3::x = _x;
                Float3::y = _y;
                Float3::z = _z;
            }
            Vec3f(Float3 const& v) : Vec3f(v.x, v.y, v.z) {}

            float x() const noexcept { return Float3::x; }
            float& x() noexcept { return Float3::x; }
            float y() const noexcept { return Float3::y; }
            float& y() noexcept { return Float3::y; }
            float z() const noexcept { return Float3::z; }
            float& z() noexcept { return Float3::z; }
            float const* Data() const noexcept { return (const float*)&(Float3::x); }
            static inline Vec3f Zero()
            {
                return Vec3f(0.0f, 0.0f, 0.0f);
            }

            static inline Vec3f Ones()
            {
                return Vec3f(1.0f, 1.0f, 1.0f);
            }

            inline float dot(const Vec3f& v) const
            {
                return (Float3::x * v.x() + Float3::y * v.y() + Float3::z * v.z());
            }

            inline void normalize()
            {
                Float3::Normalize();
            }

            inline float norm()
            {
                return sqrt(Float3::x*Float3::x+ Float3::y*Float3::y+ Float3::z*Float3::z);
            }
            Vec3f& operator= (const Vec3f& v)
            {
                Float3::x = v.x();
                Float3::y = v.y();
                Float3::z = v.z();
                return *this;
            }

            //operator Vector3f() { return (Vector3f)*this; }

            inline Vec3f cross(const Vec3f& v)
            {
                Float3 t(v.x(),v.y(),v.z());
                Float3 result = Float3::Cross(t);
                return Vec3f(result.x, result.y, result.z);
            }

            friend Vec3f operator- (const Vec3f& v1, const Vec3f& v2)
            {
                return Vec3f(v1.x() - v2.x(),v1.y() - v2.y(),v1.z() - v2.z());
            }

            Vec3f normalized()
            {
                return Vec3f(Float3::Normalized());
            }

            inline bool hasNaN() const
            {
                return (isnan(Float3::x) || isnan(Float3::y) || isnan(Float3::z));
            }

            float& operator[] (int i)   const
            {
                float* s = (float*)&(Float3::x);
                if (i > 2 && i<0)
                    assert(false);
                return s[i];  
            }
        };

        
        struct Vec4f : public Float4
        {
        public:
            Vec4f()  {}
            explicit Vec4f(float constant) : Vec4f(constant, constant, constant,constant) {}
            Vec4f(Vec4f const& v) : Vec4f(v.x(), v.y(), v.z(), v.w()) {}
            Vec4f(float _x, float _y,float _z,float _w) {
                Float4::x = _x;
                Float4::y = _y;
                Float4::z = _z;
                Float4::w = _w;
            }
        
            float x() const noexcept { return Float4::x; }
            float& x() noexcept { return Float4::x; }
            float y() const noexcept { return Float4::y; }
            float& y() noexcept { return Float4::y; }
            float z() const noexcept { return Float4::z; }
            float& z() noexcept { return Float4::z; }
            float w() const noexcept { return Float4::w; }
            float& w() noexcept { return Float4::w; }
            float const* Data() const noexcept { return (const float*)&(Float4::x); }

            template<int NRows, int NCols>
            Vec3f block(int startRow, int startCol)
            {
                if(startRow==0)
                    return Vec3f(Float4::x, Float4::y, Float4::y);
                if (startRow == 1)
                    return Vec3f(Float4::y, Float4::z, Float4::w);
                else
                    assert(false);
                return Vec3f(1.0f, 1.0f, 1.0f);
            }

            Vec4f& operator= (const Vec4f& v)
            {
                Float4::x = v.x();
                Float4::y = v.y();
                Float4::z = v.z();
                Float4::w = v.w();
                return *this;
            }

            inline bool hasNaN() const
            {
                return (isnan(Float4::x) || isnan(Float4::y) || isnan(Float4::z) || isnan(Float4::w));
            }

            float& operator[] (int i)   const
            {
                float* s = (float*)&(Float4::x);
                if (i > 3 && i < 0)
                    assert(false);
                return s[i];
            }
        };
        
        struct Vec4i
		{
		public:
			Vec4i() : mStorage() {}
			explicit Vec4i(int constant) : Vec4i(constant, constant, constant, constant) {}
			explicit Vec4i(Vec4i const& v) : Vec4i(v.x(), v.y(), v.z(), v.w()) {}
			Vec4i(int x, int y, int z, int w) : mStorage{ x, y, z, w } {}
			inline int x() const noexcept { return mStorage[0]; }
			inline int& x() noexcept { return mStorage[0]; }
			inline int y() const noexcept { return mStorage[1]; }
			inline int& y() noexcept { return mStorage[1]; }
			inline int z() const noexcept { return mStorage[2]; }
			inline int& z() noexcept { return mStorage[2]; }
			inline int w() const noexcept { return mStorage[3]; }
			inline int& w() noexcept { return mStorage[3]; }
			explicit operator Vector4i() noexcept { return { x(), y(), z(), w() }; }
			int const* Data() const noexcept { return mStorage.data(); }
		private:
			std::array<int, 4> mStorage;
		};

        struct Quaternionf : public Quaternion
        {
            Quaternionf() = default;

            Quaternionf(const Quaternionf&) = default;
            Quaternionf& operator=(const Quaternionf&) = default;
            Quaternionf(Quaternionf&&) = default;
            Quaternionf& operator=(Quaternionf&&) = default;
            Quaternionf(float _x, float _y, float _z, float _w)
            {
                Quaternion::x = _x;
                Quaternion::y = _y;
                Quaternion::z = _z;
                Quaternion::w = _w;
            }
            Quaternionf(const float* pArray)
            {
                Quaternion::x = pArray[0];
                Quaternion::y = pArray[1];
                Quaternion::z = pArray[2];
                Quaternion::w = pArray[3];
            }
            Quaternionf(Quaternion in)
            {
                Quaternion::x = in.x;
                Quaternion::y = in.y;
                Quaternion::z = in.z;
                Quaternion::w = in.w;
            }

            float x() const noexcept { return Quaternion::x; }
            float& x() noexcept { return Quaternion::x; }
            float y() const noexcept { return Quaternion::y; }
            float& y() noexcept { return Quaternion::y; }
            float z() const noexcept { return Quaternion::z; }
            float& z() noexcept { return Quaternion::z; }
            float w() const noexcept { return Quaternion::w; }
            float& w() noexcept { return Quaternion::w; }
            float const* Data() const noexcept { return (const float*)&(Quaternion::x); }

            static inline Quaternionf Identity()
            {
                return Quaternionf(0.0f, 0.0f, 0.0f, 1.0f);
            }

            inline void normalize()
            {
                Quaternion::Normalize();
            }

            Quaternionf normalized()
            {
                return Quaternionf(Quaternion::Normalized());
            }

            inline float dot(const Quaternionf& v) const
            {
                return (Quaternion::x * v.x() + Quaternion::y * v.y() + Quaternion::z * v.z() + Quaternion::z * v.z());
            }
            float const* coeffs() const noexcept
            {
                return Data();
            }
            inline Quaternionf slerp(float t, const Quaternionf& q) const
            {
                const Quaternion q1(Quaternion::x, Quaternion::y, Quaternion::z, Quaternion::w);
                const Quaternion q2(q.x(), q.y(), q.z(), q.w());
                Quaternion q3 = Quaternion::Slerp(q1,q2,t);
                return Quaternionf(q3);

            }

            inline float angularDistance(const Quaternionf& q) const
            {
                //todo
                return 0.5f;
            }
        };

        struct Matrix4x4f : public Float4x4
        {
            Matrix4x4f() {};
            Matrix4x4f(const Matrix4x4f&) = default;
            Matrix4x4f(Matrix4x4f&&) = default;
            Matrix4x4f& operator=(const Matrix4x4f&) = default;
            Matrix4x4f& operator=(Matrix4x4f&&) = default;

            Matrix4x4f(_In_reads_(16) const float* pArray) : Float4x4(pArray)
            {}

            Matrix4x4f(float m00, float m01, float m02, float m03,
                float m10, float m11, float m12, float m13,
                float m20, float m21, float m22, float m23,
                float m30, float m31, float m32, float m33)
                :Float4x4(m00, m01, m02, m03,
                    m10, m11, m12, m13,
                    m20, m21, m22, m23,
                    m30, m31, m32, m33) {}

            //Matrix4x4f(Float4x4& f) : Float4x4(f) {}
            Matrix4x4f(Float4x4 f) : Float4x4(f) {}
            /*
            Matrix4x4f& operator=(cross::Matrix4x4f& v)
            {
                Float4x4::m00 = v(0,0);  Float4x4::m01 = v(1, 0); Float4x4::m02 = v(2, 0); Float4x4::m03 = v(3, 0);
                Float4x4::m10 = v(0, 1); Float4x4::m11 = v(1, 1); Float4x4::m12 = v(2, 1); Float4x4::m13 = v(3, 1);
                Float4x4::m20 = v(0, 2); Float4x4::m21 = v(1, 2); Float4x4::m22 = v(2, 2); Float4x4::m23 = v(3, 2);
                Float4x4::m30 = v(0, 3); Float4x4::m31 = v(1, 3); Float4x4::m32 = v(2, 3); Float4x4::m33 = v(3, 3);
                return *this;
            }
            */
            float& operator[](int i)
            {
                return ((float*)&(Float4x4::m00))[i];
            }

            static inline Matrix4x4f Identity()
            {
                return Matrix4x4f(Float4x4::Identity());
            }
            void setIdentity()
            {
                Float4x4::m00 = 1.0f; Float4x4::m01 = 0.0f; Float4x4::m02 = 0.0f; Float4x4::m03 = 0.0f;
                Float4x4::m10 = 0.0f; Float4x4::m11 = 1.0f; Float4x4::m12 = 0.0f; Float4x4::m13 = 0.0f;
                Float4x4::m20 = 0.0f; Float4x4::m21 = 0.0f; Float4x4::m22 = 1.0f; Float4x4::m23 = 0.0f;
                Float4x4::m30 = 0.0f; Float4x4::m31 = 0.0f; Float4x4::m32 = 0.0f; Float4x4::m33 = 1.0f;
            }

            static inline Matrix4x4f Compose(const Vector3f& position, const Quaternionf& rotation, const Vector3f& scale)
            {
                const Float3 scale_(scale.Data());
                const Quaternion rotation_(rotation.Data());
                const Float3  translation_(position.Data());
                return Matrix4x4f(Float4x4::Compose(scale_, rotation_, translation_));
            }

            inline bool Decompose(Vector3f& scale, Quaternionf& rotation, Vector3f& translation)
            {
                Float3 scale_;
                Quaternion rotation_;
                Float3 translation_;

                Float4x4::Compose(scale_, rotation_, translation_);
                scale = scale_;
                rotation = rotation_;
                translation = translation_;
                return true;
            }

            Matrix4x4f& inverse()
            {
                Float4x4::Inverse();
                return *this;
            }

            bool Deserialize(cross::SimpleSerializer const& s)
            {
                return false;
            }
        };

        inline Matrix4x4f operator* (const Matrix4x4f& M1, const Matrix4x4f& M2)
        {
            Float4x4* f1 = (Float4x4*)&M1;
            Float4x4* f2 = (Float4x4*)&M2;
            Float4x4  f3 = (*f1)*(*f2);
            return Matrix4x4f(f3);
        }

#endif    
        
        
    }
}