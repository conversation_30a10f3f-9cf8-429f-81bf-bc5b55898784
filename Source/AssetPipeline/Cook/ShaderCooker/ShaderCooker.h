#pragma once

#include "AssetPipeline/Cook/AssetCooker.h"
#include "AssetPipeline/Cook/CookSetting.h"
#include "AssetPipeline/Utils/AssetPipelineAPI.h"
#include "CrossBase/File/File.h"

namespace cross::editor
{
    class ShaderCooker :public AssetCooker
    {
    public:
        ASSET_API bool CheckClassID(int classID) override;
        ASSET_API bool Cook(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting) override;
        
        void SetSourceNdaPath(const char* srcNdaPath) override
        {
            mSourceNdaPath = srcNdaPath;
        };

        bool CookGraphicsShader(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting);
        bool CookComputeShader(BinaryArchive* fileData, const resource::LoadNDAFileInfo& fileInfo, const char* dstNdaPath, AssetPlatform cookPlatform, CookSettingManager& setting);

    protected:
        std::string mSourceNdaPath;
    };
}
