#pragma once

#ifndef ASSET_COOK_RECORD
#define ASSET_COOK_RECORD

#include "RTTI/BaseObject.h"

namespace cross::editor {
struct AssetInfo
{
    SInt64          mModTS;
    UInt64          mSize;
    std::string     mMD5;

    AssetInfo() : mModTS(0), mSize(0), mMD5(20, '0'){};

    SerializeNode Serialize();
    bool Deserialize(const DeserializeNode& node);
};

struct AssetCookRecord
{
    AssetInfo mSrcInfo;
    AssetInfo mDesInfo;

    SerializeNode Serialize();
    bool Deserialize(const DeserializeNode& node);
};

class AssetCookRecordManager
{
public:
    //@brief Constructor
    AssetCookRecordManager();
    //@brief Destructor
    ~AssetCookRecordManager();
    //@brief Load
    void Load(std::string path);
    //@brief Save
    void Save(std::string path);
    //@brief AddRecord
    void AddRecord(std::string srcFile, std::string desFile);
    //@brief CheckRecord
    bool CheckRecord(std::string srcFile, std::string desFile, bool checkMd5 = true);
    //@brief CheckRecord
    inline bool CheckInRecord(std::string srcFile){ return mCookRecordMap.find(srcFile) != mCookRecordMap.end(); };
    //@brief SetVersion
    inline void SetVersion(int ver){ mVersion = ver; };
    //@brief SetDesAssetPath
    inline void SetDesAssetPath(std::string path) { mDesAssetPath = path; };
    //@brief Clear 
    void Clear();
private:
    //@brief GetFileAssetInfo
    AssetInfo GetFileAssetInfo(std::string path);

private:
    using CookRecordMap = std::map<std::string, AssetCookRecord*>;
    CookRecordMap       mCookRecordMap;
    int                 mVersion = 0;
    std::string         mDesAssetPath = "";
    bool                mIsChanged = false;
};
}
#endif
