#pragma once
#include "EnginePrefix.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "Resource/RuntimePCG/PCGResource.h"

namespace cross::editor {
enum class CEMeta(Editor) PCGResourceType : UInt8
{
    SHP = 0,
    OSM,
    TMAP
};

struct ASSET_API PCGResourceImportSetting : ImportSetting
{
    CE_Virtual_Serialize_Deserialize;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Longitude"))
    Float3 mLongitude;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Latitude"))
    Float3 mLatitude;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Range"))
    Float4 mRange;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Block Size"))
    Float2 mBlockSize{200000.f, 200000.f};

    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "PCG type"))
    PCGType mType;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Remove Dumplicatee"))
    bool mDedumplicate = false;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Simplify Map Ploygn Epsilon"))
    float mSimplifyMapEpsilon = 100.f;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Building Distacne Threshold To Merge"))
    float mDistanceThreshold = 100.f;
};
}   // namespace cross::editor
