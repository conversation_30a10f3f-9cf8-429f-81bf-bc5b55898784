#pragma once

#include <fbxsdk.h>
#include <vector>
#include <unordered_map>
#include "CrossBase/Math/CrossMath.h"
#include "AssetPipeline/Import/ModelImporter/TextureDescription.h"

namespace cross
{
namespace editor
{
    using MaterialID = SInt32;

    class ASSET_API MaterialDescription
    {
    public:
        inline void AddMaterialID(FbxSurfaceMaterial* fbxMaterial, MaterialID id) { materialIDs.try_emplace(fbxMaterial, id); }

        inline const std::unordered_map<FbxSurfaceMaterial*, MaterialID>& GetMaterialIDs() const
        {
            return materialIDs;
        }

        inline const MaterialID FindMaterialID(FbxSurfaceMaterial* key)
        {
            auto resultIter = materialIDs.find(key);
            return resultIter != materialIDs.end() ? resultIter->second : -1;
        }

        inline UInt32 FindMaterialIndex(UInt32 polygonIndex) const
        {
            auto resultIter = polygonMatIndexs.find(polygonIndex);
            return resultIter != polygonMatIndexs.end() ? resultIter->second : -1;
        }

        inline void InitAttachPolygon(MaterialID materialID) { attachPolygons.emplace(materialID, std::vector<UInt32>()); }

        inline void AddAttachPolygon(MaterialID materialID, UInt32 faceID)
        {
            auto resultIter = attachPolygons.find(materialID);
            if (resultIter != attachPolygons.end())
            {
                resultIter->second.emplace_back(faceID);
            }
            else
            {
                attachPolygons.try_emplace(materialID, std::vector<UInt32>{faceID});
            }
        }

        inline void AddPolygonMatIndex(UInt32 materialIndex, UInt32 faceID)
        {
            polygonMatIndexs.try_emplace(faceID, materialIndex);
        }

        inline const std::map<MaterialID, std::vector<UInt32>>& GetAttachPolygons() const { return attachPolygons; }

        void FetchFbxMatrials(std::unordered_set<FbxSurfaceMaterial*>& materials) const
        {
            std::transform(materialIDs.begin(), materialIDs.end(), std::inserter(materials, materials.end()), [](auto pair) { return pair.first; });
        }

        FbxSurfaceMaterial* FetchFbxMatrialByID(MaterialID id)
        {
            for (auto iter = materialIDs.begin(); iter != materialIDs.end(); ++iter)
            {
                if (iter->second == id)
                {
                    return iter->first;
                }
            }
            return nullptr;
        }

        void RemoveUnusedMaterial()
        {
            std::vector<MaterialID> needRemoved;
            for (auto iter = attachPolygons.begin(); iter != attachPolygons.end(); ++iter)
            {
                if (!iter->second.empty())
                {
                    needRemoved.emplace_back(iter->first);
                }
            }
            for (const auto& id : needRemoved)
            {
                RemoveMaterial(id);
            }
        }

        void RemoveMaterial(MaterialID materialID)
        {
            auto iter = attachPolygons.find(materialID);
            if (iter != attachPolygons.end())
            {
                attachPolygons.erase(iter);

                for (auto matIter = materialIDs.begin(); matIter != materialIDs.end(); ++matIter)
                {
                    if (matIter->second == materialID)
                    {
                        materialIDs.erase(matIter);
                        return;
                    }
                }
            }
        }
    private:
        std::unordered_map<FbxSurfaceMaterial*, MaterialID> materialIDs;
        std::map<MaterialID, std::vector<UInt32>> attachPolygons;
        std::unordered_map<UInt32, UInt32> polygonMatIndexs;
    };

}   // namespace editor
}   // namespace cross