#pragma once
#include "EnginePrefix.h"
#include "CrossBase/Math/CrossMath.h"
#include "Resource/MeshAssetDataInfo.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"

namespace cross::editor
{
    CEMeta(Editor) const int kMaxLength = 260;

    enum CEMeta(Editor) AnimImportType
    {
        LinearKeyReduction = 0,
        UniformSample,
        All,
        None
    };

    enum CEMeta(Editor) AnimFrameRangeType
    {
        ZeroToEnd = 0,
        StartToEnd,
        GivenRange_NOT_SUPPORTED
    };

    enum CEMeta(Editor) SkinImportType
    {
        Geometry = 0,
        Skin,
        GeometryAndSkin,
    };

    enum CEMeta(Editor) SequenceCurveDataBindingsType
    {
        NoGenerate = 0,
        Prefab = 1,
        World = 2,
    };
    
    enum class CEMeta(Editor) GenerateCollisionType
    {
        None,
        BoxCollision,
        ComplexCollision,
        HeightFiled,
    };

    struct ASSET_API CollisionGenerateSetting : ImportSetting
    {
        CEProperty(Editor)
        CECSAttribute(PropertyInfo())
        GenerateCollisionType CollisionGenerationType;

        CEProperty(Editor)
        CECSAttribute(PropertyInfo())
        float ComplexCollisionSimplifyError = 0.0f;

        CEProperty(Editor)
        CECSAttribute(PropertyInfo())
        float GridSpacingX = 50000.0f;

        CEProperty(Editor)
        CECSAttribute(PropertyInfo())
        float GridSpacingZ = 50000.0f;

        CEProperty(Editor)
        CECSAttribute(PropertyInfo())
        Float3 LocalTranslation = Float3::Zero();

        CEProperty(Editor)
        CECSAttribute(PropertyInfo())
        Quaternion LocalRotation = Quaternion::Identity();

        CE_Serialize_Deserialize;
    };

    //#pragma pack(push, 1)

    struct ASSET_API ModelImportSettings : ImportSetting
    {
        CE_Virtual_Serialize_Deserialize;

        bool ImportNormals{true};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether recompute normals"))
        bool CalculateNormals{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether import tangents"))
        bool ImportTangents{true};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether import 2UV"))
        bool ImportSecondaryUV{true};
        
        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether import mesh LOD"))
        bool ImportMeshLODs{true};

        CEMeta(Editor)
        SkinImportType ImportContentType{Geometry};

        CEMeta(Serialize, Editor) 
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Angle: The rotation offset between character face forward and entity's trans comp forward.")) 
        /* 
         * Considering not all Character face direction is in the same direction with transform component forward(actually Z-Axis direction in our engine) after imported, 
         * This will make a big trouble for Player Control, so We need a rotation offset.
         * More explanation are recorded in Function: FBXImporter::ImportSkeletonBindPose(......) in file FBXImporter.cpp
         */
        float FaceOffset{0.0f};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether normalize bone names with the format \"#depth_#num\""))
        bool NormalizeBoneName{ false };

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether split mesh which has multi materials"))
        bool SplitByMaterial{true};

        bool BakePivotInVertex{false};

        CEMeta(Serialize, Editor) 
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether import vertex color, once checked, would alert warning without color semantic")) 
        bool ImportVertexColor{true};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether import blendshape data"))
        bool ImportBlendShapes{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether create default materials"))
        bool CreateDefaultMaterials{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether ResampleCurves"))
        bool ResampleCurves{true};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether UseFileScale"))
        bool UseFileScale{true};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether ImportAnimatedCustomProperties"))
        bool ImportAnimatedCustomProperties{true};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Struct"))
        CollisionGenerateSetting CollisionSetting;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "GenerateClusters"))
        bool GenerateClusters{ false };

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "SkeletonResource", FileTypeDescriptor = "Skeleton resource#nda", ObjectClassID1 =  ClassIDType.CLASS_SkeletonResource)) 
        std::string ImportUsedSkeletonResouce;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", 
            ToolTips = "Selected custom fx for create material, which texture name from fx must include one of these keywords: color, diffuse, base, albedo, normal, specular, emissive", 
            FileTypeDescriptor = "Fx resource#nda", ObjectClassID1 =  ClassIDType.CLASS_Fx)) 
        std::string ImportMaterialFx {"EngineResource/Shader/DefaultShader.nda"};

        bool TransformVertexToAbsolute{true};

        CEMeta(Serialize, Editor)
        kSystemUnit ModelSystemUnit{kSystemUnit::UNIT_CM};
        
        CEMeta(Serialize, Editor)
        AnimImportType AnimImport{None};

        CEMeta(Serialize, Editor)
        AnimFrameRangeType AnimFramgeRange{StartToEnd};

        CEMeta(Serialize, Editor) 
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether import curve data as sequence")) 
        bool ImportCurveDataAsSequence{ false };
        
        CEMeta(Serialize, Editor) 
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether curve data bindings type should be generate")) 
        SequenceCurveDataBindingsType SequenceCurveDataBindingsType{SequenceCurveDataBindingsType::NoGenerate};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether use full float precision for normal"))
        bool ImportUseFullPrecisionNormal{ false };

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether use full float precision for normal"))
        bool ImportUseQTangents{ false };

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether use full float precision for UV"))
        bool ImportUseFullPrecisionUV{ false };

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether generate collision tree for editor use"))
        bool CanPickInEditor{ true };

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether generate model prefab"))
        bool ImportAsModel{ true };

        CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether combile fbxmesh with same material"))
        bool CombineMesh{ false };

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether cook mesh data"))
        bool IsCookMesh{ false };

        bool MergeNormals{ false };

        CEMeta(Serialize, Editor) 
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether use combine material and texture", bHide = true)) 
        bool CreateCombineMaterials{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "material texture change size..", bHide = true)) 
        ImportTexureSize MatTexImportSize = ImportTexureSize::None;

        CEMeta(Serialize, Editor) 
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "use same uv index", bHide = true)) 
        bool UseSameUVIndex{true};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "ffs mesh curvation, pointcloud only!"))
        bool UseMeshCurvation{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether break up instances to entities"))
        bool BreakUpToEntities{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether generate instance data"))
        bool UseInstanceData{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether generate HLOD data"))
        bool UseHLODData{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether mesh is streamable"))
        bool IsStreamable{false};

        static ModelImportSettings gModelImportSettings;
        void SetEngineImportSettingImp()
        { 
            gModelImportSettings = *this;
        }
    };
    //#pragma pack(pop)
}
