#pragma once
#include "CrossEngineSDK.h"

namespace cesdk { namespace cegf {
    class AssetExchange_API SDKCharacterObject : public SDKGameObject
    {
    public:
        SDKCharacterObject(::cegf::GameObject* gameObjectInstance)
            : SDKGameObject(gameObjectInstance)
        {}
    };

    AssetExchange_API SDKCharacterObject CreateCharacterObject(
        SDKGameWorld* gameWorld, const char* name, SDKGameObject parent,
        const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale
    );
}}   // namespace cesdk::cegf