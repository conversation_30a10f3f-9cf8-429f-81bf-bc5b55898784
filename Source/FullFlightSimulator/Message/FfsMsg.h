#pragma once
#if 0
#include "FullFlightSimulatorAPI.h"
#include "FfsMsgDef.h"
#include "FfsMessageHelper.h"
#include "FfsSystemG.h"
#include "FfsDebugUI.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Clocker/SyncClocker.h"

namespace cross {
class IImageGenerationMsgHandler;
using FFS_CALLBACK_HANDLE = std::unique_ptr<IImageGenerationMsgHandler>;
class FfsSystemG;
class FfsContext;
class FfsDelegateManager;

extern double gAircraftAltitudeFromNetwork;

class IImageGenerationMsgHandler
{
public:
    explicit IImageGenerationMsgHandler(FFS_MSG inType)
        : mMsgType(inType){};
    virtual ~IImageGenerationMsgHandler() = default;

    virtual void OnExecuted(const SInt8* inBuffer, SInt32 inBufferLength, FFS_TARGET inSource, FFS_TARGET inDestation, GameWorld* const inWorldPtr, UInt32 networkFrameCount, UInt32 msgSeq) = 0;

    const FFS_MSG mMsgType;   /// 请求协议号
};

template<class T>
class ImageGenerationMsgHandler : public IImageGenerationMsgHandler
{
public:
    explicit ImageGenerationMsgHandler(FFS_MSG inType)
        : IImageGenerationMsgHandler(inType)
    {}

    void OnExecuted(const SInt8* inBuffer, SInt32 inBufferLength, FFS_TARGET inSource, FFS_TARGET inDestation, GameWorld* const inWorldPtr, UInt32 networkFrameCount,UInt32 msgSeq) override
    {
        mMessage.Clear();
        if (!mMessage.ParseFromArray(inBuffer, inBufferLength))
        {
            auto descriptor_command = ce::net::FullFlightSimulatorPacket_EnumFFSCommand_descriptor();
            auto descriptor_target = ce::net::EnumFFSTarget_descriptor();

            auto command_str = descriptor_command->FindValueByNumber(GetHandledMsgType())->name().c_str();
            auto source_str = descriptor_target->FindValueByNumber(inSource)->name().c_str();

            LOG_WARN("tbuspp buffer parsed failed which message type:{} from {}, your protocol probably outof date", command_str, source_str);
            return;
        }

        if (!mFfsContext)
        {
            mFfsContext = inWorldPtr->GetGameSystem<FfsSystemG>()->GetContext();
            mDelegateManager = mFfsContext->GetDelegateManager();
        }

        OnExecutedImpl(mMessage, inSource, inDestation, inWorldPtr, networkFrameCount,msgSeq);

        // For debug use
        if (inSource == FFS_TARGET::SimulationDevice)
            FfsDebugUIMemo::GetInstance()->AddMemoTask(mMsgType, mMessage);
    }

    virtual void OnExecutedImpl(T const& inMessage, FFS_TARGET inSource, FFS_TARGET inDestation, GameWorld* const inWorldPtr, UInt32 networkFrameCount, UInt32 msgSeq) = 0;

    inline FFS_MSG GetHandledMsgType()
    {
        return mMsgType;
    }

    FfsDelegateManager* mDelegateManager = nullptr;
    FfsContext* mFfsContext = nullptr;

private:
    T mMessage;   /// rpc 请求发来的协议数据
};

#define DefineRpcHandler(Handler, Request)                                                                                                                                                                                                     \
    class Handler : public ImageGenerationMsgHandler<Request>                                                                                                                                                                                  \
    {                                                                                                                                                                                                                                          \
    public:                                                                                                                                                                                                                                    \
        explicit Handler(FFS_MSG inType)                                                                                                                                                                                                       \
            : ImageGenerationMsgHandler(inType)                                                                                                                                                                                                \
        {}                                                                                                                                                                                                                                     \
                                                                                                                                                                                                                                               \
        void OnExecutedImpl(const Request& inMessage, FFS_TARGET inSource, FFS_TARGET inDestation, GameWorld* const inWorldPtr, UInt32 networkFrameCount, UInt32 msgSeq) override;                                                                                \
    };

// 21
DefineRpcHandler(GeodeticCSUpdateHandler, ce::net::GeodeticCSUpdateStruct);
// 22
DefineRpcHandler(IndependentRectangularCSUpdateHandler, ce::net::IndependentRectangularCSUpdateStruct);
// 40
DefineRpcHandler(VisualSystemUpdateHandler, ce::net::VisualSystemUpdateStruct);
// 42
DefineRpcHandler(DatabaseUpdateStructHandler, ce::net::DatabaseUpdateStruct);
// 43
DefineRpcHandler(LightUpdateStructHandler, ce::net::LightUpdateStruct);
// 43v1
DefineRpcHandler(LightUpdateV1StructHandler, ce::net::LightUpdateV1Struct);
// 43v2
DefineRpcHandler(LightUpdateV2StructHandler, ce::net::LightUpdateV2Struct);
// 44
DefineRpcHandler(WeatherUpdateHandler, ce::net::WeatherUpdateStruct);
// 45
DefineRpcHandler(VisibilityUpdateStructHandler, ce::net::VisibilityUpdateStruct);
// 47
DefineRpcHandler(IlluminationUpdateStructHandler, ce::net::IlluminationUpdateStruct);
// 48
DefineRpcHandler(CloudGeodeticUpdateHandler, ce::net::CloudGeodeticUpdateStruct);
// 4Bv1
DefineRpcHandler(PathRecordDataStructHandler, ce::net::PathRecordDataStruct);
// 4Bv2
DefineRpcHandler(PathRecordDataV2StructHandler, ce::net::PathRecordDataV2Struct);
// 50
DefineRpcHandler(SpecialEffectsUpdateHandler, ce::net::SpecialEffectsUpdateStruct);
// 52
DefineRpcHandler(RectangularMissionFunctionUpdateStructHandler, ce::net::RectangularMissionFunctionUpdateStruct);
// 55
DefineRpcHandler(FallingSnowRainUpdateHandler, ce::net::FallingSnowRainUpdateStruct);
// 58
DefineRpcHandler(MovingModelHandler, ce::net::MovingModelStruct);
// 68
DefineRpcHandler(GenericDatabaseControlHandler, ce::net::GenericDatabaseControlStruct);
// E0W - 0307
DefineRpcHandler(WorldIntegratedGenericDatabaseUpdateHandler, ce::net::WorldIntegratedGenericDatabaseUpdateStruct);
// E2
DefineRpcHandler(ListOfDatabasesRequestHandler, ce::net::ListOfDatabasesRequestStruct);
// 0x28 Pushback Command Data
DefineRpcHandler(PushbackCommandDataHandler, ce::net::PushbackCommandDataStruct);
// 0x70  MOVING PARTS
DefineRpcHandler(MovingPartStructHandler, ce::net::MovingPartStruct);
// Operating query
// FFS系统的特定IG的网络状态获取
DefineRpcHandler(ImageGeneratorOperatingStatusHandler, ce::net::cc::ImageGeneratorOperatingStatusRequest);
// Resource query
// FFS系统的特定IG的硬件负载获取
DefineRpcHandler(ImageGeneratorDeviceStatusHandler, ce::net::cc::ImageGeneratorDeviceStatusRequest);
// Generic ultra query
// FFS系统的特定IG的超控状态获取
DefineRpcHandler(ImageGeneratorUltraStatusHandler, ce::net::cc::ImageGeneratorUltraStatusRequest);
// Geodetic query
// FFS系统的特定IG的视点地理信息信息获取
//DefineRpcHandler(ImageGeneratorGeodeticStatusHandler, ce::net::cc::ImageGeneratorGeodeticStatusRequest);
// Air route query
// FFS系统的特定IG所经过机场的icao队列信息获取
DefineRpcHandler(ImageGeneratorAirRouteStatusHandler, ce::net::cc::ImageGeneratorAirRouteStatusRequest);
// FFS系统的特定IG的特定机场的地面灯光状态获取
DefineRpcHandler(ImageGeneratorEnvironmentLightsStatusHandler, ce::net::cc::ImageGeneratorEnvironmentLightsStatusRequest);
// FFS系统的特定IG的控制飞机的机身灯光状态获取
DefineRpcHandler(ImageGeneratorAircraftLightsStatusHandler, ce::net::cc::ImageGeneratorAircraftLightsStatusRequest);

// 地理位置超控状态获取
DefineRpcHandler(ImageGeneratorGeographicUltraHandler, ce::net::cc::ImageGeneratorGeographicUltraRequest);
// 地理位置超控状态设置
DefineRpcHandler(ImageGeneratorGeographicUltraSetHandler, ce::net::cc::ImageGeneratorGeographicUltraSetRequest);
// 时间超控状态获取
DefineRpcHandler(ImageGeneratorTimeOfDayUltraHandler, ce::net::cc::ImageGeneratorTimeOfDayUltraRequest);
// 时间超控状态设置
DefineRpcHandler(ImageGeneratorTimeOfDayUltraSetHandler, ce::net::cc::ImageGeneratorTimeOfDayUltraSetRequest);
// 地理位置超控 - 录像超控状态获取
DefineRpcHandler(ImageGeneratorPlaybackUltraHandler, ce::net::cc::ImageGeneratorPlaybackUltraRequest);
// 地理位置超控 - 录像超控状态设置
DefineRpcHandler(ImageGeneratorPlaybackUltraSetHandler, ce::net::cc::ImageGeneratorPlaybackUltraSetRequest);

// Sync loading status
DefineRpcHandler(ImageGeneratorLoadingStatusQueryHandler, ce::net::ImageGeneratorLoadingStatusQueryResponse);
// Sync models response
DefineRpcHandler(ImageGeneratorSyncModelsHandler, ce::net::ImageGeneratorSyncModelsResponse);
// Sync weather random number
DefineRpcHandler(ImageGeneratorWeatherSyncHandler, ce::net::ImageGeneratorWeatherSyncRequest);

// 飞机状态请求
DefineRpcHandler(ImageGeneratorAircraftGcsStatusHandler, ce::net::cc::ImageGeneratorAircraftGcsStatusRequest);
// 机场状态请求
DefineRpcHandler(ImageGeneratorAirportGcsStatusHandler, ce::net::cc::ImageGeneratorAirportGcsStatusRequest);
// 机场列表请求
DefineRpcHandler(ImageGeneratorAirportICAOListHandler, ce::net::cc::ImageGeneratorAirportICAOListRequest);

// 灯光超控-机身灯光状态请求
DefineRpcHandler(ImageGeneratorAircraftLightsUltraHandler, ce::net::cc::ImageGeneratorAircraftLightsUltraRequest);
// 灯光超控-场景灯光状态请求
DefineRpcHandler(ImageGeneratorSceneLightsUltraHandler, ce::net::cc::ImageGeneratorSceneLightsUltraRequest);
// 灯光超控-场景灯光超控设置请求
DefineRpcHandler(ImageGeneratorSceneLightsUltraSetHandler, ce::net::cc::ImageGeneratorSceneLightsUltraSetRequest);
// 灯光超控-机身灯光超控设置请求
DefineRpcHandler(ImageGeneratorAircraftLightsUltraSetHandler, ce::net::cc::ImageGeneratorAircraftLightsUltraSetRequest);

// 环境效果超控 - 能见度 Getter
DefineRpcHandler(ImageGeneratorVisibilityUltraHandler, ce::net::cc::ImageGeneratorVisibilityUltraRequest);
// 环境效果超控 - 能见度 Setter
DefineRpcHandler(ImageGeneratorVisibilityUltraSetHandler, ce::net::cc::ImageGeneratorVisibilityUltraSetRequest);
// 环境效果超控 - 云 Getter
DefineRpcHandler(ImageGeneratorCloudUltraHandler, ce::net::cc::ImageGeneratorCloudUltraRequest);
// 环境效果超控 - 云 Setter
DefineRpcHandler(ImageGeneratorCloudUltraSetHandler, ce::net::cc::ImageGeneratorCloudUltraSetRequest);
// 环境效果超控 - 天气 Getter
DefineRpcHandler(ImageGeneratorWeatherUltraHandler, ce::net::cc::ImageGeneratorWeatherUltraRequest);
// 环境效果超控 - 天气 Setter
DefineRpcHandler(ImageGeneratorWeatherUltraSetHandler, ce::net::cc::ImageGeneratorWeatherUltraSetRequest);
// 环境效果超控 - 跑道条件 Getter
DefineRpcHandler(ImageGeneratorGroundContaminantsUltraHandler, ce::net::cc::ImageGeneratorGroundContaminantsUltraRequest);
// 环境效果超控 - 跑道条件 Setter
DefineRpcHandler(ImageGeneratorGroundContaminantsUltraSetHandler, ce::net::cc::ImageGeneratorGroundContaminantsUltraSetRequest);
}   // namespace cross
#endif