#pragma once
#include "FFSProtocol/CrossEnginePacket.pb.h"
#include "FFSProtocol/FullFlightSimulatorPacket.pb.h"
#include "FFSProtocol/FFS_RTS/RTS_Packet.pb.h"

#include "FFS_VSP/ProtocolViews/Meteorology/HighCloud/HighCloudView.pb.h"
#include "FFS_VSP/ProtocolViews/Meteorology/LowCloud/LowCloudView.pb.h"
#include "FFS_VSP/ProtocolViews/Meteorology/MiddleCloud/MiddleCloudView.pb.h"

namespace cross 
{

using FFS_MSG = ce::net::FullFlightSimulatorPacket_EnumFFSCommand;
using FfsFrame = ce::net::CrossEnginePacket;
using FfsPacket = ce::net::FullFlightSimulatorPacket;

}