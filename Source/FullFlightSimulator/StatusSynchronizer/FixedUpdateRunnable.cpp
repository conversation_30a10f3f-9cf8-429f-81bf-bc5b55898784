#include "EnginePrefix.h"
#include "FixedUpdateRunnable.h"
#include "CrossBase/Template.h"
#include "CrossBase/Threading/TaskSystem.h"
#pragma comment(lib, "Winmm.lib")
namespace cross::threading {
bool FixedUpdateRunnable::Init()
{
    DECLARE_THREAD_NAME(FixedUpdateRunnable::ThreadName);

    threading::TaskSystem::Get()->AttachThread(threading::ThreadID::ExternalThread, this);

    return true;
}

void FixedUpdateRunnable::Run()
{
    if (mFixedUpdateCb == nullptr)
        return;

    do
    {
        double deltaInUs = mRestriction.Delta.count();
        //if (mModelSyncEnabled)
        {
            mFixedUpdateCb(deltaInUs, mRestriction.mDurationInUs);
        }

        {
            //SCOPED_CPU_FP_TIMING(FrameIndexGeneratorThread, "Fixed Synchronization Stall");
            //todo: send status sync packets
            mRestriction.Sync();
        }

    } while (!mQuitRequested);
}

void FixedUpdateRunnable::Stop()
{
    mShutdownStallEvent.Wait();
}

void FixedUpdateRunnable::Exit()
{
    mShutdownStallEvent.Trigger();

    threading::TaskSystem::Get()->DetachThread(threading::ThreadID::ExternalThread, this);
}

void FixedUpdateRunnable::RequestQuit(/*SInt32 inQueueIndex*/)
{
    mQuitRequested = true;
}
void FixedFrequencyRestriction::Sleep(double seconds) {
    if (seconds <= 0)
        return;

#if CROSSENGINE_WIN
    ::timeBeginPeriod(2);
#endif

    using namespace std;
    using namespace std::chrono;

#if CROSSENGINE_WIN
    ::timeBeginPeriod(2);
#endif

    while (seconds > mEstimate || seconds > 4e-3)   // 4ms
    {
        //SCOPED_CPU_FP_TIMING(Sync, "Sleep");

        auto start = high_resolution_clock::now();
        this_thread::sleep_for(milliseconds(1));
        auto end = high_resolution_clock::now();

        double observed = (end - start).count() / 1e9;
        seconds -= observed;

        ++mCount;
        double delta = observed - mMean;
        mMean += delta / mCount;
        mM2 += delta * delta;
        double stddev = sqrt(mM2 / (mCount - 1));
        mEstimate = mMean + stddev;
    }

    // spin lock
    auto start = high_resolution_clock::now();
    while ((high_resolution_clock::now() - start).count() / 1e9 < seconds) {}
}
void FixedFrequencyRestriction::Sync() {
#if CROSSENGINE_WIN
    ::timeBeginPeriod(2);
#endif

    a = std::chrono::high_resolution_clock::now();

    constexpr double divisor = 1000.0 * 1000.0;

    // Record native start timestamp
    if (std::chrono::duration<double, std::micro>(mStartTimestampN.time_since_epoch()).count() == 0)
        mStartTimestampN = a;

    //
    auto restrictionWorkTime = std::chrono::duration<double, std::micro>(WorkTimeUs().count() + OverflowInUs);

    // current tick executes expectable in pre-set frequency
    if (restrictionWorkTime.count() < FrameDurationUs())
    {
        std::chrono::duration<double, std::micro> delta_us(FrameDurationUs() - restrictionWorkTime.count());
        Sleep(delta_us.count() / divisor);

        OverflowInUs = 0.0;
    }
    // current tick skip sleep for cur-tick blocking
    else if (WorkTimeUs().count() >= FrameDurationUs() && !mFirstSync)
    {
        // do nothing for now
    }
    // current tick skip sleep for pre-tick blocking
    else if (WorkTimeUs().count() < FrameDurationUs() && !mFirstSync)
    {
        std::chrono::duration<double, std::micro> delta_us(FrameDurationUs() - WorkTimeUs().count());

        auto overflow_delta_us = OverflowInUs - delta_us.count();
        OverflowInUs = std::max(0.0, overflow_delta_us);
    }

    if (mFirstSync)
        mFirstSync = false;
    else
        Delta = std::chrono::high_resolution_clock::now() - b;

    // Collect delta timestamp in 1s compared with desired timestamp
    double executedDurationInUs = std::chrono::duration<double, std::micro>(std::chrono::high_resolution_clock::now() - mStartTimestampN).count();
    UInt64 throughputUnit = executedDurationInUs / divisor;
    Assert(throughputUnit >= mThroughputUnit);
    if (throughputUnit > mThroughputUnit)
    {
        OverflowInUs = executedDurationInUs - std::chrono::duration<double, std::micro>(mThroughputCount * FrameDurationUs()).count();
        mThroughputUnit = throughputUnit;
    }

    // Forward sync cursor
    mThroughputCount++;

    b = std::chrono::high_resolution_clock::now();
}

}
