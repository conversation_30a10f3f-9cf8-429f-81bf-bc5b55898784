#include "Resource/ResourceManager.h"
#include "CEGameFramework/GameFramework/GameFrameworkSystem.h"
#include "Runtime/GameWorld/Prefab/PrefabStreamingSystemG.h"
#include "FullFlightSimulator/GamePlay/InstantiateManager.h"

namespace cegf
{
	cross::PrefabStreamingHandle* InstantiateManager::InstantiateAsync(const std::string& prefabPath) {
		return InstantiateAsync(prefabPath, nullptr, cross::Double3::Zero(), cross::Quaternion64::Identity());
	}
	cross::PrefabStreamingHandle* InstantiateManager::InstantiateAsync(const std::string& prefabPath, cross::Double3 position, cross::Quaternion64 rotation) {
		return InstantiateAsync(prefabPath,nullptr, position, rotation);
	}
	cross::PrefabStreamingHandle* InstantiateManager::InstantiateAsync(const std::string& prefabPath, const GameObject* parent) {
		return InstantiateAsync(prefabPath, parent, cross::Double3::Zero(), cross::Quaternion64::Identity());
	}
	cross::PrefabStreamingHandle* InstantiateManager::InstantiateAsync(const std::string& prefabPath, const GameObject* parent, cross::Double3 position, cross::Quaternion64 rotation) {
		auto GFSys = cross::EngineGlobal::GetEngine()->GetGlobalSystem<cegf::GameFrameworkSystem>();
		cegf::GameWorld* gameWorld = nullptr;
		//find suitable game world to instantiate prefab
		cross::WorldTypeTag worldType = cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeStandAlone? cross::WorldTypeTag::DefaultWorld: cross::WorldTypeTag::PIEWorld;
		for (auto& world : GFSys->GetGameEngine()->GetWorldList()) {
			if (world->GetWorldType() == worldType) {
				gameWorld = world.get();
				break;
			}
		}
		// async instantiate prefab
		std::string absolutePrefabPath = cross::PathHelper::GetAbsolutePath(prefabPath);
		AssertMsg(gameWorld, "InstantiateManager:: gameworld is invalid!!");
		cross::ecs::EntityID parentEntity = parent ? parent->GetObjectEntityID() : gameWorld->GetRootGameObject()->GetObjectEntityID();
		cross::GameWorld* crossGameWorld = gameWorld->GetCrossGameWorld();
		auto* prefabStreamingSys = crossGameWorld->GetGameSystem<cross::PrefabStreamingSystemG>();
		cross::PrefabStreamingHandlePtr handle = prefabStreamingSys->InstancingAsync(absolutePrefabPath, parentEntity);

		return handle.get();
	}
	void InstantiateManager::UnloadInstanceAsync(GameObject* go) {
		auto GFSys = cross::EngineGlobal::GetEngine()->GetGlobalSystem<cegf::GameFrameworkSystem>();
		cegf::GameWorld* gameWorld = nullptr;
		//find suitable game world to instantiate prefab
		cross::WorldTypeTag worldType = cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeStandAlone ? cross::WorldTypeTag::DefaultWorld : cross::WorldTypeTag::PIEWorld;
		for (auto& world : GFSys->GetGameEngine()->GetWorldList()) {
			if (world->GetWorldType() == worldType) {
				gameWorld = world.get();
				break;
			}
		}

		cross::GameWorld* crossGameWorld = gameWorld->GetCrossGameWorld();
		auto* prefabStreamingSys = crossGameWorld->GetGameSystem<cross::PrefabStreamingSystemG>();
		prefabStreamingSys->DestoryInstancedFrameByFrame(go->GetObjectEntityID());
	}
	void InstantiateManager::UnloadInstanceSync(GameObject* go) {
		auto GFSys = cross::EngineGlobal::GetEngine()->GetGlobalSystem<cegf::GameFrameworkSystem>();
		cegf::GameWorld* gameWorld = nullptr;
		//find suitable game world to instantiate prefab
		cross::WorldTypeTag worldType = cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeStandAlone ? cross::WorldTypeTag::DefaultWorld : cross::WorldTypeTag::PIEWorld;
		for (auto& world : GFSys->GetGameEngine()->GetWorldList()) {
			if (world->GetWorldType() == worldType) {
				gameWorld = world.get();
				break;
			}
		}

		cross::GameWorld* crossGameWorld = gameWorld->GetCrossGameWorld();
		auto* prefabStreamingSys = crossGameWorld->GetGameSystem<cross::PrefabStreamingSystemG>();
		prefabStreamingSys->DestoryInstancedSynchronously(go->GetObjectEntityID());
	}

	float InstantiateManager::GetInstanceLoadingProgressPercentage(const cross::PrefabStreamingHandle* handle)
	{
		auto* GFSys = cross::EngineGlobal::GetEngine()->GetGlobalSystem<cegf::GameFrameworkSystem>();
		cegf::GameWorld* gameWorld = nullptr;
		// Find suitable game world to instantiate prefab
		cross::WorldTypeTag worldType = cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeStandAlone ? cross::WorldTypeTag::DefaultWorld : cross::WorldTypeTag::PIEWorld;
		for (auto& world : GFSys->GetGameEngine()->GetWorldList()) {
			if (world->GetWorldType() == worldType) {
				gameWorld = world.get();
				break;
			}
		}

		if (!gameWorld)
		{
			return 0.0f;
		}

		// Prefab is loaded
		const auto& resource = gResourceMgr.Find(handle->mPrefabPath.GetCString());
		if (resource)
		{
			return 100.0f;
		}

		// Prafab is loading
		auto* crossGameWorld = gameWorld->GetCrossGameWorld();
		auto* prefabStreamingSys = crossGameWorld->GetGameSystem<cross::PrefabStreamingSystemG>();
		const auto& assetStreamingHandle = prefabStreamingSys->GetPrefabAssetStreamingHandle(const_cast<cross::PrefabStreamingHandle*>(handle)->shared_from_this());
		if (assetStreamingHandle)
		{
			return assetStreamingHandle->GetProgressPercentage();
		}

		return 0.0f;
	}
} // namespace cegf
