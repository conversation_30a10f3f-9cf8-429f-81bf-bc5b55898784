#pragma once
#include "FullFlightSimulatorAPI.h"
#include "FfsWeatherPreset.h"
#include "CECommon/Common/SystemEvent.h"
#include "FFSLightPointSystemG.h"

namespace cross {

struct FfsWeatherSettingEventData
{
    FfsWeatherDashboard* setting = nullptr;
    bool flush = false;
    SInt8 index = 0;
};

using FfsWeatherSettingEvent = SystemEvent<const FfsWeatherSettingEventData*>;
using FfsWeatherView = std::variant<FfsTopCloudView, FfsMidCloudView, FfsLowCloudView, FfsFogView, FfsPrecipitationView, FfsStormView_Deprecated>;

class FullFlightSimulator_API FfsWeatherSettingManager : public SystemEventManager<FfsWeatherSettingEvent, FfsVisibilityEvent>
{
public:
    static FfsWeatherSettingManager& Instance() noexcept;

    ~FfsWeatherSettingManager() = default;

    FfsWeatherSettingManager(const FfsWeatherSettingManager&) = delete;
    FfsWeatherSettingManager& operator=(const FfsWeatherSettingManager&) = delete;

    inline const FfsWeatherDashboard& GetSetting() const { return mSetting; }

    CEMeta(Editor) 
    FfsWeatherDashboard& GetSetting() { return mSetting; }

    CEMeta(Editor) 
    FfsTopCloudView GetTopCloudView(const std::string& key)
    {
        if (mPresets.find(key) != mPresets.end())
        {
            return std::get<FfsTopCloudView>(mPresets[key]); 
        }
        else
        {
            auto [iter, ret] = mPresets.emplace(key, FfsTopCloudView());
            return std::get<FfsTopCloudView>(iter->second);
        }
    }

    CEMeta(Editor) 
    FfsMidCloudView GetMidCloudView(const std::string& key)
    {
        if (mPresets.find(key) != mPresets.end())
        {
            return std::get<FfsMidCloudView>(mPresets[key]);
        }
        else
        {
            auto [iter, ret] = mPresets.emplace(key, FfsMidCloudView());
            return std::get<FfsMidCloudView>(iter->second);
        }
    }

    CEMeta(Editor) 
    FfsLowCloudView GetLowCloudView(const std::string& key)
    {
        if (mPresets.find(key) != mPresets.end())
        {
            return std::get<FfsLowCloudView>(mPresets[key]);
        }
        else
        {
            auto [iter, ret] = mPresets.emplace(key, FfsLowCloudView());
            return std::get<FfsLowCloudView>(iter->second);
        }
    }

    CEMeta(Editor)
    FfsStormView_Deprecated GetStormView_Deprecated(const std::string& key)
    {
        if (mPresets.find(key) != mPresets.end())
        {
            return std::get<FfsStormView_Deprecated>(mPresets[key]);
        }
        else
        {
            auto [iter, ret] = mPresets.emplace(key, FfsStormView_Deprecated());
            return std::get<FfsStormView_Deprecated>(iter->second);
        }
    }

    CEMeta(Editor) 
    FfsFogView GetFogView(const std::string& key)
    {
        if (mPresets.find(key) != mPresets.end())
        {
            return std::get<FfsFogView>(mPresets[key]); 
        }
        else
        {
            auto [iter, ret] = mPresets.emplace(key, FfsFogView());
            return std::get<FfsFogView>(iter->second);
        }
    }

    CEMeta(Editor) 
    FfsPrecipitationView GetPrecipitationView(const std::string& key)
    {
        if (mPresets.find(key) != mPresets.end())
        {
            return std::get<FfsPrecipitationView>(mPresets[key]);
        }
        else
        {
            auto [iter, ret] = mPresets.emplace(key, FfsPrecipitationView());
            return std::get<FfsPrecipitationView>(iter->second);
        }
    }

    CEMeta(Editor)
    void NotifyWeatherSettingChanged(bool bFlushPresets, SInt8 index);

    CEMeta(Editor)
    const std::string GetPresetsDirectory() const;

    CEMeta(Editor)
    const std::string GetFullPresetPath(const std::string& weatherType) const;

public:
    void InitPresets();
    bool UpdatePresets(const std::string& key, const FfsWeatherView& view);

    template<typename T>
    std::string GetFullWeatherName(T weather)
    {
        auto metaenum = gbf::reflection::query_meta_enum<T>();
        if (metaenum)
        {
            std::string weatherName = metaenum->enum_name();
            auto pos = weatherName.find_last_of(":") + 1;
            Assert(pos != std::string::npos);
            return weatherName.substr(pos).append("_").append(metaenum->GetItemName(weather));
        }
        else
        {
            return "";
        }
    }

    template<typename WeatherType>
    const FfsWeatherView& GetWeatherPreset(WeatherType weatherType)
    {
        const auto& name = GetFullWeatherName<WeatherType>(weatherType);
        Assert(mPresets.find(name) != mPresets.end());
        return mPresets[name];
    }

private:
    FfsWeatherSettingManager() = default;
    std::optional<SerializeNode> LoadPresetFile(const std::string& path);

    template<typename WeatherType, typename ViewType>
    void InitSinglePreset(std::string_view presetDir)
    {
        auto metaenum = gbf::reflection::query_meta_enum<WeatherType>();
        for (auto index = 0u; index < metaenum->GetCount(); ++index)
        {
            const auto& name = GetFullWeatherName<WeatherType>(static_cast<WeatherType>(index));
            const auto& filePath = fmt::format("{}{}{}", presetDir, name, ".json");
            const auto& fullPath = PathHelper::GetAbsolutePath(filePath);
            if (PathHelper::IsFileExist(fullPath))
            {
                auto node = LoadPresetFile(fullPath);
                if (node.has_value())
                {
                    ViewType view;
                    SerializeContext context;
                    view.Deserialize(*node, context);
                    mPresets.try_emplace(name, view);
                }
            }
        }
    }

private:
    FfsWeatherDashboard mSetting;
    FfsWeatherSettingEventData mEventData{&mSetting};
    std::unordered_map<std::string, FfsWeatherView> mPresets;

    bool mInited = false;;
};

}   // namespace cross