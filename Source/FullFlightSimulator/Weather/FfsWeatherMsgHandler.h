#pragma once

#include "FullFlightSimulatorAPI.h"
#include "FfsWeatherPreset.h"
#include "CECommon/Common/SystemEvent.h"

namespace cross {

struct FfsWeatherMsgEventData
{
    SInt32 randomSeed = 0;
    SInt32 usetype = -1;
    WeatherType weather = WeatherType::None;
    WeatherParameter content;
    bool depent_preset = false;
};

using FfsWeatherMsgEvent = SystemEvent<const FfsWeatherMsgEventData*>;

struct FfsWeatherFeaturesPack
{
    CEMeta(Reflect) UInt32 tire_marks;
    CEMeta(Reflect) UInt32 snow_scene;
    CEMeta(Reflect) UInt32 blw_snow;
    CEMeta(Reflect) UInt32 cloud1_typ;
    CEMeta(Reflect) UInt32 cloud2_typ;
    CEMeta(Reflect) UInt32 sheet_light;
    CEMeta(Reflect) UInt32 bolt_light;
    CEMeta(Reflect) UInt32 si_rainlvl;
    CEMeta(Reflect) UInt32 high_cld;
    CEMeta(Reflect) UInt32 turb_cld;
    CEMeta(Reflect) UInt32 contaminants;
};

/* Property name must equal FfsWeatherFeaturesPack's for runtime reflection. */
enum class CEMeta(Reflect) FfsWeatherFeaturesMask : UInt32
{
    undefined     = 0,
    tire_marks    = 1 << 0,
    snow_scene    = 1 << 1,
    blw_snow      = 1 << 2,
    cloud1_typ    = 1 << 3,
    cloud2_typ    = 1 << 4,
    sheet_light   = 1 << 5,
    bolt_light    = 1 << 6,
    si_rainlvl    = 1 << 7,
    high_cld      = 1 << 8,
    turb_cld      = 1 << 9,
    contaminants  = 1 << 10,
    features_num  = 11,
};

struct FfsWeatherMsgState
{
    static constexpr int ContaminantsModifier = 10;

    int mask_colour = 0;
    int mask_wind = 0;
    int mask_features = 0;
    int mask_snowcontrol = 0;
    int mask_thunderstorm = 0;
    int mask_stormlightning = 0;
    int rain_lvl;
    int colour;
    int stormlightning = 0;
    double n_wind;
    double e_wind;
    double n_gust_wind;
    double e_gust_wind;

    int snowcontrol;
    int thundercontrol;

    FfsWeatherFeaturesPack features;

    SInt32 random_number = 0;

    SInt32 temparature = 15;
};

class FullFlightSimulator_API FfsWeatherMsgHandler : public SystemEventManager<FfsWeatherMsgEvent>
{
public:
    static FfsWeatherMsgHandler& Instance() noexcept;

    ~FfsWeatherMsgHandler() = default;

    FfsWeatherMsgHandler(const FfsWeatherMsgHandler&) = delete;
    FfsWeatherMsgHandler& operator=(const FfsWeatherMsgHandler&) = delete;

    inline void SetReady(bool ready) { mReadyHandleMsg = ready; }
    inline bool IsReady() const { return mReadyHandleMsg; }

public:
    void FlushState(const FfsWeatherFeaturesPack& features, int rainLvl, int colour, double n_wind, double e_wind, double n_gust_wind, double e_gust_wind);
    void ResetState();

    UInt32 VerifySnowControl(UInt32 bit) const;
    UInt32 VerifyThunderControl(UInt32 bit) const;
    UInt32 VerifyWeatherColour(UInt32 colour) const;
    UInt32 VerifyWeatherWind(double nWind, double eWind, double nGustWind, double eGustWind) const;
    UInt32 VerifyWeatherFeatures(const FfsWeatherFeaturesPack&) const;
    UInt32 VerifyWeatherRainLevel(UInt32 rainLevel) const;
    UInt32 VerifyStormLightning(bool flag) const;
    void TriggerWeather();
    void TriggerWeatherColour();
    void TriggerWeatherFeatures();
    void TriggerWeatherWind(double nGustWind, double eGustWind);
    
    bool VerifyFogVisibility(FfsFogView const& inView) const;
    void TriggerFogVisibility(FfsFogView const& inView);

    bool VerifyTopCloud(FfsTopCloudView const& inView) const;
    void TriggerTopCloud(FfsTopCloudView const& inView);

    bool VerifyMidCloud(FfsMidCloudView const& inView) const;
    void TriggerMidCloud(FfsMidCloudView const& inView);

    bool VerifyLowCloud(FfsLowCloudView const& inView) const;
    void TriggerLowCloud(FfsLowCloudView const& inView);

    bool IsWeatherColourSand() const;
    bool IsWeatherColourDust() const;

    bool VerifyRandomNumber(SInt32 inRandomNumber) const;

private:
    template<typename UseType>
    void DispatchEvent(WeatherType weather, UseType usetype, SInt32 seed, SInt32 content = 0)
    {
        DispatchEvent<SInt32, UseType>(weather, usetype, content, seed, false);
    }

    template<typename DataType, typename UseType>
    void DispatchEvent(WeatherType inWeatherType, UseType inUseType, DataType inContent, SInt32 seed = 0, bool dependPreset = false)
    {
        SCOPED_CPU_TIMING(GroupFfsSystemG, "DispatchEvent");
        //if (!mReadyHandleMsg)
        //{
        //    return;
        //}
        mEventData.weather = inWeatherType;
        mEventData.usetype = static_cast<SInt32>(inUseType);
        mEventData.randomSeed = seed;
        mEventData.content = inContent;
        FfsWeatherMsgEvent e{&mEventData};
        DispatchImmediateEvent(e);
    }

public:
    const FfsWeatherMsgState& GetState() const { return mState; }
    FfsWeatherMsgState& GetState() { return mState; }

    const FfsFogView& GetFogView() const { return mFogVisibilityView; }
    const FfsTopCloudView& GetTopCloudView() const { return mTopCloudView; }
    const FfsMidCloudView& GetMidCloudView() const { return mMidCloudView; }
    const FfsLowCloudView& GetLowCloudView() const { return mLowCloudView; }

private:
    FfsWeatherMsgHandler() = default;

private:
    FfsFogView mFogVisibilityView;
    FfsTopCloudView mTopCloudView;
    FfsMidCloudView mMidCloudView;
    FfsLowCloudView mLowCloudView;

    mutable FfsWeatherMsgState mState;
    FfsWeatherMsgEventData mEventData;
    bool mReadyHandleMsg = false;
};

}   // namespace cross