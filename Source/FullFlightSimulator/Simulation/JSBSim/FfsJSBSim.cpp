#if JSBSIM_SIMULATOR

#include "FullFlightSimulator/Simulation/JSBSim/FfsJSBSim.h"
#include "CrossBase/FileSystem/PathHelper.h"
#include "CrossBase/Threading/TaskSystem.h"
#include "Profiling/Profiling.h"
#include "FullFlightSimulator/FfsSystemG.h"
#include "FullFlightSimulator/Context/FfsContext.h"
#include "FFSTransmission/Tbuspp/PacketHandlers/PacketUtil.h"
#include "FullFlightSimulator/VspReferenceGraph/RefGraph_RoleNode.h"
#include "FullFlightSimulator/Simulation/FfsSim.h"
#include "FullFlightSimulator/Simulation/FFSGroundCallback.h"

#define FEET_TO_METER 0.3048
#define METER_TO_FEET 3.2808398950131233595800524934383

#define FEET_TO_CENTIMETER 30.48
#define INCH_TO_CENTIMETER 2.54

#define FEET_PER_SEC_TO_KNOT 0.592484
#define KNOT_TO_FEET_PER_SEC 1.68781

namespace cross::ffs
{

JSBSimSyncRunnable::JSBSimSyncRunnable(const FfsSimConfig& config, cross::GameWorld* gameWorld)
	: SimSyncRunnable(config), mGameWorld(gameWorld)
	, mRestriction(60.0)
{
	InitJSBSim();
	preTime = std::chrono::system_clock::now();

	AddJsbSimPrinter();
}

JSBSimSyncRunnable::~JSBSimSyncRunnable()
{
	auto* ffsSys = mGameWorld->GetGameSystem<FfsSystemG>();
	// There are bugs that need to be fixed when restarting
	ffsSys->GetStatistics()->RemoveStatsPrinter(StatsDataFlag::Engine);
}

void JSBSimSyncRunnable::SetAircraftPosture(FlightPhase phase, FfsFlightStates state) {
	//ResetIC lon lat is not true
#ifndef BUILD_PURE_JSBSim
	auto IC = jsbPtr->IC;
	auto fdmex = jsbPtr->fdmex;
	//auto Winds = jsbPtr->Winds;
#endif
	IC->InitializeIC();
	IC->SetGeodLatitudeDegIC(state.lat);
	IC->SetLongitudeDegIC(state.lon);
	IC->SetAltitudeASLFtIC(state.alt);
	IC->SetPsiDegIC(state.yaw);
	IC->SetThetaDegIC(state.pitch);
	IC->SetPhiDegIC(state.roll);
	switch (phase)
	{
	case cross::ffs::Taxing:
		IC->SetVcalibratedKtsIC(0.0);
		IC->SetClimbRateFpsIC(0.0);
		fdmex->GetFCS()->SetThrottleCmd(-1, 0.2);
		fdmex->GetFCS()->SetGearCmd(1.0);
		fdmex->GetFCS()->SetDfCmd(0.0); // 0.4
		// pitch trim 0.0
		break;
	case cross::ffs::Takeoff:
		IC->SetVcalibratedKtsIC(0.0);
		IC->SetClimbRateFpsIC(0.0);
		fdmex->GetFCS()->SetThrottleCmd(-1, 1.0);
		fdmex->GetFCS()->SetGearCmd(1.0);
		fdmex->GetFCS()->SetDfCmd(0.0); // 0.4
		// pitch trim 0.2
		break;
	case cross::ffs::Cruise:
		//Winds->SetTurbType(JSBSim::FGWinds::ttCulp);
		//Winds->SetTurbGain(10.0);
		//Winds->SetTurbRate(100.0);
		//Winds->SetWindspeed20ft(100.0);
		//Winds->SetProbabilityOfExceedence(6.0);
		//IC->SetWindDirDegIC(63.206266);
		//IC->SetWindMagKtsIC(100.0);
		IC->SetVcalibratedKtsIC(250.0);
		IC->SetClimbRateFpsIC(0.0);
		fdmex->GetFCS()->SetThrottleCmd(-1, 1.0);
		fdmex->GetFCS()->SetGearCmd(0.0);
		fdmex->GetFCS()->SetDfCmd(0.0);
		// pitch trim 0.17
		break;
	//case cross::ffs::Descent:
	//	IC->SetVcalibratedKtsIC(180);
	//	IC->SetClimbRateFpsIC(20.0);
	//	break;
	case cross::ffs::Approach:
		IC->SetVcalibratedKtsIC(160);
		IC->SetClimbRateFpsIC(0.0);
		fdmex->GetFCS()->SetThrottleCmd(-1, 0.5);
		fdmex->GetFCS()->SetGearCmd(0.0);
		fdmex->GetFCS()->SetDfCmd(0.8);
		// pitch trim 0.2
		break;
	case cross::ffs::LandingTaxing:
		IC->SetVcalibratedKtsIC(70.0);
		IC->SetClimbRateFpsIC(0.0);
		fdmex->GetFCS()->SetThrottleCmd(-1, 0.45);
		fdmex->GetFCS()->SetGearCmd(1.0);
		fdmex->GetFCS()->SetDfCmd(0.8);
		// pitch trim 0.2
		break;
	case cross::ffs::EndFlight:
		IC->SetVcalibratedKtsIC(1.0);
		IC->SetClimbRateFpsIC(0.0);
		fdmex->GetFCS()->SetThrottleCmd(-1, 0.2);
		fdmex->GetFCS()->SetGearCmd(1.0);
		fdmex->GetFCS()->SetDfCmd(0.0);
		// pitch trim 0.32
		break;
	default:
		break;
	}

	// We must update HOG value before RunIC to get the correct HAT and WOW value
	UpdateGroundCache(state.lat, state.lon);
	fdmex->RunIC();
}

void JSBSimSyncRunnable::OutputEngineStatsLogs()
{
#ifdef BUILD_PURE_JSBSim
	if (!fdmex) return;
#else
	if (!jsbPtr) return;
	auto fdmex = jsbPtr->fdmex;
#endif

	std::unique_lock lock(mLogMutex);

	mEngineStatsLog.clear();

	auto ground_react = fdmex->GetGroundReactions();
	mEngineStatsLog.emplace_back(fmt::format("JsbSim {}", mConfig.AircraftModel.size() == 0 ? "None" : mConfig.AircraftModel));

	mEngineStatsLog.emplace_back(fmt::format("- ground_react"));
	mEngineStatsLog.emplace_back(fmt::format("  - static friction: {:.3f}", ground_react->GetStaticFFactor()));
	mEngineStatsLog.emplace_back(fmt::format("  - rolling friction: {:.3f}", ground_react->GetRollingFFactor()));
	mEngineStatsLog.emplace_back(fmt::format("  - dumpiness: {:.3f}", ground_react->GetBumpiness()));
	if (groundCallback)
	{
		mEngineStatsLog.emplace_back(fmt::format("  - HAT: {:.3f}m HOG: {:.3f}m", groundCallback->GetHAT(), groundCallback->GetGroundElevationM()));
	}

	mEngineStatsLog.emplace_back(fmt::format("EngineStats:"));

	auto engine = fdmex->GetPropulsion()->GetEngine(0);

	mEngineStatsLog.emplace_back(fmt::format("EngineType {} Starter {} Running {} Thrust {:.5} EngineRPM {:.5}", engine->GetType(), engine->GetStarter(), engine->GetRunning(), engine->GetThrust(), engine->GetThruster()->GetEngineRPM()));

	auto EngineType = engine->GetType();

	if (EngineType == JSBSim::FGEngine::EngineType::etTurbine)
	{
#ifdef	BUILD_PURE_JSBSim
		auto TurbinEngine = dynamic_cast<JSBSim::FGTurbine*>(engine.get());
#else
		auto TurbinEngine = dynamic_cast<JSBSim::FGTurbine*>(engine);
#endif

		mEngineStatsLog.emplace_back(fmt::format("N1 {:.3} N2 {:.3} Cutoff {} Augmentation {} Reversed {} Injection {} Iginition {}", TurbinEngine->GetN1(), TurbinEngine->GetN2(), TurbinEngine->GetCutoff(), TurbinEngine->GetAugmentation(), TurbinEngine->GetReversed(), TurbinEngine->GetInjection(), TurbinEngine->GetIgnition()));
	}

	{
		std::scoped_lock lock(mFlightCommandMutex);
		std::vector<std::string> messages;
		mCurrentFlightCommand.controls.OutputDebugMessage(messages);
		mAircraftState.GetDebugMessage(messages);
		for (int i = 0; i < mGearStatus.size(); i++)
		{
			mGearStatus[i].GetDebugMessage(messages);
		}

		for (auto& itr : messages)
		{
			mEngineStatsLog.emplace_back(itr);
		}
	}
	auto FCS = fdmex->GetFCS();
	mEngineStatsLog.emplace_back(fmt::format("Controls Throttle {} Elevator {} Aileron {} Rudder {} Spoiler {} Flap {} ParkingBrake {} LeftBrake {} RightBrake {} GearDown {}", FCS->GetThrottleCmd(0), FCS->GetDeCmd(), FCS->GetDaCmd(), FCS->GetDrCmd(), FCS->GetDspCmd(), FCS->GetDfCmd(), FCS->GetCBrake(), FCS->GetLBrake(), FCS->GetRBrake(), FCS->GetGearCmd()));
}

void JSBSimSyncRunnable::SetWindSProps(double windN, double windE, double windD)
{
#ifndef	BUILD_PURE_JSBSim
	auto Winds = jsbPtr->Winds;
#endif
	Winds->SetWindNED(windN, windE, windD);
	//Winds->SetWindspeed(windSpeed);
}

void JSBSimSyncRunnable::SetWindsByAltitude(double altitude)
{
	for (const auto& layer : windLayer) {
		if (altitude >= layer.first.first && altitude <= layer.first.second) {
			std::tuple<double, double, double> result = layer.second;
			SetWindSProps(std::get<0>(result), std::get<1>(result), std::get<2>(result));
			return;
		}
	}
	SetWindSProps(0, 0, 0);
	return;
}

void JSBSimSyncRunnable::SetWindLayers(double bottom, double top, double windN, double windE, double windD)
{
	bool isSet = false;
	for (const auto& layer : windLayer) {
		if (!(top <= layer.first.first || layer.first.second <= bottom)) {
			if (top > layer.first.first && bottom < layer.first.first && top <= layer.first.second) {
				top = layer.first.first;
			}
			else if (top > layer.first.second && bottom < layer.first.second && bottom>= layer.first.first) {
				bottom = layer.first.second;
			}
			else if (top > layer.first.second && bottom < layer.first.first) {
				SetWindLayers(bottom, layer.first.first, windN, windE, windD);
				SetWindLayers(layer.first.second, top, windN, windE, windD);
			}
			else {
				isSet = true;
			}
		}
	}
	if (top > bottom) {
		windLayer[pair<double, double>(bottom, top)] = tuple<double, double, double>(windN, windE, windD);
	}
}

void JSBSimSyncRunnable::SetTurbulence(double turbulence) {
	turb = turbulence;
}

//turbulence 0-1
 // milspec turbulence: 3=light, 4=moderate, 6=severe turbulence
// turbulence_gain normalized: 0: none, 1/3: light, 2/3: moderate, 3/3: severe
void JSBSimSyncRunnable::SetTurbulenceProp()
{
	if (turb == 0) {
		return;
	}
#ifndef	BUILD_PURE_JSBSim
	auto Winds = jsbPtr->Winds;
#endif
	Winds->SetTurbType(JSBSim::FGWinds::ttMilspec);
	double tmp = turb * turb;
	int tInt = 0;
	if (tmp <= 1.0 / 9.0) {
		tInt = (int)(tmp * 27.0);
	}
	else if (tmp <= 4.0 / 9.0) {
		tInt = (int)((tmp-1.0/9.0) * 3.0)+3;
	}
	else if (tmp <= 1.0) {
		tInt = (int)((tmp - 4.0 / 9.0) *1.8) + 4;
	}
	Winds->SetProbabilityOfExceedence(tInt);
	Winds->SetWindspeed20ft(20.0);
}


void JSBSimSyncRunnable::Run()
{
	//init
	TransmissionData syncRequest;
	bool isFirst = true;
	bool hadSetLoc = false;
	auto altBias = mConfig.FlightHeightBias;
	AircraftStateJSB simRes;
	int DebugCount = 0;
	bool changePhaseNeedSetLoc = false;
	FlightPhase tempPhase = FlightPhase::Null;
	//bool windsOn = false;
	bool switchPhase = false;
	do
	{
		while (gSimSyncQueue.try_pop(syncRequest)) {
			{
				std::scoped_lock lock(mFlightCommandMutex);
				mCurrentFlightCommand = syncRequest;
			}
			DebugCount++;
			auto status = syncRequest.mGameSession.mGameStatus;
			auto state = syncRequest.states;
			if (!hadSetLoc && status != GameStatus::Running && status != GameStatus::Pausing && status != GameStatus::Crashed) {
				simRes = AircraftStateJSB(state.lon, state.lat, state.alt, state.roll, state.pitch, state.yaw);
			}
			// switch phase
			if (syncRequest.mGameSession.mFlightPhase != cross::ffs::FlightPhase::Null&& syncRequest.mGameSession.mGameStatus!= GameStatus::Running && syncRequest.mGameSession.mGameStatus != GameStatus::Pausing) {
				//LOG_DEBUG("PhaseChange {}", syncRequest.mGameSession.mFlightPhase);
				this->setPhase = syncRequest.mGameSession.mFlightPhase;
				simRes = AircraftStateJSB(state.lon, state.lat, state.alt, state.roll, state.pitch, state.yaw);
				switchPhase = true;
				tempPhase = syncRequest.mGameSession.mFlightPhase;
			}
			//switch auto to manual
			else if (syncRequest.mGameSession.mFlightPhase != cross::ffs::FlightPhase::Null && syncRequest.mGameSession.mGameStatus == GameStatus::Running&& syncRequest.mGameSession.mApMode== FlightMode::Manual) {
				auto tempState = FfsFlightStates(state.lon, state.lat, state.alt*FEET_TO_METER, state.yaw, state.pitch, state.roll,state.groundSpeedKts,state.verticalSpeedFPM);
				simRes = AircraftStateJSB(tempState.lon, tempState.lat, tempState.alt, tempState.roll, tempState.pitch, tempState.yaw);
				mInitialized = false;				
				InitJSBSim();
				//PrepareJSBSIm(syncRequest, (syncRequest.states.alt) / FEET_TO_METER);
				if (mGameWorld != syncRequest.mGameWorld)
					mGameWorld = syncRequest.mGameWorld;
#ifdef BUILD_PURE_JSBSim
				Inertial->SetGroundCallback(groundCallback);
#else
				jsbPtr->SetGroundCallback(groundCallback);
#endif
				UpdateGroundCache();
				InitIC(AircraftStateJSB(tempState.lon, tempState.lat, tempState.alt / FEET_TO_METER, tempState.roll, tempState.pitch, tempState.yaw), syncRequest.mGameSession.mFlightPhase, groundCallback->GetGroundElevationM() / FEET_TO_METER);
#ifdef BUILD_PURE_JSBSim
				IC->SetVcalibratedKtsIC(state.groundSpeedKts);
				IC->SetClimbRateFpsIC(state.verticalSpeedFPM / 60.0);
				fdmex->RunIC();
#else
				//Set speed from auto mode
				jsbPtr->IC->SetVcalibratedKtsIC(state.groundSpeedKts);
				jsbPtr->IC->SetClimbRateFpsIC(state.verticalSpeedFPM/60.0);
				jsbPtr->fdmex->RunIC();
				//TODO: smooth
#endif
			}
			if (status == GameStatus::Preparing) {
				if (!isFirst) {
					syncRequest.states.alt -= altBias;
				}
				else {
					InitJSBSim();
					isFirst = false;
				}
			}
			else if (status == GameStatus::SettingInitLocation) {
				syncRequest.states.alt -= altBias;
				if (switchPhase) 
				{
					SetAircraftPosture(tempPhase, state);
					DoTrim(tempPhase);
					
					if (tempPhase == cross::ffs::FlightPhase::LandingTaxing || tempPhase == cross::ffs::FlightPhase::Taxing || tempPhase == cross::ffs::FlightPhase::Takeoff || tempPhase == cross::ffs::FlightPhase::EndFlight) {
						changePhaseNeedSetLoc = true;
						//tempPhase = syncRequest.mGameSession.mFlightPhase;
						hadSetLoc = false;
					}
					else {
						tempPhase = FlightPhase::Null;
#ifndef BUILD_PURE_JSBSim
						auto Propagate= jsbPtr->fdmex->GetPropagate();
#endif
						JSBSim::FGLocation LocationVRP = Propagate->GetLocation();
						//Vec3 ECEFLocation = Vec3(LocationVRP(1) * 0.3048, LocationVRP(2) * 0.3048, LocationVRP(3) * 0.3048);
						auto Latitude = LocationVRP.GetGeodLatitudeDeg();
						auto Longitude = LocationVRP.GetLongitudeDeg();
						double altitude = LocationVRP.GetGeodAltitude() * 0.3048;
						double hat = Propagate->GetDistanceAGL() * 0.3048;

						auto yaw = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::ePsi));
						auto pitch = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::eTht));
						auto roll = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::ePhi));
						simRes.lon = Longitude;
						simRes.lat = Latitude;
						simRes.alt = altitude;
						simRes.yaw = yaw;
						simRes.pitch = pitch;
						simRes.roll = roll;
					}
					switchPhase = false;
				}
				//The aircraft does not require this during the airborne phase, but it is necessary on the ground
				if (!hadSetLoc) {
#ifndef BUILD_PURE_JSBSim
					auto GroundReactions = jsbPtr->fdmex->GetGroundReactions();
					auto fdmex = jsbPtr->fdmex;
#endif
					//PrepareJSBSIm(syncRequest, (syncRequest.states.alt) / FEET_TO_METER);
					if(mGameWorld!= syncRequest.mGameWorld)
						mGameWorld = syncRequest.mGameWorld;
					groundCallback = new FFSGroundCallback(mGameWorld);
					groundCallback->SetFdmExec(fdmex);
					//groundCallback->SetOnGround(false);
#ifdef BUILD_PURE_JSBSim
					Inertial->SetGroundCallback(groundCallback);
#else
					jsbPtr->SetGroundCallback(groundCallback);
#endif
					InitIC(AircraftStateJSB(state.lon, state.lat, state.alt / FEET_TO_METER, state.roll, state.pitch, state.yaw), syncRequest.mGameSession.mFlightPhase, syncRequest.states.alt / FEET_TO_METER);
					{
						double nose_mlg_height = 0.0;
						double leg_mlg_height = 0.0;
						double nose_mlg_len = 0.0;
						double leg_mlg_len = 0.0;
						for (int i = 0; i < GroundReactions->GetNumGearUnits(); i++)
						{
							auto Gear = GroundReactions->GetGearUnit(i);
							auto GearBodyLocation = Gear->GetBodyLocation() * FEET_TO_CENTIMETER;
							auto gearName = Gear->GetName();
							std::transform(gearName.begin(), gearName.end(), gearName.begin(),
								[](unsigned char c) { return std::tolower(c); });
							if (gearName.find("nose") != std::string::npos && gearName.find("gear") != std::string::npos) {
								nose_mlg_height = GearBodyLocation(3) / 100.0;
								nose_mlg_len = GearBodyLocation(1) / 100.0;
							}
							else if (gearName.find("left") != std::string::npos && gearName.find("gear") != std::string::npos) {
								leg_mlg_height = GearBodyLocation(3) / 100.0;
								leg_mlg_len = GearBodyLocation(1) / 100.0;
							}
						}
						lenToLeg = leg_mlg_height > nose_mlg_height ? leg_mlg_height : nose_mlg_height;
						double xlength = abs(nose_mlg_len - leg_mlg_len);
						double ylength = abs(leg_mlg_height - nose_mlg_height);
						pitchAngle = atan2(ylength, xlength) * 180.0 / M_PI;
					}

					UpdateGroundCache();
					double hog = groundCallback->GetGroundElevationM();
					simRes.alt = hog + lenToLeg *cos(pitchAngle * M_PI / 180.0);
					state.alt = simRes.alt;
					state.pitch = -pitchAngle;
					LOG_DEBUG("InitAltLog:{} HOG: {} lenToLeg: {} pitchAngle:{}", simRes.alt, hog, lenToLeg, pitchAngle);
#ifndef BUILD_PURE_JSBSim
					delete jsbPtr;
					jsbPtr = nullptr;
#endif
					mInitialized = false;
					InitJSBSim();
					//PrepareJSBSIm(syncRequest, (syncRequest.states.alt) / FEET_TO_METER);
					if (mGameWorld != syncRequest.mGameWorld)
						mGameWorld = syncRequest.mGameWorld;
#ifdef BUILD_PURE_JSBSim
					Inertial->SetGroundCallback(groundCallback);
#else
					jsbPtr->SetGroundCallback(groundCallback);
#endif
					if (changePhaseNeedSetLoc) {
						changePhaseNeedSetLoc = false;
						syncRequest.mGameSession.mFlightPhase = tempPhase;
						tempPhase = FlightPhase::Null;
					}
					InitIC(AircraftStateJSB(state.lon, state.lat, state.alt / FEET_TO_METER, state.roll, state.pitch, state.yaw), syncRequest.mGameSession.mFlightPhase, hog / FEET_TO_METER);
					simRes.lon = state.lon;
					simRes.lat = state.lat;
					simRes.alt = state.alt;
					simRes.yaw = state.yaw;
					simRes.pitch = state.pitch;
					simRes.roll = state.roll;

					//AddJsbSimPrinter();
					hadSetLoc = true;
					std::string loc = "LocSet ";
					loc += std::to_string(lenToLeg);
					gSimSyncMsgQueue.try_push(loc);
				}
			}
			else if (status == GameStatus::Running) {
				mApMode = syncRequest.mGameSession.mApMode;
				//cxtest
				//UpdateGroundCache();
				if (mApMode == FlightMode::Manual)
				{
					// Update ground cache before running JSBSim
					UpdateGroundCache();
					auto controls = syncRequest.controls;
#ifdef BUILD_PURE_JSBSim
					copy_to_JSBsim(controls);
					fdmex->Run();
					simRes = copy_from_JSBsim();
#else
					SetTurbulenceProp();
					simRes = jsbPtr->RunJSBSim(controls.get_throttles(), controls.get_rudder(), controls.get_elevator(), controls.get_aileron(), controls.get_aileron_trim(), controls.get_elevator_trim(), controls.get_rudder_trim(), controls.get_spoilers(), controls.get_flaps(), controls.get_brake_parking(), controls.get_brake_left(), controls.get_brake_right(), controls.get_gear_down());
					//test winds
					/*auto altt = simRes.alt / FEET_TO_METER;
					if (altt > 5000 && !windsOn) {
						windsOn = true;
						jsbPtr->Winds->SetWindNED(100,100,100);
						jsbPtr->Winds->SetWindspeed(1000);

					}
					else if (altt <= 4000 && windsOn) {
						windsOn = false;
						jsbPtr->Winds->SetWindNED(0, 0, 0);
						jsbPtr->Winds->SetWindspeed(0);
					}*/
#endif
					AricraftStateUpdate();
					SetWindsByAltitude(simRes.alt);
				}
			}
			else if (status == GameStatus::Pausing || status == GameStatus::Crashed) {
				simRes.alt -= altBias;
			}
			if ((mApMode == FlightMode::Manual) && status!=GameStatus::Null) {
				ce::net::RTSJSBsimControl syncMessage;

				syncMessage.set_altitude(simRes.alt + altBias);
				syncMessage.set_lon(simRes.lon);
				syncMessage.set_lat(simRes.lat);
#ifndef BUILD_PURE_JSBSim
				if ((status == GameStatus::Running || status == GameStatus::Pausing|| status == GameStatus::Crashed)) {
					syncMessage.set_roll(MathUtils::ConvertToDegrees(simRes.roll));
					syncMessage.set_pitch(MathUtils::ConvertToDegrees(simRes.pitch));
					syncMessage.set_yaw(MathUtils::ConvertToDegrees(simRes.yaw));
				}
				else 
#endif
				{
					syncMessage.set_roll(simRes.roll);
					syncMessage.set_pitch(simRes.pitch);
					syncMessage.set_yaw(simRes.yaw);
				}
				if (DebugCount % 100 == 0) {
					auto hog = 0.0;
					if (groundCallback)
						hog = groundCallback->GetGroundElevationM();
					LOG_DEBUG("GameStageChange:{} Phase: {} locationToVSD lon: {} lat: {} alt: {} roll: {} pitch: {} yaw: {} hog: {}", syncRequest.mGameSession.mGameStatus, syncRequest.mGameSession.mFlightPhase, simRes.lon, simRes.lat, simRes.alt, simRes.roll, simRes.pitch, simRes.yaw, hog);
				}

				if (FfsSetting::GetFfsSetting().TbusEnable)
				{
					auto* packetPtr = PacketUtil::AssembleRTSPacket_ToVSD(ce::net::RTSControlPacket_EnumRTSCommand_RTSJSBSimControl, syncMessage);
					int ret = cross::ffs::TbusppClient::Instance().SendMsg_VSD({ packetPtr });
				}
				else
				{
					gSimSyncControlQueue.try_push(SimSyncControlPacket(syncMessage));
				}
			}
		}

		OutputEngineStatsLogs();
		mRestriction.Sync();
	} while (!mQuitRequested);
}

std::string JSBSimSyncRunnable::GetThreadName() const
{
	return JSBSimSyncRunnable::ThreadName;
}


void JSBSimSyncRunnable::InitIC(AircraftStateJSB state, FlightPhase phase, double groundAltFT) 
{
#ifndef BUILD_PURE_JSBSim
	auto Propulsion = jsbPtr->Propulsion;
	auto IC = jsbPtr->IC;
	auto fdmex = jsbPtr->fdmex;
#endif
	Propulsion->InitRunning(-1);
	IC->SetGeodLatitudeDegIC(state.lat);
	IC->SetLongitudeDegIC(state.lon);
	IC->SetAltitudeASLFtIC(state.alt);
	IC->SetTerrainElevationFtIC(groundAltFT);
	IC->SetPsiDegIC(state.yaw);
	IC->SetThetaDegIC(state.pitch);
	IC->SetPhiDegIC(state.roll);
	if (phase != FlightPhase::Null) {
		SetAircraftPosture(phase, FfsFlightStates(state.lon, state.lat, state.alt, state.yaw, state.pitch, state.roll));
	}
	else {
		fdmex->RunIC();
	}

	DoTrim(phase);

	for (int index = 0; index < Propulsion->GetNumTanks(); index++)
	{
		auto tank = Propulsion->GetTank(index);
		tank->SetContentsGallons(tank->GetContentsGallons() * 0.5);
	}
	Propulsion->SetFuelFreeze(true);
}


void JSBSimSyncRunnable::InitJSBSim()
{
	if (!mInitialized) {
		std::string jsbsimAssetPath = std::format("{}/Contents/GlobalAssets/JSBSim", PathHelper::GetCurrentDirectoryPath());
		std::string aircraftName = mConfig.AircraftModel;
		std::string resetName = "reset03.xml";
#ifdef BUILD_PURE_JSBSim
		fdmex = new JSBSim::FGFDMExec();
		fdmex->SetRootDir(SGPath(jsbsimAssetPath));
		fdmex->SetAircraftPath(SGPath("aircraft"));
		fdmex->SetEnginePath(SGPath("engine"));
		fdmex->SetSystemsPath(SGPath("systems"));
		fdmex->SetOutputPath(SGPath("."));
		bool result = fdmex->LoadModel(SGPath("aircraft"),
			SGPath("engine"),
			SGPath("systems"),
			aircraftName);
		if (!result)
		{
			LOG_ERROR("JSBSim load aircraft {} error!", aircraftName);
			SAFE_DELETE(fdmex);
			return;
		}
		Atmosphere = fdmex->GetAtmosphere();
		Winds = fdmex->GetWinds();
		FCS = fdmex->GetFCS();
		MassBalance = fdmex->GetMassBalance();
		Propulsion = fdmex->GetPropulsion();
		Aircraft = fdmex->GetAircraft();
		Propagate = fdmex->GetPropagate();
		Auxiliary = fdmex->GetAuxiliary();
		Inertial = fdmex->GetInertial();
		//Inertial->SetGroundCallback(new UEGroundCallback(this)); // Register ground callback.
		Aerodynamics = fdmex->GetAerodynamics();
		GroundReactions = fdmex->GetGroundReactions();
		Accelerations = fdmex->GetAccelerations();
		IC = fdmex->GetIC();
		PropertyManager = fdmex->GetPropertyManager();
#else		
		jsbPtr = new JSBSimInterface(aircraftName, SGPath(jsbsimAssetPath));
		jsbPtr->fdmex->Setdt(1.0 / 120.0f);
		jsbPtr->fdmex->SetDebugLevel(0);
#endif
		mInitialized = true;
	}
}

//void JSBSimSyncRunnable::PrepareJSBSIm(const TransmissionData& initCmd, double mGroudFT)
//{
//	auto state = initCmd.states;
//	//jsbPtr->InitIC(AircraftStateJSB(state.lon, state.lat, state.alt, state.roll, state.pitch, state.yaw), nullptr);
//	mGameWorld = initCmd.mGameWorld;
//	/*InitIC(AircraftStateJSB(state.lon, state.lat, state.alt/FEET_TO_METER, state.roll, state.pitch, state.yaw), mGroudFT);*/
//}

void JSBSimSyncRunnable::AricraftStateUpdate() {
#ifndef BUILD_PURE_JSBSim
	auto FCS = jsbPtr->fdmex->GetFCS();
	auto Auxiliary = jsbPtr->fdmex->GetAuxiliary();
	auto Propagate = jsbPtr->fdmex->GetPropagate();
	auto Aerodynamics = jsbPtr->fdmex->GetAerodynamics();
	auto MassBalance = jsbPtr->fdmex->GetMassBalance();
	auto GroundReactions = jsbPtr->fdmex->GetGroundReactions();
	auto Accelerations = jsbPtr->fdmex->GetAccelerations();
#endif
	std::scoped_lock mAirLock(mFlightCommandMutex);

	mAircraftState.ElevatorPosition = FCS->GetDePos(JSBSim::ofDeg);
	mAircraftState.LeftAileronPosition = FCS->GetDaLPos(JSBSim::ofDeg);
	mAircraftState.RightAileronPosition = FCS->GetDaRPos(JSBSim::ofDeg);
	mAircraftState.RudderPosition = -1 * FCS->GetDrPos(JSBSim::ofDeg);
	mAircraftState.FlapPosition = FCS->GetDfPos(JSBSim::ofDeg);
	mAircraftState.SpeedBrakePosition = FCS->GetDsbPos(JSBSim::ofDeg);
	mAircraftState.SpoilersPosition = FCS->GetDspPos(JSBSim::ofDeg);
	
	// Speed
	mAircraftState.CalibratedAirSpeedKts = Auxiliary->GetVcalibratedKTS();
	mAircraftState.GroundSpeedKts = Auxiliary->GetVground() * FEET_PER_SEC_TO_KNOT;
	mAircraftState.TotalVelocityKts = Auxiliary->GetVt() * FEET_PER_SEC_TO_KNOT;
	mAircraftState.VelocityNEDfps = { static_cast<float>(Propagate->GetVel(JSBSim::FGJSBBase::eNorth)), static_cast<float>(Propagate->GetVel(JSBSim::FGJSBBase::eEast)), -static_cast<float>(Propagate->GetVel(JSBSim::FGJSBBase::eDown)) };
	mAircraftState.AltitudeASLFt = Propagate->GetAltitudeASL();
	mAircraftState.AltitudeRateFtps = Propagate->Gethdot();
	mAircraftState.StallWarning = Aerodynamics->GetStallWarn();
	const auto& pqr =Propagate->GetPQR();
	const auto& pqrDot = Accelerations->GetPQRdot();
	mAircraftState.AngularVelocity = { pqr(3), pqr(2), pqr(1) };
	mAircraftState.AngularAcceleration = { pqrDot(3), pqrDot(2), pqrDot(1) };

	// Transformation
	// Convert to engine coordinate and length unit
	JSBSim::FGLocation LocationVRP = Propagate->GetLocation();
	mAircraftState.ECEFLocation = Double3{ LocationVRP(2), LocationVRP(3), -LocationVRP(1) } * FEET_TO_CENTIMETER;
	mAircraftState.Latitude = LocationVRP.GetGeodLatitudeDeg();
	mAircraftState.Longitude = LocationVRP.GetLongitudeDeg();
	auto yaw = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::ePsi));
	auto pitch = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::eTht));
	auto roll = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::ePhi));
	mAircraftState.LocalEulerAngles.x = yaw;
	mAircraftState.LocalEulerAngles.y = pitch;
	mAircraftState.LocalEulerAngles.z = roll;
	mAircraftState.EulerRates = { static_cast<float>(Auxiliary->GetEulerRates(JSBSim::FGJSBBase::ePhi)), static_cast<float>(Auxiliary->GetEulerRates(JSBSim::FGJSBBase::eTht)), static_cast<float>(Auxiliary->GetEulerRates(JSBSim::FGJSBBase::ePsi)) };
	mAircraftState.AltitudeAGLFt = Propagate->GetDistanceAGL();
	mAircraftState.Weight = MassBalance->GetWeight();
	mAircraftState.Alpha = MathUtils::ConvertToDegrees(Auxiliary->Getalpha());

	// Some aircraft state related data cannot be accessed from here, use a callback to update them 
	if (mAircraftStateUpdateCB)
	{
		mAircraftStateUpdateCB(mAircraftState);
	}

	// force a sim crashed if crashed (altitude AGL < 0)
	//if (AircraftState.AltitudeAGLFt < -10.0 || AircraftState.AltitudeASLFt < -10.0) {
		//Exec->SuspendIntegration();

	//}

	auto tNow = std::chrono::system_clock::now();
	auto deltaTime = std::chrono::duration_cast<std::chrono::milliseconds>(tNow - preTime).count();
	AircraftDisplay aircraftDisplay;
	aircraftDisplay.mHeading = yaw;
	aircraftDisplay.mAltitudeAGL = mAircraftState.AltitudeAGLFt;
	aircraftDisplay.mAltitudeASL = mAircraftState.AltitudeASLFt;
	aircraftDisplay.mGroundSpeedKts = mAircraftState.GroundSpeedKts;
	aircraftDisplay.mFlaps = FCS->GetDfPos(JSBSim::ofNorm) / 0.8;
	//aircraftDisplay.mFlaps = (AircraftState.VelocityNEDfps.z - preVerSpeed) * FEET_TO_METER / (deltaTime * 0.001 * 9.8);
	aircraftDisplay.mVerticalSpeedFPM = static_cast<double>(mAircraftState.VelocityNEDfps.z) * 60.0;
	aircraftDisplay.mTrim = FCS->GetPitchTrimCmd();
	aircraftDisplay.mGear = FCS->GetGearCmd();
	aircraftDisplay.mApproachAngle = atan2(((static_cast<double>(-mAircraftState.VelocityNEDfps.z) * FEET_PER_SEC_TO_KNOT)), mAircraftState.GroundSpeedKts) * _Rad2Deg;
	//aircraftDisplay.mLeftEngine = FCS->GetThrottlePos(0) * 15000.0;
	//aircraftDisplay.mRightEngine = FCS->GetThrottlePos(1) * 15000.0;
	double engineSign = 1.0;
#ifdef BUILD_PURE_JSBSim
	if (fdmex) {
		
		std::array<double, 2> throttles;
		for (int index = 0; index < 2; index++)
		{
			auto engine = fdmex->GetPropulsion()->GetEngine(0);
			auto TurbineEngine = dynamic_cast<JSBSim::FGTurbine*>(engine.get());
			aircraftDisplay.mLeftEngine = TurbineEngine->GetN1() / TurbineEngine->GetMaxN1() * 100;
			aircraftDisplay.mRightEngine = TurbineEngine->GetN2() / TurbineEngine->GetMaxN2() * 100;
			engineSign = TurbineEngine->GetReversed() ? -1.0 : 1.0;
			throttles[index] = FCS->GetThrottlePos(index) * engineSign;
		}
		aircraftDisplay.mThrottle = throttles[0];
		aircraftDisplay.mThrottle_1 = throttles[0];
		aircraftDisplay.mThrottle_2 = throttles[1];
		aircraftDisplay.mThrottle_3 = 0.0;
		aircraftDisplay.mThrottle_4 = 0.0;
	}
#else
	if (jsbPtr) {
		auto* fdmex = jsbPtr->fdmex;
		std::array<double, 2> throttles;
		for (int index = 0; index < 2; index++)
		{
			auto* engine = fdmex->GetPropulsion()->GetEngine(0);
			auto* TurbineEngine = dynamic_cast<JSBSim::FGTurbine*>(engine);
			aircraftDisplay.mLeftEngine = TurbineEngine->GetN1() / TurbineEngine->GetMaxN1() * 100;
			aircraftDisplay.mRightEngine = TurbineEngine->GetN2() / TurbineEngine->GetMaxN2() * 100;
			engineSign = TurbineEngine->GetReversed() ? -1.0 : 1.0;
			throttles[index] = FCS->GetThrottlePos(index) * engineSign;
		}
		aircraftDisplay.mThrottle = throttles[0];
		aircraftDisplay.mThrottle_1 = throttles[0];
		aircraftDisplay.mThrottle_2 = throttles[1];
		aircraftDisplay.mThrottle_3 = 0.0;
		aircraftDisplay.mThrottle_4 = 0.0;
}
#endif
	

	aircraftDisplay.mCourse = 0.0;
	aircraftDisplay.mLon = mAircraftState.Longitude;
	aircraftDisplay.mLat = mAircraftState.Latitude;
	aircraftDisplay.mRoll = roll;
	aircraftDisplay.mPitch = pitch;
	aircraftDisplay.mPhase = static_cast<int>(setPhase);
	preVerSpeed = mAircraftState.VelocityNEDfps.z;
	gAircraftDisplayQueue.try_push(aircraftDisplay);

	mAircraftDisplay = aircraftDisplay;
	preTime = tNow;

	//local gravity center;
	//for now we assume plane is in origin; 
	JSBSim::FGColumnVector3 CGLocationStructural = MassBalance->StructuralToBody(JSBSim::FGColumnVector3()) * FEET_TO_CENTIMETER;

	// JSBSim structural frame: x -> back; y -> right; z -> up
	// JSBSim body frame: x -> front; y -> right; z -> down
	Double3 CGLocalPosition = { CGLocationStructural(2), -CGLocationStructural(3), CGLocationStructural(1) };

	Double4x4 transform = Double4x4::CreateTranslation(CGLocalPosition.x, CGLocalPosition.y, CGLocalPosition.z);

	mGearStatus.resize(GroundReactions->GetNumGearUnits());
	for (int i = 0; i < GroundReactions->GetNumGearUnits(); i++)
	{
		auto Gear = GroundReactions->GetGearUnit(i);

		mGearStatus[i].NormalizedPosition = Gear->GetGearUnitPos();
		mGearStatus[i].IsBogey = Gear->IsBogey();
		mGearStatus[i].HasWeightOnWheel = Gear->GetWOW();
		mGearStatus[i].WheelRollLinearVelocityMetersPerSec = Gear->GetWheelRollVel() * FEET_TO_METER;
		mGearStatus[i].IsUp = Gear->GetGearUnitUp();
		mGearStatus[i].IsDown = Gear->GetGearUnitDown();
		mGearStatus[i].Name = std::string(Gear->GetName().c_str());

		// JSBSim body frame: x -> front; y -> right; z -> down
		auto GearBodyLocation = Gear->GetBodyLocation() * FEET_TO_CENTIMETER;
		mGearStatus[i].RelativeLocation = { GearBodyLocation(2), -GearBodyLocation(3), GearBodyLocation(1) };
		//if "gear" is not retractable and has weight on it, it's possible to crash.
		if (Gear->GetWOW() && (!Gear->GetRetractable()||mAircraftState.VelocityNEDfps.z<-15.0f)) {
			if (mCrashCB)
				mCrashCB();
		}
	}
}

bool JSBSimSyncRunnable::UpdateGroundCache()
{
#ifdef BUILD_PURE_JSBSim
	if (!fdmex || !groundCallback)
		return false;
#else
	if (!jsbPtr || !groundCallback)
		return false;
	auto fdmex = jsbPtr->fdmex;
#endif

	JSBSim::FGLocation cartFt = fdmex->GetAuxiliary()->GetLocationVRP();
	SGVec3d cartM = SGVec3d(cartFt(1), cartFt(2), cartFt(3))* FEET_TO_METER;
	SGGeod geod = SGGeod::fromCart(cartM);
	groundCallback->GetElevationM(SGGeod::fromGeodM(geod, 10000.0));
	return true;
}

bool JSBSimSyncRunnable::UpdateGroundCache(double latitude, double longitude)
{
	SGGeod geod;
	geod.setLatitudeDeg(latitude);
	geod.setLongitudeDeg(longitude);
	groundCallback->GetElevationM(SGGeod::fromGeodM(geod, 10000.0));
	return true;
}

void JSBSimSyncRunnable::DoTrim(FlightPhase phase) const
{
	if (!mInitialized)
	{
		return;
	}

	JSBSim::TrimMode trimMode = JSBSim::TrimMode::tNone;
	if (phase == FlightPhase::Cruise || phase == FlightPhase::Descent || phase == FlightPhase::Approach)
	{
		trimMode = JSBSim::TrimMode::tFull;
	}
	else
	{
		trimMode = JSBSim::TrimMode::tGround;
	}
#ifdef BUILD_PURE_JSBSim
	JSBSim::FGTrim* trimmer = new JSBSim::FGTrim(fdmex, trimMode);
#else
	JSBSim::FGTrim* trimmer = new JSBSim::FGTrim(jsbPtr->fdmex, trimMode);
#endif
	if (!trimmer->DoTrim()) {
		trimmer->Report();
	}
	delete trimmer;
}

#ifdef BUILD_PURE_JSBSim
bool JSBSimSyncRunnable::copy_to_JSBsim(FfsSimControl controls)
{
	if (fdmex == nullptr)
		return false;
	unsigned int i;
	fgControls = &controls;
	FCS->SetDaCmd(fgControls->get_aileron());
	FCS->SetRollTrimCmd(fgControls->get_aileron_trim());
	FCS->SetDeCmd(fgControls->get_elevator());
	FCS->SetPitchTrimCmd(fgControls->get_elevator_trim());
	FCS->SetDrCmd(-fgControls->get_rudder());
	FCS->SetDsCmd(fgControls->get_rudder());
	FCS->SetYawTrimCmd(-fgControls->get_rudder_trim());
	FCS->SetDfCmd(fgControls->get_flaps());
	double simTime = fdmex->GetSimTime();
	auto pm = fdmex->GetPropertyManager();
	FCS->SetDsbCmd(0.0);
	FCS->SetLBrake(std::max(fgControls->get_brake_left(), fgControls->get_brake_parking()));
	FCS->SetRBrake(std::max(fgControls->get_brake_right(), fgControls->get_brake_parking()));
	FCS->SetCBrake(std::max(fgControls->get_brake_parking(), fgControls->get_brake_parking()));
	FCS->SetGearCmd(fgControls->get_gear_down());

	for (i = 0; i < Propulsion->GetNumEngines(); i++) {
		//SGPropertyNode* node = fgGetNode("engines/engine", i, true);

		/*FCS->SetThrottleCmd(i,fgControls->get_throttle(i));
		FCS->SetMixtureCmd(i,fgControls->get_mixture(i));*/
		FCS->SetThrottleCmd(i, fgControls->get_throttle(i));
		FCS->SetMixtureCmd(i, 1);
		//FCS->SetPropAdvanceCmd(i,fgControls->get_prop_advance(i));
		//FCS->SetFeatherCmd(i,fgControls->get_feather(i));

		switch (Propulsion->GetEngine(i)->GetType()) {
		case JSBSim::FGEngine::etPiston:
		{ // FGPiston code block
			std::shared_ptr < JSBSim::FGPiston> eng = std::static_pointer_cast<JSBSim::FGPiston>(Propulsion->GetEngine(i));
			eng->SetMagnetos(3);
			break;
		} // end FGPiston code block
		case JSBSim::FGEngine::etTurbine:
		{ // FGTurbine code block
			auto TurbineEngine = std::static_pointer_cast<JSBSim::FGTurbine>(Propulsion->GetEngine(i));
			TurbineEngine->SetReverse(false);
			TurbineEngine->SetCutoff(false);
			TurbineEngine->SetIgnition(false);
			TurbineEngine->SetAugmentation(false);
			TurbineEngine->SetInjection(false);
			break;
		} // end FGTurbine code block
		case JSBSim::FGEngine::etRocket:
		{ // FGRocket code block
//        FGRocket* eng = (FGRocket*)Propulsion->GetEngine(i);
			break;
		} // end FGRocket code block
		case JSBSim::FGEngine::etTurboprop:
		{ // FGTurboProp code block
			std::shared_ptr < JSBSim::FGTurboProp> eng = std::static_pointer_cast<JSBSim::FGTurboProp>(Propulsion->GetEngine(i));
			eng->SetReverse(fgControls->get_reverser(i));
			eng->SetCutoff(fgControls->get_cutoff(i));
			// eng->SetIgnition(fgControls->get_ignition(i) );

			eng->SetGeneratorPower(fgControls->get_generator_breaker(i));
			eng->SetCondition(fgControls->get_condition(i));
			break;
		} // end FGTurboProp code block
		default:
			break;
		}


		Propulsion->GetEngine(i)->SetStarter(false);
		Propulsion->GetEngine(i)->SetRunning(true);
	}
	for (i = 0; i < Propulsion->GetNumTanks(); i++) {
		//SGPropertyNode* node = fgGetNode("/consumables/fuel/tank", i, true);
		std::shared_ptr<JSBSim::FGTank> tank = Propulsion->GetTank(i);
		//double fuelDensity = node->getDoubleValue("density-ppg");
		double fuelDensity = 0;
		if (fuelDensity < 0.1)
			fuelDensity = 6.0; // Use average fuel value

		tank->SetDensity(fuelDensity);
		//tank->SetContents(node->getDoubleValue("level-lbs"));
	}
	fgControls = nullptr;
	return true;
}

AircraftStateJSB JSBSimSyncRunnable::copy_from_JSBsim()
{
	if (fdmex == nullptr)
		return AircraftStateJSB();
	//// Transformation
	JSBSim::FGLocation LocationVRP = Propagate->GetLocation();

	//Vec3 ECEFLocation = Vec3(LocationVRP(1) * 0.3048, LocationVRP(2) * 0.3048, LocationVRP(3) * 0.3048);
	auto Latitude = LocationVRP.GetGeodLatitudeDeg();
	auto Longitude = LocationVRP.GetLongitudeDeg();
	double altitude = LocationVRP.GetGeodAltitude() * 0.3048;
	double hat = Propagate->GetDistanceAGL() * 0.3048;

	auto yaw = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::ePsi));
	auto pitch = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::eTht));
	auto roll = MathUtils::ConvertToDegrees(Propagate->GetEuler(JSBSim::FGJSBBase::ePhi));
	AircraftStateJSB res;
	res.lon = Longitude;
	res.lat = Latitude;
	res.alt = altitude;
	res.yaw = yaw;
	res.pitch = pitch;
	res.roll = roll;
	return res;
}
#endif

//maybe need delete 
void JSBSimSyncRunnable::AddJsbSimPrinter()
{
	auto ContextStatePrinter = [this](FrameVector<std::string>* printerPtr) -> void {
		std::shared_lock mLogLock(mLogMutex);

		for(auto & itr : mEngineStatsLog)
		{
			printerPtr->EmplaceBack(itr);
		}
	};

	auto* ffsSys = mGameWorld->GetGameSystem<FfsSystemG>();
	// There are bugs that need to be fixed when restarting
	ffsSys->GetStatistics()->AddStatsPrinter(StatsDataFlag::Engine, ContextStatePrinter);
}

} // namespace cross::ffs

#endif
