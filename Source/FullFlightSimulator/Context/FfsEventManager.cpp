#pragma warning(disable : 4996)

#include "EnginePrefix.h"
#include "FfsEventManager.h"
#include "Runtime/GameWorld/ReflectionProbeSystemG.h"

#include "Runtime/GameWorld/FoliageSystemG.h"
#include "Runtime/GameWorld/FoliageComponentG.h"
#include "Runtime/GameWorld/SkyLightSystemG.h"
#include "Runtime/GameWorld/LightSystemG.h"

#include "FfsSystemG.h"
#include "FfsContext.h"
#include "FullFlightSimulator/Common/FfsUtils.h"
#include "FullFlightSimulator/ControllableUnit/ControllableAirport.h"
#include "FullFlightSimulator/ControllableUnit/ControllableLandScape.h"

namespace cross 
{
    static constexpr std::string_view NIGHT = "NIGHT";

    FfsEventManager::FfsEventManager()
    {
    }

    FfsEventManager::~FfsEventManager() 
    {
    }

	void FfsEventManager::SetRefreshTODDelayFrameCount(UInt32 delay)
	{
		mDelayRefreshTod = delay;
	}

    void FfsEventManager::Tick()
    {
        if (mDelayRefreshTod > 0)
        {
            mDelayRefreshTod--;
			if (mDelayRefreshTod == 0)
			{
				RefreshTODTime();
			}
        }
    }
    
    void FfsEventManager::TODLightChangeEvent(const TODConfigChangedEvent& event)
    {
#if !defined(CE_USE_DOUBLE_TRANSFORM)
        return;
#endif

        const auto& config = event.mData.config;
        auto* ffsSys = mGameWorld->GetGameSystem<FfsSystemG>();
        auto* context = ffsSys->GetContext();
        if (!context->IsGameWorldValid())
        {
            TODLightChangeEventLogicInEditor(config);
            return;
        }
        if (!context->GetDelegateManager()->GetAircraftPtr())
            return;

        // sometimes need force refresh tod when prefabs loaded finish
        // todtime from sim dont need force refresh
        if (!event.mData.RefreshForce && mLastTodConfig.InSameMinute(config) && mLastTimezone == event.mData.timezone)
            return;

        mLastTodConfig = config;
        mLastTimezone = event.mData.timezone;
        TODLightChangeEventLogic(config, event.mData.RefreshForce);
    }

    void FfsEventManager::RefreshTODTime()
    {
        auto todSystem = mGameWorld->GetGameSystem<TODLightSystemG>();
		auto todQueryResult = mGameWorld->Query<TODLightComponentG>();
        for (const auto& todComp : todQueryResult)
        {
            todSystem->SetTODLightConfig(todComp.Write(), mLastTodConfig);
            todSystem->OnChangeConfig(todComp.Read(), false);
        }
        RefreshSkyLightAndRefProbe();
    }


    void FfsEventManager::TODLightChangeEventLogicInEditor(const TODLightConfig& todData)
    {
        auto setAllLightEnable = [&](bool isNight, const std::set<ecs::EntityID>& allLights) {
            auto* lightSystem = mGameWorld->GetGameSystem<LightSystemG>();
            for (const auto& chid : allLights)
            {
                if (mGameWorld->IsEntityAlive(chid) && mGameWorld->HasComponent<LightComponentG>(chid))
                {
                    auto lightComp = mGameWorld->GetComponent<LightComponentG>(chid);
                    lightSystem->SetLightEnable(lightComp.Write(), isNight);
                }
            }
        };
        auto* ffsLightSystem = mGameWorld->GetGameSystem<FFSLightPointSystemG>();
        auto unitQueryResult = mGameWorld->Query<ControllableUnitComponentG>();

        bool emissiveOn = false;
        for (const auto& unitComp : unitQueryResult)
        {
            auto* landController = cc::ControllableLandScape::GetController(unitComp.Read());
            auto* airportController = cc::ControllableAirport::GetController(unitComp.Read());
            if (landController || airportController)
            {
                auto eid = unitComp.GetEntityID();
                if (mGameWorld->HasComponent<FFSWGS84ComponentG>(eid))
                {
                    auto* wgsSys = mGameWorld->GetGameSystem<WGS84SystemG>();
                    auto lon_lat_alt = wgsSys->GetLonLatAlt(mGameWorld->GetComponent<FFSWGS84ComponentG>(eid).Write());
                    gFfsUtils.CheckIsNight(lon_lat_alt, todData, mGameWorld);
                    emissiveOn = (gFfsUtils.EmissiveOn ||gFfsUtils.IsOvercast);
                }
                else
                {
                    continue;
                }
                if (landController)
                {
                    // The lights inside the city were turned off
                    landController->LoadedPrefabCheckLight(mGameWorld);
                    setAllLightEnable(gFfsUtils.LightOn, landController->GetRealLightEntities());
                    RefreshFfsEnvLight(gFfsUtils.LightOn, landController->GetFfsEnvLightEntities());
                    // Foliage point light turned off
                    auto foliageQueryResult = mGameWorld->Query<FoliageComponentG>();
                    for (const auto& foliageComp : foliageQueryResult)
                    {
                        auto foliageSystem = mGameWorld->GetGameSystem<FoliageSystemG>();
                        if (foliageSystem->GetFoliageGenerationType(foliageComp.Read()) != FoliageGenerationType::FOLIAGE_GENERATION_TYPE_LIGHT)
                            continue;
                        auto entityMetaSystem = mGameWorld->GetGameSystem<EntityMetaSystem>();

                        entityMetaSystem->SetEntityVisibility(foliageComp.GetEntityID(), gFfsUtils.LightOn);
                        
                    }
                }
                else if (airportController)
                {
                    // The lights inside the airport were turned off
                    airportController->LoadedPrefabCheckLight(mGameWorld);
                    setAllLightEnable(gFfsUtils.LightOn, airportController->GetRealLightEntities());

                    RefreshFfsEnvLight(gFfsUtils.LightOn, airportController->GetFfsEnvLightEntities());
                }
            }
        }
        const auto& setting = std::get<FfsPrecipitationView>((&FfsWeatherSettingManager::Instance())->GetWeatherPreset<PrecipitationType>(PrecipitationType::None));
        auto mpcResource = TypeCast<resource::MaterialParameterCollection>(gAssetStreamingManager->GetResource(setting.EnvMPCPath));
        mpcResource->SetParameter(NIGHT, static_cast<float>(emissiveOn));
        
        RefreshSkyLightAndRefProbe();
    }

    void FfsEventManager::TODLightChangeEventLogic(const TODLightConfig& todData, bool refreshForce)
    {
        //LOG_INFO("TOD:{}:{}:{}", todData.Hour, todData.Minute, todData.Second);
        auto* ffsSys = mGameWorld->GetGameSystem<FfsSystemG>();
        auto delegateManager = ffsSys->GetContext()->GetDelegateManager();

        // use Aircraft wgs84 to check
        if (delegateManager->GetAircraftPtr())
        {

            auto aircraftEntity = delegateManager->GetAircraftPtr()->Entity;
            auto& aircraftWgs84 = mGameWorld->GetComponent<FFSWGS84ComponentG>(aircraftEntity).Read()->mDoubleWGS84;

            Double3 lon_lat_alt = Double3(aircraftWgs84.mLongitude, aircraftWgs84.mLatitude, aircraftWgs84.mElevation);
            gFfsUtils.CheckIsNight(lon_lat_alt, todData, mGameWorld);
            bool emissiveOn = gFfsUtils.EmissiveOn || gFfsUtils.IsOvercast;

            const auto& setting = std::get<FfsPrecipitationView>((&FfsWeatherSettingManager::Instance())->GetWeatherPreset<PrecipitationType>(PrecipitationType::None));
            auto mpcResource = TypeCast<resource::MaterialParameterCollection>(gAssetStreamingManager->GetResource(setting.EnvMPCPath));

            auto nightValue = mpcResource->GetNumericParameterValue(NIGHT);
            if (nightValue.size()==1&& nightValue[0]!= static_cast<float>(emissiveOn))
            {
                mpcResource->SetParameter(NIGHT, static_cast<float>(emissiveOn));
            }
            RefreshSkyLightAndRefProbe();
        }

    }

    //void FfsEventManager::WeatherChangeEvent(const FfsWeatherSettingEvent& event)
    //{
    //}

    void FfsEventManager::RefreshSkyLightAndRefProbe() 
    {
        // Refresh reflection probes
        // Find skylight real time capture slice count
        SInt32 Delay = 0;
        auto* skylightSys = mGameWorld->GetGameSystem<SkyLightSystemG>();
        auto skylights = mGameWorld->Query<SkyLightComponentG>();
        for (const auto& skylightComp : skylights)
        {
            if (!skylightSys->GetRealTimeCapture(skylightComp.Read()))
            {
                continue;
            }
            SInt32 RealTimeCaptureSliceCount = std::min(skylightSys->GetRealTimeCaptureSliceCount(skylightComp.Read()), REALTIME_CAPTURE_MAX_SLICE) - 1;
            Delay = std::max(Delay, RealTimeCaptureSliceCount * 2);
        }

        auto probeSys = mGameWorld->GetGameSystem<ReflectionProbeSystemG>();
        auto probes = mGameWorld->Query<ReflectionProbeComponentG>();
        for (const auto& probeComp : probes)
        {
            probeSys->TriggerRealTimeProbeCaptureByScript(probeComp.Write(), Delay);
        }
    }
}   // namespace cross
