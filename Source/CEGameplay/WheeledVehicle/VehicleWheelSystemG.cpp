#include "EnginePrefix.h"

#include "VehicleWheelSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"

#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"

namespace cross {
ecs::ComponentDesc* VehicleWheelComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::VehicleWheelComponentG>(
        { false, true, true },
        &VehicleWheelSystemG::SerializeVehicleWheelComponent,
        &VehicleWheelSystemG::DeserializeVehicleWheelComponent, 
        &VehicleWheelSystemG::PostDeserializeVehicleWheelComponent
    );
}

VehicleWheelSystemG::VehicleWheelSystemG() {}
VehicleWheelSystemG::~VehicleWheelSystemG() {}

VehicleWheelSystemG* VehicleWheelSystemG::CreateInstance()
{
    return new VehicleWheelSystemG();
}

void VehicleWheelSystemG::Release()
{
    delete this;
}
void VehicleWheelSystemG::NotifyAddRenderSystemToRenderWorld() {}

RenderSystemBase* VehicleWheelSystemG::GetRenderSystem()
{
    return nullptr;
}

SerializeNode VehicleWheelSystemG::SerializeVehicleWheelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SerializeNode localJson;
    auto compPtr = static_cast<VehicleWheelComponentG*>(componentPtr);

    SerializeContext context;
    SerializeNode outNode = compPtr->Serialize(context);
    return outNode;
}

void VehicleWheelSystemG::DeserializeVehicleWheelComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
        return;

    auto compPtr = static_cast<VehicleWheelComponentG*>(componentPtr);
    if (json.IsObject())
    {
        SerializeContext node;
        compPtr->Deserialize(json, node);
    }
}

void VehicleWheelSystemG::PostDeserializeVehicleWheelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    auto wheelSys = gameWorld->GetGameSystem<VehicleWheelSystemG>();
    auto wheelComp = gameWorld->GetComponent<VehicleWheelComponentG>(entityId);
    wheelSys->SetAttachedBone(wheelComp.Write(), wheelComp.Read()->mBoneName);
}

void VehicleWheelSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    auto* transSys = mGameWorld->GetGameSystem<TransformSystemG>();
    auto* metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
    auto* primitiveSystem = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();

    auto t1 = CreateTaskFunction(FrameTickStage::Update, {}, [this, transSys]() {
        auto queryResult = mGameWorld->Query<VehicleWheelComponentG, LocalTransformComponentG>();
        for (const auto& [wheelComp, localTransComp] : queryResult)
        {
            auto attachedEntity = transSys->GetEntityParent(wheelComp.GetEntityID());
            if (mGameWorld->IsEntityAlive(attachedEntity))
            {
                auto attchedSkeleton = mGameWorld->GetComponent<SkeletonComponentG>(attachedEntity);
                if (wheelComp.Read()->mBoneH && attchedSkeleton.Read()->PosePtr)
                {
                    PoseBoneHandle curPoseH = attchedSkeleton.Read()->PosePtr->GetPoseIndexFromFilteredBoneIndex(wheelComp.Read()->mBoneH);
                    Assert(curPoseH);

                    //auto revRot = Quaternion::EulerToQuaternion(wheelComp.Read()->mRotation);
                    auto revPos = wheelComp.Read()->mTranslation;
                    auto revRot = wheelComp.Read()->mRotation;

                    auto attchedBoneTrans = attchedSkeleton.Read()->PosePtr->GetRootSpaceTransform(curPoseH);
                    transSys->SetLocalTranslation(localTransComp.Write(), revPos + attchedBoneTrans.GetTranslation());
                    transSys->SetLocalRotation(localTransComp.Write(), revRot * attchedBoneTrans.GetRotation());
                    transSys->SetLocalScale(localTransComp.Write(), attchedBoneTrans.GetScale());
                }
            }
        }
    });

#ifdef CROSSENGINE_EDITOR
    auto worldType = mGameWorld->GetWorldType();
    auto appStartUpType = EngineGlobal::GetSettingMgr()->GetAppStartUpType();
    bool onlyInEditor = appStartUpType == AppStartUpType::AppStartUpTypeCrossEditor && worldType != WorldTypeTag::PIEWorld;

    if (onlyInEditor)
    {
        auto t2 = CreateTaskFunction(FrameTickStage::Update, {t1}, [this, transSys, metaSys, primitiveSystem]() {
            auto queryResult = mGameWorld->Query<VehicleWheelComponentG, WorldTransformComponentG>();
            for (const auto& [wheelComp, worldTransComp] : queryResult)
            {
                auto entityID = wheelComp.GetEntityID();
                if (metaSys->IsSelected(entityID) || metaSys->IsSelected(transSys->GetEntityParent(entityID)))
                {
                    auto worldMatrix = transSys->GetWorldMatrix(worldTransComp.Read());
                    if (mWheelPrimitives.count(entityID) == 0)
                    {
                        PrimitiveGenerator::GenerateCircleLine(&mWheelPrimitives[entityID], wheelComp.Read()->mWheelRadius);
                    }
                    primitiveSystem->DrawPrimitive(&mWheelPrimitives[entityID], worldMatrix, {ColorRGBAf(1.f, 1.f, 1.f), PrimitiveDepth::SelfDepth, 2U});
                }
                else
                {
                    if (auto it = mWheelPrimitives.find(entityID); it != mWheelPrimitives.end())
                    {
                        mWheelPrimitives.erase(it);
                    }
                }
            }
        });
    }
#endif
}

void VehicleWheelSystemG::GetVehicleWheelComponent(const VehicleWheelCompReader& comp, VehicleWheelComponentG& outValue)
{
    outValue = *comp;
}

void VehicleWheelSystemG::SetVehicleWheelComponent(const VehicleWheelCompWriter& comp, const VehicleWheelComponentG& inValue)
{
    *comp = inValue;
    SetAttachedBone(comp, inValue.mBoneName);
}

void VehicleWheelSystemG::SetShow(const VehicleWheelCompWriter& comp, bool bShow)
{
    auto entity = comp.GetEntityID();
    if (!bShow)
    {
        mWheelPrimitives.erase(entity);
    }
    else if (mWheelPrimitives.find(entity) == mWheelPrimitives.end())   // Add
    {
        float wheelRadius = comp->mWheelRadius;
        PrimitiveGenerator::GenerateCircleLine(&mWheelPrimitives[entity], wheelRadius);
    }
}

void VehicleWheelSystemG::SetAttachedBone(const VehicleWheelCompWriter& comp, const std::string& boneName)
{
    auto transSys = mGameWorld->GetGameSystem<TransformSystemG>();
    comp->mBoneH = SkBoneHandle::InvalidHandle();
    comp->mBoneName = boneName;

    if (boneName != "")
    {
        auto attachedEntity = transSys->GetEntityParent(comp.GetEntityID());
        if (mGameWorld->IsEntityAlive(attachedEntity))
        {
            auto attchedSkeleton = mGameWorld->GetComponent<SkeletonComponentG>(attachedEntity);
            if (attchedSkeleton.IsValid() && attchedSkeleton.Read()->RunSkelt)
            {
                CEName boneNameLocal = CEName(comp->mBoneName.c_str());
                comp->mBoneH = attchedSkeleton.Read()->RunSkelt->GetReferenceSkeleton().FindRawBoneIndex(boneNameLocal);
            }
        }
    }
}
}   // namespace cross