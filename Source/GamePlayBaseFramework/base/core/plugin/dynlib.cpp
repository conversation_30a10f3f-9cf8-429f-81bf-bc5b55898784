#include "core/plugin/dynlib.h"
#include "core/core_global.hpp"

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT
  #define WIN32_LEAN_AND_MEAN
  #if !defined(NOMINMAX) && defined(_MSC_VER)
    #define NOMINMAX  // required to stop windows.h messing up std::min
  #endif
  #include <windows.h>
#endif

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE_IOS
  #include "Platform/iOS/macUtils.h"
#endif
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE_IOS
  #include <dlfcn.h>
#endif

namespace gbf {
DynLib::DynLib(const std::string& name) {
  name_ = name;
  instance_ = nullptr;
}

//-----------------------------------------------------------------------
DynLib::~DynLib() {}

//-----------------------------------------------------------------------
void DynLib::Load() {
  // Log library load
  std::cerr << StringUtil::Format("Loading library %s", name_.c_str()) << std::endl;

  std::string name = name_;
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_LINUX || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_ANDROID
  // dlopen() does not add .so to the filename, like windows does for .dll
  name = "lib" + name;
  if (name.find(".so") == std::string::npos) {
    name += ".so";
    // name += ".so.";
    // name += StringConverter::toString(RSTUDIO_VERSION_MAJOR) + ".";
    // name += StringConverter::toString(RSTUDIO_VERSION_MINOR) + ".";
    // name += StringConverter::toString(RSTUDIO_VERSION_PATCH);
  }
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE_IOS
  // dlopen() does not add .dylib to the filename, like windows does for .dll
  if (name.substr(name.find_last_of(".") + 1) != "dylib") name += ".dylib";
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT
  // Although LoadLibraryEx will add .dll itself when you only specify the library name,
  // if you include a relative path then it does not. So, add it to be sure.
  if (name.substr(name.find_last_of(".") + 1) != "dll") name += ".dll";
#endif
  instance_ = (DYNLIB_HANDLE)DYNLIB_LOAD(name.c_str());
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE_IOS
  if (!instance_) {
    // Try again as a framework
    instance_ = (DYNLIB_HANDLE)GBF_LOAD(name_.c_str());
  }
#endif
  if (!instance_) {
    auto errstr = StringUtil::Format("DynLib::load() Could not load dynamic library %s. System Error: %s", name.c_str(),
                                     DynlibError().c_str());
    throw std::runtime_error(errstr);
  }
}

//-----------------------------------------------------------------------
void DynLib::Unload() {
  if (DYNLIB_UNLOAD(instance_)) {
    std::cerr << StringUtil::Format("DynLib::unload() Could not unload dynamic library %s. System Error: %s",
                                    name_.c_str(), DynlibError().c_str());
    throw std::runtime_error("Can not load module!");
  }
}

//-----------------------------------------------------------------------
void* DynLib::GetSymbol(const std::string& name) const throw() {
  return (void*)DYNLIB_GETSYM(instance_, name.c_str());
}
//-----------------------------------------------------------------------
std::string DynLib::DynlibError(void) {
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  LPTSTR lpMsgBuf;
  FormatMessage(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS, nullptr,
                GetLastError(), MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), (LPTSTR)&lpMsgBuf, 0, nullptr);
  std::string ret = lpMsgBuf;
  // Free the buffer.
  LocalFree(lpMsgBuf);
  return ret;
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT
  WCHAR wideMsgBuf[1024];
  if (0 == FormatMessageW(FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS, NULL, GetLastError(),
                          MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), wideMsgBuf,
                          sizeof(wideMsgBuf) / sizeof(wideMsgBuf[0]), NULL)) {
    wideMsgBuf[0] = 0;
  }

  char narrowMsgBuf[2048] = "";
  if (0 == WideCharToMultiByte(CP_ACP, 0, wideMsgBuf, -1, narrowMsgBuf, sizeof(narrowMsgBuf) / sizeof(narrowMsgBuf[0]),
                               NULL, NULL)) {
    narrowMsgBuf[0] = 0;
  }
  std::string ret = narrowMsgBuf;

  return ret;
#elif GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_LINUX || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_APPLE || \
    GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_ANDROID
  return std::string(dlerror());
#else
  return std::string("");
#endif
}

}  // namespace gbf
