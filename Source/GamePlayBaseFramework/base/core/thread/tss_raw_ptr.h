#pragma once

#include "core/thread/thread_config.hpp"

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT

#else
  #include <pthread.h>
#endif

namespace gbf {
namespace threads {

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT
////typedef unsigned long DWORD;
using tls_key_type = unsigned long;
#else
// Helper function to create thread-specific storage.
using tls_key_type = pthread_key_t;
#endif

GBF_CORE_API void tss_raw_ptr_create(tls_key_type& key);

GBF_CORE_API void tss_raw_ptr_destroy(tls_key_type& key);

GBF_CORE_API void* tss_raw_ptr_get_data(const tls_key_type& key);

GBF_CORE_API void tss_raw_ptr_set_data(const tls_key_type& key, void* raw_pointer);

template <typename T>
class tss_raw_ptr {
 private:
  tss_raw_ptr(const tss_raw_ptr&);

 public:
  // Constructor.
  tss_raw_ptr() { tss_raw_ptr_create(tss_key_); }

  // Destructor.
  ~tss_raw_ptr() {
    tss_raw_ptr_destroy(tss_key_);
    ////::pthread_key_delete(tss_key_);
  }

  // Get the value.
  operator T*() const { return static_cast<T*>(tss_raw_ptr_get_data(tss_key_)); }

  T* get() const { return operator T*(); }

  // Set the value.
  void operator=(T* value) { tss_raw_ptr_set_data(tss_key_, value); }

 private:
  // Thread-specific storage to allow unlocked access to determine whether a
  // thread is a member of the pool.
  tls_key_type tss_key_;
};

}  // namespace threads
}  // namespace gbf
