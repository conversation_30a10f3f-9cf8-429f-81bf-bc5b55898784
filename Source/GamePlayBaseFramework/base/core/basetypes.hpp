#pragma once

#include "core/config.hpp"

namespace gbf {
/* Initial platform/compiler-related stuff to set.
 */

// Integer formats of fixed bit width
typedef unsigned int uint32;
typedef unsigned short uint16;
typedef unsigned char uint8;
typedef int int32;
typedef short int16;
typedef signed char int8;

// define uint64 type
#if GBF_CORE_COMPILER == GBF_CORE_COMPILER_MSVC
typedef unsigned __int64 uint64;
typedef __int64 int64;
#else
typedef unsigned long long uint64;
typedef long long int64;
#endif

typedef double Real;

/** In order to avoid finger-aches :)
 */
typedef unsigned char uchar;
typedef unsigned short ushort;
typedef unsigned int uint;
typedef unsigned long ulong;

#ifndef BOOL
typedef int BOOL;
#endif  // !BOOL

#ifndef TRUE
  #define TRUE (1)
#endif  // TRUE
#ifndef FALSE
  #define FALSE (0)
#endif  // FALSE
}  // namespace gbf
