#pragma once

#include "export.hpp"

#include "core/imodules/ilog_module.h"
#include "core/imodules/iprofiler_module.h"
#include "core/modules/game_framework.h"
#include "core/thread/thread_timer.h"


GBF_CORE_API extern gbf::GameFramework* GGame;
////GBF_CORE_API extern rstudio::ILogModule* GLog;		//already defined in ilog_module.inl

////GBF_CORE_API extern rstudio::IProfilerModule* GProfiler;	//already defined in iprofiler_module.inl
GBF_CORE_API extern gbf::IProfilerModule* GDefaultProfiler;

GBF_CORE_API extern gbf::threads::ThreadTimer* GTimer;




