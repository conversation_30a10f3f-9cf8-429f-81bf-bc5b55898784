#pragma once

#include "core/error/errors.hpp"

namespace gbf {
class GBF_CORE_API ByteBufferError : public Error {
 public:
  ByteBufferError(bool read, size_t pos, size_t opsize, size_t size, const std::string_view errorMessage);
};

class GBF_CORE_API ByteBufferWriteOverflow : public Error {
 public:
  ByteBufferWriteOverflow(size_t wpos, size_t wsize, size_t size, const std::string_view errorMessage);
};
}  // namespace gbf