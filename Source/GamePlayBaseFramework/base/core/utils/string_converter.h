#pragma once

#include <iostream>

#include "core/basetypes.hpp"
#include "core/config.hpp"
#include "core/utils/string_util.h"

namespace gbf {
class GBF_CORE_API StringConverter {
 public:
  // Nested Types
  using StringStream = std::basic_stringstream<char, std::char_traits<char>, std::allocator<char>>;

 public:
  /** Converts a float to a String. */
  static std::string ToString(float val, unsigned short precision = 6, unsigned short width = 0, char fill = ' ',
                              int flags = std::ios::fmtflags(0));

  // This counter-intuitive guard is correct. In case of enabled double precision
  // the toString() version above using Captain::float already provides a double precision
  // version and hence we need to explicitly declare a float version as well.

  /** Converts a double to a String. */
  static std::string ToString(double val, unsigned short precision = 6, unsigned short width = 0, char fill = ' ',
                              int flags = std::ios::fmtflags(0));

  /** Converts an int to a String. */
  static std::string ToString(std::int32_t val, unsigned short width = 0, char fill = ' ',
                              int flags = std::ios::fmtflags(0));

  /** Converts an unsigned int to a String. */
  static std::string ToString(std::uint32_t val, unsigned short width = 0, char fill = ' ',
                              int flags = std::ios::fmtflags(0));

  /** Converts a size_t to a String. */
  static std::string ToString(std::int64_t val, unsigned short width = 0, char fill = ' ',
                              int flags = std::ios::fmtflags(0));

  /** Converts a long to a String. */
  static std::string ToString(std::uint64_t val, unsigned short width = 0, char fill = ' ',
                              int flags = std::ios::fmtflags(0));

  template <typename T>
  static auto ToString(T val, unsigned short width = 0, char fill = ' ', int flags = std::ios::fmtflags(0))
      -> std::enable_if_t<std::is_same<long, T>::value, std::string> {
    return ToString(static_cast<std::int32_t>(val), width, fill, flags);
  }

  template <typename T>
  static auto ToString(T val, unsigned short width = 0, char fill = ' ', int flags = std::ios::fmtflags(0))
      -> std::enable_if_t<std::is_same<unsigned long, T>::value, std::string> {
    return ToString(static_cast<std::uint32_t>(val), width, fill, flags);
  }

  /** Converts a boolean to a String.
  @param yesNo If set to true, result is 'yes' or 'no' instead of 'true' or 'false'
  */
  static std::string ToString(bool val, bool yesNo = false);

  static std::string ToString(const StringVector& val);

  /** Converts a std::string to a Real.
  @return
      0.0 if the value could not be parsed, otherwise the float version of the String.
  */
  static float ParseFloat(const std::string& val, float defaultValue = 0);

  static double ParseDouble(const std::string& val, double defaultValue = 0);

  /** Converts a std::string to a whole number.
  @return
      0.0 if the value could not be parsed, otherwise the numeric version of the String.
  */
  static int ParseInt(const std::string& val, int defaultValue = 0);
  /** Converts a std::string to a whole number.
  @return
      0.0 if the value could not be parsed, otherwise the numeric version of the String.
  */
  static unsigned int ParseUnsignedInt(const std::string& val, unsigned int defaultValue = 0);
  /** Converts a std::string to a whole number.
  @return
      0.0 if the value could not be parsed, otherwise the numeric version of the String.
  */
  static long ParseLong(const std::string& val, long defaultValue = 0);
  /** Converts a std::string to a whole number.
  @return
      0.0 if the value could not be parsed, otherwise the numeric version of the String.
  */
  static unsigned long ParseUnsignedLong(const std::string& val, unsigned long defaultValue = 0);

  static int64 ParseInt64(const std::string& val, int64 defaultValue = 0);

  static uint64_t ParseUint64(const std::string& val, uint64_t defaultValue = 0);
  /** Converts a std::string to size_t.
  @return
      defaultValue if the value could not be parsed, otherwise the numeric version of the String.
  */
  static size_t ParseSizeT(const std::string& val, size_t defaultValue = 0);
  /** Converts a std::string to a boolean.
  @remarks
      Returns true if case-insensitive match of the start of the string
      matches "true", "yes", "1", or "on", false if "false", "no", "0"
      or "off".
  */
  static bool ParseBool(const std::string& val, bool defaultValue = 0);

  /** Parses a StringVector from a string.
  @remarks
      Strings must not contain spaces since space is used as a delimiter in
      the output.
  */
  static StringVector ParseStringVector(const std::string& val);
  /** Checks the std::string is a valid number value. */
  static bool IsNumber(const std::string& val);

  //-----------------------------------------------------------------------
  static void SetDefaultStringLocale(const std::string& loc) {
    msDefaultStringLocale = loc;
    msLocale = std::locale(msDefaultStringLocale.c_str());
  }
  //-----------------------------------------------------------------------
  static std::string GetDefaultStringLocale(void) { return msDefaultStringLocale; }
  //-----------------------------------------------------------------------
  static void SetUseLocale(bool useLocale) { msUseLocale = useLocale; }
  //-----------------------------------------------------------------------
  static bool IsUseLocale() { return msUseLocale; }
  //-----------------------------------------------------------------------

 public:
  static std::string msDefaultStringLocale;
  static std::locale msLocale;
  static bool msUseLocale;
};

/** @} */
/** @} */

}  // namespace gbf
