#pragma once

#include <assert.h>
#include <memory.h>

#include <list>
#include <map>
#include <string>
#include <vector>

#include <algorithm>
#include <memory>

#include "core/config.hpp"

namespace gbf {
////extern const std::string STD_STRING_EMPTY;
using StringVector = std::vector<std::string>;

/** Utility class for manipulating Strings.  */
class GBF_CORE_API StringUtil {
 public:
  /** Removes any whitespace characters, be it standard space or
          TABs and so on.
          @remarks
          The user may specify whether they want to trim only the
          beginning or the end of the String ( the default action is
          to trim both).
  */
  static void Trim(std::string& str, bool left = true, bool right = true);

  /** Returns a StringVector that contains all the substrings delimited
          by the characters in the passed <code>delims</code> argument.
          @param
          delims A list of delimiter characters to split by
          @param
          maxSplits The maximum number of splits to perform (0 for unlimited splits). If this
          parameters is > 0, the splitting process will stop after this many splits, left to right.
          @param
          preserveDelims Flag to determine if delimiters should be saved as substrings
  */
  static StringVector Split(const std::string& str, const std::string& delims = "\t\n ", unsigned int maxSplits = 0,
                            bool preserveDelims = false);

  static StringVector SplitByWord(const std::string& str, const std::string& delims = "\r\n", unsigned int maxSplits = 0, bool preserveDelims = false); 

  /** Returns a StringVector that contains all the substrings delimited
          by the characters in the passed <code>delims</code> argument,
          or in the <code>doubleDelims</code> argument, which is used to include (normal)
          delimeters in the tokenised string. For example, "strings like this".
          @param
          delims A list of delimiter characters to split by
          @param
          doubleDelims A list of double delimeters characters to tokenise by
          @param
          maxSplits The maximum number of splits to perform (0 for unlimited splits). If this
          parameters is > 0, the splitting process will stop after this many splits, left to right.
  */
  static StringVector Tokenise(const std::string& str, const std::string& delims = "\t\n ",
                               const std::string& doubleDelims = "\"", unsigned int maxSplits = 0);

  /** Lower-cases all the characters in the string.
   */
  static void ToLowerCase(std::string& str);

  /** Upper-cases all the characters in the string.
   */
  static void ToUpperCase(std::string& str);

  /** Upper-cases the first letter of each word.
   */
  static void ToTitleCase(std::string& str);

  /** Returns whether the string begins with the pattern passed in.
          @param pattern The pattern to compare with.
          @param lowerCase If true, the start of the string will be lower cased before
          comparison, pattern should also be in lower case.
  */
  static bool StartsWith(const std::string& str, const std::string& pattern, bool lowerCase = true);

  /** Returns whether the string ends with the pattern passed in.
          @param pattern The pattern to compare with.
          @param lowerCase If true, the end of the string will be lower cased before
          comparison, pattern should also be in lower case.
  */
  static bool EndsWith(const std::string& str, const std::string& pattern, bool lowerCase = true);

  /** Method for standardising paths - use forward slashes only, end with slash.
   */
  static void StandardisePath(std::string& path);

  /** Method for splitting a fully qualified filename into the base name
          and path.
          @remarks
          Path is standardised as in standardisePath
  */
  static void SplitFilename(const std::string& qualifiedName, std::string& outBasename, std::string& outPath);

  /** Method for splitting a fully qualified filename into the base name,
          extension and path.
          @remarks
          Path is standardised as in standardisePath
  */
  static void SplitFullFilename(const std::string& qualifiedName, std::string& outBasename, std::string& outExtention,
                                std::string& outPath);

  /** Method for splitting a filename into the base name
          and extension.
  */
  static void SplitBaseFilename(const std::string& fullName, std::string& outBasename, std::string& outExtention);

  /**
          Remove the trailing separator("/" or "\\") if exists.
  */
  static void RemoveTrailingPathSeparator(std::string& path);

  /** Simple pattern-matching routine allowing a wildcard pattern.
          @param str String to test
          @param pattern Pattern to match against; can include simple '*' wildcards
          @param caseSensitive Whether the match is case sensitive or not
  */
  static bool Match(const std::string& str, const std::string& pattern, bool caseSensitive = true);

  /** Replace all instances of a sub-string with a another sub-string.
          @param source Source string
          @param replaceWhat Sub-string to find and replace
          @param replaceWithWhat Sub-string to replace with (the new sub-string)
          @return An updated string with the sub-string replaced
  */
  static const std::string ReplaceAll(const std::string& source, const std::string& replaceWhat,
                                      const std::string& replaceWithWhat);

  static const std::string ReplaceStringCharacters(const std::string& source, const std::string& replaceWhat,
                                                   const std::string& replaceWithWhat);

  static std::string Format(const char* format, ...);
  // format string into `buff`, it's size will be auto enlarge(max 4096)
  // Tips: if you want to format big message, resize `buff` previously
  // Note1: make sure `buff` isn't in the arguments lists, it will cause memory corruption.
  // Note2: in `CAP_DEBUG_MODE` compile option, it will throw exception when error(or overflow).
  static std::string& Format(std::string& buff, const char* format, ...);

  // whether all char is digit
  static bool IsDigit(const std::string& str);

  static uint64_t Hash64Fnv1a(const char* key, const uint64_t len) {
    uint64_t hash = 0xcbf29ce484222325;
    uint64_t prime = 0x100000001b3;

    for (int i = 0; i < len; ++i) {
      uint8_t value = key[i];
      hash = hash ^ value;
      hash *= prime;
    }

    return hash;
  }

  static uint64_t HashString(const std::string_view key) { return Hash64Fnv1a(key.data(), key.length()); }

  static constexpr uint64_t Hash64Fnv1aConstExpr(const char* key, const uint64_t len) {
    uint64_t hash = 0xcbf29ce484222325;
    uint64_t prime = 0x100000001b3;

    for (int i = 0; i < len; ++i) {
      uint8_t value = key[i];
      hash = hash ^ value;
      hash *= prime;
    }

    return hash;
  }

  static uint32_t Hash32String(const std::string str) {
    uint32_t hash = 0x811c9dc5;
    uint32_t prime = 0x1000193;

    for (int i = 0; i < str.size(); ++i) {
      uint8_t value = str[i];
      hash = hash ^ value;
      hash *= prime;
    }

    return hash;
  }

  static constexpr uint64_t HashStringConstExpr(const std::string_view key) {
    return Hash64Fnv1aConstExpr(key.data(), key.length());
  }

  static std::string GenerateMD5(const std::string_view str);
};
}  // namespace gbf
