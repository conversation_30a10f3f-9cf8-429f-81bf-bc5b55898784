#pragma once

#include <stdint.h>
#include <vector>
#include "memory/allocator/allocate_policy.hpp"
#include <memory_resource>
namespace gbf {
namespace allocator {

class RangeAllocator : public AllocatePolicy {
 public:
  GBF_MEMORY_API RangeAllocator(size_t preAllocateSize, size_t extendSize);
  RangeAllocator(const RangeAllocator&) = delete;
  GBF_MEMORY_API ~RangeAllocator();

  GBF_MEMORY_API AllocateResult Allocate(size_t bytes, size_t alignment = 0) override;

  GBF_MEMORY_API void Free(void* mem) override;

  ////virtual bool TryFree(void* mem) override;

  GBF_MEMORY_API AllocateResult Reallocate(void* mem, size_t newsize, size_t alignment) override;

  GBF_MEMORY_API void ForceTrim() override;

  GBF_MEMORY_API bool IsContain(void* mem) const;

  GBF_MEMORY_API size_t GetChunkSize(void* mem) const override;

  GBF_MEMORY_API bool IsThreadSafe() const override;

  GBF_MEMORY_API void  Reset() override;


  GBF_MEMORY_API size_t GetAllocatedSize() const override;

  GBF_MEMORY_API size_t GetReservedSize() const override;

  ////virtual size_t GetTotalFreeChunks() const override;

  GBF_MEMORY_API size_t GetMaxAllocatedSize() const override;

 protected:
  // Nested Types
  struct TLSFPoolInfo {
    bool IsContain(const void* ptr) const noexcept { return ptr >= poolSegmentBegin && ptr < poolSegmentEnd; }

    void Free(void* ptr);

    void* Allocate(size_t bytes, size_t alignment);

    bool IsEmpty() const noexcept { return totalAllocationCount == 0; }

    void* tlsfPool = nullptr;
    uint8_t* poolSegmentBegin = nullptr;
    uint8_t* poolSegmentEnd = nullptr;
    uint32_t poolReservedSize = 0;
    uint32_t totalAllocationCount = 0;

    uint32_t poolAllocatedSize = 0;

    ////uint32 maxBlockSize = 0;
  };

 protected:
  TLSFPoolInfo CreateTLSFPool(uint32_t expectSize);

  void DestroyTLSFPool(TLSFPoolInfo& poolInfo);

  void* TryAllocateChunkInExistPools(size_t bytes, size_t alignment, int exceptPoolIndex);

  void* RequestNewPoolAndAllocateOneChunk(size_t poolSize, size_t bytes, size_t alignment);

  void OnChunkFreed(size_t poolIndex);

 protected:
  size_t mPreAllocateSize = 0;

  size_t mExtendSize = 0;

  int mNowUsePoolIndex = 0;

private:
  using TLSFPoolArray = std::vector<TLSFPoolInfo>;
  TLSFPoolArray mTLSFPoolArray;
};
class RangeAllocatorResource : public std::pmr::memory_resource
{
public:
  explicit GBF_MEMORY_API RangeAllocatorResource(size_t preAllocateSize, size_t extendSize);

  inline memory_resource* upstream_resource() const
  {
    return this->upstream;
  }

  GBF_MEMORY_API void* do_allocate(std::size_t bytes, std::size_t alignment) override;
  GBF_MEMORY_API void  do_deallocate(void* p, std::size_t bytes, std::size_t alignment) override;

  GBF_MEMORY_API bool do_is_equal(const memory_resource& that) const noexcept override;
  auto& GetAllocator() { return mAllocator; }
  private:
  memory_resource* upstream = std::pmr::get_default_resource();
  RangeAllocator mAllocator;
};
class RangeAllocatorResourceThreadSafe : public std::pmr::memory_resource
{
public:
    explicit GBF_MEMORY_API RangeAllocatorResourceThreadSafe(size_t preAllocateSize, size_t extendSize);

    inline memory_resource* upstream_resource() const { return this->upstream; }

    GBF_MEMORY_API void* do_allocate(std::size_t bytes, std::size_t alignment) override;
    GBF_MEMORY_API void do_deallocate(void* p, std::size_t bytes, std::size_t alignment) override;

    GBF_MEMORY_API bool do_is_equal(const memory_resource& that) const noexcept override;
    auto& GetAllocator() { return mAllocator; }

private:
    memory_resource* upstream = std::pmr::get_default_resource();
    RangeAllocator mAllocator;
    std::mutex mMtx;
};

class RangeAllocatorV2 : public AllocatePolicy {
 public:
  GBF_MEMORY_API RangeAllocatorV2(size_t preAllocateSize); // Changed extendSize to initialMinPoolSize for clarity
  RangeAllocatorV2(const RangeAllocatorV2&) = delete;
  GBF_MEMORY_API ~RangeAllocatorV2();

  GBF_MEMORY_API AllocateResult Allocate(size_t bytes, size_t alignment = 0) override;

  GBF_MEMORY_API void Free(void* mem) override;

  GBF_MEMORY_API AllocateResult Reallocate(void* mem, size_t newsize, size_t alignment) override;

  GBF_MEMORY_API void ForceTrim() override;

  GBF_MEMORY_API bool IsContain(void* mem) const;

  GBF_MEMORY_API size_t GetChunkSize(void* mem) const override;

  GBF_MEMORY_API void Reset() override;

  GBF_MEMORY_API bool IsThreadSafe() const override;

  GBF_MEMORY_API size_t GetAllocatedSize() const override;

  GBF_MEMORY_API size_t GetReservedSize() const override;

  GBF_MEMORY_API size_t GetMaxAllocatedSize() const override;

 protected:
  // Nested Types - can reuse TLSFPoolInfo or redefine if V2 needs different pool info
  // For now, let's assume TLSFPoolInfo can be reused. If not, we'll define TLSFPoolInfoV2.
  // For simplicity and to avoid direct reuse if it might change, let's copy it.
  struct TLSFPoolInfoV2 {
    bool IsContain(const void* ptr) const noexcept { return ptr >= poolSegmentBegin && ptr < poolSegmentEnd; }
    void Free(void* ptr);
    void* Allocate(size_t bytes, size_t alignment);
    bool IsEmpty() const noexcept { return totalAllocationCount == 0; }

    void* tlsfPool = nullptr;
    uint8_t* poolSegmentBegin = nullptr;
    uint8_t* poolSegmentEnd = nullptr;
    uint32_t poolReservedSize = 0;
    uint32_t totalAllocationCount = 0;
    uint32_t poolAllocatedSize = 0;
  };

 protected:
  TLSFPoolInfoV2 CreateTLSFPool(uint32_t expectSize);
  void DestroyTLSFPool(TLSFPoolInfoV2& poolInfo);
  void* TryAllocateChunkInExistPools(size_t bytes, size_t alignment, int exceptPoolIndex);
  void* RequestNewPoolAndAllocateOneChunk(size_t poolSize, size_t bytes, size_t alignment);
  void OnChunkFreed(size_t poolIndex);

 protected:
  size_t mPreAllocateSize = 0; // Size of the very first pool
  int mNowUsePoolIndex = 0;

private:
  using TLSFPoolArrayV2 = std::vector<TLSFPoolInfoV2>;
  TLSFPoolArrayV2 mTLSFPoolArray;
};

class RangeAllocatorResourceV2 : public std::pmr::memory_resource
{
public:
  explicit GBF_MEMORY_API RangeAllocatorResourceV2(size_t preAllocateSize);

  inline memory_resource* upstream_resource() const
  {
    return this->upstream;
  }

  GBF_MEMORY_API void* do_allocate(std::size_t bytes, std::size_t alignment) override;
  GBF_MEMORY_API void  do_deallocate(void* p, std::size_t bytes, std::size_t alignment) override;

  GBF_MEMORY_API bool do_is_equal(const memory_resource& that) const noexcept override;
  auto& GetAllocator() { return mAllocator; }
  private:
  memory_resource* upstream = std::pmr::get_default_resource();
  RangeAllocatorV2 mAllocator;
};

class RangeAllocatorResourceThreadSafeV2 : public std::pmr::memory_resource
{
public:
    explicit GBF_MEMORY_API RangeAllocatorResourceThreadSafeV2(size_t preAllocateSize);

    inline memory_resource* upstream_resource() const { return this->upstream; }

    GBF_MEMORY_API void* do_allocate(std::size_t bytes, std::size_t alignment) override;
    GBF_MEMORY_API void do_deallocate(void* p, std::size_t bytes, std::size_t alignment) override;

    GBF_MEMORY_API bool do_is_equal(const memory_resource& that) const noexcept override;
    auto& GetAllocator() { return mAllocator; }

private:
    memory_resource* upstream = std::pmr::get_default_resource();
    RangeAllocatorV2 mAllocator;
    std::mutex mMtx;
};

}  // namespace allocator
}  // namespace gbf
