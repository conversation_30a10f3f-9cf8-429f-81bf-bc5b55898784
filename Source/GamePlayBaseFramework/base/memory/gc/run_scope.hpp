#pragma once

#include <memory>
#include <vector>
#include "memory/memory_export.hpp"
#include "memory/gc/gc_fwd.hpp"
#include "memory/gc/smart_ptr.hpp"
#include "memory/gc/gc_util.hpp"
#include "memory/gc/gc_vtable.hpp"

namespace gbf {
namespace gc {

class GBF_MEMORY_API run_scope {
  friend class gc_util;
 public:
  run_scope(uint64_t scope_id);
  run_scope() = delete;
  run_scope(const run_scope&) = delete;
  ~run_scope();

  void do_scope_mark();

  size_t total_objects() const noexcept { return m_scope_objects.size(); }

 protected:
  void add_to_scope(gc_info* info) { m_scope_objects.push_back(info); }

  void _destruct();
 protected:
  std::vector<gc_info*> m_scope_objects;

  bool m_is_closed = false;
  uint64_t m_scope_id;
};


}  // namespace gc
}  // namespace gbf
