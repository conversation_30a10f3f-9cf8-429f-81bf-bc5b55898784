#include "memory/gc/gc_info.hpp"

#include <cassert>

#include "memory/gc/gc_vtable.hpp"
#include "memory/gc/gc_manager.hpp"
#include "memory/gc/enable_gc_from_this.hpp"


namespace gbf {
namespace gc {

bool gc_info::is_disposed() const noexcept { return test_flag(gc_flag_type::kAlreadyDisposed); }

void gc_info::do_post_ctor_action() {
  vtbl_->post_ctor_action(this);
}

////void gc_info::set_expired() { set_flag(gc_flag_type::kExpiring); }
////
////bool gc_info::is_expired() const noexcept { return test_flag(gc_flag_type::kExpiring); }

////void GcObject::gc_mark_child(GCManager* gc) {
////  // mark linked array
////#if ENABLE_GCOBJECT_META_SUPPORT
////  auto metaInfo = this->get_meta_info();
////  if (!metaInfo) return;
////
////  for (auto offset : metaInfo->get_mark_point()) {
////    GcObject* pObj = *reinterpret_cast<GcObject**>(reinterpret_cast<char*>(this) + offset);
////    if (pObj) {
////      assert(get_parent_manager()->is_gc_object(pObj) && "GCObject::gc_mark_child() the marked object must be allocate by gc!");
////      gc->mark_one_obj(pObj);
////    }
////  }
////#else
////  auto offset_list = get_link_members_offset();
////  for (auto offset : offset_list) {
////    GcObject* pObj = *reinterpret_cast<GcObject**>(reinterpret_cast<char*>(this) + offset);
////    if (pObj) {
////      assert(get_parent_manager()->is_gc_object(pObj) && "GCObject::gc_mark_child() the marked object must be allocate by gc!");
////      gc->mark_one_obj(pObj);
////    }
////  }
////#endif
////}

#if ENABLE_GCOBJECT_META_SUPPORT
uint32_t GcObject::GetCxxTypeId() const { return (uint32_t)(GCObj2Head(this)->type); }

const std::string& GcObject::GetCxxTypeName() const { return get_meta_info()->type_name(); }

const MetaTypeInfo* GcObject::get_meta_info() const { return meta_type_helper::get_meta_info(get_type_id()); }

bool GcObject::is_type_of(const MetaTypeInfo* type_info) {
  if (is_type_equal(type_info)) return true;

  auto* base_type = meta_type_helper::get_meta_info(get_type_id());
  while (base_type) {
    if (is_type_equal(base_type)) return true;
    base_type = base_type->get_base_type();
  }
  return false;
}

bool GcObject::is_type_of(const std::string& type_name) { return is_type_of(meta_type_helper::get_meta_info(type_name)); }

bool GcObject::is_type_equal(const MetaTypeInfo* type_info) { return get_type_id() == type_info->type_id(); }

bool GcObject::is_lua_type() const {
  auto* metaInfo = get_meta_info();
  if (!metaInfo) return false;
  return metaInfo->class_type() == MetaClassType::Lua;
}
#endif

gc_manager* gc_info::get_manager() const noexcept {
  return gc_manager::get_manager_by_id(parent_id_);
}

void gc_info::dispose() noexcept {
  // Only do dispose when object not run dispose() before
  if (!test_flag(gc_flag_type::kAlreadyDisposed)) {
    vtbl_->dtor_(get_obj_ptr());
    set_flag(gc_flag_type::kAlreadyDisposed);
  }
}

void gc_info::free_memory(gc_manager* manager) noexcept {
  vtbl_->free_(this, manager);
}

}  // namespace gc
}  // namespace gbf
