#pragma once

#include <functional>
#include <vector>

#include <functional>
#include <string>
#include <vector>

#include "core/config.hpp"
#include "imod_shared/imodules/ijob_system_module.h"
#include "core/utils/byte_buffer.h"
#include "core/utils/string_util.h"
#include "imod_shared/imodules/package_fwd.hpp"

namespace gbf {

static const char kModulePackageSystemName[] = "PackageSystemModule";
using ResourceLoadFunction = std::function<void(const jobs::job_ticket_ptr& ticket, std::string_view res_name, std::string_view group_name,
                                                const ByteBufferPtr& loaded_data)>;

class IPackageSystemModule : public IModule {
 public:
  IPackageSystemModule(){};

  virtual ~IPackageSystemModule(){};
 public:
  virtual void SetBundleAssetsPath(std::string_view path) = 0;
  virtual void SetAppPath(std::string_view path) = 0;
  virtual void SetRootPath(std::string_view path) = 0;
  virtual void SetLibPath(std::string_view path) = 0;
  virtual void SetWorkPath(std::string_view path) = 0;
  virtual std::string_view GetBundlePath() = 0;
  virtual std::string_view GetAppPath() = 0;
  virtual std::string_view GetRootPath() = 0;
  virtual std::string_view GetAssetsPath() = 0;
  virtual std::string_view GetLibPath() = 0;
  virtual std::string_view GetWorkPath() = 0;

  // other public method
  virtual size_t WriteDataToFile(std::string_view relative_path, const uint8_t* buffer, size_t count) = 0;
  virtual ByteBufferPtr ReadDataFromFile(std::string_view relative_path) = 0;
  virtual DataStreamPtr OpenFileStream(std::string_view relative_path, bool read_only) = 0;
  virtual DataStreamPtr CreateWritableFileStream(std::string_view relative_path) = 0;

  virtual void AddResourceLocation(std::string_view relative_path, std::string_view location_type, std::string_view group_name, bool is_recursive = false) = 0;
  virtual void AddResourceLocationSmart(std::string_view relative_path, std::string_view group_name, bool is_recursive = false) = 0;
  virtual void RemoveResourceLocation(std::string_view name, std::string_view groupName) = 0;

  virtual StringVector GetAllRsourceGroupNames() = 0;
  virtual StringVector GetCustomResourceGroupNames() = 0;
  virtual StringVector GetInternalResourceGroupNames() = 0;

  virtual void CreateResourceGroup(std::string_view group_name, bool is_internal = false) = 0;
  virtual bool ResourceGroupExists(std::string_view group_name) = 0;
  virtual bool ResourceLocationExists(std::string_view rel_path, std::string_view group_name) = 0;
  virtual bool IsContainInGroup(std::string_view group_name,std::string_view file_name) = 0;
  virtual bool IsContainInAnyGroup(std::string_view file_name) = 0;
  virtual std::string_view QueryGroupByResourceName(std::string_view file_name) = 0;

  virtual void TraverseMatchedListInGroup(std::string_view group_name, const StringVector& patternList, ResourceLoadFunction&& handler) = 0;
  virtual void TraverseMatchedInGroup(std::string_view group_name, std::string_view pattern, ResourceLoadFunction&& handler) = 0;
  virtual void TraverseMatchedInArchive(std::string_view archiveName, std::string_view archiveType, std::string_view pattern, bool recursive,
                                ResourceLoadFunction&& handler) = 0;

  virtual ByteBufferPtr LoadResource(std::string_view resName, std::string_view groupName, bool searchGroupIfNotFound = true,
                                                    bool bOutputLog = true) = 0;
  virtual ByteBufferPtr LoadSubPackageResource(std::string_view resPackage, std::string_view subFile, std::string_view groupName,
                                                              bool searchGroupsIfNotFound = true, bool bOutputLog = true) = 0;
  virtual jobs::job_ticket_ptr LoadResourceAsync(std::string_view resName, std::string_view groupName, const ResourceLoadFunction& loadFunc,
                                            bool searchGroupIfNotFound = true) = 0;

  virtual void RemoveLocalResource(std::string_view location, std::string_view locationType, std::string_view resName) = 0;
  virtual bool CreateLocalResource(std::string_view location, std::string_view locationType, const EmbededResourceInfo& info, bool need_compress = false, bool need_crypto = false, bool overwrite = true) = 0;

  virtual ResourceVersionInfo QueryVersionInfo(std::string_view groupName, std::string_view /*notUsedParam*/, std::string_view resName) = 0;
  virtual std::string QueryConfigInfo(std::string_view resName) = 0;

  virtual StringVector GetArchiveFileNames(std::string_view location, std::string_view locationType, std::string_view pattern, bool recursive) = 0;
  virtual StringVector GetGroupFileNames(std::string_view groupName, std::string_view pattern) = 0;

  virtual void UnloadSqliteArchive(std::string_view location) = 0;

  virtual StringVector GetGroupLocationNames(std::string_view groupName) = 0;

  virtual void SetWritablePath(std::string_view path) = 0;
  virtual std::string_view GetWritablePath() = 0;

  virtual void SetCachePath(std::string_view path) = 0;
  virtual std::string_view GetCachePath() = 0;

  virtual void AddGroupResourceIndex(std::string_view resName, std::string_view location) = 0;
  virtual void RemoveGroupResourceIndex(std::string_view resName, std::string_view location) = 0;
  virtual void ReplaceConfigInfo(std::string_view location, std::string_view locationType, std::string_view resName, std::string_view configInfo) = 0;

  virtual void SetDefaultSearchGroup(std::string_view grpName) = 0;
};

}  // namespace gbf
