#pragma once

#include <functional>
#include <string>
#include <vector>

#include "core/config.hpp"
#include "imod_shared/imodules/network_fwd.hpp"
#include "imod_shared/imodules/ijob_system_module.h"
#include "core/utils/byte_buffer.h"
#include "core/utils/string_util.h"

#if GBF_ENABLE_CPP20
#include "async_task/tasks/coroutines/cotask20.hpp"
#endif

namespace gbf {
static const char kMoudleFileSystemName[] = "FileSystemModule";

using FileLoadFunction = std::function<void(const jobs::job_ticket_ptr& ticket, const std::string_view file_name,
                                            const std::string_view groupName, const ByteBufferPtr& loadedData)>;

class IFileSystemModule : public IModule {
 public:
  IFileSystemModule(){};

  virtual ~IFileSystemModule(){};

  // method from IModule
  ModuleCallReturnStatus Init() override = 0;

  ModuleCallReturnStatus Start() override = 0;

  ModuleCallReturnStatus Update() override = 0;

  ModuleCallReturnStatus Stop() override = 0;

  ModuleCallReturnStatus Release() override = 0;

  void Free() override = 0;

 public:
  virtual bool SetAppResourcePath(const std::string_view workPath) = 0;

  virtual std::string GetAppResourcePath() const = 0;

  virtual std::string GetFullPath(const std::string_view relativePath) = 0;

  virtual bool IsFileExist(const std::string_view fileFullName) = 0;

  virtual bool IsDirectoryExist(const std::string_view directoryFullName) = 0;

  virtual std::string GetAppCurrentPath() = 0;

  virtual unsigned long long GetLastWriteTime(const std::string_view fileFullName) = 0;

  virtual bool WriteDataToFileSystemPath(const std::string_view fileFullName, ByteBufferPtr writeData) = 0;

  virtual bool WriteDataToFile(const std::string_view relPath, ByteBufferPtr writeData) = 0;

  virtual ByteBufferPtr ReadDataFromFileSystemPath(const std::string_view fileFullName) = 0;

  virtual ByteBufferPtr ReadDataFromFile(const std::string_view relPath) = 0;

  virtual jobs::job_ticket_ptr ReadDataFromFileAsync(const std::string_view relPath,
                                                     const FileLoadFunction& loadFunc) = 0;

#if GBF_ENABLE_CPP20
  virtual coro::cotask20<ByteBuffer> AwaitReadDataFromFile(const std::string_view rel_path, JobType target_job_type = JobType::kLogicJob) = 0;
#endif

  virtual StringVector ListFiles(const std::string_view relPath, bool isRecursive = false) = 0;

  virtual StringVector ListFilesSystemPath(const std::string_view sysPath, bool isRecursive = false) = 0;

  ////virtual StringVector ListFilesRecursive(const std::string& relPath) = 0;

  virtual bool CreateDirectory(const std::string_view relPath) = 0;

  virtual bool CreateDirectories(const std::string_view relPath) = 0;

  virtual bool Remove(const std::string_view relPath) = 0;

  virtual bool RemoveAll(const std::string_view relPath) = 0;

  virtual std::string CombinePath(const std::string_view path1, const std::string_view path2) = 0;

  virtual bool SetModuleRootPath(const std::string moduleName, const std::string_view workPath) = 0;

  virtual std::string GetFullPathByModuleName(const std::string moduleName, const std::string_view relativePath) = 0;

  virtual ByteBufferPtr ReadDataFromModuleFile(const std::string moduleName, const std::string_view relPath) = 0;

  ByteBufferPtr MakeMemoryBuffer(size_t dataSize = 0) { return std::make_shared<ByteBuffer>(dataSize); }

  virtual void SetScriptRootPath(const char* scriptRootPath) = 0;

  virtual std::string GetScriptRootPath() const = 0;
};

}  // namespace gbf