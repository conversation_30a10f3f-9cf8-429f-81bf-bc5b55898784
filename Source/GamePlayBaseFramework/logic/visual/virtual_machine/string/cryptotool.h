#pragma once

#include <string>
#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf {
namespace machine {

class VIRTUAL_MACHINE_API CryptoTool {
 public:
  struct HashInfo {
    unsigned int hash0;
    unsigned int hash1;
    unsigned int hash2;

    inline bool operator<(const HashInfo& a) const {
      if (hash0 < a.hash0) return true;
      if (hash0 > a.hash0) return false;
      if (hash1 < a.hash1) return true;
      if (hash1 > a.hash1) return false;
      return hash2 < a.hash2;
    }

    inline bool operator==(const HashInfo& a) const { return (a.hash0 == hash0) && (a.hash1 == hash1) && (a.hash2 == hash2); }
  };

 public:
  CryptoTool();
  ~CryptoTool();

  static unsigned int HashString(unsigned int type, const char* str_in);

  static unsigned int HashString(unsigned int type, const char* str_in, size_t len);

  ////static unsigned int HashString(unsigned int type, const std::string& strIn);

  static HashInfo GetHashInfoFromString(const std::string& str);

  static void EncryptDataAsWow(void* pData, unsigned int dataLen, unsigned int seed);
  static void DecryptDataAsWow(void* pData, unsigned int dataLen, unsigned int seed);

 public:
  static unsigned int* GetCryptTable();
  static unsigned int m_crypt_table[0x500];
};


}  // namespace machine
}  // namespace gbf
