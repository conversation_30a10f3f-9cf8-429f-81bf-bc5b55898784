#pragma once

#include "visual/virtual_machine/vmcore_define.hpp"


#include "reflection/objects/make_value.hpp"
#include "reflection/objects/value.hpp"
#include "reflection/objects/rtti_base.hpp"

namespace gbf {
namespace machine {

class VIRTUAL_MACHINE_API MemoryScope {
 public:
  using ValueMap = std::map<uint32_t, VValuePtr>;
  using TempValueSequence = std::vector<VValuePtr>;
  static constexpr size_t max_temp_size = 16;
 public:
  MemoryScope();
  ~MemoryScope();

  template <typename T>
  VValuePtr CreateValue(uint32_t id, T val) {
    using RealT = std::remove_reference_t<std::remove_cv_t<T>>;

    VValuePtr ret;
    if constexpr (std::is_same_v<RealT, VValue>) {
      ret = std::make_shared<VValue>(val);
    } 
    else if constexpr (std::is_same_v<RealT, VValuePtr>) {
      ret = std::make_shared<VValue>(std::move(((VValuePtr)val)->Clone()));
    }
    else {
      ret = std::make_shared<VValue>(std::move(reflection::make_value(val)));
    }
    AddToValueMap(id, ret);
    return ret;
  }

  VValuePtr CreateRttiValue(uint32_t id, reflection::RttiBase* obj) {
    auto tmp_val = std::make_shared<VValue>(reflection::make_value(reflection::__box_rtti_object(obj)));
    AddToValueMap(id, tmp_val);
    return tmp_val;
  }

  template <typename T>
  T LoadValue(uint32_t id, T default_val) {
    auto val = QueryValue(id);
    if (val) {
      return reflection::value_cast<T>();
    } 
    else {
      return default_val;
    }
  }

  template <typename T>
  VValuePtr AssignOrCreateValue(uint32_t id, T default_val) {
    VValuePtr val = QueryValue(id);
    if (val) {
      *val = reflection::make_value(default_val);
      return val;
    }
    else {
      return CreateValue(id, default_val);
    }
  }

  void DestroyValue(uint32_t id);
  VValuePtr QueryValue(uint32_t id);

  uint32_t NextCanUsedId() noexcept { return --m_free_id; }

  VValuePtr RequireTempValue();
  void ClearTempValues();

 protected:
  bool AddToValueMap(uint32_t id, VValuePtr val);

 protected:
  uint32_t m_free_id = 0xffffffffu;
  size_t m_temp_cursor = 0;

  ValueMap              m_variables_map;
  TempValueSequence     m_temp_variables;
};

}  // namespace machine
}  // namespace gbf
