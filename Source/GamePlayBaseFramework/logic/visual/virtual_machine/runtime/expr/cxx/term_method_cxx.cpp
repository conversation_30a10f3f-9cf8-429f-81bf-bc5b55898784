#include "visual/virtual_machine/runtime/expr/cxx/term_method_cxx.hpp"

#include <stdexcept>
#include "visual/virtual_machine/runtime/expr/term_field_const.hpp"
#include "visual/virtual_machine/string/conststringconverter.h"
#include "visual/virtual_machine/string/gcstring.h"
#include "reflection/builder/class_builder.hpp"
#include "reflection/runtime/cxx_runtime.hpp"
#include "core/imodules/ilog_module.h"
#include "visual/virtual_machine/utils/vvalue_util.hpp"
#include "visual/virtual_machine/utils/stream_util.hpp"

namespace gbf {
namespace machine {

void TermMethodCxx::__meta_auto_register() {
  __register_cxx_type<TermMethodCxx>()
    .base<ITerm>()
    .property("class_name", &TermMethodCxx::class_name_)
    .property("field_name", &TermMethodCxx::method_name_);
}

int TermMethodCxx::GetParamCount() { return (int)(param_array_.size()); }

ITerm* TermMethodCxx::GetParamByIndex(int index) {
  if (index < (int)param_array_.size()) {
    return (ITerm*)param_array_[index].get();
  }
  return nullptr;
}

void TermMethodCxx::Init() {
  term_type_ = VMTermType::Method;

  auto* meta_class = __type_of_name(class_name_);
  assert(meta_class != nullptr);
  meta_class->TryGetFunction(method_name_, meta_func_);
  assert(meta_func_ != nullptr);

  int arg_start = 0;
  switch (meta_func_->call_type()) {
    case reflection::FuncCallType::ObjectCall:
      arg_start = 1;
      break;
    case reflection::FuncCallType::StaticCall:
      arg_start = 0;
      break;
    default:
      ERR_DEF("Overload and other types do not support in TermMethodCxx~~");
      return;
  }
  
  param_array_.clear();
  const auto* caller = meta_func_->GetOneCaller(reflection::FuncLanguageType::AsCxx);
  for (size_t i = arg_start; i < caller->GetParamCount(); i++) {
    auto kind = caller->GetParamType(i);
    auto val = VValueUtil::CreateDefaultValueByType(kind);
    
    auto term = reflection::make_shared_with_rtti<TermFieldConst>(kind);
    term->set_saved_value(val);
    param_array_.push_back(term);
  }
}

VValue TermMethodCxx::Evaluate(const VObject& obj) {
  assert(meta_func_);

  reflection::Args args;
  for (size_t i = 0; i < param_array_.size(); i++) {
    auto val = param_array_[i]->Evaluate(obj);
    args += val;
  }

  VValue ret;
  try {
    switch (meta_func_->call_type())
    {
    case reflection::FuncCallType::ObjectCall:
      ret = reflection::cxx::CallWithArgs(*meta_func_, obj, std::move(args));
      break;
    case reflection::FuncCallType::StaticCall:
      ret = reflection::cxx::CallStaticWithArgs(*meta_func_, std::move(args));
      break;
    default:
      ERR_DEF("Overload and other types do not support in TermMethodCxx~~");
      break;
    }
  }
  catch (...) {
    ERR_DEF("TermMethodCxx[%s::%s()] run failed!", class_name_.c_str(), method_name_.c_str());
  }

  return ret;
}

VMRunStepStatus TermMethodCxx::EvaluateAsResultFunctor(const VObject& obj, const VValue& check_value) {
  assert(meta_func_);
  reflection::Args args;
  if (check_value != VValue::nothing) {
    args += check_value;
  }

  if (args.GetCount() != param_array_.size()) {
    ERR_DEF("TermMethodCxx::EvaluateAsResultFunctor() check param number not equal to VMTermMethod, method name: %s!", method_name_.data());
    return VMRunStepStatus::Failed;
  }

  VValue ret;
  try {
    switch (meta_func_->call_type()) {
    case reflection::FuncCallType::ObjectCall:
      ret = reflection::cxx::CallWithArgs(*meta_func_, obj, std::move(args));
      break;
    case reflection::FuncCallType::StaticCall:
      ret = reflection::cxx::CallStaticWithArgs(*meta_func_, std::move(args));
      break;
    default:
      ERR_DEF("Overload and other types do not support in TermMethodCxx~~");
      break;
    }
  } catch (...) {
    ERR_DEF("TermMethodCxx[%s::%s()] run failed!", class_name_.c_str(), method_name_.c_str());
  }

  if (ret.kind() == reflection::ValueKind::kInteger) {
    return (VMRunStepStatus)reflection::value_cast<int>(ret);
  } else {
    ERR_DEF("TermMethodCxx::EvaluateAsResultFunctor() return value is not a VMRunStepStatus, class name:%s, method name: %s!", class_name_.c_str(), method_name_.c_str());
    return VMRunStepStatus::Failed;
  }
}

void TermMethodCxx::SerializeToJson(IVMStreamWriter& writer) {
  ITerm::SerializeToJson(writer);

  StreamUtil::RttiObjectWriteToJson(this, writer);

  writer.StartArray("param_array");
  for (auto& param : param_array_) {
    writer.StartObject("");
    if (param) {
      param->SerializeToJson(writer);
    }
    else {
      assert(false && "m_param_array contains non VMTerm");
    }
    writer.EndObject();
  }
  writer.EndArray();
}

void TermMethodCxx::DeserializeFromJson(IVMStreamReadNode& node) {
  ITerm::DeserializeFromJson(node);

  StreamUtil::RttiObjectReadFromJson(this, node);

  Init();

  auto* value = node.GetNode("param_array");
  if (value != nullptr) {
    for (size_t i = 0; i < value->GetArraySize(); i++) {
      auto term = ITerm::CreateFromJson(*(value->GetArrayElement(i)));
      SetParam(i, term);
    }
  }
}

bool TermMethodCxx::SetParam(int index, ParamPtr param) {
  assert((size_t)index < param_array_.size() && "TermMethodCxx::set_param our of bounds");
  if ((size_t)index >= param_array_.size()) return false;
  param_array_[index] = param;
  return true;
}

bool TermMethodCxx::SetConstParam(size_t index, VValueKind hint_kind, std::string_view param_content) {
  assert(index < param_array_.size() && index < meta_func_->GetOneCaller(reflection::FuncLanguageType::AsCxx)->GetParamCount() && "VMTermMethod::set_const_param our of bounds");
  if (index >= param_array_.size() || index >= meta_func_->GetOneCaller(reflection::FuncLanguageType::AsCxx)->GetParamCount()) return false;

  int param_index = index;
  switch (meta_func_->call_type()) {
  case reflection::FuncCallType::ObjectCall:
    param_index += 1;
    break;
  case reflection::FuncCallType::StaticCall:
    break;
  case reflection::FuncCallType::OverloadObjectCall:
    param_index += 1;
    break;
  default:
    break;
  }

  auto syskind = meta_func_->GetOneCaller(reflection::FuncLanguageType::AsCxx)->GetParamType(index);
  if (hint_kind != VValueKind::kNone && hint_kind != syskind) {
    WRN_DEF("TermMethodCxx::SetConstParam() called by a not right hint_kind, index = %d, hint_kind = %s, sys_kind = %s, content = %s",
      (int)index,
      reflection::detail::ValueKindAsString(hint_kind).data(),
      reflection::detail::ValueKindAsString(syskind).data(),
      param_content.data());
  }
  hint_kind = syskind;    //Just overwrite kind by system for cxx
  auto term = std::make_shared<TermFieldConst>(VValueUtil::CreateVValueFromString(hint_kind, param_content.data()));
  param_array_[index] = term;
  return true;
}



}  // namespace machine
}  // namespace gbf
