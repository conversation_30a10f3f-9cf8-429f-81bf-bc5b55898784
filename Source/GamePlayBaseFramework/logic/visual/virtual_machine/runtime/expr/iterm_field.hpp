#pragma once

#include "visual/virtual_machine/runtime/expr/iterm.hpp"

namespace gbf {
namespace machine {

//-------------------------------------------------------------------------------------------
class VIRTUAL_MACHINE_API ITermField : public ITerm {
 public:
  ITermField() {
    term_type_ = VMTermType::Field;
  }

  virtual void Assign(const VObject& obj, const VValue& val) = 0;
};

}  // namespace machine
}  // namespace gbf
