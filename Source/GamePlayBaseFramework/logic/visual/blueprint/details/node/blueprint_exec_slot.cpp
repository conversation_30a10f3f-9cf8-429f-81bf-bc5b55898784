#include "visual/blueprint/details/node/blueprint_exec_slot.h"
#include "visual/blueprint/details/blueprint_instruction_impl.h"
#include "visual/blueprint/details/node/blueprint_connection.h"
#include "visual/blueprint/details/node/blueprint_node.h"
// #include "visual/virtual_machine/extend/vmvalue.h"
#include "visual/blueprint/details/blueprint_graph_function.h"
#include "visual/virtual_machine/runtime/memory_scope.hpp"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "reflection/objects/make_user_object.hpp"

namespace gbf { namespace logic {
    UBlueprintExecSlot::UBlueprintExecSlot(size_t slot_index, UBlueprintNode* node_, BlueprintLinkDirection link_dir, std::string_view _name)
        : UBlueprintSlot(slot_index, node_, link_dir, _name)
    {
        m_slot_type = BlueprintSlotType::Exec;
    }

    UBlueprintExecSlot::UBlueprintExecSlot(UBlueprintNode* node)
        : UBlueprintSlot(node)
    {}

    void UBlueprintExecSlot::RtPushLinksAsInstructions(machine::VCoroutine* coroutine_)
    {
        // because call sequence is a stack, we should push all nodes reversed(stack is a later add first remove container)
        for (auto iter = m_used_connection_array.rbegin(); iter != m_used_connection_array.rend(); iter++)
        {
            auto* connection = *iter;
            assert(connection->IsInstructionConnection() && "InstructionSlot::register_nodes() must need a instruction slot here!");
            UBlueprintExecSlot* ins_slot = (UBlueprintExecSlot*)(connection->in_slot());
            auto ins = reflection::make_shared_with_rtti<NGInstructionImpl>(coroutine_, ins_slot);
            coroutine_->PushInstruction(ins);
        }

        ////if (Activated != null)
        ////{
        ////	Activated.Invoke(this, EventArgs.Empty);
        ////}
    }
}}   // namespace gbf::logic
