#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/blueprint/details/node/blueprint_node.h"

namespace gbf { namespace logic {
    class BLUEPRINT_API CEMeta(Reflect) UBlueprintActionNode : public UBlueprintNode
    {
    public:
        // nested types
        enum class LogicState : int
        {
            Ok = 0,
            Warning,
            Error,
        };
        struct BLUEPRINT_API ProcessingInfo
        {
            LogicState State;
            std::string ErrorMessage;
        };

    protected:
        UBlueprintActionNode(BlueprintSlotAvailableFlag flag, std::string_view title = {});

    public:
        virtual ~UBlueprintActionNode() = default;
        ////const ProcessingInfo& processing_state() const noexcept { return m_processing_state; }
        ////std::string_view get_error_message() const noexcept { return m_processing_state.ErrorMessage; }

        ProcessingInfo RtActivate(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot);

    protected:
        virtual ProcessingInfo RtActivateLogicImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot) = 0;

    protected:
        ////ProcessingInfo m_processing_state;
    };

}}   // namespace gbf::logic
