#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/blueprint_node.h"

#include "visual/virtual_machine/runtime/memory_scope.hpp"
#include "visual/virtual_machine/runtime/named_memory_scope.hpp"

namespace gbf { namespace logic {

    class BLUEPRINT_API CEMeta(Reflect) UBlueprintLazyEvaluationNode : public UBlueprintNode
    {
    protected:
        UBlueprintLazyEvaluationNode(std::string_view title = {});
        virtual bool Evaluate(machine::MemoryScope * local, machine::NamedMemoryScope * global) = 0;
        void PullInput(machine::MemoryScope * local, machine::NamedMemoryScope * global);

    public:
        machine::VValuePtr RtPullResult(UBlueprintDataSlot * slot, machine::MemoryScope * local, machine::NamedMemoryScope * global) override;
    };
}}   // namespace gbf::logic
