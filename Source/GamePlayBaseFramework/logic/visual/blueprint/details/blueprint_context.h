#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/virtual_machine/runtime/named_memory_scope.hpp"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/virtual_machine/runtime/vscheduler.hpp"
#include "reflection/objects/value.hpp"
#include "reflection/objects/rtti_base.hpp"

namespace gbf { namespace logic {

    //-----------------------------------------------------------------------------------------------

    class BLUEPRINT_API BlueprintEventInstance
    {
    public:
        std::string event_name;
        size_t out_slot_index;

        BlueprintEventParamList param_list;
        machine::VValue param_value;
    };

    //-----------------------------------------------------------------------------------------------
    class BLUEPRINT_API CEMeta(Reflect) UBlueprintContext : public reflection::RttiBase
    {
        friend class UBlueprintGraphGroup;

    public:
        using BpEventInsList = std::vector<BlueprintEventInstance>;
        UBlueprintContext(UBlueprintGraphPtr main_seq, machine::NamedMemoryScopePtr global_mem_scope);
        ~UBlueprintContext();

        machine::VCoroutine* GetMainCoroutine()
        {
            return m_main_coroutine.get();
        }
        UBlueprintWorkspace* GetParentWorkspace()
        {
            return m_parent_workspace;
        }
        machine::NamedMemoryScope* GetGlobalMemoryScope()
        {
            return m_global_memory_scope.get();
        }

        UBlueprintGraph* GetMainGraph()
        {
            return m_main_graph.get();
        }

        void OnGlobalEvent(std::string_view event_name, machine::VValue _param = {}, size_t out_slot_index = 0);
        void OnGlobalEvent(std::string_view event_name, BlueprintEventParamList param_list = {}, size_t out_slot_index = 0);
        void OnImmediateEvent(std::string_view event_name, BlueprintEventParamList param_list = {}, size_t out_slot_index = 0);

        machine::VMRunStepStatus Update();

        const int GetId()
        {
            return m_Id;
        }

    protected:
        void InitRunEnvironment(machine::NamedMemoryScopePtr global_mem_scope);

    protected:
        int m_Id;

        UBlueprintGraphPtr m_main_graph = nullptr;
        machine::NamedMemoryScopePtr m_global_memory_scope;
        machine::VCoroutinePtr m_main_coroutine;
        UBlueprintWorkspace* m_parent_workspace = nullptr;
        machine::VSchedulerPtr m_scheduler;
        BpEventInsList m_event_ins_list;
    };

}}   // namespace gbf::logic
