#include "visual/blueprint/extend/btree/node/attachment/btree_effector.h"
#include "reflection/objects/make_user_object.hpp"

namespace gbf {
namespace logic {

UBtreeEffector::UBtreeEffector() {}

UBtreeEffector::~UBtreeEffector() {}

bool UBtreeEffector::IsNeedWork(BTEffectPhaseType phase_type) const noexcept { return ((int)m_phase_type & (int)phase_type) != 0; }

void UBtreeEffector::Ser<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(machine::IVMStreamWriter& writer) {
  base::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(writer);
  writer.AddIntProperty("phase_type", (int)m_phase_type);
}
void UBtreeEffector::Deserialize<PERSON><PERSON><PERSON><PERSON>(machine::IVMStreamReadNode& node) {
  base::Deserialize<PERSON><PERSON><PERSON><PERSON>(node);

  m_phase_type = (BTEffectPhaseType)node.GetIntProperty("phase_type");
}

UBtreeEffectorPtr UBtreeEffector::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(machine::IVMStreamReadNode& node) {
  auto effector = reflection::make_shared_with_rtti<UBtreeEffector>();
  effector->Deserialize<PERSON><PERSON><PERSON><PERSON>(node);
  return effector;
}
}  // namespace logic
}  // namespace gbf
