#include "visual/blueprint/extend/btree/node/composite/btree_sequence.h"

namespace gbf {
namespace logic {

UBtreeSequence::UBtreeSequence() {
  m_excute_mode = BTExcuteMode::Sequence;
  m_title = "Sequence";
}

UBtreeSequence::~UBtreeSequence() {}

machine::VMRunStepStatus UBtreeSequence::UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) {
  return machine::VMRunStepStatus::Succeed;
}

void UBtreeSequence::InitializeSlotsImpl() {
  CreateInSlot();
  CreateOutSlot(false);
}

}  // namespace logic
}  // namespace gbf
