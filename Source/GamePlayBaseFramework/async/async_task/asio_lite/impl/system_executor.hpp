#pragma once

#include "async_task/asio_lite/operation/executor_op.hpp"
#include "async_task/asio_lite/sync_primitives/sync_primitives.hpp"
#include "async_task/asio_lite/memory/recycling_allocator.hpp"
#include "async_task/asio_lite/system_context.hpp"

namespace asio {

inline system_context& system_executor::context() const noexcept
{
  return detail::global<system_context>();
}

template <typename Function, typename Allocator>
void system_executor::dispatch(Function&& f, const Allocator&) const
{
  typename std::decay<Function>::type tmp(static_cast<Function&&>(f));
  asio_handler_invoke_helpers::invoke(tmp, tmp);
}

template <typename Function, typename Allocator>
void system_executor::post(Function&& f, const Allocator& a) const
{
  typedef typename std::decay<Function>::type function_type;

  system_context& ctx = detail::global<system_context>();

  // Allocate and construct an operation to wrap the function.
  typedef detail::executor_op<function_type, Allocator> op;
  typename op::ptr p = { detail::addressof(a), op::ptr::allocate(a), 0 };
  p.p = new (p.v) op(static_cast<Function&&>(f), a);

  ASIO_HANDLER_CREATION((ctx, *p.p,
        "system_executor", &this->context(), 0, "post"));

  ctx.scheduler_.post_immediate_completion(p.p, false);
  p.v = p.p = 0;
}

template <typename Function, typename Allocator>
void system_executor::defer(Function&& f, const Allocator& a) const
{
  typedef typename std::decay<Function>::type function_type;

  system_context& ctx = detail::global<system_context>();

  // Allocate and construct an operation to wrap the function.
  typedef detail::executor_op<function_type, Allocator> op;
  typename op::ptr p = { detail::addressof(a), op::ptr::allocate(a), 0 };
  p.p = new (p.v) op(static_cast<Function&&>(f), a);

  ASIO_HANDLER_CREATION((ctx, *p.p,
        "system_executor", &this->context(), 0, "defer"));

  ctx.scheduler_.post_immediate_completion(p.p, true);
  p.v = p.p = 0;
}

} // namespace asio


