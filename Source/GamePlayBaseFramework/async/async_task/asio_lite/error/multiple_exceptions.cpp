#include "async_task/asio_lite/error/multiple_exceptions.hpp"

namespace asio {


multiple_exceptions::multiple_exceptions(
    std::exception_ptr first) noexcept
  : first_(ASIO_MOVE_CAST(std::exception_ptr)(first))
{
}

const char* multiple_exceptions::what() const noexcept
{
  return "multiple exceptions";
}

std::exception_ptr multiple_exceptions::first_exception() const
{
  return first_;
}


} // namespace asio

