using System.Collections.Generic;

namespace CppAst.Ponder
{
    public class MethodContainerDrop : General.MethodContainerDrop
    {
        private static DotLiquid.Template msBridgeBodyLT;

        private static DotLiquid.Template msLuaOverrideLT;
        private static DotLiquid.Template msLuaBaseCallHelperLT;

        static MethodContainerDrop()
        {
            msBridgeBodyLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/native_method.liquid");

            msLuaOverrideLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/native_lua_override_method.liquid");
            msLuaBaseCallHelperLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/native_lua_base_call_helper_method.liquid");
        }

        public MethodContainerDrop(General.ClassDrop parentDrop,
            Exporter parentExporter,
            CppFunction method,
            Dictionary<string, CppTemplateArgument> allTempArgTypeMap, bool isOverload, string targetName = null) :
            base(parentDrop, parentExporter, method, allTempArgTypeMap, isOverload, targetName)
        {
        }

        internal string GenerateRenderContent(DotLiquid.Template tempLT,
            string useCase,
            bool stripEndLineWrap = false)
        {
            string result = tempLT.Render(DotLiquid.Hash.FromAnonymousObject(new
            {
                method = this,
                use_case = useCase,
            }));

            return stripEndLineWrap ? Clangen.NamespaceTool.TrimTailLineWrap(result) : result;
        }

        public string Render
        {
            get
            {
                string useCaseName = "Normal";

                if (IsOverload)
                {
                    useCaseName = "Overload";
                }
                else
                {
                    if (IsStatic)
                    {
                        useCaseName = "NormalStatic";
                    }
                    else
                    {
                        useCaseName = "Normal";
                    }
                }

                return GenerateRenderContent(msBridgeBodyLT, useCaseName, true);
            }
        }

        public string RenderRpcClangProxy
        {
            get
            {
                string useCaseName = "RpcClangProxy";
                return GenerateRenderContent(msBridgeBodyLT, useCaseName, true);
            }
        }

        public string RenderEntityRpcClangProxy
        {
            get
            {
                string useCaseName = "EntityRpcClangProxy";
                return GenerateRenderContent(msBridgeBodyLT, useCaseName, true);
            }
        }

        public string RenderConstructor
        {
            get
            {
                string useCaseName = "Constructor";
                return GenerateRenderContent(msBridgeBodyLT, useCaseName, true);
            }
        }

        public string RenderLuaOverride
        {
            get
            {
                string result = msLuaOverrideLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));
                return result;
            }
        }

        public string RenderLuaBaseHelperDefinition
        {
            get
            {
                string result = msLuaBaseCallHelperLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    method = this,
                }));
                return result;
            }
        }
    }
}
