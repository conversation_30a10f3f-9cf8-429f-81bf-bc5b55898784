{%- assign enum_name = enum.name -%}
{%- case use_case -%}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'Ponder' -%}
static int ponder_register_enum_{{ enum.identify_name }}() {
{%- unless enum.with_name -%}
    enum class {{ enum.identify_name }}{};
    {%- assign enum_name = enum.identify_name -%}
{%- endunless -%}
    __register_enum<{{ enum_name }}>()
{%- for item in enum.items -%}
        .value("{{ item.name }}", (int)({{ enum.name }}::{{ item.name }}))
{%- endfor -%}
    ;
    return 0;
}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
 {%- when 'Lua' -%}
 static int ponder_expose_enum_to_lura_{{ enum.identify_name }}(lua_State* L) {
     lura::get_global_namespace(L)
    {% for ns in enum.export_namespace_list %}.begin_namespace("{{ns}}"){% endfor %}
        {%- if enum.is_extract_mode -%}
        .extract_enum<{{ enum_name }}>()
        {%- else -%}
        .expose_enum<{{ enum_name }}>("{{ enum_name }}")
        {%- endif -%}
    {% for ns in enum.export_namespace_list %}.end_namespace(){% endfor %};
    return 0;
 }
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
 {%- when 'Embeded' -%}
{%- unless enum.with_name -%}
    enum class {{ enum.identify_name }}{};
    {%- assign enum_name = enum.identify_name -%}
{%- endunless -%}
    __register_enum<{{ enum_name }}>()
{%- for item in enum.items -%}
        .value("{{ item.name }}", (int)({{ enum.name }}::{{ item.name }}))
{%- endfor -%}
    ;
{%- endcase -%}