using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using CppAst;


namespace CppAst.ChatDoc
{

    public class ClassDrop : DotLiquid.Drop
    {
        private static DotLiquid.Template msBridgeBodyLT;
        private CppClass mNativeClass;
        private Exporter mExporter;

        public List<FunctionDrop> Constructors { get; private set; } = new List<FunctionDrop>();
        public List<FunctionDrop> Functions { get; private set; } = new List<FunctionDrop>();
        public List<FieldDrop> Fields { get; private set; } = new List<FieldDrop>();

        public string FullName => mNativeClass.FullName;

        public string Namespace => mNativeClass.FullParentName;

        public string AiDoc { get; set; }

        public string AiDocUseCase { get; set; }

        static ClassDrop()
        {
            msBridgeBodyLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ChatDoc/Templates/chatdoc_class_body.liquid");
        }

        public ClassDrop(CppClass cls, Exporter exporter)
        {
            mExporter = exporter;
            mNativeClass = cls;

        }

        public string RenderBody
        {
            get
            {
                string result = msBridgeBodyLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    this_class = this,
                }));
                return result;
            }
        }
    }

}
