using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.IO;
using System.Text;
using DotLiquid;
using CppAst;
using Clangen;


namespace CppAst.ChatDoc
{
    public class Exporter
    {
        //////private static Template msFileBodyTemplate;
        ////private static Template msPInvokeHelperTemplate;

        private ChatDocConfig mConfig = null;

        public static Exporter Instance = null;

        public readonly string kMonoExportDestPath;
        public readonly string kMonoDetialConfigFile;

        public CppCompilation ParentCompilation { get; private set; }

        static Exporter()
        {
            ////msFileBodyTemplate = Clangen.TemplateHelper.ParseTemplate("Exporters/ChatDoc/Templates/chatdoc_file_body.liquid");
        }

        public Exporter(CppCompilation builder, string exportPath, string detailConfigFile)
        {
            Instance = this;
            ParentCompilation = builder;
            mConfig = ChatDocConfig.GetConfig(detailConfigFile);
            kMonoExportDestPath = exportPath;
            kMonoDetialConfigFile = detailConfigFile;
        }

        private string BuildClassInfo(CppClass cls)
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendLine($"已知C++类: {cls.FullName} ");

            sb.AppendLine($"## 包含成员函数:");
            int maxfn = 20;
            int fncount = 0;
            foreach (var fn in cls.Functions)
            {
                if (fn.Visibility != CppVisibility.Public)
                {
                    continue;
                }

                sb.AppendLine($"{fn};");
                fncount++;
                if (fncount >= maxfn) break;
            }

            sb.AppendLine($"共计{cls.Functions.Count}个函数.");

            sb.AppendLine($"## 包含成员变量如下:");
            int maxfield = 10;
            int fieldcount = 0;
            foreach (var field in cls.Fields)
            {
                if (field.Visibility != CppVisibility.Public)
                {
                    continue;
                }

                sb.AppendLine($"{field};");

                fieldcount++;
                if (fieldcount >= maxfield) break;
            }

            sb.AppendLine($"共计{fieldcount}个成员变量.");

            AppendClassExtrasInfo(cls, sb);

            return sb.ToString();
        }

        ////bool TryToGenerateDropForFunctions(CppClass cls, CppFunction[] funcs, bool isConstructor)
        ////{
        ////    List<string> subTitles = new List<string>();
        ////    subTitles.Add("`概要`");
        ////    subTitles.Add("`参数列表`");
        ////    if (!isConstructor)
        ////    {
        ////        subTitles.Add("`返回值`");
        ////    }
        ////    subTitles.Add("`函数说明`");

        ////    StringBuilder sb = new StringBuilder();
        ////    sb.AppendLine($"`{cls.FullName}` C++类中包含成员函数:");
        ////    foreach(var func in funcs)
        ////    {
        ////        sb.AppendLine($"{func};");
        ////    }
        ////    sb.AppendLine($"共{funcs.Length}个.");
        ////    string funcType = isConstructor ? "构造函数" : "成员函数";
        ////    sb.Append($"请用markdown格式逐个给出这些{funcType}的说明, 每个函数使用`---`分隔, 说明中需要包含:");
        ////    foreach(var st in subTitles)
        ////    {
        ////        sb.Append($"{st}, ");
        ////    }
        ////    sb.AppendLine($"这几部分的内容, 这些内容以四级子标题组织");

        ////    string multiTest = ChatGptInstance.Instance.ChatToAi(sb.ToString(), false);
        ////    Debugger.Break();

        ////    return true;
        ////}


        FunctionDrop TryToGenerateDropForFunction(CppClass cls, CppFunction func, int funcIndex, bool isConstructor, string markdownComment)
        {
            List<string> subTitles = new List<string>();
            subTitles.Add("概要");
            subTitles.Add("参数列表");
            if (!isConstructor)
            {
                subTitles.Add("返回值");
            }
            subTitles.Add("函数说明");

            StringBuilder sb = new StringBuilder();
            string funcType = isConstructor ? "构造函数" : "成员函数";

            sb.AppendLine($"已知C++类 {cls.FullName},");

            AppendClassExtrasInfo(cls, sb);

            sb.AppendLine($"该类中包含声明为`{func}` 的{funcType}");

            AppendFunctionExtrasInfo(func, sb);

            sb.Append($"请给出该{funcType}的说明, 说明中仅包含:\n");
  
           
            foreach (var st in subTitles)
            {
                sb.AppendLine($"\t-{st}");
            }
            sb.AppendLine($"\n 这几部分的内容.");      //每部分的内容尽量以列表的方式组织.
            sb.AppendLine(markdownComment);

            sb.AppendLine("## 输出的参考如下:");
            sb.AppendLine(@"```
#### 概要
这是一个用于检测光线和轴对齐盒子是否相交的成员函数。

#### 参数列表
该函数有一个参数：
- `const AxisAlignedBox& box`：待检测的轴对齐盒子。

#### 返回值
该函数返回一个`RayTestResult`类型的对象，表示光线和轴对齐盒子的相交情况。

#### 函数说明
该函数用于检测光线和轴对齐盒子是否相交。```");

            FunctionDrop funcDrop = new FunctionDrop(func, funcIndex, this);

            funcDrop.AiDoc = ChatGptInstance.Instance.ChatToAi(sb.ToString(), false);

            return funcDrop;
        }

        List<string> QueryClassEtraInfos(CppClass cls)
        {
            List<string> extras = new List<string>();
            foreach (var attr in cls.Attributes)
            {
                if (attr.Kind == AttributeKind.CommentAttribute)
                {
                    extras.Add(attr.Arguments);
                }
            }
            return extras;
        }

        void AppendClassExtrasInfo(CppClass cls, StringBuilder sb)
        {
            var extras = QueryClassEtraInfos(cls);
            if (extras.Count > 0)
            {
                sb.AppendLine($"## 同时类包含以下附加信息:");
                foreach (var extra in extras)
                {
                    sb.AppendLine(extra);
                }
                sb.AppendLine($"共计{extras.Count}条信息.");
            }
        }

        void AppendFunctionExtrasInfo(CppFunction func, StringBuilder sb)
        {
            var extras = QueryFunctionExtraInfos(func);
            if (extras.Count > 0)
            {
                sb.AppendLine($"## 同时该函数包含以下附加信息:");
                foreach (var extra in extras)
                {
                    sb.AppendLine(extra);
                }
                sb.AppendLine($"共计{extras.Count}条信息.");
            }
        }

        List<string> QueryFunctionExtraInfos(CppFunction func)
        {
            List<string> extras = new List<string>();
            foreach (var attr in func.Attributes)
            {
                if (attr.Kind == AttributeKind.CommentAttribute)
                {
                    extras.Add(attr.Arguments);
                }
            }
            return extras;
        }

        ClassDrop TryToGenerateClassDrop(CppClass cls)
        {
            ClassDrop clsdrop = new ClassDrop(cls, this);

            string clsinfo = BuildClassInfo(cls);
            
            string generate_info = $"{clsinfo}\n 请给出类的具体作用说明, 不用给出具体的属性和函数的说明, 仅包含类本身的作用和说明即可.";

            clsdrop.AiDoc = ChatGptInstance.Instance.ChatToAi(generate_info, true);

            string generate_usecase_info = $"{clsinfo}\n 请给出该类的一段示例代码, 用 markdown 格式输出.";
            clsdrop.AiDocUseCase = ChatGptInstance.Instance.ChatToAi(generate_usecase_info, false);

            string markdownComment = "输出内容的格式请使用 markdown, 每部分内容以四级子标题组织";

            ////TryToGenerateDropForFunctions(cls, cls.Functions.Where(x=>x.Visibility == CppVisibility.Public).ToArray(), false);

            int conIndex = 0;
            foreach (var fn in cls.Constructors)
            {
                if (fn.Visibility != CppVisibility.Public)
                {
                    continue;
                }

                ////var funcdrop = new FunctionDrop(fn, ++conIndex, this);
                ////string methodinput = $"`{cls.FullName}` C++类中包含成员函数:`{fn}`\n, 请给出该构造函数的说明, 说明中仅包含`概要`, `参数列表`, `函数说明` 这几部分内容, 请使用markdown格式组织内容, 每部分内容以四级子标题组织";
                ////funcdrop.AiDoc = ChatGptInstance.Instance.ChatToAi(methodinput, false);

                clsdrop.Constructors.Add(TryToGenerateDropForFunction(cls, fn, ++conIndex, false, markdownComment));
            }

            int funcIndex = 0;
            foreach (var fn in cls.Functions)
            {
                if (fn.Visibility != CppVisibility.Public)
                {
                    continue;
                }


                ////var funcdrop = new FunctionDrop(fn, ++funcIndex, this);

                ////string methodinput = $"`{cls.FullName}` C++类中包含成员函数:`{fn}`\n, 请给出该函数的说明, 说明中仅包含`概要`, `参数列表`, `返回值`, `函数说明` 这几部分内容, 请使用markdown格式组织内容, 每部分内容以四级子标题组织";
                ////funcdrop.AiDoc = ChatGptInstance.Instance.ChatToAi(methodinput, false);
                clsdrop.Functions.Add(TryToGenerateDropForFunction(cls, fn, ++funcIndex, false, markdownComment));
            }

            int fieldIndex = 0;
            foreach (var field in cls.Fields)
            {
                if (field.Visibility != CppVisibility.Public)
                {
                    continue;
                }

                var fielddrop = new FieldDrop(field, ++fieldIndex, this);

                string fieldinput = $"`{cls.FullName}` C++类中包含成员变量:`{field}`\n, 请给出该字段的说明";
                fielddrop.AiDoc = ChatGptInstance.Instance.ChatToAi(fieldinput, false);
                
                clsdrop.Fields.Add(fielddrop);
            }

            return clsdrop;
        }


        public void DoExport(Clangen.ExportPassInfo passInfo)
        {
            string autoExportPath = kMonoExportDestPath;  //Path.Combine(kMonoExportDestPath, "auto");
            if (!Directory.Exists(autoExportPath))
            {
                Directory.CreateDirectory(autoExportPath);
            }

            //Init chatgpt
            ChatGptInstance.Instance.Init(mConfig.ApiKey);
            ChatGptInstance.Instance.MainDirection = mConfig.MainDirection;
            Log.Info($"chatgpt direction:{mConfig.MainDirection}");

            //collect class drop first~~
            foreach(var clsname in mConfig.Classes)
            {
                var cppname = NamespaceTool.CookNamespaceToCppNamespace(clsname);
                var cls = ParentCompilation.FindByFullName<CppClass>(cppname);
                if(cls != null)
                {
                    string filename = $"{NamespaceTool.GetIdentifyName(cls.FullName)}.md";
                    string classMdPath = Path.Combine(kMonoExportDestPath, filename);

                    Log.Info($"Try to generate doc for {cls.FullName}...");
                    var drop = TryToGenerateClassDrop(cls);
                    string docContent = drop.RenderBody;
                    File.WriteAllText(classMdPath, docContent, Encoding.UTF8);

                    Log.Info($"generate doc for {cls.FullName} to {filename} suc.");
                }
                else
                {
                    Log.Warning($"Can not find class in AST, name:{clsname}");
                }
            }
        }
    }
}


