{"profiles": {"manged_gbf": {"commandName": "Project", "commandLineArgs": "ParseGBF -cfg=\"C://CrossEngine//Source//GamePlayBaseFramework//editor//managed//clangen_cfg.yaml\"", "workingDirectory": "C:\\CrossEngine\\Source\\GamePlayBaseFramework\\editor\\managed"}, "CrossEngine": {"commandName": "Project", "commandLineArgs": "ParseCrossEngine build_x64", "workingDirectory": "../../../../.."}, "TestUnit": {"commandName": "Project", "commandLineArgs": "ParseCrossEngine -c compile_options.yml -o GeneratedCode --skip-binding-editor --skip-binding-script", "workingDirectory": "../../../../../Source/GamePlayBaseFramework/test/clang_tool_test"}, "Plugin-NoEngineSrc": {"commandName": "Project", "commandLineArgs": "ParseCrossEngine --compile-options-file D:/Projects/FlightModule/ProjFlightGame/build_x64/compile_info/compile_options.yml --generated-dir D:/Projects/FlightModule/ProjFlightGame/Source/GeneratedCode/  --without-engine-source", "workingDirectory": "D:\\install"}, "Install": {"commandName": "Project", "commandLineArgs": "Install -o E:/Tencent/CrossEngine_Package", "workingDirectory": "../../../../.."}, "InsertLog": {"commandName": "Project", "commandLineArgs": "InsertLog -c build_x64/compile_info/compile_options.yml -cfg=Tools/clang-tool/InsertLog.yml", "workingDirectory": "../../../../.."}, "RemoveLog": {"commandName": "Project", "commandLineArgs": "InsertLog --clean -c build_x64/compile_info/compile_options.yml -cfg=Tools/clang-tool/InsertLog.yml", "workingDirectory": "../../../../.."}}}