#!/bin/sh

HASH=""
HASH=$(git rev-parse --short HEAD)

out_path="Publish"
if [ $# -ge 1 ]; then
    out_path=$1
fi

echo "out path: ${out_path}"

dotnet publish -c Release --no-self-contained -p:PublishSingleFile=false -p:OutputPath=${out_path}/18.1.0.3/win/ -r win-x64
dotnet publish -c Release --no-self-contained -p:PublishSingleFile=false -p:OutputPath=${out_path}/18.1.0.3/linux/ -r linux-x64
dotnet publish -c Release --no-self-contained -p:PublishSingleFile=false -p:OutputPath=${out_path}/18.1.0.3/osx/ -r osx-x64
