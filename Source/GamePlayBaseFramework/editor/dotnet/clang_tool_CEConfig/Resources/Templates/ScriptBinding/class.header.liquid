class {{class.script_name}} : public script::ScriptClass
{
public:
    using BaseType = script::ScriptClass;
    using ClassType = script::ClassDefine<{{class.script_name}}>;
    using ValueType = {{class.full_name}};
public:
    explicit {{class.script_name}}(script::Arguments const& args);
    static ClassType& Class();
    static ValueType Create(script::Arguments const& args);
public:
{%- for field in class.fields -%}
    LocalValue Get{{field.name}}() const;
    void Set{{field.name}}(const LocalValue& value);
{%- endfor -%}
public:
    ValueType Value;
};
