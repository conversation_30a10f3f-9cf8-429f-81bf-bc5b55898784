using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;


namespace Clangen
{
    public static class StringUtil
    {
        static byte[] c_escaped_len = new byte[] {
            4, 4, 4, 4, 4, 4, 4, 4, 4, 2, 2, 4, 4, 2, 4, 4,  // \t, \n, \r
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            1, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1,  // ", '
            1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  // '0'..'9'
            1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  // 'A'..'O'
            1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1,  // 'P'..'Z', '\'
            1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,  // 'a'..'o'
            1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4,  // 'p'..'z', DEL
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
            4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
          };

        // Calculates the length of the C-style escaped version of 'src'.
        // Assumes that non-printable characters are escaped using octal sequences, and
        // that UTF-8 bytes are not handled specially.
        public static int CEscapedLength(string src)
        {
            int escaped_len = 0;
            for (int i = 0; i < src.Length; ++i)
            {
                byte c = (byte)(src[i]);
                escaped_len += c_escaped_len[c];
            }
            return escaped_len;
        }

        public static bool IsPrintChar(char c)
        {
            return c >= 0x20 && c <= 0x7E;
        }

        // ----------------------------------------------------------------------
        // Escapes 'src' using C-style escape sequences, and appends the escaped string
        // to 'dest'. This version is faster than calling CEscapeInternal as it computes
        // the required space using a lookup table, and also does not do any special
        // handling for Hex or UTF-8 characters.
        // ----------------------------------------------------------------------
        public static void CEscapeAndAppend(string src, StringBuilder dest)
        {
            int escaped_len = CEscapedLength(src);
            if (escaped_len == src.Length)
            {
                dest.Append(src);
                return;
            }

            ////int cur_dest_len = dest.Length;
            ////dest.Capacity = cur_dest_len + escaped_len;
            ////char* append_ptr = &(*dest)[cur_dest_len];

            for (int i = 0; i < src.Length; ++i)
            {
                char c = (char)(src[i]);
                switch (c)
                {
                    case '\n': dest.Append('\\'); dest.Append('n'); break;
                    case '\r': dest.Append('\\'); dest.Append('r'); break;
                    case '\t': dest.Append('\\'); dest.Append('t'); break;
                    case '\"': dest.Append('\\'); dest.Append('\"'); break;
                    case '\'': dest.Append('\\'); dest.Append('\''); break;
                    case '\\': dest.Append('\\'); dest.Append('\\'); break;
                    default:
                        if (!IsPrintChar(c))
                        {
                            int startZero = (int)'0';
                            byte curVal = (byte)c;
                            dest.Append('\\');
                            dest.Append((char)(startZero + curVal / 64));
                            dest.Append((char)(startZero + (curVal % 64) / 8));
                            dest.Append((char)('0' + curVal % 8));
                        }
                        else
                        {
                            dest.Append(c);
                        }
                        break;
                }
            }
        }

        public static string CEscape(string src)
        {
            StringBuilder dest = new StringBuilder();
            CEscapeAndAppend(src, dest);
            return dest.ToString();
        }

        // Escape C++ trigraphs by escaping question marks to \?
        public static string EscapeTrigraphs(string to_escape)
        {
            return to_escape.Replace("?", "\\?");
        }

        public static string BinaryToString(byte[] binData, int startIndex, int len)
        {
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < len; i++)
            {
                int tmpIndex = startIndex + i;
                if (tmpIndex >= binData.Length) break;
                sb.Append((char)binData[tmpIndex]);
            }
            return sb.ToString();
        }

        public static string EscapeBinaryToString(byte[] binData, string indentStr = "    ")
        {
            StringBuilder sb = new StringBuilder();
            if (binData.Length > 65535)
            {
                // Workaround for MSVC: "Error C1091: compiler limit: string exceeds
                // 65535 bytes in length". Declare a static array of chars rather than
                // use a string literal. Only write 25 bytes per line.
                const int kBytesPerLine = 25;
                sb.Append("  static const signed char descriptor[] = { \n");
                for (int i = 0; i < binData.Length;)
                {
                    sb.Append($"{indentStr}");
                    for (int j = 0; j < kBytesPerLine && i < binData.Length; ++i, ++j)
                    {
                        sb.Append($"{(int)((System.SByte)binData[i])}, ");
                    }
                    sb.Append("\n");
                }
                sb.Append("};");  // null-terminate
            }
            else
            {
                // Only write 40 bytes per line.
                const int kBytesPerLine = 40;
                for (int i = 0; i < binData.Length; i += kBytesPerLine)
                {
                    sb.Append($"{indentStr}\"{EscapeTrigraphs(CEscape(BinaryToString(binData, i, kBytesPerLine)))}\"\n");
                }
            }
            sb.Append($"{indentStr}");

            return sb.ToString();
        }

        public static string GetCombinedString(List<string> strArray, string combinedSeparator)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < strArray.Count; i++)
            {
                if (i == 0)
                {
                    sb.Append(strArray[i]);
                }
                else
                {
                    sb.Append(combinedSeparator);
                    sb.Append(strArray[i]);
                }
            }

            return sb.ToString();
        }

        public static void CombineToStringBuffer(StringBuilder outputSb, List<string> strArray, string combinedSeparator)
        {
            for (int i = 0; i < strArray.Count; i++)
            {
                if (i == 0)
                {
                    outputSb.Append(strArray[i]);
                }
                else
                {
                    outputSb.Append(combinedSeparator);
                    outputSb.Append(strArray[i]);
                }
            }
        }

        public static string WildcardToRegex(string pattern)
        {
            //. 为正则表达式的通配符，表示：与除 \n 之外的任何单个字符匹配。
            //* 为正则表达式的限定符，表示：匹配上一个元素零次或多次
            //? 为正则表达式的限定符，表示：匹配上一个元素零次或一次
            return "^" + Regex.Escape(pattern).Replace("\\*", ".*").Replace("\\?", ".") + "$";
        }

        public static bool IsMatchWildcard(string pattern, string detectTarget)
        {
            string needPattern = WildcardToRegex(pattern);
            return Regex.IsMatch(detectTarget, needPattern);

        }

        public static string ReplicateString(string org, int replicateCount)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < replicateCount; i++)
            {
                sb.Append(org);
            }
            return sb.ToString();
        }

        public static string ReplaceToLinuxLineEnd(string content)
        {
            return content.Replace("\r\n", "\n");
        }

        public static string TrimTailLineWrap(string content)
        {
            if (content.EndsWith("\r\n"))
            {
                return content.Substring(0, content.Length - 2);
            }
            else if (content.EndsWith("\n"))
            {
                return content.Substring(0, content.Length - 1);
            }
            else
            {
                return content;
            }
        }

        public static string FilterProtoName(string orgName)
        {
            Dictionary<string, string> filterWords = new Dictionary<string, string>();
            filterWords.Add("_", "_5f");
            filterWords.Add("/", "_2f");
            filterWords.Add(".", "_2e");

            foreach (var pair in filterWords)
            {
                orgName = orgName.Replace(pair.Key, pair.Value);
            }

            return orgName;
        }

        public static string ParseToEmbededPath(string relativePath)
        {
            relativePath = relativePath.Replace("/", ".");
            relativePath = relativePath.Replace("\\", ".");

            return $"clang-tool_CE.{relativePath}";
        }
    }

}