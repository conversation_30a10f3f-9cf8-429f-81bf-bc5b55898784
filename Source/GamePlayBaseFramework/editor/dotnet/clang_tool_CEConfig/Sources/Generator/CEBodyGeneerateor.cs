using Clangen.Model;
using DotLiquid;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;

namespace Clangen
{
    class CEBodyGenerator
    {
        private Template fileTemplate;
        private CrossEngineOption CEConfig;
        private HashSet<string> GeneratedFiles = new HashSet<string>();

        public CEBodyGenerator(CrossEngineOption config)
        {
            fileTemplate = Template.Parse("{% include 'CEBody/generated_header' %}");
            CEConfig = config;
        }

        public string GenerateRenderContent(IList<ClassModel> classes, string file_id)
        {
            return fileTemplate.Render(Hash.FromAnonymousObject(new
            {
                current_file_id = file_id,
                classes = classes
            }));
        }

        public static string GetOutputPath(string filePath, ModuleInfo module, CrossEngineOption CEConfig, bool? isCoreModule = null)
        {
            string outputDir;

            bool isCore = module.is_core;
            var baseName = Path.GetFileNameWithoutExtension(filePath);
            var dirName = Path.GetDirectoryName(filePath);
            
            if (isCore)
            {
                var relativeDir = Path.GetRelativePath(CEConfig.CrossEngineSourceRoot, dirName);
                outputDir = CEConfig.GeneratedCodeDir + "/" + relativeDir;
            }
            else
            {
                var relativeDir = Path.GetRelativePath(module.source_dir, dirName);
                outputDir = $"{module.source_dir}/GeneratedCode/" + relativeDir;
            }
            var finalPath = Path.GetFullPath($"{outputDir}/{baseName}.generated.h").Replace('\\', '/');
            return finalPath;
        }

        public void Prepare()
        {
            Regex CEBodyTagRegex = new Regex(@"\bCEGeneratedCode\(\b", RegexOptions.Compiled);

            foreach (var module in CEConfig.ModuleNeedToBeGenerated)
            {
                foreach (var filePath in module.sources)
                {
                    if (!File.Exists(filePath) || filePath.EndsWith("CEMetaMacros.h"))
                        continue;
                    var text = File.ReadAllText(filePath);
                    if (CEBodyTagRegex.IsMatch(text))
                    {
                        var path = GetOutputPath(filePath, module, CEConfig, module.is_core);
                        if (!File.Exists(path))
                        {
                            FileUtils.WriteTextAfterCompare(path, "");
                        }
                    }
                }
            }
        }

        public HashSet<string> Generate(AstContent astContent)
        {
            GeneratedFiles.Clear();

            var groupAst = astContent.GroupByModule(CEConfig.ModuleNeedToBeGenerated);
            var classDict = new Dictionary<string, ClassModel>();

            foreach (var c in astContent.Classes)
            {
                if (c.HasGeneratedBody)
                {
                    classDict[c.FullName] = c;
                }
            }

            foreach (var c in astContent.Classes)
            {
                if (c.CEMeta.HasKey("PartOf") && c.CEMeta.GetValue("PartOf")[0] is Dictionary<string, object> partialClasses)
                {
                    string names = string.Join(", ", partialClasses.Keys);
                    //Console.WriteLine($"{c.FullName}: {names}");
                    foreach (var name in partialClasses.Keys)
                    {
                        var fullname = name.Contains("::") ? name : c.Namespace + "::" + name;
                        if (classDict.ContainsKey(fullname))
                        {
                            var prtialClass = classDict[fullname];
                            var csatrributestiring = "CsAttribute(\"PropertyInfo(PropertyType = \\\"Struct\\\")\")";
                            prtialClass.AddField(c, "m" + c.Name, "CEMeta:Serialize, Editor, Reflect, ScriptReadWrite, " + csatrributestiring);
                        }
                    }
                }
            }

            foreach (var module in CEConfig.ModuleNeedToBeGenerated)
            {
                if (groupAst.TryGetValue(module.name, out var moduleAst))
                {
                    foreach (var (filePath, ast) in moduleAst.GroupByFile())
                    {
                        var classes = ast.Classes.Where(c => c.HasGeneratedBody).ToList();
                        if (classes.Count > 0)
                        {
                            string file_id = filePath.Replace(CEConfig.CrossEnginePath, "");
                            file_id = "FID" + file_id.Replace('/', '_').Replace('.', '_');

                            var content = GenerateRenderContent(classes, file_id);
                            var path = GetOutputPath(filePath, module, CEConfig, module.is_core);
                            FileUtils.WriteTextAfterCompare(path, content);
                            GeneratedFiles.Add(path);
                        }
                    }
                }
            }
            return GeneratedFiles;
        }
    }
}
