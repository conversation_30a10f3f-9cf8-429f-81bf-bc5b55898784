using CppAst;
using System;
using System.Collections.Generic;

namespace Clangen.Model
{
    class EnumItemModel : EntityModel
    {
        public EnumItemModel(CppEnumItem EnumItem)
            : base(EnumItem)
        {
            CEMeta = new Parser.CEMetaManager(EnumItem.MetaAttributes);
            Name = EnumItem.Name;
            Value = EnumItem.Value;
        }
        public long Value { get; private set; }
    }

    class EnumModel : EntityModel
    {
        public List<EnumItemModel> Enumerators { get; }
        public CppEnum EnumDecl;
        ClassModel Parent;
        public string Namespace { get; }
        public string FullName { get; }
        public bool hasParent;
        public bool HasTypeScriptNameSpace => FullName.Contains("::");
        public string TypeScriptNameSpace { get; }
        public string DelegateScriptName { get; }
        public EnumModel(CppEnum decl, ClassModel parent)
            : base(decl)
        {
            CEMeta = new Parser.CEMetaManager(decl.MetaAttributes);
            Parent = parent;
            EnumDecl = decl;
            Name = EnumDecl.Name;

            FullName = decl.FullName;
            Namespace = Parent is null ? FullName.Remove(Math.Max(0, FullName.Length - decl.Name.Length - 2)) : Parent.Namespace;

            TypeScriptNameSpace = Parent is null ? FullName.Remove(Math.Max(0, FullName.Length - decl.Name.Length - 2)) : Parent.FullName;
            TypeScriptNameSpace = TypeScriptNameSpace.Replace("::", ".");

            DelegateScriptName = FullName.Replace("::", "") + "DelegateScript";

            if (Parent is null)
            {
                Namespace = FullName.Remove(Math.Max(0, FullName.Length - decl.Name.Length - 2));
                hasParent = false;
                //IsPublic = true;
            }
            else
            {
                Namespace = Parent.Namespace;
                hasParent = true;
                //if (Parent.)
            }

            Enumerators = new List<EnumItemModel>();
            foreach (var e in EnumDecl.Items)
            {
                Enumerators.Add(new EnumItemModel(e));
            }
        }
        public bool Equals(EnumModel other)
        {
            bool parentEquals = Parent == other.Parent || (Parent != null && other.Parent != null && Parent.Equals(other.Parent));

            // 实现逻辑，比如比较某些关键属性
            return other != null && FullName == other.FullName && parentEquals;
        }

        public override bool Equals(object obj) => Equals(obj as EnumModel);
        public override int GetHashCode()
        {
            // 返回一个适当的哈希码
            return FullName.GetHashCode();
        }
    }
}