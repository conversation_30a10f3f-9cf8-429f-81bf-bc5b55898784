using Clangen.Parser;
using CppAst;
using System.Collections.Generic;

namespace Clangen.Model
{
    class FieldModel : EntityModel
    {
        public TypeModel Type { get; }
        public string Namespace { get; }
        public string FullName { get; }

        public string Getter { get; } = null;
        public string Setter { get; } = null;
        public bool GetterExists { get; set; } = false;
        public bool SetterExists { get; set; } = false;
        public bool IsAdditional { get; set; } = false;
        public ClassModel Parent { get; }
        public CppField FieldDecl;

        public FieldModel(CppField decl, ClassModel parent, string Namespace = null)
            : base(decl)
        {
            Name = decl.Name;
            CEMeta = new CEMetaManager(decl.MetaAttributes);

            FieldDecl = decl;
            Parent = parent;
            Type = new TypeModel(FieldDecl.Type);
            if (parent != null)
            {
                this.Namespace = parent.Namespace;
                FullName = parent.FullName + "::" + Name;
            }
            else
            {
                this.Namespace = Namespace ?? "";
                FullName = string.IsNullOrEmpty(Namespace) ? Name : Namespace + "::" + Name;
            }

            var pureName = Name.StartsWith('m') ? Name.Substring(1) : Name;
            bool hasGetter = CEMeta.HasKey("Getter");
            bool hasSetter = CEMeta.HasKey("Setter");

            if (hasGetter || !hasSetter)
            {
                Getter = CEMeta.GetFirstValueAsStringWithDefault("Getter", $"Get{pureName}");
            }

            if (hasSetter || !hasGetter)
            {
                Setter = CEMeta.GetFirstValueAsStringWithDefault("Setter", $"Set{pureName}");
            }
        }
        public bool Equals(FieldModel other)
        {
            bool parentEquals = Parent == other.Parent || (Parent != null && other.Parent != null && Parent.Equals(other.Parent));
            // 实现逻辑，比如比较某些关键属性
            return other != null && Declaration == other.Declaration && parentEquals;
        }

        public override bool Equals(object obj) => Equals(obj as FieldModel);
        public override int GetHashCode()
        {
            // 返回一个适当的哈希码
            return Declaration.GetHashCode();
        }
        public string ToTsField()
        {
            return Type.ToTypeScriptName();
        }

        public bool IsStatic => FieldDecl.StorageQualifier == CppStorageQualifier.Static;

        //public bool IsPublic => FieldDecl.Visibility == CppVisibility.Public;
        public bool IsWritable => CEMeta.HasKey("Script") || CEMeta.HasKey("ScriptReadWrite");
        public bool IsReadable => CEMeta.HasKey("Script") || CEMeta.HasKey("ScriptReadWrite") || CEMeta.HasKey("ScriptReadOnly");

        public override bool NeedSerialization => (CEMeta.HasKey("Serialize") || (CEMeta.IsCeProperty && !CEMeta.HasKey("SkipSerialization"))) && !IsPrivate;
        //public override bool NeedReflection => CEMeta.HasKey("Reflect") || (CEMeta.IsCeProperty && !CEMeta.HasKey("SkipSerialization"));
        //public override bool NeedBindingEditor => IsPublic && CEMeta.HasKey("Editor");

        public override bool NeedBindingScript
        {
            get
            {
                bool bNeedBindingScript = CEMeta.HasKey("Script") || CEMeta.HasKey("ScriptReadOnly") || CEMeta.HasKey("ScriptReadWrite");
                if (Parent != null && Parent.IsGameplay)
                {
                    return bNeedBindingScript;
                }
                else
                {
                    return IsPublic && bNeedBindingScript;
                }
            }
        }
        public override bool NeedBindingTs
        {
            get
            {
                return NeedBindingScript;
            }
        }
        public override string ToString()
        {
            return $"Field {Type} {Name}";
        }

        public string Declaration => $"{Type.FullName} {Name};";

        private List<string> _csAttributes;
        public List<string> CsAttributes
        {
            get
            {
                if (_csAttributes == null)
                {
                    _csAttributes = new List<string>();
                    foreach (var t in CEMeta.GetValue("CsAttribute"))
                    {
                        var s = t.ToString();
                        _csAttributes.Add(s.Substring(1, s.Length - 2));
                    }
                    foreach (var t in CEMeta.GetValue("EditorPropertyInfo"))
                    {
                        var s = StringUtil.CEscape(CEMetaManager.CEMetaObjectToString(t));
                        _csAttributes.Add("PropertyInfo(" + s + ")");
                    }
                }
                return _csAttributes;
            }
        }

        public string PropertyDeclaration
        {
            get
            {
                string ret = $"{Type} {Name}";
                ret += " { ";
                if (Getter != null) ret += $" Getter = {Getter};";
                if (Setter != null) ret += $" Setter = {Setter};";
                ret += " } ";
                return ret;
            }
        }
    }
}