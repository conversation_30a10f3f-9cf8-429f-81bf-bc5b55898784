#include "workflow_editor.h"

#include "editor/crude_logger.h"

#include "CrossBase/String/StringCodec.h"

#include "CrossBase/Serialization/SerializeNode.h"
#include "reflection/meta/meta_class.hpp"
#include "reflection/meta/user_property.hpp"
#include "base/core/utils/byte_buffer.h"

#include "visual/blueprint/details/blueprint_workspace.h"
#include "visual/blueprint/details/blueprint_graph_group.h"
#include "visual/blueprint/details/blueprint_graph.h"
#include "visual/blueprint/details/blueprint_graph_function.h"
#include "visual/blueprint/details/blueprint_named_variable.h"
#include "visual/blueprint/details/blueprint_event.h"
#include "visual/blueprint/details/node/event/blueprint_event_on_enter_function.h"
#include "visual/blueprint/details/node/action/blueprint_function_return.h"

#include "visual/blueprint/serialize_impl/jsonvmstreamreader.h"
#include "Resource/WorkflowGraphResource.h"
#include "WorkflowPreview.h"
#include <fstream>
#include <streambuf>

#include "GameEngine.h"
#include "Input/blueprint_inputaction_event_node.h"
#include "Input/blueprint_key_event_node.h"
#include "Runtime/GameWorld/WorldSystemG.h"
#include "ValueConverter/Converter.h"

namespace gbf { namespace logic {

    void VarInspectorHelper_BasicType::SyncFromRuntime()
    {
        auto bp_var = mGraphGroupProvider()->GetNamedVariableByName(mVarName);
        const auto& val = bp_var->get_default_value();
        Value = bp_var->get_value_type() == reflection::ValueKind::kString ? reflection::value_ref_as<std::string>(val) : 
        machine::VValueUtil::ValueToJsonStr(val);
    }

    void VarInspectorHelper_BasicType::UpdateToRuntime()
    {
        auto bp_var = mGraphGroupProvider()->GetNamedVariableByName(mVarName);
        gbf::machine::VValue machine_value = bp_var->get_value_type() == reflection::ValueKind::kString ? reflection::make_value(Value) : 
            gbf::machine::VValueUtil::CreateVValueFromJsonString(Value);
        bp_var->set_default_value(machine_value);
    }

}}

namespace cross {

std::vector<std::string> cross::WorkflowEditor::ClassNames;
WorkflowEditor::WorkflowEditor(WorkflowEditorCallback* callback)
    : EditorImGuiContext{callback}
    , m_WorkflowGraph(*this, callback)
    , m_TransactionManager(*this, callback)
    , m_NeedUpdateNodeState(false)
    , m_WorkflowNodeRegistry(WorkflowNodeRegistry::GetInstance())
{
    Init();
}

std::string UserObjectClassNameToHelperClassName(std::string_view input)
{
    std::string result(input.begin(), input.end());

    std::transform(result.begin(), result.end(), result.begin(), [](char c) { return c == ':' ? '_' : c; });

    return "gbf::logic::VarInspectorHelper_" + result;
}

void WorkflowVariableDesc::SpawnInspectorHelper()
{
    // _UserObjectClassName to helper class name
    const std::string& className = UserObjectClassNameToHelperClassName(_UserObjectClassName);
    auto metaClass = gbf::reflection::query_meta_class_by_name(className);
    if (metaClass == nullptr)
    {
        if (Type <= GbfValueFieldType::String)
        {
            metaClass = gbf::reflection::query_meta_class<gbf::logic::VarInspectorHelper_BasicType>();
        }
        else
        {
            metaClass = gbf::reflection::query_meta_class<gbf::logic::VarInspectorHelper>();
        }
    }

    if (metaClass == nullptr)
    {
        mHelperObj = {};
        return;
    }
    mHelperObj = gbf::reflection::make_user_object_by_class(metaClass);   // gbf::reflection::cxx::CreateShared(*metaClass);
    if (mHelperObj.IsEmpty())
    {
        LOG_ERROR("new object failed, create user object failed {}", metaClass->name());
    }
    auto helper = static_cast<gbf::logic::VarInspectorHelper*>(mHelperObj.GetPointer());
    if (helper)
    {
        helper->Init(mGraphGroupProvider, Name);
    }
}

gbf::logic::VarInspectorHelper* WorkflowVariableDesc::GetInspectorHelper()
{
    gbf::logic::VarInspectorHelper* ret = static_cast<gbf::logic::VarInspectorHelper*>(mHelperObj.GetPointer());
    if (ret == nullptr)
    {
        auto metaClass = gbf::reflection::query_meta_class<gbf::logic::VarInspectorHelper_BasicType>();
        mHelperObj = gbf::reflection::make_user_object_by_class(metaClass);   // gbf::reflection::cxx::CreateShared(*metaClass);
        ret = static_cast<gbf::logic::VarInspectorHelper*>(mHelperObj.GetPointer());
    }
    ret->Init(mGraphGroupProvider, Name);
    return ret;
}

WorkflowEditor::~WorkflowEditor()
{
    ClearPreGraph();
}

void WorkflowEditor::FillImguiConfigNodeEditorConfig(ax::NodeEditor::Config& config)
{
    m_WorkflowGraph.FillImguiNodeEditorConfig(config);
}

void WorkflowEditor::OnStart(node_editor::utility::NodeBuilderBlueprint* nodeBuilder)
{
    m_NodeBuiler = nodeBuilder;
}

void WorkflowEditor::OnFrame()
{
    // if Resource::ResetVersion is different, WorkflowGraphResource is reloaded. should rebind Node shared point
    if (m_WorkflowGraphResource && m_WorkflowGraphResourceResetVersion != m_WorkflowGraphResource->GetResetVersion())
    {
        Open(m_FilePath.c_str());
    }

    m_WorkflowGraph.Tick(m_NodeBuiler);

    // UpdateNodeState in main thread
    {
        if (m_NeedUpdateNodeState)
        {
            m_NeedUpdateNodeState = false;
            DoUpdateNodesState();
        }
    }
}

void WorkflowEditor::Undo()
{
    m_TransactionManager.Undo();
}

void WorkflowEditor::Redo()
{
    m_TransactionManager.Redo();
}

void WorkflowEditor::BeginAction()
{
    m_TransactionManager.BeginAction();
}

void WorkflowEditor::EndAction()
{
    m_TransactionManager.EndAction();
}

WorkflowNode* WorkflowEditor::Command_CreateNode(std::string nodeName, int32_t positionX, int32_t positionY)
{
    std::shared_ptr<WorkflowNode> newNode = FindSharedPtrNode(CreateNodeInner(nodeName, positionX, positionY));

    m_TransactionManager.EmplaceTransaction<WorkflowTransactionCreateNode>(newNode);

    return newNode.get();
}

void WorkflowEditor::Command_DeleteNode(WorkflowNode* node)
{

    auto nodePtr = FindSharedPtrNode(node);
    if (nodePtr)
    {
        if (!nodePtr->CanDeleteByUser())
        {
            return;
        }
    }
    m_TransactionManager.EmplaceTransaction<WorkflowTransactionDeleteNode>(nodePtr);

    DeleteNode(nodePtr);
}

void WorkflowEditor::Command_MoveNode(WorkflowNode* node, int32_t targetPositionX, int32_t targetPositionY)
{
    m_TransactionManager.EmplaceTransaction<WorkflowTransactionMoveNode>(node, targetPositionX, targetPositionY);

    MoveNode(node, targetPositionX, targetPositionY);
}

void WorkflowEditor::Command_CreateLink(WorkflowSlot* output, WorkflowSlot* input)
{
    m_TransactionManager.EmplaceTransaction<WorkflowTransactionCreateLink>(output, input);

    CreateLink(output, input);
}

void WorkflowEditor::Command_BreakLink(WorkflowSlot* output, WorkflowSlot* input)
{
    m_TransactionManager.EmplaceTransaction<WorkflowTransactionBreakLink>(output, input);

    BreakLink(output, input);
}

// void WorkflowEditor::Command_SetProperty(WorkflowNode* node, std::string_view propertyName, gbf::reflection::Value value) {
//   m_TransactionManager.EmplaceTransaction<TransactionSetProperty>(node, propertyName, value);
//
//   SetProperty(node, propertyName, value);
// }
void WorkflowEditor::GetAvailableClasses()
{
    ClassNames.clear();
    for (size_t i = 0; i < gbf::reflection::meta_class_count(); i++)
    {
        auto classptr = gbf::reflection::meta_class_by_index(i);

        if (classptr->SearchBaseClass(gbf::reflection::query_meta_class_by_name("cegf::GameObject")->id()))
        {
            ClassNames.push_back(classptr->name());
        }
    }
}
void WorkflowEditor::CreateNode(std::string nodeName, int32_t positionX, int32_t positionY)
{
    m_WorkflowGraph.CreateNodeAtPos(nodeName, {}, positionX, positionY);
    // Command_CreateNode(nodeName, positionX, positionY);
}

WorkflowNode* WorkflowEditor::CreateNodeInner(std::string nodeName, int32_t positionX, int32_t positionY)
{
    auto newNode = m_WorkflowNodeRegistry.CreateNode(nodeName, m_WorkflowGraph.GetLogicGraph());

    m_Nodes.push_back(newNode);
    m_WorkflowGraph.AddNode(newNode.get());
    MoveNode(newNode.get(), positionX, positionY);
    return newNode.get();
}

void WorkflowEditor::AddNode(std::shared_ptr<WorkflowNode> node)
{
    m_Nodes.push_back(node);

    m_WorkflowGraph.AddNode(node.get());

    UpdateNodesState();
}

void WorkflowEditor::DeleteNode(std::shared_ptr<WorkflowNode> node)
{
    m_WorkflowGraph.DeleteNode(node.get());

    for (auto iter = m_Nodes.begin(); iter != m_Nodes.end(); iter++)
    {
        if (*iter == node)
        {
            m_Nodes.erase(iter);
            break;
        }
    }
    UpdateNodesState();
}

void WorkflowEditor::MoveNode(WorkflowNode* node, int32_t targetPositionX, int32_t targetPositionY)
{
    node->m_EditorPositionX = targetPositionX;
    node->m_EditorPositionY = targetPositionY;

    m_WorkflowGraph.UpdateNodePosition(node);
}

void WorkflowEditor::CreateLink(WorkflowSlot* output, WorkflowSlot* input)
{

    m_WorkflowGraph.CreateLink(output, input);

    UpdateNodesState();
}

void WorkflowEditor::BreakLink(WorkflowSlot* output, WorkflowSlot* input)
{

    m_WorkflowGraph.BreakLink(output, input);

    UpdateNodesState();
}

std::shared_ptr<WorkflowNode> WorkflowEditor::FindSharedPtrNode(WorkflowNode* node)
{
    for (auto& one_node : m_Nodes)
    {
        if (one_node.get() == node)
        {
            return one_node;
        }
    }
    return nullptr;
}

std::vector<WorkflowSlot*> WorkflowEditor::GetLinkedInputPins(WorkflowSlot* output) const
{
    return output->m_LinkedSlots;
}

void WorkflowEditor::SelectNodes(std::vector<WorkflowNode*> nodes)
{
    m_SelectedNodes = nodes;
}
void WorkflowEditor::Init()
{
    auto& EventLib = GlobalBlueprintEventLibrary::GetInstance();
    /*
     *
    temp_LogicGraph->CreateBpEvent("BeginPlay");
    temp_LogicGraph->CreateBpEvent("EndPlay");

    {
        auto tick_event = temp_LogicGraph->CreateBpEvent("Tick");
        tick_event->AddParamInfo("delta", "delta time from last time", gbf::machine::VValueKind::kReal, nullptr);
    }
    {
        auto tick_event = temp_LogicGraph->CreateBpEvent("KeyPressed");
        tick_event->AddParamInfo("Key", "key info", gbf::machine::VValueKind::kUser, gbf::reflection::query_meta_class_by_name("cross::input::CEKey"));
        tick_event->set_related_node_class(gbf::reflection::query_meta_class<cegf::BlueprintKeyPressedEventNode>());
    }
    {
        auto tick_event = temp_LogicGraph->CreateBpEvent("KeyReleased");
        tick_event->AddParamInfo("Key", "key info", gbf::machine::VValueKind::kUser, gbf::reflection::query_meta_class_by_name("cross::input::CEKey"));
        tick_event->set_related_node_class(gbf::reflection::query_meta_class<cegf::BlueprintKeyReleasedEventNode>());
    }
     */
    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "ConstructionScript";
        EventLib.AddEvent(std::move(desc));
    }
    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "BeginPlay";
        EventLib.AddEvent(std::move(desc));
    }
    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "EndPlay";
        EventLib.AddEvent(std::move(desc));
    }
    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "Tick";
        desc.param_list.push_back({
            "delta",
            gbf::machine::VValueKind::kReal,
            nullptr ,"delta time from last time"
        });
        EventLib.AddEvent(std::move(desc));
    }
    
    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "KeyPressed";
        desc.related_node_class = gbf::reflection::query_meta_class<cegf::BlueprintKeyPressedEventNode>();
        desc.param_list.push_back({
            "Key",
            gbf::machine::VValueKind::kUser,
            gbf::reflection::query_meta_class<cross::input::CEKey>(),
            "Key Info"
        });
        EventLib.AddEvent(std::move(desc));
    }
    
    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "KeyReleased";
        desc.related_node_class = gbf::reflection::query_meta_class<cegf::BlueprintKeyReleasedEventNode>();
        desc.param_list.push_back({
            "Key",
            gbf::machine::VValueKind::kUser,
            gbf::reflection::query_meta_class<cross::input::CEKey>(),
            "Key Info"
        });
        EventLib.AddEvent(std::move(desc));
    }

    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "KeyAnalog";
        desc.related_node_class = gbf::reflection::query_meta_class<cegf::BlueprintKeyAnalogEventNode>();
        desc.param_list.push_back({
            "Key",
            gbf::machine::VValueKind::kUser,
            gbf::reflection::query_meta_class<cross::input::CEKey>(),
            "Key Info"
        });
        desc.param_list.push_back({"X", gbf::machine::VValueKind::kReal, nullptr, "Axis X Value"});
        desc.param_list.push_back({"Y", gbf::machine::VValueKind::kReal, nullptr, "Axis Y Value"});
        desc.param_list.push_back({"Z", gbf::machine::VValueKind::kReal, nullptr, "Axis Z Value"});
        EventLib.AddEvent(std::move(desc));
    }

    {
        GlobalBlueprintEventLibrary::EventDesc desc;
        desc.event_name = "InputAction";
        desc.related_node_class = gbf::reflection::query_meta_class<cegf::BlueprintInputActionEventNode>();
        //desc.param_list.push_back({"InputAction", gbf::machine::VValueKind::kUser, gbf::reflection::query_meta_class<cross::input::CEKey>(), "Key Info"});
        desc.param_list.push_back({"Action Value X", gbf::machine::VValueKind::kReal, nullptr, "Axis X Value"});
        desc.param_list.push_back({"Action Value Y", gbf::machine::VValueKind::kReal, nullptr, "Axis Y Value"});
        desc.param_list.push_back({"Action Value Z", gbf::machine::VValueKind::kReal, nullptr, "Axis Z Value"});
        EventLib.AddEvent(std::move(desc));
    }
}

void WorkflowEditor::ClearPreGraph()
{
    // 切换graph时 先重置为nullptr 这样删除nodes的时候就可以避免logic graph被影响
    m_WorkflowGraph.Reset(nullptr);
    for (auto& one_node : m_Nodes)
    {
        // 然后断开所有的连接
        one_node->BreakUpLinks();
    }
    for (auto nodePtr : m_Nodes)
    {
        m_WorkflowGraph.DeleteNode(nodePtr.get());
    }
    m_Nodes.clear();
}
std::string WorkflowEditor::GetCurrentClassName() 
{
    return m_AgentClassName;
}
void WorkflowEditor::SetWorkflowPreview(WorkflowPreview* preview)
{
    mPreview = preview;
    mPreview->CreateClassObjectWithWork(m_AgentClassName.c_str());
}
void WorkflowEditor::Open(const char* filePath)
{
    ClearPreGraph();
    m_FilePath = filePath;
    m_WorkflowGraphResource = TypeCast<resource::WorkflowGraphResource>(gAssetStreamingManager->LoadSynchronously(m_FilePath));
    m_WorkflowGraphResourceResetVersion = m_WorkflowGraphResource->GetResetVersion(); 
    m_AgentClassName = m_WorkflowGraphResource->GetSuperClassName();
    m_WorkflowNodeRegistry.ResetAgent(m_AgentClassName);

    auto logicGroup = m_WorkflowGraphResource->GetBlueprint();
    m_Functions.clear();
    for (std::uint32_t i = 0; i < logicGroup->GetFunctionCount(); i++)
    {
        const auto one_func = logicGroup->GetFunctionByIndex(i);
        m_WorkflowNodeRegistry.AddBlueprintFunction(one_func->name(), one_func->description());
        WorkflowFunctionDesc one_func_desc;
        one_func_desc.Name = one_func->name();
        one_func_desc.Description = one_func->description();
        for (const auto one_slot : one_func->get_func_slots_array())
        {
            WorkflowArgDesc one_var_desc;
            one_var_desc.Name = one_slot->name();
            one_var_desc.Type = GbfValueFieldType(int(one_slot->variable_type()));
            if (one_var_desc.Type == GbfValueFieldType::UserObject)
            {
                one_var_desc.Value = one_slot->variable_class()->name();
            }

            if (one_slot->func_slot_type() == gbf::logic::BlueprintFunctionSlotType::Input)
            {
                one_func_desc.Args.push_back(one_var_desc);
            }
            else
            {
                one_func_desc.Returns.push_back(one_var_desc);
            }
        }
        m_Functions[one_func_desc.Name] = one_func_desc;
    }

    m_NamedVariables.clear();
    for (std::uint32_t i = 0; i < logicGroup->GetVariableCount(); i++)
    {
        const auto one_var = logicGroup->GetNamedVariableByIndex(i);
        m_WorkflowNodeRegistry.AddNamedVar(one_var->get_name());
        WorkflowVariableDesc one_var_desc;
        one_var_desc.Name = one_var->get_name();
        one_var_desc.Type = GbfValueFieldType(int(one_var->get_value_type()));
        if (one_var_desc.Type == GbfValueFieldType::UserObject)
        {
            one_var_desc.Value = "0";
                //one_var->get_default_value().ConstRef<std::string>();
            auto meta_class = gbf::reflection::query_meta_class_by_id(one_var->get_type_id());
            if (meta_class)
            {
                one_var_desc.SetUserObjectClassName(meta_class->alias_count() > 0 ?  meta_class->alias() : meta_class->name());
            }
        }
        else if (one_var_desc.Type == GbfValueFieldType::String)
        {
            one_var_desc.Value = one_var->get_default_value().ConstRef<std::string>();
        }
        else
        {
            one_var_desc.Value = gbf::machine::VValueUtil::ValueToJsonStr(one_var->get_default_value());
        }
        one_var_desc.Description = one_var->m_desciption;
        one_var_desc.mGraphGroupProvider = [this]() { return this->m_WorkflowGraphResource->GetBlueprint(); };
        m_NamedVariables[one_var_desc.Name] = one_var_desc;
    }

    m_Graphs.clear();
    for (std::uint32_t i = 0; i < logicGroup->GetGraphCount(); i++)
    {
        const auto one_graph = logicGroup->GetGraphByIndex(i);
        WorkflowGraphDesc one_graph_desc;
        one_graph_desc.Name = one_graph->name();
        one_graph_desc.Description = one_graph->description();
        m_Graphs[one_graph_desc.Name] = one_graph_desc;
    }

    m_Events.clear();
    for (std::uint32_t i = 0; i < logicGroup->GetEventCount(); i++)
    {
        // Local event And added global event
        const auto one_event = logicGroup->GetEventByIndex(i);
        WorkflowEventDesc one_event_desc;
        one_event_desc.Name = one_event->get_event_name();
        one_event_desc.Description = one_event->description();
        for (const auto one_param : one_event->GetParamArray())
        {
            WorkflowArgDesc one_var_desc;
            one_var_desc.Name = one_param.name;
            one_var_desc.Type = GbfValueFieldType(int(one_param.var_type));
            if (one_var_desc.Type == GbfValueFieldType::UserObject)
            {
                one_var_desc.Value = one_param.var_class->name();
            }
            one_event_desc.Args.push_back(one_var_desc);
        }
        m_Events[one_event_desc.Name] = one_event_desc;
        const GlobalBlueprintEventLibrary::EventDesc* event_desc = nullptr;
        if (GlobalBlueprintEventLibrary::GetInstance().HasEvent(one_event_desc.Name))
        {
            event_desc = &GlobalBlueprintEventLibrary::GetInstance().GetEventDesc(one_event_desc.Name);
        }
        m_WorkflowNodeRegistry.AddEvent(one_event_desc.Name, one_event_desc.Description, event_desc);
    }
    SwitchToGraphByIndex(0);
}

void WorkflowEditor::New(const char* filePath, const char* agentClassName)
{
    Apply();
    CreateAndSave(filePath, agentClassName);
    Open(filePath);
}

void WorkflowEditor::SwitchGraph(WorkflowGraphType GraphType, const std::string& GraphName)
{
    auto logicGroup = m_WorkflowGraphResource->GetBlueprint();
    gbf::logic::UBpGraphBase* new_graph = nullptr;
    if (GraphType == WorkflowGraphType::None)
    {
        new_graph = nullptr;
    }
    else if (GraphType == WorkflowGraphType::Sequence)
    {
        new_graph = logicGroup->GetGraphByName(GraphName);
    }
    else if (GraphType == WorkflowGraphType::Function)
    {
        new_graph = logicGroup->GetFunctionByName(GraphName);
    }
    SwitchToGraphByPtr(new_graph);
    

}

void WorkflowEditor::SwitchToGraphByPtr(gbf::logic::UBpGraphBase* new_graph)
{
    if (!new_graph)
    {
        ClearPreGraph();
    }
    else
    {
        if (new_graph == m_WorkflowGraph.GetLogicGraph())
        {
            return;
        }
        
        ClearPreGraph();

        m_WorkflowGraph.Reset(new_graph);

        for (std::uint32_t i = 0; i < new_graph->GetNodesCount(); i++)
        {
            auto cur_logic_node = new_graph->GetNodeByIndex(i);
            auto temp_node = gbf::reflection::make_shared_with_rtti<WorkflowNode>();
            temp_node->Init(cur_logic_node);
            m_Nodes.push_back(temp_node);
            m_WorkflowGraph.AddNode(temp_node.get());
        }
        // link pins
        m_WorkflowGraph.ReconstructLinks();

        m_TransactionManager.Clear();
        if (!m_Nodes.empty())
        {
            auto cur_min_id = 0xffffffff;
            for (const auto& one_node : m_Nodes)
            {
                cur_min_id = std::min(cur_min_id, one_node->GraphNode()->Id());
            }
            m_FocusNodeId = cur_min_id;
        }
        
    }
    

    
}

void WorkflowEditor::SwitchToGraphByIndex(std::uint32_t newGraphIndex)
{
    auto dest_graph = m_WorkflowGraphResource->GetBlueprint()->GetGraphByIndex(newGraphIndex);
    SwitchToGraphByPtr(dest_graph);
}
struct InternalNodeLink
{
    int32_t outputNode;
    int32_t outputSlot;
    int32_t inputNode;
    int32_t inputSlot;
    bool isDataLink;
    void Serialize(cross::SerializeNode& node)
    {
        node["InputNode"] = inputNode;
        node["InputSlot"] = inputSlot;
        node["OutputNode"] = outputNode;
        node["OutputSlot"] = outputSlot;
        node["IsDataLink"] = isDataLink;
    }

    void Deserialize(const cross::DeserializeNode& node)
    {
        outputNode = node["OutputNode"].AsInt32();
        outputSlot = node["OutputSlot"].AsInt32();
        inputNode = node["InputNode"].AsInt32();
        inputSlot = node["InputSlot"].AsInt32();
        isDataLink = node["IsDataLink"].AsBoolean();
    }
};

cross::SerializeNode WorkflowEditor::SerializeNodes(std::vector<WorkflowNode*> nodes)
{
    cross::SerializeNode tempNodeArray;
    std::unordered_map<int32_t, int32_t> tempIndexById;
    for (std::uint32_t i = 0; i < nodes.size(); i++)
    {
        auto one_node = nodes[i];
        cross::SerializeNode temp_node;
        one_node->Serialize(temp_node);
        temp_node["id"] = 0;   // override id to 0 let the bpgraphbase to auto allocate new id
        tempNodeArray.PushBack(std::move(temp_node));
        tempIndexById[one_node->GetLogicNodeId()] = tempNodeArray.Size() - 1;
    }
    cross::SerializeNode tempLinksArray;

    for (std::uint32_t i = 0; i < nodes.size(); i++)
    {
        auto one_node = nodes[i];
        auto totalInputPins = one_node->GetInputDataPins();
        totalInputPins.insert(totalInputPins.end(), one_node->GetInExecPins().begin(), one_node->GetInExecPins().end());
        for (auto oneInputPin : totalInputPins)
        {
            for (auto curLinkedOutputPin : oneInputPin->m_LinkedSlots)
            {
                auto curLinkedNode = WorkflowGraph::Cast(curLinkedOutputPin->m_GraphPin->m_Node)->m_WorkflowNode;
                auto tempIndexIter = tempIndexById.find(curLinkedNode->GetLogicNodeId());
                if (tempIndexIter == tempIndexById.end())
                {
                    continue;
                }
                InternalNodeLink tempInternalLink;
                tempInternalLink.inputNode = i;
                tempInternalLink.inputSlot = oneInputPin->m_SlotIndex;
                tempInternalLink.outputNode = tempIndexIter->second;
                tempInternalLink.outputSlot = curLinkedOutputPin->m_SlotIndex;
                tempInternalLink.isDataLink = oneInputPin->m_IsDataSlot;
                cross::SerializeNode tempLinkNode;
                tempInternalLink.Serialize(tempLinkNode);
                tempLinksArray.PushBack(std::move(tempLinkNode));
            }
        }
    }

    cross::SerializeNode resultNode;
    // format wrt UBpGraphBase::DeserializeNode
    resultNode["node_list"] = std::move(tempNodeArray);
    resultNode["link_list"] = std::move(tempLinksArray);
    return std::move(resultNode);
}

std::vector<WorkflowNode*> WorkflowEditor::DeserializeNodes(const cross::DeserializeNode& node, bool isPaste)
{
    // TODO need json format fix
    // auto temp_str = node.FormatToJson();
    std::string temp_str = node.FormatToJson();
    gbf::ByteBuffer temp_buffer(temp_str);
    gbf::logic::JsonVMStreamReader temp_reader;
    auto* dom = temp_reader.LoadFromMemory(temp_buffer);
    auto new_logic_nodes = m_WorkflowGraph.GetLogicGraph()->DeserializeNode(*dom);
    if (new_logic_nodes.empty())
    {
        return {};
    }
    std::vector<WorkflowNode*> new_workflow_nodes;
    new_workflow_nodes.reserve(new_logic_nodes.size());
    for (auto one_logic_node : new_logic_nodes)
    {
        auto temp_workflow_node = gbf::reflection::make_shared_with_rtti<WorkflowNode>();
        m_Nodes.push_back(temp_workflow_node);
        temp_workflow_node->Init(one_logic_node);

        m_WorkflowGraph.AddNode(temp_workflow_node.get());
        new_workflow_nodes.push_back(temp_workflow_node.get());
    }
    
    // Reconstruct Links
    if (!node.HasMember("link_list"))
    {
        return new_workflow_nodes;
    }

    auto link_list_json_node = node["link_list"];
    for (std::uint32_t i = 0; i < link_list_json_node.Size(); i++)
    {
        InternalNodeLink temp_link;
        temp_link.Deserialize(link_list_json_node[i]);

        if (temp_link.isDataLink)
        {
            CreateLink(new_workflow_nodes[temp_link.outputNode]->GetOutputDataPins()[temp_link.outputSlot], new_workflow_nodes[temp_link.inputNode]->GetInputDataPins()[temp_link.inputSlot]);
        }
        else
        {
            CreateLink(new_workflow_nodes[temp_link.outputNode]->GetOutExecPins()[temp_link.outputSlot], new_workflow_nodes[temp_link.inputNode]->GetInExecPins()[temp_link.inputSlot]);
        }
    }
    return new_workflow_nodes;
}

void WorkflowEditor::Reinstance()
{
    if (m_WorkflowGraphResource)
    {
        cross::WorldSystemG* worldSys = cross::EngineGlobal::GetEngine()->GetGlobalSystem<cross::WorldSystemG>();
        const auto& allWorld = worldSys->GetWorldsList();
        for (const auto& world : allWorld)
        {
            if(world->GetWorldType() == WorldTypeTag::PIEWorld)
            {
                continue;
            }
            UInt32 id = world->GetRuntimeID();
            cegf::GameWorld* gameWorld = gGameEngine->GetGameWorld(id);
            gameWorld->TraverseGameObjects([=](cegf::GameObjectPtr gameObj) {
                if(auto* comp = gameObj->GetComponent<cegf::WorkFlowComponent>();comp)
                {
                   if(comp->GetWorkflowRes() == m_WorkflowGraphResource->GetGuid_Str())
                   {
                       comp->InitByWorkFlowResource(cross::TypeCast<cross::Resource>(m_WorkflowGraphResource));

                       gameObj->RunConstructionScript();
                   }
                }
            });
        }
    }
}
void WorkflowEditor::Apply()
{
    using namespace cross;
    using namespace cross::resource;
    if (m_WorkflowGraphResource)
    {
        m_WorkflowGraphResource->Serialize(m_FilePath);
        Reinstance();
    }
}

// void WorkflowEditor::SetProperty(WorkflowNode* node, std::string_view propertyName, gbf::reflection::Value value) {
//   const gbf::reflection::Property* propertyInfo = nullptr;
//   bool hasProperty = node->__rtti_meta()->TryGetProperty(propertyName, propertyInfo);
//   assert(hasProperty);
//   propertyInfo->Set(make_user_object(node, gbf::reflection::remote_storage_policy{}), value);
// }

void WorkflowEditor::UpdateNodesState()
{
    m_NeedUpdateNodeState = true;
}

void WorkflowEditor::CopyNodesSelected()
{
    std::vector<WorkflowNode*> filteredNode;

    for (auto* node : m_SelectedNodes)
    {

        filteredNode.push_back(node);
    }

    m_CopyNodesString = SerializeNodes(filteredNode).FormatToJson();
}

void WorkflowEditor::Command_PasteNodes(int32_t targetX, int32_t targetY)
{
    auto json_node = cross::DeserializeNode::ParseFromJson(m_CopyNodesString);
    auto nodes = DeserializeNodes(json_node, true);

    int32_t minX = INT_MAX, minY = INT_MAX;
    int32_t maxX = INT_MIN, maxY = INT_MIN;
    for (auto* one_node : nodes)
    {
        minX = std::min(minX, one_node->m_EditorPositionX);
        minY = std::min(minY, one_node->m_EditorPositionY);
        maxX = std::max(maxX, one_node->m_EditorPositionX);
        maxY = std::max(maxY, one_node->m_EditorPositionY);
    }

    int32_t offsetX = targetX - (minX + maxX) / 2;
    int32_t offsetY = targetY - (minY + maxY) / 2;

    for (auto* one_node : nodes)
    {
        one_node->m_EditorPositionX += offsetX;
        one_node->m_EditorPositionY += offsetY;

        m_WorkflowGraph.UpdateNodePosition(one_node);
    }

    std::vector<std::shared_ptr<WorkflowNode>> nodePtrs(nodes.size());
    std::transform(nodes.begin(), nodes.end(), nodePtrs.begin(), [this](WorkflowNode* node) { return FindSharedPtrNode(node); });

    m_TransactionManager.EmplaceTransaction<WorkflowTransactionPasteNodes>(nodePtrs);
}

void WorkflowEditor::DoUpdateNodesState()
{
    //assert(false);
    // TODO save to temp file
}

void WorkflowEditor::OnNodeEditorFieldsChange(WorkflowNode* node, const std::string& fieldName)
{
    node->OnEditorFieldChange(fieldName);
    if (node->ShouldRefreshSlots())
    {
        auto cur_logic_node = node->LogicNode().get();
        m_WorkflowGraph.BreakUpLinksForNode(cur_logic_node);
        m_WorkflowGraph.RefreshNodeSlots(cur_logic_node);
    }
    UpdateNodesState();
}

void WorkflowVariableDesc::SetName(const std::string& new_name)
{
    Name = new_name;
}

const std::string& WorkflowVariableDesc::GetName()
{
    return Name;
}

void WorkflowFunctionDesc::SetName(const std::string& new_name)
{
    Name = new_name;
}

const std::string& WorkflowFunctionDesc::GetName()
{
    return Name;
}

void WorkflowEventDesc::SetName(const std::string& new_name)
{
    Name = new_name;
}

const std::string& WorkflowEventDesc::GetName()
{
    return Name;
}

void WorkflowGraphDesc::SetName(const std::string& new_name)
{
    Name = new_name;
}

const std::string& WorkflowGraphDesc::GetName()
{
    return Name;
}

void WorkflowEditor::OnNameVariableChange(const std::string& itemName, const std::string& OtherItemName, VariableChangedType type)
{
    auto logicGroup = m_WorkflowGraphResource->GetBlueprint();
    auto blueprint_named_variable = logicGroup->GetNamedVariableByName(itemName);

    if (!OtherItemName.empty())
    {
        Assert(type == VariableChangedType::Rename);
        // rename
        if (!blueprint_named_variable)
        {
            return;
        }
        auto editor_var_itr = m_NamedVariables.find(OtherItemName);
        if (editor_var_itr == m_NamedVariables.end())
        {
            return;
        }
        m_WorkflowNodeRegistry.DeleteNamedVar(itemName);
        m_WorkflowNodeRegistry.AddNamedVar(OtherItemName);
        blueprint_named_variable->set_name(OtherItemName);
        m_WorkflowGraph.OnVariableRenamed(itemName, OtherItemName);
    }
    else
    {
        auto editor_var_itr = m_NamedVariables.find(itemName);
        if (editor_var_itr == m_NamedVariables.end())
        {
            Assert(type == VariableChangedType::Delete);
            // delete
            logicGroup->DestroyNamedVariable(itemName);
            m_WorkflowNodeRegistry.DeleteNamedVar(itemName);
            m_WorkflowGraph.OnVariableDeleted(itemName);
            return;
        }

        if (blueprint_named_variable)
        {
            // change
            blueprint_named_variable->m_desciption = editor_var_itr->second.Description;

            if (type == VariableChangedType ::TypeChanged)
            {
                gbf::machine::VValue new_val;
                // if change type, construct a default type value
                if ((gbf::machine::VValueKind)editor_var_itr->second.Type != blueprint_named_variable->get_value_type())
                {
                    editor_var_itr->second.Value = "0";
                    Assert(type == VariableChangedType::TypeChanged);
                }
                if (editor_var_itr->second.Type == GbfValueFieldType::String || editor_var_itr->second.Type == GbfValueFieldType::UserObject)
                {
                    new_val = gbf::reflection::make_value(editor_var_itr->second.Value);
                }
                else
                {
                    new_val = gbf::machine::VValueUtil::CreateVValueFromJsonString(editor_var_itr->second.Value);
                }

                blueprint_named_variable->set_value_with_type(new_val, (gbf::machine::VValueKind)editor_var_itr->second.Type);
                blueprint_named_variable->set_var_category((gbf::logic::VariableCategory)editor_var_itr->second.Category);

                WorkflowVariableType type_struct;
                type_struct.FieldType = editor_var_itr->second.Type;
                type_struct.AdditionalTypeId = UINT64_MAX;
                if (editor_var_itr->second.Type == GbfValueFieldType::UserObject && !editor_var_itr->second._UserObjectClassName.empty())
                {
                    auto meta_class = gbf::reflection::query_meta_class_by_name(editor_var_itr->second._UserObjectClassName);
                    if (meta_class == nullptr)
                    {
                        Assert(false);
                        LOG_ERROR("when set variable, cann't find meta class for \"{}\".", editor_var_itr->second._UserObjectClassName);
                    }
                    if (meta_class == gbf::reflection::query_meta_class<cross::Float4>())
                    {
                        meta_class = gbf::reflection::query_meta_class<cross::MaterialPtr>();
                        blueprint_named_variable->set_type_id_for_user_obj(meta_class->id());
                        type_struct.AdditionalTypeId = meta_class->id();
                    }
                    else
                    {
                        blueprint_named_variable->set_type_id_for_user_obj(meta_class->id());
                        type_struct.AdditionalTypeId = meta_class->id();
                    }
                }

                m_WorkflowGraph.OnVariableTypeChanged(itemName, type_struct);
            }
        }
        else
        {
            Assert(type == VariableChangedType::Add);

            // add
            auto new_varibale = logicGroup->CreateNamedVariable(itemName, (gbf::machine::VValueKind)editor_var_itr->second.Type);
            editor_var_itr->second.Value = gbf::machine::VValueUtil::ValueToJsonStr(new_varibale->get_default_value());
            m_WorkflowNodeRegistry.AddNamedVar(itemName);

            m_NamedVariables[itemName].mGraphGroupProvider = [this]() { return this->m_WorkflowGraphResource->GetBlueprint(); };
            //WorkflowVariableDesc one_var_desc;
            //one_var_desc.Name = one_var->get_name();
            //one_var_desc.Type = GbfValueFieldType(int(one_var->get_value_type()));
            //if (one_var_desc.Type == GbfValueFieldType::UserObject)
            //{
            //    one_var_desc.Value = "0";
            //    // one_var->get_default_value().ConstRef<std::string>();
            //    auto meta_class = gbf::reflection::query_meta_class_by_id(one_var->get_type_id());
            //    if (meta_class)
            //    {
            //        one_var_desc.SetUserObjectClassName(meta_class->name());
            //    }
            //}
            //else if (one_var_desc.Type == GbfValueFieldType::String)
            //{
            //    one_var_desc.Value = one_var->get_default_value().ConstRef<std::string>();
            //}
            //else
            //{
            //    one_var_desc.Value = gbf::machine::VValueUtil::ValueToJsonStr(one_var->get_default_value());
            //}
            //one_var_desc.Description = one_var->m_desciption;
            //one_var_desc.mLogicGroupPtr = logicGroup;
            //m_NamedVariables[one_var_desc.Name] = one_var_desc;
        }
    }
    UpdateNodesState();
}

void WorkflowEditor::OnGraphChange(const std::string& itemName, const std::string& OtherItemName)
{
    auto logicGroup = m_WorkflowGraphResource->GetBlueprint();
    auto pre_graph = logicGroup->GetGraphByName(itemName);
    if (!OtherItemName.empty())
    {
        // rename
        if (!pre_graph)
        {
            return;
        }
        auto temp_iter = m_Graphs.find(OtherItemName);
        if (temp_iter == m_Graphs.end())
        {
            return;
        }
        pre_graph->set_name(OtherItemName);
    }
    else
    {
        auto temp_iter = m_Graphs.find(itemName);
        if (temp_iter == m_Graphs.end())
        {
            // delete
            if (pre_graph == m_WorkflowGraph.GetLogicGraph())
            {
                SwitchToGraphByPtr(nullptr);
            }
            logicGroup->DestroyGraph(itemName);
            return;
        }

        if (pre_graph)
        {
            // change
            pre_graph->set_description(temp_iter->second.Description);
        }
        else
        {
            // add
            logicGroup->CreateGraph(itemName, std::string());
        }
    }
    UpdateNodesState();
}

void WorkflowEditor::OnFunctionChange(const std::string& itemName, const std::string& OtherItemName)
{
    auto logicGroup = m_WorkflowGraphResource->GetBlueprint();
    auto pre_function = logicGroup->GetFunctionByName(itemName);
    if (!OtherItemName.empty())
    {
        // rename
        if (!pre_function)
        {
            return;
        }
        auto temp_iter = m_Functions.find(OtherItemName);
        if (temp_iter == m_Functions.end())
        {
            return;
        }
        m_WorkflowNodeRegistry.DeleteBlueprintFunction(itemName);
        m_WorkflowNodeRegistry.AddBlueprintFunction(OtherItemName, temp_iter->second.Description);
        pre_function->set_name(OtherItemName);
    }
    else
    {
        
        auto temp_iter = m_Functions.find(itemName);
        if (temp_iter == m_Functions.end())
        {
            // delete
            if (pre_function == m_WorkflowGraph.GetLogicGraph())
            {
                SwitchToGraphByPtr(nullptr);
            }
            logicGroup->DestroyFunction(itemName);
            m_WorkflowNodeRegistry.DeleteBlueprintFunction(itemName);
            return;
        }

        if (pre_function)
        {
            // change
            pre_function->set_description(temp_iter->second.Description);
            m_WorkflowNodeRegistry.AddBlueprintFunction(itemName, temp_iter->second.Description);
            if (m_WorkflowGraph.GetLogicGraph() == pre_function)
            {
                // 先断开所有连接
                auto cur_enter_logic_node = pre_function->GetEnterEventNode();
                auto cur_return_logic_node = pre_function->GetReturnNode();
                m_WorkflowGraph.BreakUpLinksForNode(cur_enter_logic_node);
                m_WorkflowGraph.BreakUpLinksForNode(cur_return_logic_node);
            }
            

            
            pre_function->ClearFuncSlots();
            for (const auto& one_input_arg : temp_iter->second.Args)
            {
                const gbf::reflection::MetaClass* cur_meta_class = nullptr;
                if (one_input_arg.Type == GbfValueFieldType::UserObject)
                {
                    cur_meta_class = gbf::reflection::query_meta_class_by_name(one_input_arg.Value);
                }
                pre_function->AddInputFunctionSlot(one_input_arg.Name, gbf::reflection::ValueKind(one_input_arg.Type), cur_meta_class);
            }

            for (const auto& one_output_arg : temp_iter->second.Returns)
            {
                const gbf::reflection::MetaClass* cur_meta_class = nullptr;
                if (one_output_arg.Type == GbfValueFieldType::UserObject)
                {
                    cur_meta_class = gbf::reflection::query_meta_class_by_name(one_output_arg.Value);
                }
                pre_function->AddOutputFunctionSlot(one_output_arg.Name, gbf::reflection::ValueKind(one_output_arg.Type), cur_meta_class);
            }
            
            if (m_WorkflowGraph.GetLogicGraph() == pre_function)
            {
                // 然后重新初始化slots
                auto cur_enter_logic_node = pre_function->GetEnterEventNode();
                auto cur_return_logic_node = pre_function->GetReturnNode();
                m_WorkflowGraph.RefreshNodeSlots(cur_enter_logic_node);
                m_WorkflowGraph.RefreshNodeSlots(cur_return_logic_node);
            }

        }
        else
        {
            // add
            auto new_func = logicGroup->CreateFunction(itemName, std::string());
            m_WorkflowNodeRegistry.AddBlueprintFunction(itemName, temp_iter->second.Description);
        }
    }
    UpdateNodesState();
}

void WorkflowEditor::OnEventChange(const std::string& itemName, const std::string& OtherItemName)
{
    auto logicGroup = m_WorkflowGraphResource->GetBlueprint();
    auto pre_event = logicGroup->GetEvent(itemName);
    if (!OtherItemName.empty())
    {
        // rename
        if (!pre_event)
        {
            return;
        }
        auto temp_iter = m_Events.find(OtherItemName);
        if (temp_iter == m_Events.end())
        {
            return;
        }
        m_WorkflowNodeRegistry.RenameEvent(itemName, OtherItemName, temp_iter->second.Description);
        pre_event->set_event_name(OtherItemName);
    }
    else
    {
        auto temp_iter = m_Events.find(itemName);
        if (temp_iter == m_Events.end())
        {
            // delete
            logicGroup->DeleteEvent(itemName);
            if (!GlobalBlueprintEventLibrary::GetInstance().HasEvent(itemName))
            {
                m_WorkflowNodeRegistry.DeleteEvent(itemName);
            }
            return;
        }

        if (pre_event)
        {
            // change
            pre_event->set_description(temp_iter->second.Description);
            m_WorkflowNodeRegistry.AddEvent(itemName, temp_iter->second.Description, nullptr);
            pre_event->ClearAllParam();
            for (const auto& one_input_arg : temp_iter->second.Args)
            {
                const gbf::reflection::MetaClass* cur_meta_class = nullptr;
                if (one_input_arg.Type == GbfValueFieldType::UserObject)
                {
                    cur_meta_class = gbf::reflection::query_meta_class_by_name(one_input_arg.Value);
                }
                pre_event->AddParamInfo(one_input_arg.Name, one_input_arg.Description, gbf::reflection::ValueKind(one_input_arg.Type), cur_meta_class);
            }
        }
        else
        {
            // add
            auto new_event = logicGroup->CreateBpEvent(itemName);
            new_event->set_description(temp_iter->second.Description);
            m_WorkflowNodeRegistry.AddEvent(itemName, temp_iter->second.Description, nullptr);
        }
    }
    UpdateNodesState();
}

void WorkflowEditor::CreateAndSave(const std::string& fileName, const std::string& className)
{
    auto workflowResoucePtr = gbf::reflection::make_shared_with_rtti<cross::resource::WorkflowGraphResource>();
    workflowResoucePtr->CreateAsset(fileName);
    workflowResoucePtr->Deserialize({});
    auto blueprint = workflowResoucePtr->GetBlueprint();
    blueprint->set_default_agent(className);
    blueprint->CreateGraph("Entry", "Entry Graph");
    workflowResoucePtr->Serialize(fileName);
}

WorkflowNode* WorkflowEditor::FindNode(const gbf::logic::UBlueprintNode* in_logic_node) {
    for (auto& one_node : m_Nodes)
    {
        if (one_node->LogicNode().get() == in_logic_node)
        {
            return one_node.get();
        }
    }
    return nullptr;
}
}   // namespace cross