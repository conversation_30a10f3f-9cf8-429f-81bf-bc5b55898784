#pragma once

#include "node_editor_bridge_fwd.hpp"

#include <imgui.h>
#include <imgui_node_editor.h>
#include "crude/runtime/crude/crude_node.h"
#include "crude/runtime/crude/crude_pin.h"
#include "crude/runtime/crude/crude_node_type_info.h"
#include "crude/runtime/crude/crude_blueprint.h"

namespace node_editor::utility {

using namespace crude_blueprint;

// Show overlay while blueprint is executed presenting
// current state and execution point.
struct DebugOverlay : private ContextMonitor {
  DebugOverlay(Blueprint& blueprint);
  ~DebugOverlay();

  void Begin();
  void End();

  void DrawNode(const Node& node);
  void DrawInputPin(const Pin& pin);
  void DrawOutputPin(const Pin& pin);

 private:
  void OnEvaluatePin(const Context& context, const Pin& pin) override;

  Blueprint* m_Blueprint = nullptr;
  const Node* m_CurrentNode = nullptr;
  const Node* m_NextNode = nullptr;
  FlowPin m_CurrentFlowPin;
  ImDrawList* m_DrawList = nullptr;
  ImDrawListSplitter m_Splitter;
};

}

