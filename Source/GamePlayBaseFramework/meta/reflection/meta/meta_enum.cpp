/****************************************************************************
**
** This file is part of the <PERSON><PERSON> library, formerly CAMP.
**
** The MIT License (MIT)
**
** Copyright (C) 2009-2014 TEGESO/TEGESOFT and/or its subsidiary(-ies) and mother company.
** Copyright (C) 2015-2020 Nick Trout.
**
** Permission is hereby granted, free of charge, to any person obtaining a copy
** of this software and associated documentation files (the "Software"), to deal
** in the Software without restriction, including without limitation the rights
** to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
** copies of the Software, and to permit persons to whom the Software is
** furnished to do so, subject to the following conditions:
**
** The above copyright notice and this permission notice shall be included in
** all copies or substantial portions of the Software.
**
** THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
** IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
** FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
** AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
** LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
** OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
** THE SOFTWARE.
**
****************************************************************************/

#include "reflection/meta/meta_enum.hpp"
#include "reflection/error/errors.hpp"

namespace gbf {
namespace reflection {

MetaEnum::MetaEnum(IdRef name, TypeId typeIndex, EnumVtable const* vtbl) : enum_name_(name), type_index_(typeIndex), vtbl_(vtbl) {
  meta_kind_ = MetaTypeKind::kMetaEnum;
}

size_t MetaEnum::GetCount() const { return enums_.size(); }

MetaEnum::Pair MetaEnum::GetPair(size_t index) const {
  // Make sure that the index is not out of range
  if (index >= enums_.size()) PONDER_ERROR(OutOfRange(index, enums_.size()));

  auto iter = enums_.begin();
  for (int i = 0; i < index; i++) {
    iter++;
  }

  return Pair(iter->first, iter->second);
}

bool MetaEnum::HasName(IdRef name) const { return enums_.find(name.data()) != enums_.end(); }

bool MetaEnum::HasValue(EnumValue value) const {
  for (auto iter = enums_.begin(); iter != enums_.end(); iter++) {
    if (iter->second == value) {
      return true;
    }
  }
  return false;
}

IdReturn MetaEnum::GetItemName(EnumValue value) const {
  for (auto iter = enums_.begin(); iter != enums_.end(); iter++) {
    if (iter->second == value) {
      return iter->first;
    }
  }

  PONDER_ERROR(EnumValueNotFound(value, enum_name()));
}

MetaEnum::EnumValue MetaEnum::GetValue(IdRef name) const {
  auto it = enums_.find(name.data());

  if (it == enums_.end()) PONDER_ERROR(EnumNameNotFound(name, enum_name_));

  return it->second;
}

bool MetaEnum::operator==(const MetaEnum& other) const { return enum_name() == other.enum_name(); }

bool MetaEnum::operator!=(const MetaEnum& other) const { return enum_name() != other.enum_name(); }

}  // namespace reflection
}  // namespace gbf
