
#include "reflection/meta/enum_property.hpp"
#include "reflection/meta/meta_enum.hpp"

namespace gbf {
namespace reflection {

EnumProperty::EnumProperty(MetaClass* owner, IdRef name, const MetaEnum& propEnum)
    : Property(owner, name, ValueKind::kEnum, propEnum.type_index()), enum_(&propEnum) {}

EnumProperty::~EnumProperty() {}

const MetaEnum& EnumProperty::GetEnum() const { return *enum_; }



}  // namespace reflection
}  // namespace gbf
