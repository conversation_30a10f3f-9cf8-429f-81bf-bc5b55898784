
#pragma once

#include "reflection/meta/property.hpp"

namespace gbf {
namespace reflection {
class MetaClass;

/**
 * \brief Specialized type of property for user types.
 */
class GBF_REFLECTION_API UserProperty : public Property {
 public:
  /**
   * \brief Construct the property from its description
   *
   * \param name Name of the property
   * \param propClass Eumeration the property is bound to
   */
  UserProperty(MetaClass* owner, IdRef name, const MetaClass& propClass);

  /**
   * \brief Destructor
   */
  virtual ~UserProperty();

  /**
   * \brief Get the owner class
   *
   * \return Class the property is bound to
   */
  const MetaClass& GetClass() const;


 private:
  const MetaClass* class_;  ///< Owner class of the property
};

}  // namespace reflection
}  // namespace gbf
