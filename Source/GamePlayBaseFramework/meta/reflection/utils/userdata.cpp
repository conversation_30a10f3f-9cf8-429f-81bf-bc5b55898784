#include "reflection/utils/userdata.hpp"
#include <map>

namespace gbf {
namespace reflection {

class TypeUserDataStore : public IUserDataStore {
  using key_t = const Type*;
  using store_t = std::map<Id, Value>;
  using class_store_t = std::map<key_t, store_t>;

 public:
  void SetValue(const Type& t, IdRef name, const Value& v) final;
  const Value* GetValue(const Type& t, IdRef name) const final;
  void RemoveValue(const Type& t, IdRef name) final;

 private:
  class_store_t store_;
};

void TypeUserDataStore::SetValue(const Type& t, IdRef name, const Value& v) {
  auto it = store_.find(&t);
  if (it == store_.end()) {
    auto ret = store_.insert(class_store_t::value_type(&t, store_t()));
    it = ret.first;
  }
  it->second.insert(std::make_pair(name, v));
}

const Value* TypeUserDataStore::GetValue(const Type& t, IdRef name) const {
  auto it = store_.find(&t);
  if (it != store_.end()) {
    auto vit = it->second.find(name.data());
    if (vit != it->second.end()) return &vit->second;
  }
  return nullptr;
}

void TypeUserDataStore::RemoveValue(const Type& t, IdRef name) {
  auto it = store_.find(&t);
  if (it != store_.end()) {
    it->second.erase(name.data());
  }
}

std::unique_ptr<TypeUserDataStore> g_memberDataStore;

IUserDataStore* GetGlobalUserDataStore() {
  auto p = g_memberDataStore.get();

  if (p == nullptr) {
    g_memberDataStore = detail::make_unique<TypeUserDataStore>();
    p = g_memberDataStore.get();
  }

  return p;
}

}  // namespace reflection
}  // namespace gbf
