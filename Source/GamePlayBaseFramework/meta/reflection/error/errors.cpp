
#include "reflection/error/errors.hpp"
#include "reflection/meta/meta_class.hpp"
#include "reflection/utils/util.hpp"

namespace gbf {
namespace reflection {

BadType::BadType(ValueKind provided, ValueKind expected)
    : Error("[ponder error]value of type " + typeName(provided) + " couldn't be converted to type " +
            typeName(expected)) {}

BadType::BadType(const String& message) : Error(message) {}

reflection::String BadType::typeName(ValueKind type) { return reflection::String(detail::ValueKindAsString(type)); }

BadArgument::BadArgument(ValueKind provided, ValueKind expected, size_t index, IdRef functionName)
    : BadType("argument #" + str(index) + " of function " + String(functionName) + " couldn't be converted from type " +
              typeName(provided) + " to type " + typeName(expected)) {}

ClassAlreadyCreated::ClassAlreadyCreated(IdRef type)
    : Error("[ponder error]class named " + String(type) + " already exists") {}

ClassNotFound::ClassNotFound(IdRef name)
    : Error("[ponder error]the metaclass " + String(name) + " couldn't be found") {}

ClassUnrelated::ClassUnrelated(IdRef sourceClass, IdRef requestedClass)
    : Error("[ponder error]failed to convert from " + String(sourceClass) + " to " + String(requestedClass) +
            ": it is not a base nor a derived") {}

EnumAlreadyCreated::EnumAlreadyCreated(IdRef typeName)
    : Error("[ponder error]enum named " + String(typeName) + " already exists") {}

EnumNameNotFound::EnumNameNotFound(IdRef name, IdRef enumName)
    : Error("[ponder error]the value " + String(name) + " couldn't be found in metaenum " + String(enumName)) {}

EnumNotFound::EnumNotFound(IdRef name) : Error("[ponder error]the metaenum " + String(name) + " couldn't be found") {}

EnumValueNotFound::EnumValueNotFound(long value, IdRef enumName)
    : Error("[ponder error]the value " + str(value) + " couldn't be found in metaenum " + String(enumName)) {}

ForbiddenCall::ForbiddenCall(IdRef functionName)
    : Error("[ponder error]the function " + String(functionName) + " is not callable") {}

ForbiddenRead::ForbiddenRead(IdRef propertyName)
    : Error("[ponder error]the property " + String(propertyName) + " is not readable") {}

ForbiddenWrite::ForbiddenWrite(IdRef propertyName)
    : Error("[ponder error]the property " + String(propertyName) + " is not writable") {}

FunctionNotFound::FunctionNotFound(IdRef name, IdRef className)
    : Error("[ponder error]the function " + String(name) + " couldn't be found in metaclass " + String(className)) {}

OverloadFunctionNotFound::OverloadFunctionNotFound(IdRef name, IdRef className, size_t argcount)
    : Error("[ponder error]the overload function " + String(name) + " with argument count:" + str(argcount) +
            " couldn't be found in metaclass " + String(className)) {}

NotEnoughArguments::NotEnoughArguments(IdRef functionName, size_t provided, size_t expected)
    : Error("[ponder error]not enough arguments for calling " + String(functionName) + " - provided " + str(provided) +
            ", expected " + str(expected)) {}

NullObjectError::NullObjectError(const MetaClass* objectClass)
    : Error("[ponder error]trying to use a null metaobject of class " +
            (objectClass ? String(objectClass->name()) : String("unknown"))) {}

OutOfRange::OutOfRange(size_t index, size_t size)
    : Error("[ponder error]the index (" + str(index) + ") is out of the allowed range [0, " + str(size - 1) + "]") {}

PropertyNotFound::PropertyNotFound(IdRef name, IdRef className)
    : Error("[ponder error]the property " + String(name) + " couldn't be found in metaclass " + String(className)) {}

TypeAmbiguity::TypeAmbiguity(IdRef typeName) : Error("[ponder error]type " + String(typeName) + " ambiguity") {}

WithoutDefaultCtor::WithoutDefaultCtor(IdRef typeName) : Error("[ponder error]type " + String(typeName) + " without default ctor") {}

WithoutCopyCtor::WithoutCopyCtor(IdRef typeName) : Error("[ponder error]type " + String(typeName) + " without copy ctor") {}

RemoteStoragePolicyNotSupportCtor::RemoteStoragePolicyNotSupportCtor() : Error("[ponder error] remote storage policy not support default ctor") {}

ArrayNotDynamicUsePushBackElement::ArrayNotDynamicUsePushBackElement() : Error("[ponder error] call PushBackElement in not dynamic array!") {}

RttiObjectNotBindWithValidInfo::RttiObjectNotBindWithValidInfo(IdRef _type_name): Error("[ponder error] type " + String(_type_name) + "rtti object not bind with valid info, please create it by make_shared_with_rtti()") {
}

ArrayPropertyCanNotSupportNormalGetOrSet::ArrayPropertyCanNotSupportNormalGetOrSet():Error("[ponder error] array property can not support normal get or set, please use index version to operate.") {}

ArrayNotSupportRawPointerTryGetPointer::ArrayNotSupportRawPointerTryGetPointer(): Error("[ponder error] array not support raw pointer try get pointer.") {}

SameTypeId::SameTypeId(TypeId typeId, IdRef typeName1, IdRef typeName2)
    : Error("type " + String(typeName1) + " and " + String(typeName2) + " have the same typeId: " + str(typeId) + ".") {}
}  // namespace reflection
}  // namespace gbf
