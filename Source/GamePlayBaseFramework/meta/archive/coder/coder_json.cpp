#include "archive/coder/coder_json.h"

#include <algorithm>
#include <string>
#include <variant>
#include <vector>

#include "core/imodules/ilog_module.h"
#include "rapidjson/document.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/writer.h"
#include "archive/coder/coder_helper.h"
#include "reflection/meta/array_property.hpp"
#include "reflection/meta/property.hpp"
#include "reflection/reflection_fwd.hpp"
#include "reflection/reflection_export.hpp"
#include "reflection/runtime/cxx_runtime.hpp"
#include "reflection/type.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/make_value.hpp"

namespace gbf {
namespace reflection {

constexpr std::string_view kMetaClassFieldName = "class";
constexpr std::string_view kPropCountFieldName = "count";
constexpr std::string_view KPropMapFieldName = "property";

std::string CoderJson::CreateJsonString(const UserObject& uo, bool filter_default /* = true */) {
  std::string output;
  bool result = WriteUserObjectToJsonString(uo, output, filter_default);
  if (!result) {
    ERR_DEF("convert uo to jsonstring failed");
    return {};
  }

  return output;
}

UserObject CoderJson::ReadUserObjectFromJsonString(const std::string& json_string) {
  const char* json_cstr = json_string.c_str();

  rapidjson::Document doc;
  try {
    doc.Parse(json_cstr);
  } catch (...) {
    return {};
  }

  return ReadUserObjectFromJson(doc);
}

bool CoderJson::WriteJsonStringToUserObject(const std::string& json_string, const UserObject& uo) {
  const char* json_cstr = json_string.c_str();

  rapidjson::Document doc;
  try {
    doc.Parse(json_cstr);
  } catch (...) {
    ERR_DEF("parse json string failed, json string: %s", json_cstr);
    return false;
  }

  return FillUserObjectPropertysFromJson(doc, uo);
}

bool CoderJson::WriteUserObjectToJsonString(const UserObject& uo, std::string& json_string, bool filter_default /* = true */) {
  JsonStringBuffer string_buffer;
  JsonSimpleWriter writer(string_buffer);

  bool result = WriteUserObjectToJson(uo, writer, filter_default);
  if (!result) {
    ERR_DEF("convert userobject to doc failed");
    return false;
  }

  const char* output = string_buffer.GetString();
  json_string.assign(output, string_buffer.GetSize());
  return true;
}

bool CoderJson::WriteUserObjectToJson(const UserObject& uo, JsonSimpleWriter& writer, bool filter_default /*= true*/) {
  const MetaClass& metaClass = uo.GetClass();
  const auto& className = metaClass.name();

  writer.StartObject();

  writer.Key(kMetaClassFieldName.data());
  writer.String(className.c_str());

  std::vector<const Property*> needFields;
  if (filter_default) {
    CoderHelper::FilterFields(metaClass, uo, needFields);
  } else {
    CoderHelper::GetAllFields(metaClass, uo, needFields);
  }

  writer.Key(kPropCountFieldName.data());
  writer.Int((int)needFields.size());

  writer.Key(KPropMapFieldName.data());
  writer.StartObject();

  for (auto& needField : needFields) {
    const Property& prop = *needField;
    auto pn = prop.name();

    writer.Key(pn.c_str(), pn.length());

    const ValueKind& kind = prop.kind();
    if (kind == ValueKind::kArray) {
      WriteArrayPropToJsonValue(uo, prop, writer, filter_default);
    } else if (kind == ValueKind::kUser) {
      WriteUserPropToJsonValue(uo, prop, writer, filter_default);
    } else {
      WriteSimplePropToJsonValue(uo, prop, writer, filter_default);
    }
  }

  writer.EndObject();

  writer.EndObject();

  return true;
}

////rapidjson::Document CoderJson::CreateDocument(const UserObject& uo, bool filter_default /*= true*/) {
////  rapidjson::Document doc;
////  doc.SetObject();
////
////  WriteUserObjectToDoc(uo, doc, filter_default);
////
////  return doc;
////}

UserObject CoderJson::ReadUserObjectFromJson(const rapidjson::Value& doc) {
  if (!doc.HasMember(kMetaClassFieldName.data())) {
    ERR_DEF("class field not exist");
    return {};
  }

  const char* className = doc[kMetaClassFieldName.data()].GetString();
  const MetaClass* metaClass = query_meta_class_by_name(className);
  if (!metaClass) {
    ERR_DEF("class name: %s not found", className);
    return {};
  }

  UserObject uo = cxx::Create(*metaClass);
  bool result = FillUserObjectPropertysFromJson(doc, uo);
  if (!result) {
    ERR_DEF("convert doc to userobject failed, class name: %s", className);
    return {};
  }

  return uo;
}

bool CoderJson::WriteUserPropToJsonValue(const UserObject& uo, const Property& prop, JsonSimpleWriter& writer, bool filter_default /*= true*/) {
  const Value& value = prop.Get(uo);
  const UserObject& subUo = std::get<UserObject>(value.value_);

  return WriteUserObjectToJson(subUo, writer, filter_default);
}

bool CoderJson::WriteArrayPropToJsonValue(const UserObject& uo, const Property& prop, JsonSimpleWriter& writer, bool filter_default /*= true*/) {
  const Value& value = prop.Get(uo);
  const ArrayObject& ao = std::get<ArrayObject>(value.value_);

  return WriteArrayObjectToJsonValue(ao, writer, filter_default);
}

bool CoderJson::WriteSimplePropToJsonValue(const UserObject& uo, const Property& prop, JsonSimpleWriter& writer, bool filter_default /*= true*/) {
  const Value& value = prop.Get(uo);
  const ValueKind& kind = prop.kind();

  return WriteSimpleValueToJsonValue(value, kind, writer);
}

bool CoderJson::WriteSimpleValueToJsonValue(const Value& value, const ValueKind& kind, JsonSimpleWriter& writer) {
  switch (kind) {
    case ValueKind::kEnum: {
      //Save enum as int64_t here~~
      writer.Int64(value_cast<int64_t>(value));
    } break;
    case ValueKind::kBoolean: {
      bool tmp = value_cast<bool>(value);
      writer.Bool(tmp);
    } break;
    case ValueKind::kInteger: {
      int64_t tmp = value_cast<int64_t>(value);
      writer.Int64(tmp);
    } break;
    case ValueKind::kReal: {
      double tmp = value_cast<double>(value);
      writer.Double(tmp);
    } break;
    case ValueKind::kString: {
      std::string tmp = value_cast<std::string>(value);
      writer.String(tmp.c_str());
    } break;
    default: {
      ERR_DEF("not support type: %d", kind);
      return false;
    }
  }

  return true;
}

bool CoderJson::WriteArrayObjectToJsonValue(const ArrayObject& ao, JsonSimpleWriter& writer, bool filter_default /*= true*/) {
  const size_t arrayLen = ao.GetSize();
  const ValueKind& subKind = ao.GetElementKind();

  writer.StartArray();
  for (size_t i = 0; i < arrayLen; ++i) {
    bool ret = false;
    rapidjson::Value child;

    const Value& subValue = ao.GetElement(i);

    if (subKind == ValueKind::kUser) {
      const UserObject& subUo = std::get<UserObject>(subValue.value_);
      ret = WriteUserObjectToJson(subUo, writer, filter_default);
    } else if (subKind == ValueKind::kArray) {
      const ArrayObject& subAo = std::get<ArrayObject>(subValue.value_);
      ret = WriteArrayObjectToJsonValue(subAo, writer, filter_default);
    } else {
      ret = WriteSimpleValueToJsonValue(subValue, subValue.kind(), writer);
    }

    if (!ret) {
      WRN_DEF("write element in array object fail, index: %d, element kind: %d", i, subKind);
    }
  }
  writer.EndArray();

  return true;
}

bool CoderJson::ReadArrayPropertyFromJson(const rapidjson::Value& root, const Property& prop, const UserObject& uo) {
  if (root.GetArray().Size() <= 0) {
    return true;
  }

  auto elementType = root[0].GetType();

  if (elementType == rapidjson::kArrayType) {
    ERR_DEF("not support nested array in an array currently");
    return false;
  }

  for (auto& child : root.GetArray()) {
    if (child.GetType() != elementType) {
      ERR_DEF("elements in json array must be same type");
      return false;
    }
  }

  const ArrayProperty& arrayProp = static_cast<const ArrayProperty&>(prop);

  size_t arrayLen = root.GetArray().Size();
  const auto& impType = arrayProp.implement_type();
  const auto& subKind = arrayProp.element_type();

  if (impType == PropertyImplementType::kPbRepeatProperty) {
    const size_t cap = arrayProp.Size(uo);
    if (cap < arrayLen) {
      arrayLen = cap;
    }
  } else if (impType == PropertyImplementType::kCppArrayProperty) {
    arrayProp.ReSize(uo, arrayLen);
  }

  const MetaClass* subClass;

  if (subKind == ValueKind::kUser) {
    subClass = query_meta_class_by_id(arrayProp.element_type_index());

    if (!subClass) {
      ERR_DEF("element is object, while class is null, prop name: %s", arrayProp.name().data());
      return false;
    }
  }

  bool ret;

  for (size_t i = 0; i < root.GetArray().Size(); ++i) {
    auto& child = root[i];

    if (subKind == ValueKind::kUser) {
      const UserObject subUo = cxx::Create(*subClass);

      ret = FillUserObjectPropertysFromJson(child, subUo);
      if (ret) {
        arrayProp.Set(uo, i, make_value(subUo));
      }
    } else {
      Value value;

      ret = ReadSimpleValueFromJson(child, subKind, value);
      if (ret) {
        arrayProp.Set(uo, i, value);
      }
    }

    if (!ret) {
      WRN_DEF("parse element json value failed, index: %d, prop name: %s", i, arrayProp.name().data());
    }
  }

  return true;
}

bool CoderJson::ReadObjectPropertyFromJson(const rapidjson::Value& root, const Property& prop, const UserObject& uo) {
  const MetaClass* subClass = query_meta_class_by_id(prop.type_index());

  if (!subClass) {
    ERR_DEF("sub class is null, prop name: %s", prop.name().data());
    return false;
  }

  const UserObject subUo = cxx::Create(*subClass);

  bool ret = FillUserObjectPropertysFromJson(root, subUo);
  if (ret) {
    prop.Set(uo, make_value(subUo));
    return true;
  } else {
    ERR_DEF("write object json to user obj failed, prop name: %s", prop.name().data());
    return false;
  }
}

bool CoderJson::ReadSimplePropertyFromJson(const rapidjson::Value& root, const Property& prop, const UserObject& uo) {
  Value value;

  bool ret = ReadSimpleValueFromJson(root, prop.kind(), value);

  if (ret) {
    prop.Set(uo, value);
    return true;
  } else {
    ERR_DEF("write simple json value to value failed, prop name: %s", prop.name().data());
    return false;
  }
}

bool CoderJson::FillUserObjectPropertysFromJson(const rapidjson::Value& root, const UserObject& uo) {
  const MetaClass& metaClass = uo.GetClass();
  if (!root.HasMember(KPropMapFieldName.data())) {
    ERR_DEF("read property from json failed! class name: %s", metaClass.name().c_str());
    return false;
  }

  auto& prop_map = root[KPropMapFieldName.data()];
  for (rapidjson::Value::ConstMemberIterator itr = prop_map.MemberBegin(); itr != prop_map.MemberEnd(); ++itr) {
    const rapidjson::Value& child = prop_map[itr->name.GetString()];

    if (!metaClass.HasProperty(itr->name.GetString())) {
      WRN_DEF("no property found, class: %s, keyname: %s", metaClass.name().data(), itr->name.GetString());
    }

    const Property& property = metaClass.GetProperty(itr->name.GetString());

    bool ret;

    if (child.IsArray()) {
      ret = ReadArrayPropertyFromJson(child, property, uo);
    } else if (child.IsObject()) {
      ret = ReadObjectPropertyFromJson(child, property, uo);
    } else {
      ret = ReadSimplePropertyFromJson(child, property, uo);
    }

    if (!ret) {
      WRN_DEF("write sub json value to property failed, keyname: %s", itr->name.GetString());
    }
  }

  return true;
}

bool CoderJson::ReadSimpleValueFromJson(const rapidjson::Value& root, const ValueKind& kind, Value& value) {
  switch (kind) {
    case ValueKind::kBoolean: {
      value = make_value(root.GetBool());
    } break;
    case ValueKind::kInteger: {
      value = make_value(root.GetInt64());
    } break;
    case ValueKind::kReal: {
      value = make_value(root.GetDouble());
    } break;
    case ValueKind::kString: {
      value = make_value(root.GetString());
    } break;
    default: {
      ERR_DEF("not support type: %d", kind);
      return false;
    }
  }

  return true;
}
}  // namespace reflection
}  // namespace gbf
