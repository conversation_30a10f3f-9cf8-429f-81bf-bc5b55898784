#pragma once

#include "reflection/builder/base_builder.hpp"
////#include "reflection/builder/function_builder.hpp"
////#include "reflection/builder/property_builder.hpp"
////#include "bridges/lura/builder/lua_function_builder.hpp"
#include "archive/protobuf/builder/pb_property_builder.hpp"
#include "reflection/builder/enum_builder.hpp"

namespace gbf {
namespace reflection {

//Notice: Do not change the function name here!!! (clang tools use the functions)

/**
 * \brief Proxy class which fills a metaclass with its members
 *
 * This class is returned by Class::declare<T> in order construct a
 * new metaclass. It contains functions to declare and bind metaproperties,
 * metafunctions, base metaclasses, metaconstructors, etc. with many overloads
 * in order to accept as many types of binds as possible.
 *
 * ClassBuilder also contains functions to set attributes of metafunctions
 * and metaproperties.
 *
 * This class should never be explicitely instantiated, unless you
 * need to split the metaclass creation in multiple parts.
 */
template <typename T>
class PbClassBuilder {
 public:
  /**
   * \brief Construct the builder with a target metaclass to fill
   *
   * \param target Metaclass to build
   */
   PbClassBuilder(MetaClass* meta_class):
    target_(meta_class), 
    base_builder_(meta_class), 
    func_builder_(meta_class), 
    ////property_builder_(meta_class),
    pb_property_builder_(meta_class),
    current_type_(meta_class){
  }

  /**
   * \brief Declare a base metaclass
   *
   * The template parameter U is the C++ base class of T.
   *
   * This function makes the target metaclass inherit of all the metaproperties and
   * metafunctions of the given base metaclass.
   *
   * \note We *do not* support virtual inheritance fully here due to the associated problems
   *       with compiler specific class layouts. e.g. see Class::applyOffset.
   *
   * \return Reference to this, in order to chain other calls
   *
   * \throw ClassNotFound no metaclass is bound to U
   */
  template <typename U>
  PbClassBuilder<T>& base(){
    current_type_ = base_builder_.template base<U>();
    return *this;
  }

  /**
   * \brief Declare a constructor for the metaclass.
   *
   * Variable number of parameters can be passed.
   *
   * \return Reference to this, in order to chain other calls
   */
  template <typename... A>
  PbClassBuilder<T>& constructor(){
    current_type_ = func_builder_.template constructor<A...>();
    return *this;
  }

  /**
   * \brief Add user data to the last declared member type
   *
   * \code
   * reflection::Class::declare<MyClass>("MyClass")
   *     .function("foo", &MyClass::foo)( reflection::UserData("user", 3) );
   * \endcode
   *
   * \return Reference to this, in order to chain other calls
   */
  template <typename... U>
  PbClassBuilder<T>& operator()(U&&... uds) {
    const std::initializer_list<UserData> il = {uds...};
    for (UserData const& ud : il) GetGlobalUserDataStore()->SetValue(*current_type_, ud.name(), ud.value());
    return *this;
  }

  template <typename ValueType>
  PbClassBuilder<T>& meta_attribute(const std::string_view metaName, ValueType&& val) {
    GetGlobalUserDataStore()->SetValue(*current_type_, metaName, Value(std::forward<ValueType>(val)));
    return *this;
  }

  template <typename F>
  PbClassBuilder<T>& property(IdRef name, F accessor) {
    current_type_ = pb_property_builder_.property(name, accessor, {});
    return *this;
  }

  template <typename F1, typename F2>
  PbClassBuilder<T>& property(IdRef name, F1 accessor1, F2 accessor2) {
    current_type_ = pb_property_builder_.property(name, accessor1, accessor2, {});
    return *this;
  }

  template <typename F, typename... P>
  PbClassBuilder<T>& function(IdRef name, F function, P... policies) {
    current_type_ = func_builder_.function(name, function, policies...);
    return *this;
  }
   
  MetaClass* get_target() { return target_; }

  Type* get_current_type() { return current_type_; }

  // For lurapb register
  template <class ElementT>
  PbClassBuilder<T>& pb_optional_property(IdRef field_name, const protobuf::PbDetailFeildInfo* detail_field_info) {
    current_type_ = pb_property_builder_.template pb_optional_property<ElementT>(field_name, detail_field_info);
    return *this;
  }

  template <class ElementT>
  PbClassBuilder<T>& pb_repeat_property(IdRef field_name, const protobuf::PbDetailFeildInfo* detail_field_info) {
    current_type_ = pb_property_builder_.template pb_repeat_property<ElementT>(field_name, detail_field_info);
    return *this;
  }

  template <class ElementT>
  PbClassBuilder<T>& pb_oneof_property(IdRef name, IdRef oneof_name, const protobuf::PbDetailFeildInfo* detail_field_info) {
    current_type_ = pb_property_builder_.template pb_oneof_property<ElementT>(name, oneof_name, detail_field_info);
    return *this;
  }

  PbClassBuilder<T>& property_tag_number(uint32_t tagNumber) {
    // Now just use dynamic cast here~~
    Property* prop = dynamic_cast<Property*>(current_type_);
    if (GBF_LIKELY(tagNumber != 0 && prop != nullptr)) {
      prop->set_tag_number(tagNumber);
      target_->CheckPropertyTagNumberSetting(tagNumber);
    }

    return *this;
  }

  PbClassBuilder<T>& function_tag_number(uint32_t tagNumber) {
    // Now just use dynamic cast here~~
    Function* func = dynamic_cast<Function*>(current_type_);
    if (GBF_LIKELY(tagNumber != 0 && func != nullptr)) {
      func->set_tag_number(tagNumber);
      target_->CheckFunctionTagNumberSetting(tagNumber);
    }

    return *this;
  }

 private:
  BaseBuilder<T> base_builder_;
  ////PropertyBuilder<T> property_builder_;
  ::protobuf::PbPropertyBuilder<T> pb_property_builder_;
  FunctionBuilder<T> func_builder_;
  ////LuaFunctionBuilder<T> lua_func_builder_;

  MetaClass* target_ = nullptr;   // Target metaclass to fill
  Type* current_type_ = nullptr;  // Last member type which has been declared
};

}  // namespace reflection
}  // namespace gbf

#include "archive/protobuf/builder/pb_class_builder.inl"


