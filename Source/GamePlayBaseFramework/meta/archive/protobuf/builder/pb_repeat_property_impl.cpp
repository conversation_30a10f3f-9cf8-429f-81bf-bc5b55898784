#include "archive/protobuf/builder/pb_repeat_property_impl.h"
#include "core/error/errors.hpp"
#include "archive/protobuf/builder/pb_converter_tool.h"

namespace gbf {
namespace reflection {
namespace detail {

PbRepeatPropertyImpl::PbRepeatPropertyImpl(MetaClass* owner, IdRef name, ValueKind elementKind, TypeId elementTypeId, uint32_t tag,
                                           protobuf::cxx::FieldDataType dataType, uint32_t dataOffset,
                                           uint32_t dataSize, uint32_t arrayCapacity, uint32_t offsetForSize,
                                           bool isFixedArray)
    : ArrayProperty(owner, name, elementKind, elementTypeId, elementTypeId),
      pb_data_type_(dataType),
      build_kind_(protobuf::cxx::TypeHelper::ToBuildKind(pb_data_type_)),
      data_offset_(dataOffset),
      data_size_(dataSize),
      array_capacity_(arrayCapacity),
      offset_for_size_(offsetForSize),
      is_fixed_array_(isFixedArray) {
  implement_type_ = PropertyImplementType::kPbRepeatProperty;
  tag_number_ = tag;
  dynamic_ = true;
}

PbRepeatPropertyImpl::~PbRepeatPropertyImpl() {}

size_t PbRepeatPropertyImpl::GetSize(const UserObject& object) const {
  if (is_fixed_array_) {
    return array_capacity_;
  } else {
    return protobuf::PbConverterTool::GetRepeatSize(object.GetPointer(), data_offset_, offset_for_size_);
  }
}

void PbRepeatPropertyImpl::SetSize(const UserObject& object, size_t size) const {
  if (!is_fixed_array_) {
    size_t minSize = std::min((size_t)GetRepeatCapacity(), size);
    protobuf::PbConverterTool::SetRepeatSize(object.GetPointer(), data_offset_, offset_for_size_, (protobuf::pb_size_t)minSize);
  }
}

Value PbRepeatPropertyImpl::GetElement(const UserObject& object, size_t index) const {
  uint32_t elementOffset = data_offset_ + data_size_ * index;
  switch (build_kind_) {
    case protobuf::cxx::FieldBuildKind::kEnum:
      return protobuf::PbConverterTool::EnumPointerGetAsValue(object.GetPointer(), elementOffset, data_size_);
    case protobuf::cxx::FieldBuildKind::kString:
      return protobuf::PbConverterTool::StringPointerGetAsValue(object.GetPointer(), elementOffset, data_size_);
    case protobuf::cxx::FieldBuildKind::kMessage:
      return protobuf::PbConverterTool::MessagePointerGetAsValue(object.GetPointer(), type_index(), elementOffset,
                                                               data_size_);
    case protobuf::cxx::FieldBuildKind::kPrimitive:
      return protobuf::PbConverterTool::PrimitivePointerGetAsValue(object.GetPointer(), pb_data_type_, elementOffset,
                                                                 data_size_);
    case protobuf::cxx::FieldBuildKind::kBytes:
      return protobuf::PbConverterTool::BytesPointerGetAsValue(object.GetPointer(), elementOffset, data_size_);
      //-------------------------------------------------------------------------------------
    case protobuf::cxx::FieldBuildKind::kExtension:
    default:
      GBF_ERROR(NotImplementError("pb not support build type find here!"));
      break;
  }

  return Value::nothing;
}

void PbRepeatPropertyImpl::SetElement(const UserObject& object, size_t index, const Value& value) const {
  uint32_t elementOffset = data_offset_ + data_size_ * index;

  bool setValueSuc = false;
  switch (build_kind_) {
    case protobuf::cxx::FieldBuildKind::kEnum:
      setValueSuc = protobuf::PbConverterTool::EnumPointerSetAsValue(object.GetPointer(), elementOffset, data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kString:
      setValueSuc = protobuf::PbConverterTool::StringPointerSetAsValue(object.GetPointer(), elementOffset, data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kMessage:
      setValueSuc = protobuf::PbConverterTool::MessagePointerSetAsValue(object.GetPointer(), type_index(), elementOffset,
                                                                      data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kPrimitive:
      setValueSuc = protobuf::PbConverterTool::PrimitivePointerSetAsValue(object.GetPointer(), pb_data_type_, elementOffset,
                                                                        data_size_, value);
      break;
    case protobuf::cxx::FieldBuildKind::kBytes:
      setValueSuc = protobuf::PbConverterTool::BytesPointerSetAsValue(object.GetPointer(), elementOffset, data_size_, value);
      break;
      //-------------------------------------------------------------------------------------
    case protobuf::cxx::FieldBuildKind::kExtension:
    default:
      GBF_ERROR(NotImplementError("Extension not support here!"));
      break;
  }
}

void PbRepeatPropertyImpl::InsertElement(const UserObject& object, size_t before, const Value& value) const {
  size_t totalSize = GetSize(object);
  if (totalSize < GetRepeatCapacity()) {
    if (before < totalSize) {
      // Move first
      int totalMoves = (int)totalSize - before;
      MovAllRightElementsRight(object, before, totalMoves, before + 1);
      SetElement(object, before, value);
    } else {
      // Just set element
      SetElement(object, totalSize, value);
    }
    SetSize(object, totalSize + 1);
  }
}

void PbRepeatPropertyImpl::RemoveElement(const UserObject& object, size_t index) const {
  size_t totalSize = GetSize(object);
  if (index < GetSize(object)) {
    int totalMoves = (int)totalSize - index - 1;
    MovAllRightElementsLeft(object, index + 1, (size_t)totalMoves, index);
  }
}

size_t PbRepeatPropertyImpl::GetElementSize() const { return data_size_; }

bool PbRepeatPropertyImpl::SupportRawPointer() const { return true; }

void* PbRepeatPropertyImpl::GetRawPointer(const UserObject& object) const {
  return (char*)object.GetPointer() + data_offset_;
}

void PbRepeatPropertyImpl::MovAllRightElementsLeft(const UserObject& object, size_t index, size_t totalMoves,
                                                   size_t targetIndex) const {
  assert(index > targetIndex);
  char* dataPtr = (char*)object.GetPointer();
  for (size_t i = 0; i < totalMoves; i++) {
    size_t curIndex = index + i;
    size_t curDstIndex = targetIndex + i;

    uint32_t srcOffset = data_offset_ + data_size_ * curIndex;
    uint32_t dstOffset = data_offset_ + data_size_ * curDstIndex;

    memcpy(dataPtr + dstOffset, dataPtr + srcOffset, data_size_);
  }
}

void PbRepeatPropertyImpl::MovAllRightElementsRight(const UserObject& object, size_t index, size_t totalMoves,
                                                    size_t targetIndex) const {
  assert(index < targetIndex);
  char* dataPtr = (char*)object.GetPointer();
  for (int i = (int)totalMoves - 1; i >= 0; i--) {
    size_t curIndex = index + i;
    size_t curDstIndex = targetIndex + i;

    uint32_t srcOffset = data_offset_ + data_size_ * curIndex;
    uint32_t dstOffset = data_offset_ + data_size_ * curDstIndex;

    memcpy(dataPtr + dstOffset, dataPtr + srcOffset, data_size_);
  }
}

}  // namespace detail
}  // namespace reflection
}  // namespace gbf
