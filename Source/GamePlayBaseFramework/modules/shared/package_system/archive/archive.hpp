// Copyright 2015-2018 Captain Games, Inc. All Rights Reserved.

#pragma once


#include <ctime>
#include <memory>
#include <vector>

#include "core/utils/byte_buffer.h"
#include "core/utils/string_util.h"
#include "imod_shared/imodules/ipackage_system_module.h"
#include "package_system_export.hpp"


namespace gbf {

class Archive;

struct FileInfo {
  /// The archive in which the file has been found (for info when performing
  /// multi-Archive searches, note you should still open through ResourceGroupManager)
  Archive* archive;
  /// The file's fully qualified name
  std::string filename;
  /// Path name; separated by '/' and ending with '/'
  std::string path;
  /// Base filename
  std::string basename;
  /// Compressed size
  size_t compressedSize;
  /// Uncompressed size
  size_t uncompressedSize;
};

using FileInfoList = std::vector<FileInfo>;

class GBF_PACKAGE_SYSTEM_API Archive {
 protected:
  /// Archive name
  std::string mName;
  /// Archive type code
  std::string mType;
  /// Read-only flag
  bool mReadOnly;
 public:
  /** Constructor - don't call direct, used by ArchiveFactory.
   */
  Archive(std::string_view name, std::string_view archType) : mName(name), mType(archType), mReadOnly(true) {}

  /** Default destructor.
   */
  virtual ~Archive() {}

  /// Get the name of this archive
  std::string_view get_name(void) const { return mName; }
  /// Return the type code of this Archive
  std::string_view get_type(void) const { return mType; }

  /// Returns whether this archive is case sensitive in the way it matches files
  virtual bool IsCaseSensitive(void) const = 0;

  virtual void Load() = 0;

  virtual void Unload() = 0;

  virtual bool IsReadOnly() const { return mReadOnly; }

  virtual ByteBufferPtr ReadFileData(std::string_view filename, bool readOnly = true) = 0;

  virtual DataStreamPtr OpenFileStream(std::string_view filename, bool readOnly) = 0;

  virtual DataStreamPtr CreateWritableFileStream(std::string_view filename) = 0;

  virtual bool ReplaceByEmbeded(const EmbededResourceInfo& info, bool use_compress, bool use_crypto) { return true; }
  
  //---------------------------------------------------------------------
  virtual void RemoveFile(std::string_view) {}
  
  virtual StringVector ListFileNames(bool recursive = true, bool dirs = false) = 0;

  virtual FileInfoList ListFileInfos(bool recursive = true, bool dirs = false) = 0;

  virtual StringVector FindMatchNames(std::string_view pattern, bool recursive = true, bool dirs = false) = 0;

  virtual FileInfoList FindMatchInfos(std::string_view pattern, bool recursive = true, bool dirs = false) = 0;

  /** Find out if the named file exists (note: fully qualified filename required) */
  virtual bool FileExist(std::string_view filename) = 0;

  virtual bool ConfigExist(std::string_view filename) { return false; }
  
  /** Retrieve the modification time of a given file */
  virtual time_t GetModifiedTime(std::string_view filename) = 0;


  virtual void QueryVersionInfo(std::string_view filename, ResourceVersionInfo& versionInfo) {}
  
  virtual void QueryConfigInfo(std::string_view filename, std::string& configInfo) {}
  
  virtual void ReplaceConfigInfo(const char* filename, const char* configInfo) {}
};


}  // namespace gbf
