using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;
namespace CrossEditor
{
    class EditorConfigUI : DialogUI
    {
        static EditorConfigUI gInstance = new EditorConfigUI();

        Panel mMainPanel;
        HSplitter mHSplitter;
        Tree mTree;
        Texture mTextureTreeItem;
        ScrollView mScrollView;
        Panel mScrollPanel;
        List<Inspector> mInspectors = new List<Inspector>();
        Button mSaveButton;

        int mWidth;
        int mHeight;
        InspectorHandler mInspectorHandler;

        public static EditorConfigUI GetInstance()
        {
            return gInstance;
        }

        public EditorConfigUI()
        {
            mInspectorHandler = new InspectorHandler();
            mInspectorHandler.InspectObject = UpdateConfig;
            mInspectorHandler.UpdateLayout = UpdateLayout;
            mInspectorHandler.ReadValue = ReadValue;
        }

        public void Initialize(UIManager UIManager)
        {
            mTextureTreeItem = UIManager.LoadUIImage("Editor/Tree/Game/GameObject.png");
            mWidth = 1000;
            mHeight = 850;

            base.Initialize(UIManager, "EditorConfig", mWidth, mHeight);

            mMainPanel = new Panel();
            mMainPanel.Initialize();
            mMainPanel.SetWidth(_PanelDialog.GetWidth());
            mMainPanel.SetHeight(_PanelDialog.GetHeight());
            _PanelDialog.AddChild(mMainPanel);

            mHSplitter = new HSplitter();
            mHSplitter.Initialize();
            mMainPanel.AddChild(mHSplitter);

            mTree = new Tree();
            mTree.Initialize();
            mTree.SetEnableRename(false);
            mTree.SetPosition(20, 40, mWidth / 4 - 10, mHeight - 100);
            mTree.ItemSelectedEvent += OnTreeItemSelected;
            mHSplitter.AddChild(mTree);

            mScrollView = new ScrollView();
            mScrollView.Initialize();
            mScrollView.SetBackgroundColor(Color.FromRGBA(30, 30, 30, 255));
            mScrollView.SetPosition(mWidth / 4 + 10, 40, mWidth * 3 / 4 + 30, mHeight - 100);
            mHSplitter.AddChild(mScrollView);

            mHSplitter.SetPosition(10, 40, mWidth - 20, mHeight - 80);

            mSaveButton = new Button();
            mSaveButton.Initialize();
            mSaveButton.SetFontSize(16);
            mSaveButton.SetBorderColor(Color.FromRGBA(64, 224, 208, 255));
            mSaveButton.SetText("Save Config");
            mSaveButton.SetTextColor(Color.FromRGBA(255, 255, 255, 255));
            mSaveButton.SetPosition(mWidth / 2 - 60, mHeight - 35, 120, 30);
            mSaveButton.ClickedEvent += OnButtonSaveClicked;
            mMainPanel.AddChild(mSaveButton);

            mScrollPanel = mScrollView.GetScrollPanel();

            IntializeConfigs();
        }

        public void IntializeConfigs()
        {
            mTree.ClearChildren();
            TreeItem rootItem = mTree.GetRootItem();
            rootItem.SetExpanded(true);
            rootItem.SetText("EditorConfig");
            rootItem.SetImageExpanded(mTextureTreeItem);

            List<ConfigNode> configs = EditorConfigManager.GetInstance().mConfigList;
            foreach (ConfigNode config in configs)
            {
                AddCoonfigItem(config, rootItem);
            }
            mTree.SelectItem(rootItem.GetChild(0));
        }

        public void AddCoonfigItem(ConfigNode configNode, TreeItem parentItem)
        {
            TreeItem treeItem = mTree.CreateItem();
            treeItem.SetText(configNode.mName);
            treeItem.SetTagObject(configNode.mConfig);
            treeItem.SetImageExpanded(mTextureTreeItem);
            parentItem.AddChild(treeItem);
            treeItem.SetFolder(configNode.mSubConfigs.Count > 0 ? true : false);
            foreach (ConfigNode subConfigNode in configNode.mSubConfigs)
            {
                AddCoonfigItem(subConfigNode, treeItem);
            }
        }

        public void UpdateLayout()
        {
            int width = mScrollView.GetWidth() - 50;
            int Y = 0;
            foreach (Inspector inspector in mInspectors)
            {
                inspector.UpdateLayout(width, ref Y);
            }
            int hight = Y + 20;
            mScrollPanel.SetSize(width + 50, Math.Max(hight, mHeight - 100));
            mScrollView.UpdateScrollBar();
        }

        public void UpdateConfig()
        {
            TreeItem item = mTree.GetSelectedItem();
            // clear ScrollView
            mInspectors.Clear();
            mScrollPanel.ClearChildren();
            // 
            object config = item.GetTagObject();
            if (config == null) return;
            Type tp = config.GetType();
            PropertyInfo[] properties = tp.GetProperties();
            foreach (PropertyInfo propertyInfo in properties)
            {
                AddPropertyInspector(config, propertyInfo);
            }
            UpdateLayout();
        }

        void OnTreeItemSelected(Tree sender, TreeItem item)
        {
            UpdateConfig();
        }

        protected void AddPropertyInspector(object obj, PropertyInfo PropertyInfo)
        {
            string PropertyTypeString = PropertyInfo.PropertyType.ToString();
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
            if (PropertyInfoAttribute.PropertyType != "")
            {
                PropertyTypeString = PropertyInfoAttribute.PropertyType;
            }
            Type PropertyType = PropertyInfo.PropertyType;
            bool bIsEnum = PropertyType.IsEnum;
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = obj;
            ObjectProperty.Name = PropertyInfo.Name;
            ObjectProperty.Type = PropertyType;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
            ObjectProperty.GetPropertyValueFunction = delegate (object Object, string name, ValueExtraProperty ValueExtraProperty) { return PropertyInfo.GetValue(Object); };
            ObjectProperty.SetPropertyValueFunction = delegate (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) { PropertyInfo.SetValue(Object, PropertyValue); }; ;
            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, bIsEnum);
            Inspector_Property.InspectProperty(ObjectProperty);
            Inspector_Property.SetContainer(mScrollPanel);
            Inspector_Property.SetInspectorHandler(mInspectorHandler);
            mInspectors.Add(Inspector_Property);
        }

        void OnButtonSaveClicked(Button Sender)
        {
            TreeItem item = mTree.GetSelectedItem();

            if (item != null)
            {
                if (item == mTree.GetRootItem())
                {
                    dynamic configs = EditorConfigManager.GetInstance().mConfigList;
                    foreach (dynamic config in configs)
                    {
                        config.mConfig.Dump();
                    }
                }
                else
                {
                    while (item.GetParent() != mTree.GetRootItem())
                    {
                        item = item.GetParent();
                    }
                    dynamic config = item.GetTagObject();
                    config.Dump();
                }
            }
        }

        void ReadValue()
        {
            foreach (Inspector inspector in mInspectors)
            {
                inspector.ReadValue();
            }
        }
        public override void CloseDialog()
        {
            OnButtonSaveClicked(null);
            base.CloseDialog();
        }
    }
}
