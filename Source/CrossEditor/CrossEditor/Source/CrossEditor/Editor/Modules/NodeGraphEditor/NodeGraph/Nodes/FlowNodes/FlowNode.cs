using EditorUI;
using System.Collections.Generic;
using System.Reflection;
using System.Text.Json.Nodes;
using CEngine;
using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
namespace CrossEditor
{
    public class FlowNode : Node
    {
        public string TemplateExpression = "";
        public virtual object Eval(int OutSlotIndex)
        {
            return null;
        }

        public virtual void Run()
        {

        }

        public bool GetInSlotValue_Bool(int InSlotIndex, out bool bValue)
        {
            int OutSlotIndex;
            FlowNode InNode = GetInputNode(InSlotIndex, out OutSlotIndex) as FlowNode;
            if (InNode != null)
            {
                object Value1 = InNode.Eval(OutSlotIndex);
                if (Value1 != null && Value1 is bool)
                {
                    bValue = (bool)Value1;
                    return true;
                }
                else
                {
                    CommitInSlotError(InSlotIndex, "slot is not connected with a bool value.");
                    bValue = false;
                    return false;
                }
            }
            CommitInSlotError(InSlotIndex, "slot is not connected.");
            bValue = false;
            return false;
        }

        public bool GetInSlotValue_Int(int InSlotIndex, out int Value)
        {
            int OutSlotIndex;
            FlowNode InNode = GetInputNode(InSlotIndex, out OutSlotIndex) as FlowNode;
            if (InNode != null)
            {
                object Value1 = InNode.Eval(OutSlotIndex);
                if (Value1 != null && Value1 is int)
                {
                    Value = (int)Value1;
                    return true;
                }
                else
                {
                    CommitInSlotError(InSlotIndex, "slot is not connected with a int value.");
                    Value = 0;
                    return false;
                }
            }
            CommitInSlotError(InSlotIndex, "slot is not connected.");
            Value = 0;
            return false;
        }

        public bool GetInSlotValue_String(int InSlotIndex, out string String)
        {
            int OutSlotIndex;
            FlowNode InNode = GetInputNode(InSlotIndex, out OutSlotIndex) as FlowNode;
            if (InNode != null)
            {
                object Value1 = InNode.Eval(OutSlotIndex);
                if (Value1 != null && Value1 is string)
                {
                    String = (string)Value1;
                    return true;
                }
                else
                {
                    CommitInSlotError(InSlotIndex, "slot is not connected with a string value.");
                    String = "";
                    return false;
                }
            }
            CommitInSlotError(InSlotIndex, "slot is not connected.");
            String = "";
            return false;
        }

        public void RunOutSlot(int OutSlotIndex)
        {
            List<Node> OutputNodes = GetOutputNodes(OutSlotIndex);
            foreach (Node OutputNode in OutputNodes)
            {
                FlowNode FlowNode = OutputNode as FlowNode;
                FlowNode.Run();
            }
        }

        public virtual string ToExpression()
        {
            return TemplateExpression;
        }

        public List<Node> GetOutputNodes(int Index)
        {
            Slot Slot = GetOutSlot(Index);
            List<Connection> Connections = Slot.GetConnections();
            List<Node> Nodes = new List<Node>();
            foreach (Connection Connection in Connections)
            {
                Nodes.Add(Connection.InSlot.Node);
            }
            return Nodes;
        }

        public Node GetInputNode(int Index, out int OutSlotIndex)
        {
            Slot Slot = GetInSlot(Index);
            List<Connection> Connections = Slot.GetConnections();
            int ConnectionCount = Connections.Count;
            if (ConnectionCount > 0)
            {
                Connection Connection = Connections[0];
                OutSlotIndex = Connection.OutSlot.Index;
                return Connection.OutSlot.Node;
            }
            else
            {
                OutSlotIndex = -1;
                return null;
            }
        }

        public virtual void CommitInSlotError(int InSlotIndex, string ErrorInformation)
        {
            SetError();
            Slot Slot = GetInSlot(InSlotIndex);
            Slot.SetError();
            string ErrorMessage = string.Format("Error: Graph Slot {0}({1})-{2}({3}): {4}", Name, ID, Slot.Name, InSlotIndex, ErrorInformation);
            ConsoleUI.GetInstance().AddLogItem(LogMessageType.Error, ErrorMessage);
            MainUI.GetInstance().ActivateDockingCard_Console();
        }

        //protected NodeData NodeData;

        //protected void FillNodeDataBaseInfo(NodeData NodeData)
        //{
        //    NodeData.Name = ID.ToString();
        //    NodeData.Type = GetType().Name;
        //    NodeData.PosX = X;
        //    NodeData.PosY = Y;
        //}

        //public System.Type NodeDataType => NodeData == null ? typeof(NodeData) : NodeData.GetType();

        public virtual void FromJsonObject(JsonObject JsonObject)
        {
            ID = int.Parse(JsonObject["Name"].ToString());
            Name = JsonObject["Type"].ToString();
            X = JsonObject["PosX"].GetValue<int>();
            Y = JsonObject["PosY"].GetValue<int>();

            PropertyInfo[] Properties = GetType().GetProperties();
            foreach (PropertyInfo Info in Properties)
            {
                var Object = JsonObject[Info.Name];
                if (Object != null)
                {
                    object Value = null;
                    if (Info.PropertyType == typeof(bool))
                    {
                        Value = Object.GetValue<bool>();
                    }
                    else if (Info.PropertyType == typeof(int))
                    {
                        Value = Object.GetValue<int>();
                    }
                    else if (Info.PropertyType == typeof(string))
                    {
                        Value = Object.GetValue<string>();
                    }
                    else if (Info.PropertyType.IsEnum)
                    {
                        Value = Enum.ToObject(Info.PropertyType, Object.GetValue<int>());
                    }
                    if (Value != null)
                    {
                        Info.SetValue(this, Value);
                    }
                }
        
            }
        }

        public virtual JsonObject ToJsonObject()
        {
            JsonObject JsonObject = new JsonObject()
            {
                {"PosX", X},
                {"PosY", Y},
                {"Type", GetType().Name},
                {"Name", ID.ToString() }
            };
            PropertyInfo[] Properties = GetType().GetProperties();
            foreach (PropertyInfo Info in Properties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(Info);
                if (PropertyInfoAttribute.bHide == false)
                {
                    var value = Info.GetValue(this);
                    if (value != null)
                        JsonObject[Info.Name] = JsonValue.Create(value);
                }
            }
            return JsonObject;
        }
    }
}
