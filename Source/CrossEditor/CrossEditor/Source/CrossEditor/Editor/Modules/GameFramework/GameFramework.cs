using CEngine;
using Clicegf;
using EditorUI;

namespace CrossEditor
{
    public class Game<PERSON>rameworkTools
    {
        public static GameFrameworkTools Instance { get; } = new GameFrameworkTools();

        private GOContext mGoContext;
        GameFrameworkTools()
        {
            var World = EditorScene.GetInstance().GetWorld();
            mGoContext = new GOContext(World._WorldInterface);
        }
        public GOContext GOContext
        { get { return mGoContext; } }
    }

    class EditOperation_CreateGameObject : EditOperation
    {
        private GOHandle mHandle;
        private GOContext mContext;
        private World mWorld;
        public EditOperation_CreateGameObject(GOHandle inHandle, GOContext inContext, World inWorld)
        {
            mHandle = new GOHandle(inHandle);
            mContext = new GOContext(inContext);
            mWorld = inWorld;
        }

        public override void Undo()
        {
            mContext.DeleteGameObjectByEntity(mHandle.EntityID);
            mWorld.Root.RefreshTree();
            HierarchyUI.GetInstance().UpdateHierarchy();
        }

        public override void Redo()
        {
            var ret = new GOHandle();
            ret.GOName = mHandle.GOName;
            mHandle.EntityID = EntityIDStruct.InvalidHandle().GetValue();

            mContext.CreateGameObjectWithHandle(mHandle, ret);
            mWorld.Root.RefreshTree();
            HierarchyUI.GetInstance().UpdateHierarchy();

            // newly created entity is different from mHandle.EntityID, so assign the new one
            mHandle = ret;
        }
    }

    public class FlowPathDragProcessor : PathDragProcessor
    {
        public override string RegExpPattern
        {
            get { return @"(.*)(\.flow)$"; }
        }
        protected override bool DoProcessImpl(string InPath, Entity inRootEntity, Vector3d DropPoint)
        {
            string FilePath1 = EditorUtilities.EditorFilenameToStandardFilename(InPath);
            CEngine.ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(FilePath1);
            if (ObjectClassID != CEngine.ClassIDType.CLASS_WorkflowGraphResource)
            {
                return false;
            }

            // prepare context
            var World = HierarchyUI.GetInstance().GetScene().GetWorld();
            GOContext GoContext = new GOContext(World._WorldInterface);
            GOHandle handle = new GOHandle();

            // do create task
            GoContext.CreateGameObjectWithFlowFile(FilePath1, DropPoint.ToCliDouble3(), handle);

            // the entity is created from runtime, so sync entity data and refresh hierarchy ui
            World.Root.RefreshTree();
            HierarchyUI.GetInstance().UpdateHierarchy();

            // add operation
            EditOperation_CreateGameObject op = new EditOperation_CreateGameObject(handle, GoContext, World);
            EditOperationManager.GetInstance().AddOperation(op);

            return true;
        }
    }

    [ToolBarMenuAttribute(DisplayUINames = "UseDynamicUIName", UseDynamicUINames = true)]
    public class CreateGameObjectMenuItem : ToolBarMenuItem
    {
        public override void OnClick(MenuItem sender)
        {
            // prepare context
            var World = HierarchyUI.GetInstance().GetScene().GetWorld();
            GOContext GoContext = new GOContext(World._WorldInterface);
            string button = sender.GetText();
            GOHandle handle = new GOHandle();

            var parent_entity = HierarchyUI.GetInstance().GetSelectedEntity();
            if (parent_entity != null)
            {
                var parent_go = GoContext.GetGameObject(parent_entity.EntityID);
                if (parent_go != null)
                {
                    handle.Parent = parent_go;
                }
                else
                {
                    EditorLogger.Log(LogMessageType.Error, "Can not create GameObject under Entity. The created GameObject will be move to root.");
                }
            }

            // do create task
            // button will be "cegf::GameObject", "cegf::Character" and other type names which are generated by auto reflection
            GoContext.CreateGameObject(button, handle);

            // the entity is created from runtime, so sync entity data and refresh hierarchy ui
            World.Root.RefreshTree();
            HierarchyUI.GetInstance().UpdateHierarchy();

            // add operation
            EditOperation_CreateGameObject op = new EditOperation_CreateGameObject(handle, GoContext, World);
            EditOperationManager.GetInstance().AddOperation(op);

            base.OnClick(sender);
        }

        public override string GetDynamicUINames()
        {
            // Construct a string like "GameObject/Create/[cegf::GameObject, cegf::Pawn, cegf::Character]"

            //prefix
            string name = "GameObject/[";
            var tempClassList = GameFrameworkTools.Instance.GOContext.GOTypeNames;

            for (int i = 0; i < tempClassList.Count; i++)
            {
                // item
                var className = tempClassList[i];

                name += className;
                if (i != tempClassList.Count - 1)
                {
                    name += ",";
                }
            }

            // end
            name += "]";
            return name;
        }
    }

}