using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class ParticleEmitterNode : Node
    {
        public const int DefaultOneLineRectWidth = 80;
        public const int ModuleItemHeight = 20;
        public const int ModuleItemMarginX = 5;
        public const int ModuleItemMarginY = 5;
        public const int OffsetY = 2;

        private ParticleEmitterInfo _EmitterInfo = null;
        public ParticleEmitterInfo EmitterInfo { get => _EmitterInfo; }

        private ParticleEmitterResource _Resource = null;
        public ParticleEmitterResource Resource { get => _Resource; set => _Resource = value; }

        private int _EmitterIndex = -1;
        public int EmitterIndex { get => _EmitterIndex; }

        private int _InspectIndex = -1;
        public int InspectIndex
        {
            set => _InspectIndex = value;
            get => _InspectIndex;
        }

        List<ParticleModuleItem> _ModuleList;
        List<ParticleModuleItem> ModuleList { get => _ModuleList; }

        public ParticleEmitterNode(int Index, ParticleEmitterInfo EmitterInfo)
        {
            _EmitterIndex = Index;
            NodeName = EmitterInfo.EmitterName;
            _EmitterInfo = EmitterInfo;
            var Res = Clicross.ParticleSimulationSystemG.FX_CreateParticleEmitter();
            _Resource = new ParticleEmitterResource(Res, EmitterInfo);

            ContentWidth = 210;
            ContentHeight = (ModuleItemHeight + 5) * GetItemCount() + 32;
            Width = 214;
            Height = ContentHeight + 1;
        }

        public void Initialize()
        {
            _ModuleList = new List<ParticleModuleItem>();

            PropertyInfo[] Properties = typeof(ParticleEmitterInfo).GetProperties();
            for (int Index = 0; Index < Properties.Length; Index++)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(Properties[Index]);
                string Tag = null;
                if (AttributeList != null)
                {
                    AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();
                    if (AttributeData != null && AttributeData.GetNamedAttribute("Category") != null)
                    {
                        Tag = AttributeData.GetNamedAttribute("Category").ToString();
                    }
                    else
                    {
                        continue;
                    }
                }
                var Item = new ParticleModuleItem(Index, Properties[Index].Name, ContentWidth, ModuleItemHeight, Tag);
                Item.SetParent(this);

                if (EmitterInfo != null)
                {
                    var PropValue = Properties[Index].GetValue(EmitterInfo);
                    var EnablePropInfo = PropValue.GetType().GetProperty("Enabled");
                    if (EnablePropInfo != null)
                    {
                        Item.Checked = (bool)EnablePropInfo.GetValue(PropValue);
                    }
                    Item.OnEnableChanged += (bool Checked) =>
                    {
                        if (EnablePropInfo != null && PropValue != null)
                        {
                            EnablePropInfo.SetValue(PropValue, Checked);
                            FlushParticleRendererItem(PropValue.GetType(), Checked);
                            if (ParticleSystemUI.GetInstance().Controller.State != SimulationState.STOPPED)
                            {
                                ParticleSystemUI.GetInstance().RestartParticleSystem();
                            }
                            ParticleSystemUI.GetInstance().SetTileState(true);
                        }
                    };
                }

                _ModuleList.Add(Item);
            }
        }


        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            ContentWidth = DefaultOneLineRectWidth / 2;
            ContentHeight = (ModuleItemHeight * ModuleList.Count);
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color BgColor = Color.FromRGBA(10, 10, 10, 250);
            GraphicsHelper.FillRectangle(UIManager, BgColor, ContentX + 1, ContentY + 1, ContentWidth, ContentHeight);

            int ListItemY = ContentY - ContentHeight / 2 + ModuleItemHeight;

            Font TitleFont = UIManager.GetMainUIManager().GetDefaultFont(14);
            string NodeTitle = string.Format("[{0}]{1}", EmitterIndex.ToString(), NodeName);
            GraphicsHelper.DrawString(UIManager, TitleFont, NodeTitle, Color.White, ContentX, ListItemY, ContentWidth, ContentHeight, TextAlign.CenterCenter);

            foreach (var Item in ModuleList)
            {
                ListItemY += (ModuleItemHeight + ModuleItemMarginY);
                Item.Draw(UIManager, ContentX + ModuleItemMarginX, ListItemY, ContentWidth, ContentHeight);
            }
        }

        public override void DoLayout()
        {
            //base.DoLayout();
        }

        public ParticleModuleItem HitModuleItem(int WorldX, int WorldY)
        {
            foreach (var Item in ModuleList)
            {
                if (Item.Hitted(WorldX, WorldY))
                {
                    return Item;
                }
            }
            return null;
        }

        public void ClearHighlightItem()
        {
            foreach (var Item in ModuleList)
            {
                if (Item.Highlight)
                {
                    Item.Highlight = false;
                }
            }
        }

        private int GetItemCount()
        {
            int ItemCnt = 0;
            PropertyInfo[] Properties = typeof(ParticleEmitterInfo).GetProperties();
            for (int Index = 0; Index < Properties.Length; Index++)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(Properties[Index]);
                if (AttributeList != null)
                {
                    AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();
                    if (AttributeData != null && AttributeData.GetNamedAttribute("Category") != null)
                    {
                        ItemCnt += 1;
                    }
                    else
                    {
                        continue;
                    }
                }
            }
            return ItemCnt;
        }

        private ParticleModuleItem FindItem(string Name)
        {
            var Ret = ModuleList.Find(delegate (ParticleModuleItem Item)
            {
                return Item.Name == Name;
            });

            return Ret;
        }

        private void FlushParticleRendererItem(System.Type InfoType, bool Enable)
        {
            // When input Enable is true, flush other renderer as unchekced
            if (!Enable || !InfoType.Name.Contains("Renderer"))
            {
                return;
            }

            if (InfoType == typeof(ParticleSpriteRendererInfo))
            {
                EmitterInfo.RendererType = ParticleRendererType.Sprite;
            }
            else if (InfoType == typeof(ParticleMeshRendererInfo))
            {
                EmitterInfo.RendererType = ParticleRendererType.Mesh;
            }
            else
            {
                EmitterInfo.RendererType = ParticleRendererType.Unset;
            }

            PropertyInfo[] Properties = typeof(ParticleEmitterInfo).GetProperties();
            for (int Index = 0; Index < Properties.Length; Index++)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(Properties[Index]);
                if (AttributeList == null || !Properties[Index].Name.Contains("Renderer"))
                {
                    continue;
                }
                AttributeData PropertyInfo = AttributeList.GetPropertyInfoAttr();
                var CurrentItemProp = Properties[Index].GetValue(EmitterInfo);
                // Change other renderer checked status
                if (CurrentItemProp.GetType() != InfoType)
                {
                    var EnableProp = CurrentItemProp.GetType().GetProperty("Enabled");
                    if (EnableProp != null)
                    {
                        EnableProp.SetValue(CurrentItemProp, false);
                    }

                    var Item = FindItem(Properties[Index].Name);
                    if (Item != null)
                    {
                        Item.Checked = false;
                    }
                }
            }
        }
    }
}