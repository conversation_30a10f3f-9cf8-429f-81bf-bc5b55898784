using CEngine;
using EditorUI;

namespace CrossEditor
{
    public class CurveTrackHolder : HolderTrack<CurveTrack>
    {
        protected void AddCurveAccordingToType(CurveUseType inUseType)
        {
            int X = _ButtonAddTrack.GetScreenX();
            int Y = _ButtonAddTrack.GetScreenY() + _ButtonAddTrack.GetHeight();
            int Width = FontSize * 10;
            int Height = FontSize;

            Renamer Renamer = new Renamer();
            Renamer.Rename(_ButtonAddTrack, "", null,
                X, Y, Width, Height, 0, FontSize, Color.White, false,
                (RenameSender, NewName, OldName, Object) =>
                {
                    if (GetTagObject() is PreviewAnimAsset)
                    {
                        foreach (var ExistTrack in Children)
                        {
                            if (ExistTrack.GetCurve().Name == NewName)
                            {
                                string Title = "Information:";
                                string Content = string.Format("Curve: \"{0}\" already exist", NewName);
                                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), Title, Content);
                                return;
                            }
                        }

                        Track NewTrack = CreateNewTrack(NewName) as CurveTrack;
                        NewTrack.GetCurve().UseType = inUseType;

                        AddTrack(NewTrack);
                        MSAPreviewContext.GetInstance().AddSubData(GetTagObject() as PreviewAnimAsset, MSAPreviewContext.AnimSubDataType.CurveTrack, NewTrack.GetCurve().RuntimeCurve);
                    }
                    RenameSender.Close();
                },
                (RenameSender) => RenameSender.Close());
        }

        protected void OnMenuAddCurveClicked(MenuItem Sender)
        {
            CurveUseType UseType = CurveUseType.Common;
            for (var Type = CurveUseType.Common; Type < CurveUseType.Count; ++Type)
            {
                if (Type.ToString() == Sender.GetTagString())
                    UseType = Type;
            }

            AddCurveAccordingToType(UseType);
        }

        protected override void ShowAddTrackMenu()
        {
            Menu TrackMenu = new Menu(GetUIManager());
            TrackMenu.Initialize();

            MenuItem MenuItem_AddCommonCurve = new MenuItem();
            MenuItem_AddCommonCurve.SetText("Add Common Curve");
            MenuItem_AddCommonCurve.SetTagString(CurveUseType.Common.ToString());
            MenuItem_AddCommonCurve.ClickedEvent += OnMenuAddCurveClicked;
            TrackMenu.AddMenuItem(MenuItem_AddCommonCurve);

            MenuItem MenuItem_AddBlendShapeCurve = new MenuItem();
            MenuItem_AddBlendShapeCurve.SetText("Add Blend Shape Curve");
            MenuItem_AddBlendShapeCurve.SetTagString(CurveUseType.BlendShape.ToString());
            MenuItem_AddBlendShapeCurve.ClickedEvent += OnMenuAddCurveClicked;
            TrackMenu.AddMenuItem(MenuItem_AddBlendShapeCurve);

            var Item = _ButtonAddTrack;
            GetUIManager().GetContextMenu().ShowMenu(TrackMenu, Item.GetScreenX(), Item.GetScreenY() + Item.GetHeight());
        }

        public override void Refresh()
        {
            if (!(TagObject is PreviewAnimAsset))
                return;

            FloatCurveList curveSet = new FloatCurveList();
            MSAPreviewContext.GetInstance().GetCurveList(TagObject as PreviewAnimAsset, curveSet);
            ClearTrack();
            foreach (var curveTrack in curveSet.mTracks)
            {
                CurveTrack NewTrack = CreateNewTrack(curveTrack.Name.GetCString()) as CurveTrack;
                //NewTrack.Initialize(_ScaleUI, this, curveTrack.mName.GetCString(), TagObject);
                NewTrack.GetCurve().RuntimeCurve = curveTrack;

                AddTrack(NewTrack);
            }
        }
    }
}
