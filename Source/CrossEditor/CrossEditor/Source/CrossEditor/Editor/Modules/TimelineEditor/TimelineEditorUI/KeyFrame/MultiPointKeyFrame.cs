using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class MultiPointKeyFrame : KeyFrame
    {
        protected float Radius;

        public MultiPointKeyFrame(List<KeyFrame> KeyFrames, Track Owner, decimal KeyValue) :
            base(KeyFrames, Owner, KeyValue)
        {
            UnSelectedColor = Color.FromRGB(255, 0, 0);
            Radius = 5f;
        }

        public override RectangleF GetBound()
        {
            float KeyX = (float)_ScaleUI.ValueToScreenX(KeyValue);
            Control TrackItem = OwnerTrack.GetTrackItem();

            float X = KeyX - Radius;
            float Y = TrackItem.GetScreenY() + TrackItem.GetHeight() / 2f - Radius;
            return new RectangleF(X, Y, Radius, Radius);
        }

        public override void SetKeyValue(decimal NewKeyValue)
        {
            base.SetKeyValue(NewKeyValue);
            List<KeyFrame> KeyFrames = BindObject as List<KeyFrame>;
            foreach (var KeyFrame in KeyFrames) KeyFrame.SetKeyValue(NewKeyValue);
        }

        public override void Draw()
        {
            if (!bEnable) return;

            float KeyX = (float)_ScaleUI.ValueToScreenX(KeyValue);
            Control TrackItem = OwnerTrack.GetTrackItem();
            Vector2f Origin = new Vector2f(KeyX, TrackItem.GetScreenY() + TrackItem.GetHeight() / 2f);

            var Vertices = CurveMathHelper.GetSphere(Origin, Radius, 20);
            Color KeyColor = bSelected ? SelectedColor : UnSelectedColor;

            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillPolygonF(Vertices, ref KeyColor);
        }

        public override void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper GraphicsHelper)
        {
            CumulatedX += (float)DeltaX;

            decimal BeginLoc = _ScaleUI.ValueToScreenX((decimal)BeginX);
            BeginLoc += (decimal)CumulatedX;
            BeginLoc = _ScaleUI.ScreenXToValue(BeginLoc);
            BeginLoc = _ScaleUI.GetNearestValue(BeginLoc);

            SetKeyValue(BeginLoc);

            OnMove(new MoveEvnetArgs());
        }

        public override void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper GraphicsHelper)
        {
            BeginX = GetKeyValue();
            CumulatedX = 0f;

            List<KeyFrame> KeyFrames = BindObject as List<KeyFrame>;
            foreach (var KeyFrame in KeyFrames) KeyFrame.MoveBegin(StartPoint, GraphicsHelper);
        }

        public override void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper GraphicsHelper)
        {
            base.MoveEnd(EndPoint, GraphicsHelper);
            List<KeyFrame> KeyFrames = BindObject as List<KeyFrame>;
            foreach (var KeyFrame in KeyFrames) KeyFrame.MoveEnd(EndPoint, GraphicsHelper);
        }

        public override void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper GraphicsHelper)
        {
            decimal NewKeyValue = _ScaleUI.ScreenXToValue((decimal)DispX);

            KeyValue = NewKeyValue;
            List<KeyFrame> KeyFrames = BindObject as List<KeyFrame>;
            foreach (var KeyFrame in KeyFrames) KeyFrame.SetKeyValue(NewKeyValue);
        }
    }
}
