using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class VectorKeyFrame : SimpleKeyFrame
    {
        protected static float Radius = 5.0f;
        public static new Color UnSelectedColor = Color.White;

        public bool IsSolidFrame
        {
            get
            {
                return (BindObject as ICollection<KeyFrame>).Count == SolidFrameCount;
            }
        }

        public int SolidFrameCount { get; set; }

        protected CurveGraphicsHelper _Drawing = new CurveGraphicsHelper();

        public VectorKeyFrame(object BindObject, Track Owner, decimal KeyValue) : base(BindObject, Owner, KeyValue) { }

        public override RectangleF GetBound()
        {
            float KeyX = (float)_ScaleUI.ValueToScreenX(KeyValue);
            Control TrackItem = OwnerTrack.GetTrackItem();
            return new RectangleF(KeyX - Size, TrackItem.GetScreenY(), Size, TrackItem.GetHeight());
        }

        public bool GetVisible()
        {
            if (KeyValue < _ScaleUI.GetStart() || KeyValue > _ScaleUI.GetEnd())
            {
                return false;
            }
            return true;
        }

        public Vector2f GetCenter()
        {
            float KeyX = (float)_ScaleUI.ValueToScreenX(KeyValue);
            Control TrackItem = OwnerTrack.GetTrackItem();
            return new Vector2f(KeyX, TrackItem.GetScreenY() + TrackItem.GetHeight() / 2);
        }

        public override void Draw()
        {
            if (!bEnable) return;

            Color Color = bSelected ? SelectedColor : UnSelectedColor;

            float KeyX = (float)_ScaleUI.ValueToScreenX(KeyValue);
            Control TrackItem = OwnerTrack.GetTrackItem();

            float radius = 5.0f;

            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawCircleF(
                Color,
                new Vector2f(KeyX, TrackItem.GetScreenY() + TrackItem.GetHeight() / 2.0f),
                radius,
                IsSolidFrame ? radius : 2f);
        }

        public override void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper GraphicsHelper)
        {
            CumulatedX += (float)DeltaX;

            decimal BeginLoc = _ScaleUI.ValueToScreenX((decimal)BeginX);
            BeginLoc += (decimal)CumulatedX;
            BeginLoc = _ScaleUI.ScreenXToValue(BeginLoc);
            BeginLoc = _ScaleUI.GetNearestValue(BeginLoc);

            KeyValue = BeginLoc;
            OnMove(new MoveEvnetArgs());
        }

        public static void DrawBatched(UIManager UIManager, List<KeyFrame> KeyFrames)
        {
            List<VectorKeyFrame> VisibleKeyFrames = new List<VectorKeyFrame>();
            foreach (VectorKeyFrame KeyFrame in KeyFrames)
            {
                if (KeyFrame.bEnable && KeyFrame.GetVisible())
                {
                    VisibleKeyFrames.Add(KeyFrame);
                }
            }

            if (VisibleKeyFrames.Count == 0)
                return;

            List<Color> Colors = new List<Color>();
            List<Vector2f> Centers = new List<Vector2f>();
            List<float> Radiuses = new List<float>();
            List<float> Widths = new List<float>();

            Vector2f LastCenter = new Vector2f();
            for (int i = 0; i < VisibleKeyFrames.Count; i++)
            {
                VectorKeyFrame KeyFrame = VisibleKeyFrames[i];
                Vector2f Center = KeyFrame.GetCenter();
                bool Ellapsed = false;
                if (i != 0 && Center.X - LastCenter.X < Radius)
                {
                    Ellapsed = true;
                }
                if (!Ellapsed)
                {
                    Colors.Add(KeyFrame.bSelected ? SelectedColor : UnSelectedColor);
                    Centers.Add(Center);
                    Radiuses.Add(Radius);
                    Widths.Add(KeyFrame.IsSolidFrame ? Radius : 2f);
                    LastCenter = Center;
                }
            }

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawCirclesF(Colors.ToArray(), Centers.ToArray(), Radiuses.ToArray(), Widths.ToArray());
        }
    }
}
