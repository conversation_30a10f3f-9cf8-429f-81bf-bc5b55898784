using EditorUI;

namespace CrossEditor
{
    public class EntityTrack : UneditableTrack
    {
        protected override void ShowAddTrackMenu()
        {
            Entity Entity = TagObject as Entity;
            Menu TrackMenu = new Menu(GetUIManager());
            TrackMenu.Initialize();

            bool bHasEntity = Entity.Children.Count > 0;
            bool bHasComponent = Entity.Components.Count > 0;

            foreach (Entity Child in Entity.Children)
            {
                MenuItem NewItem = new MenuItem();
                NewItem.SetText(Child.GetName());
                NewItem.ClickedEvent += (ItemSender) =>
                {
                    Track NewTrack = new EntityTrack();
                    NewTrack.Initialize(_ScaleUI, this, Entity.GetName());
                    NewTrack.SetTagObject(Entity);
                    Children.Add(NewTrack);

                    PostAddTrack(NewTrack);
                };

                TrackMenu.AddMenuItem(NewItem);
            }

            if (bHasEntity && bHasComponent)
                TrackMenu.AddSeperator();

            foreach (Component Comp in Entity.Components)
            {
                MenuItem NewItem = new MenuItem();
                NewItem.SetText(Comp.GetType().Name);
                NewItem.ClickedEvent += (ItemSender) =>
                {
                    Track NewTrack = new ComponentTrack();
                    NewTrack.Initialize(_ScaleUI, this, Comp.GetType().Name);
                    NewTrack.SetTagObject(Comp);
                    Children.Add(NewTrack);

                    PostAddTrack(NewTrack);
                };

                TrackMenu.AddMenuItem(NewItem);
            }

            if (bHasEntity || bHasComponent)
            {
                var Item = _ButtonAddTrack;
                GetUIManager().GetContextMenu().ShowMenu(TrackMenu, Item.GetScreenX(), Item.GetScreenY() + Item.GetHeight());
            }
        }
    }
}
