using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Xml.Serialization;

namespace CrossEditor
{
    [XmlRoot]
    public class BuildConfig
    {
        [XmlElement("BuildPlatform")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "BuildPlatform")]
        public AssetPlatform BuildPlatform { set; get; }

        [XmlElement("ClientName")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "ClientName")]
        public string ClientName { set; get; }

        [XmlElement("EnginePath")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Staging")]
        public string Staging
        {
            set => mStaging = PathHelper.ToStandardForm(value);
            get => mStaging;
        }
        private string mStaging;

        [XmlElement("InitWorld")]
        [PropertyInfo(PropertyType = "StringAsFile", ToolTips = "Init World")]
        public string InitWorld
        {
            set
            {
                string editorPath = EditorUtilities.StandardFilenameToEditorFilename(value);
                if (FileHelper.IsFileExists(editorPath))
                {
                    mInitWorld = EditorUtilities.EditorFilenameToStandardFilename(editorPath);
                }
                else
                {
                    mInitWorld = EditorScene.GetInstance().GetCurrentSceneFilename() == "" ? EditorScene.GetInstance().GetTemplateSceneFilename() : EditorScene.GetInstance().GetCurrentSceneFilename();
                }
            }
            get { return mInitWorld; }
        }
        private string mInitWorld;

        [XmlElement("BuildConfiguration")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "BuildConfiguration")]
        public BuildConfiguration BuildConfiguration { set; get; }

        [XmlElement("BuildMode")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Build Mode")]
        public BuildMode BuildMode { set; get; }

        [XmlElement("CookAssetForce")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Force Cook Asset")]
        public bool CookAssetForce { set; get; }

        [XmlElement("CleanUpRedundantFile")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Clean up redundant files")]
        public bool CleanUpRedundantFile { set; get; }

        [XmlElement("CookAssetMaxNum")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Max cook num frame")]
        public int CookAssetMaxNum { set; get; }

        [XmlArray("AssetRequireDirs")]
        [PropertyInfo(PropertyType = "List", ToolTips = "Require Cook dir")]
        public List<string> AssetRequireDirs
        {
            set
            {
                mAssetRequireDirs.Resize(value.Count);
                for (int idx = 0; idx < value.Count; idx++)
                {
                    mAssetRequireDirs[idx] = value[idx] == "" ? "" : EditorUtilities.StandardDirectoryToEditorDirectory(PathHelper.ToStandardForm(value[idx]));
                }
            }
            get { return mAssetRequireDirs; }
        }
        private List<string> mAssetRequireDirs;

        [XmlArray("AssetIgnoreDirs")]
        [PropertyInfo(PropertyType = "List", ToolTips = "Ignore Cook dir")]
        public List<string> AssetIgnoreDirs
        {
            set
            {
                mAssetIgnoreDirs.Resize(value.Count);
                for (int idx = 0; idx < value.Count; idx++)
                {
                    mAssetIgnoreDirs[idx] = value[idx] == "" ? "" : EditorUtilities.StandardDirectoryToEditorDirectory(PathHelper.ToStandardForm(value[idx]));
                }
            }
            get { return mAssetIgnoreDirs; }
        }
        private List<string> mAssetIgnoreDirs;

        [XmlElement("CheckMD5")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Weather Check md5")]
        public bool CheckMD5 { set; get; }

        [XmlElement("DeleteProject")]
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Weather Delete Project")]
        public bool DeleteProject { set; get; }

        public const string GLOBAL_CONFIG_FILENAME = "BuildConfig.config";

        public BuildConfig()
        {
            BuildPlatform = AssetPlatform.WIN;
            ClientName = "CrossGame";
            mStaging = PathHelper.CombinePath(MainUI.GetInstance().GetProjectDirectory(), "Build");
            InitWorld = EditorScene.GetInstance().GetCurrentSceneFilename() == "" ? EditorScene.GetInstance().GetTemplateSceneFilename() : EditorScene.GetInstance().GetCurrentSceneFilename();
            BuildConfiguration = BuildConfiguration.Debug;
            BuildMode = BuildMode.ALL;
            CookAssetForce = false;
            CleanUpRedundantFile = false;
            CookAssetMaxNum = 5;
            mAssetRequireDirs = new List<string>();
            mAssetIgnoreDirs = new List<string>();
            CheckMD5 = true;
            DeleteProject = true;
        }

        public void Load()
        {
            string filePath = PathHelper.CombinePath(MainUI.GetInstance().GetProjectDirectory(), GLOBAL_CONFIG_FILENAME);
            if (!File.Exists(filePath)) return;

            XmlSerializer Ser = new XmlSerializer(typeof(BuildConfig));
            using (StreamReader Reader = new StreamReader(filePath))
            {
                BuildConfig config = (BuildConfig)Ser.Deserialize(Reader);
                BuildPlatform = config.BuildPlatform;
                if (config.ClientName != null) ClientName = config.ClientName;
                if (config.Staging != null) Staging = config.Staging;
                if (config.InitWorld != null) InitWorld = config.InitWorld;
                BuildConfiguration = config.BuildConfiguration;
                BuildMode = config.BuildMode;
                CookAssetForce = config.CookAssetForce;
                CookAssetMaxNum = config.CookAssetMaxNum;
                if (config.AssetRequireDirs != null) AssetRequireDirs = config.AssetRequireDirs;
                if (config.AssetIgnoreDirs != null) AssetIgnoreDirs = config.AssetIgnoreDirs;
                DeleteProject = config.DeleteProject;
                Reader.Close();
            }
        }

        public void Dump()
        {
            string filePath = PathHelper.CombinePath(MainUI.GetInstance().GetProjectDirectory(), GLOBAL_CONFIG_FILENAME);
            XmlSerializer Ser = new XmlSerializer(typeof(BuildConfig));
            using (StreamWriter Writer = new StreamWriter(filePath))
            {
                Ser.Serialize(Writer, this);
                Writer.Close();
            }
        }
    }
}
