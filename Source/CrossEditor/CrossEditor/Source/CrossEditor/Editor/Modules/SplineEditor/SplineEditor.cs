/*using System;
using System.Collections.Generic;
using System.Drawing;

namespace CrossEditor
{
   class SplineEditor
   {
       static SplineEditor Instance;
       private Spline _Spline;
       private Vector3f NewPos;
       private World _World;
       private Dictionary<Vector3f, bool> TangentNode = new Dictionary<Vector3f, bool>();
       private List<Spline> _SplineList = new List<Spline>();
       bool _TestFlag = false;

       public static SplineEditor GetInstance() 
       {
           if (Instance == null)
           {
               Instance = new SplineEditor();
           }
           return Instance;
       }

       public void CreateSpline(World World) 
       {
           if (_Spline == null) 
           {
               _Spline = new Spline();
               _World = World;
               _Spline.Reset();
           }
           Spline CurSpline = new Spline();
           _World = World;
           CurSpline.Reset();
           _SplineList.Add(CurSpline);
       }
       public List<Spline> GetSplines() 
       {
           return _SplineList;
       }
       public void Draw(bool IsEdit) 
       {
           foreach (var _Spline in _SplineList) 
           {
               for(int i = 0; i < _Spline.GetCurves().Count; i++) 
               {
                   DrawBezierSample(_Spline.Curves[i]);
                   Vector3f StartPos = _Spline.Curves[i].N1.Position;
                   Vector3f EndPos = _Spline.Curves[i].N2.Position;
                   Runtime.MSA_DrawPoint(_World.GetNativePointer(), ref StartPos, _Spline.Curves[i].N1._IsSelected ? true : false);
                   Runtime.MSA_DrawPoint(_World.GetNativePointer(), ref EndPos, _Spline.Curves[i].N2._IsSelected ? true : false);

                   if (IsEdit) 
                   {
                       if (_Spline.Curves[i].N1._IsSelected)
                       {
                           Vector3f Direction1 = _Spline.Curves[i].N1.Direction;
                           Vector3f InverseDirection1 = _Spline.Curves[i].N1.Position * 2 -_Spline.Curves[i].N1.Direction;
                           Runtime.MSA_DrawLine(_World.GetNativePointer(), ref StartPos, ref Direction1, true);
                           Runtime.MSA_DrawLine(_World.GetNativePointer(), ref StartPos, ref InverseDirection1, true);
                           Runtime.MSA_DrawPoint(_World.GetNativePointer(), ref Direction1, true);
                           Runtime.MSA_DrawPoint(_World.GetNativePointer(), ref InverseDirection1, false);

                           TangentNode.Clear();
                           TangentNode.Add(Direction1, true);
                           TangentNode.Add(InverseDirection1, false);
                       }
                       if (i == _Spline.GetCurves().Count - 1) 
                       {
                           if (_Spline.Curves[i].N2._IsSelected)
                           {
                               Vector3f Direction2 = _Spline.Curves[i].N2.Direction;
                               Vector3f InverseDirection2 = _Spline.Curves[i].N2.Position * 2 - _Spline.Curves[i].N2.Direction;

                               Runtime.MSA_DrawLine(_World.GetNativePointer(), ref EndPos, ref Direction2, true);
                               Runtime.MSA_DrawLine(_World.GetNativePointer(), ref EndPos, ref InverseDirection2, true);
                               Runtime.MSA_DrawPoint(_World.GetNativePointer(), ref Direction2, true);
                               Runtime.MSA_DrawPoint(_World.GetNativePointer(), ref InverseDirection2, false);
                               TangentNode.Clear();
                               TangentNode.Add(Direction2, true);
                               TangentNode.Add(InverseDirection2, false);
                           }
                       }
                   }
               }
               if (_Spline.Nodes.Count == 1) 
               {
                   Vector3f Pos = _Spline.Nodes[0].Position;
                   Runtime.MSA_DrawPoint(_World.GetNativePointer(), ref Pos, false);
               }
           }
       }

       void DrawBezierSample(CubicBezier3DCurve Curve) 
       {
           int Count = Curve.GetCurveSamples().Count;
           if (Count == 0) 
           {
               return;
           }
           Vector3f OldPos = Curve.GetCurveSamples()[0].Location;
           foreach (var Item in Curve.GetCurveSamples()) 
           {
               NewPos = Item.Location;
               Runtime.MSA_DrawLine(_World.GetNativePointer(), ref OldPos, ref NewPos, false);
               OldPos = NewPos;
           }
       }

       Vector3f GetInverseDirection(Vector3f Pos, Vector3f Dir) 
       {
           return (Pos * 2) - Dir;
       }

       public Dictionary<Vector3f, bool> GetTangentNode() 
       {
           return TangentNode;
       }

       public Vector3f CalcuteTangent(Vector3f Pos) 
       {
           int Number = _SplineList[_SplineList.Count - 1].Nodes.Count;
           Vector3f Value = new Vector3f(0.0f, 0.0f, 0.0f);
           Vector3f Temp;
           switch (Number) 
           {
               case 0:
                   Value = new Vector3f(0.0f, 0.0f, 0.0f);
                   break;
               case 1:
                   Temp = Pos - _SplineList[_SplineList.Count - 1].Nodes[0].Position;
                   _SplineList[_SplineList.Count - 1].Nodes[0].Direction = Temp;
                   Value = _SplineList[_SplineList.Count - 1].Nodes[0].Direction * 2 - _SplineList[_SplineList.Count - 1].Nodes[0].Position;
                   //Value = Temp;
                   break;
               default:
                   Temp = Pos - _SplineList[_SplineList.Count - 1].Nodes[Number - 1].Position;
                   _SplineList[_SplineList.Count - 1].Nodes[Number - 1].Direction = (_SplineList[_SplineList.Count - 1].Nodes[Number - 2].Direction + Temp) / 2; 
                   Value = Temp;
                   break;
           }
           return Value;
       }

       public void Generate() 
       {
           _TestFlag = true;
       }

       public bool GetTestFlag() 
       {
           return _TestFlag;
       }
   }
}
*/