using CEngine;
using Editor<PERSON>;

namespace CrossEditor
{
    public class REDVisualizer : DockingUI
    {
        public static REDVisualizer Instance { get; } = new REDVisualizer();

        Panel _Panel;
        bool _IsPIEPlaying = false;
        bool _EditorSceneVisible = false;
        int _Image;

        public bool Initialize()
        {
            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.LeftMouseDownEvent += (Control sender, int x, int y, ref bool _) => OnMouseButtonEvent(InputButton.Mouse_Left, InputAction.MouseDown);
            _Panel.LeftMouseUpEvent += (Control sender, int x, int y, ref bool _) => OnMouseButtonEvent(InputButton.Mouse_Left, InputAction.MouseUp);
            _Panel.RightMouseDownEvent += (Control sender, int x, int y, ref bool _) => OnMouseButtonEvent(InputButton.Mouse_Right, InputAction.MouseDown);
            _Panel.RightMouseUpEvent += (Control sender, int x, int y, ref bool _) => OnMouseButtonEvent(InputButton.Mouse_Right, InputAction.MouseUp);
            _Panel.MiddleMouseDownEvent += (Control sender, int x, int y, ref bool _) => OnMouseButtonEvent(InputButton.Mouse_Center, InputAction.MouseDown);
            _Panel.MiddleMouseUpEvent += (Control sender, int x, int y, ref bool _) => OnMouseButtonEvent(InputButton.Mouse_Center, InputAction.MouseUp);

            _Panel.MouseMoveEvent += OnPanelMouseMove;
            _Panel.MouseWheelEvent += OnPanelMouseWheel;
            _Panel.PaintEvent += OnPanelPaint;
            _Image = EditorUICanvas.GetInstance().GetUICanvasInterface().CreateREDVisualizerImage();

            base.Initialize(nameof(REDVisualizer), _Panel);
            return true;
        }

        void OnMouseButtonEvent(InputButton btn, InputAction act)
        {
            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            using (var e = new MouseEvent())
            {
                e.mButton = btn;
                e.mAction = act;
                EditorUICanvas.GetUICanvasInterface().OnREDVisualizerMouseEvent(e);
            }
        }
        void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            using (var e = new MouseEvent())
            {
                e.mButton = InputButton.Mouse_None;
                e.mAction = InputAction.MouseMove;
                e.mPoint = new Float2(MouseX - _Panel.GetScreenX(), MouseY - _Panel.GetScreenY());
                EditorUICanvas.GetUICanvasInterface().OnREDVisualizerMouseEvent(e);
            }
        }
        public override void Update(long TimeElapsed)
        {
            if (!EditorScene.GetInstance().IsVisible())
            {
                _EditorSceneVisible = false;
            }
            else
            {
                _EditorSceneVisible = true;
            }
            _IsPIEPlaying = GameScene.GetInstance()._IsPIEPlaying;
            if (_Panel.GetVisible_Recursively())
            {
                if (_IsPIEPlaying)
                {
                    GameScene.GetInstance().GetWorld().SetWorldEnable(true);
                    EditorScene.GetInstance().GetWorld().SetWorldEnable(false);
                }
                else
                {
                    EditorScene.GetInstance().GetWorld().SetWorldEnable(true);
                    if (GameScene.GetInstance().GetWorld() != null)
                        GameScene.GetInstance().GetWorld().SetWorldEnable(false);
                }
            }
            else
            {
                EditorScene.GetInstance().GetWorld().SetWorldEnable(_EditorSceneVisible);
                if (GameScene.GetInstance().GetWorld() != null)
                    GameScene.GetInstance().GetWorld().SetWorldEnable(_IsPIEPlaying);
            }
        }
        void OnPanelMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            using (var e = new MouseEvent())
            {
                e.mButton = InputButton.Mouse_None;
                e.mAction = InputAction.MouseWheel;
                e.mWheelDelta = MouseDeltaZ;
                EditorUICanvas.GetUICanvasInterface().OnREDVisualizerMouseEvent(e);
            }
        }

        void OnPanelPaint(Control Sender)
        {
            UIManager UIManager = Sender.GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.GetUICanvasInterface().OnREDVisualizerPaint(_Image, (uint)_Panel.GetWidth(), (uint)_Panel.GetHeight());
            Color ColorWhite = new Color(1.0f, 1.0f, 1.0f, 1.0f);
            EditorUICanvas.DrawImage(_Image, _Panel.GetScreenX(), _Panel.GetScreenY(), _Panel.GetWidth(), _Panel.GetHeight(), ref ColorWhite);
            Sender.PaintChildren();
        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            UIManager UIManager = GetUIManager();
            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            base.OnClose(Sender, ref bNotToClose);
            EditorUICanvas.GetUICanvasInterface().OnREDVisualizerClosed();
        }
    }
}
