using EditorUI;

namespace CrossEditor
{
    public class WelcomeUI : DockingUI
    {
        const int ITEM_WIDTH = 1000;
        const string String_CreateNewScene = "+ Create New Scene +";

        static WelcomeUI _Instance = new WelcomeUI();

        ScrollView _ScrollView;
        Panel _ScrollPanel;

        public static WelcomeUI GetInstance()
        {
            return _Instance;
        }

        public bool Initialize()
        {
            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.SetSize(500, 500);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            base.Initialize("Welcome", _ScrollView);

            return true;
        }

        public void RefreshUI()
        {
            _ScrollPanel.ClearChildren();

            int X = 10;
            int Y = 10;
            int Step = 30 + 6;

            RecentList RecentList = RecentList.GetInstance();
            RecentList.RemoveDeletedRecentItems();
            int Count = RecentList.GetRecentItemCount();
            for (int i = 0; i < Count; i++)
            {
                string RecentItem = RecentList.GetRecentItem(i);
                AddItemButton(X, Y, RecentItem);
                Y += Step;
            }
            AddItemButton(X, Y, String_CreateNewScene);
            Y += Step;

            _ScrollPanel.SetSize(ITEM_WIDTH, Y);
        }

        int AddItemButton(int X, int Y, string ItemFilename)
        {
            string ItemText = ItemFilename;
            if (ItemFilename != String_CreateNewScene)
            {
                string ItemName = PathHelper.GetNameOfPath(ItemFilename);
                ItemText = string.Format("{0} - ({1})", ItemName, ItemFilename);
            }

            Button ButtonItem = new Button();
            ButtonItem.Initialize();
            ButtonItem.SetPosition(X, Y, ITEM_WIDTH - 2 * X, 30);
            ButtonItem.SetFontSize(16);
            ButtonItem.SetTextAlign(TextAlign.CenterLeft);
            ButtonItem.SetTextOffsetX(8);
            ButtonItem.SetTextOffsetY(3);
            ButtonItem.SetTagString1(ItemFilename);
            ButtonItem.SetDownColor(Color.EDITOR_UI_MENU_BACK_COLOR);
            ButtonItem.ClickedEvent += OnButtonItemClicked;
            _ScrollPanel.AddChild(ButtonItem);

            int OffsetX = 20;

            Label LabelItem = new Label();
            LabelItem.Initialize();
            LabelItem.SetPosition(OffsetX, 5, ButtonItem.GetWidth(), 20);
            LabelItem.SetText(ItemText);
            LabelItem.SetTextAlign(TextAlign.CenterLeft);
            LabelItem.SetFontSize(16);
            ButtonItem.AddChild(LabelItem);

            Font Font = GetUIManager().GetDefaultFont(16);
            int ItemWidth = Font.MeasureString_Fast(ItemFilename) + 50;

            return ItemWidth;
        }

        void OnButtonItemClicked(Button Sender)
        {
            MainUI MainUI = MainUI.GetInstance();
            EditorScene EditorScene = EditorScene.GetInstance();
            string ItemFilename = Sender.GetTagString1();
            string ItemFilename1 = EditorUtilities.StandardFilenameToEditorFilename(ItemFilename);
            if (ItemFilename == String_CreateNewScene)
            {
                MainUI.OnMenuItemFileNewSceneClicked(null);
                return;
            }
            OpenScene(ItemFilename1);
        }

        public void OpenLastScene()
        {

            bool bOpenLastScene = ProjectsUI.GetInstance().GetOpenLastScene();
            string LastScenePath = "";
            if (bOpenLastScene)
            {
                RecentList RecentList = RecentList.GetInstance();
                RecentList.RemoveDeletedRecentItems();
                int Count = RecentList.GetRecentItemCount();
                if (Count >= 1)
                {
                    string RecentItem = RecentList.GetRecentItem(0);
                    string RecentItem1 = EditorUtilities.StandardFilenameToEditorFilename(RecentItem);
                    if (FileHelper.IsFileExists(RecentItem1))
                    {
                        LastScenePath = RecentItem1;
                    }
                }
            }
            MainUI.GetInstance().NewScene();

        }

        public void OpenScene(string Path)
        {
            MainUI MainUI = MainUI.GetInstance();
            if (FileHelper.IsFileExists(Path) == false)
            {
                return;
            }
            string Extension = PathHelper.GetExtension(Path);
            if (StringHelper.IgnoreCaseEqual(Extension, ".world"))
            {
                MainUI.GeneralOpenScene(Path);
            }
        }
    }
}
