using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;

namespace CrossEditor
{
    public delegate void ResourceSelectCallback(string Resource);

    public class ResourceFilterUI
    {
        static ResourceFilterUI _Instance = new ResourceFilterUI();

        BackgroundMask _BackgroundMask;
        Panel _Panel;

        Edit _EditPattern;

        ScrollView _ScrollView;
        Panel _ScrollPanel;

        ClassIDType _ObjectClassID1;
        ClassIDType _ObjectClassID2;
        ClassIDType _ObjectClassID3;
        HashSet<string> _ResourceList;
        List<string> _FilteredResourceList;

        ResourceSelectCallback _ResourceSelectCallback;

        bool _AsyncCollectionReady;
        ManualResetEvent _CollectionThreadEvent;
        Thread _CollectingThread;

        FileSystemWatcher _ContentsDirWatcher;
        FileSystemWatcher _EngineResourceDirWatcher;
        FileSystemWatcher _PipelineResourceDirWatcher;

        public static ResourceFilterUI GetInstance()
        {
            return _Instance;
        }

        ResourceFilterUI()
        {
            _ResourceList = new HashSet<string>();
            _FilteredResourceList = new List<string>();

            _AsyncCollectionReady = false;
            _CollectionThreadEvent = new ManualResetEvent(true);
            _CollectingThread = new Thread(AsyncUpdateResourceList);

            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ContentsDirectory = ProjectDirectory + "/Contents";
            _ContentsDirWatcher = new FileSystemWatcher(ContentsDirectory);
            AddFilters(ref _ContentsDirWatcher);

            _ContentsDirWatcher.NotifyFilter = NotifyFilters.CreationTime
                                 | NotifyFilters.DirectoryName
                                 | NotifyFilters.FileName;

            _ContentsDirWatcher.Created += OnFileCreated;
            _ContentsDirWatcher.Deleted += OnFileDeleted;
            _ContentsDirWatcher.Renamed += OnFileRenamed;
            _ContentsDirWatcher.IncludeSubdirectories = true;
            _ContentsDirWatcher.EnableRaisingEvents = true;

            string ResourceDirectory = EditorUtilities.GetResourceDirectory();

            string EngineResourceDirectory = ResourceDirectory + "/EngineResource";
            _EngineResourceDirWatcher = new FileSystemWatcher(EngineResourceDirectory);
            AddFilters(ref _EngineResourceDirWatcher);

            _EngineResourceDirWatcher.Created += OnFileCreated;
            _EngineResourceDirWatcher.Deleted += OnFileDeleted;
            _EngineResourceDirWatcher.Renamed += OnFileRenamed;
            _EngineResourceDirWatcher.IncludeSubdirectories = true;
            _EngineResourceDirWatcher.EnableRaisingEvents = true;

            string PipelineResourceDirectory = ResourceDirectory + "/PipelineResource";
            _PipelineResourceDirWatcher = new FileSystemWatcher(PipelineResourceDirectory);
            AddFilters(ref _PipelineResourceDirWatcher);

            _PipelineResourceDirWatcher.Created += OnFileCreated;
            _PipelineResourceDirWatcher.Deleted += OnFileDeleted;
            _PipelineResourceDirWatcher.Renamed += OnFileRenamed;
            _PipelineResourceDirWatcher.IncludeSubdirectories = true;
            _PipelineResourceDirWatcher.EnableRaisingEvents = true;
        }

        private void AddFilters(ref FileSystemWatcher watcher)
        {
            foreach (var filter in ResourceTypeCache.GetInstance().FilterIncludeList)
            {
                watcher.Filters.Add(filter);
            }
        }

        private void OnFileCreated(object sender, FileSystemEventArgs e)
        {
            string Path = e.FullPath.Replace("\\", "/");
            _ResourceList.Add(Path);
        }

        private void OnFileDeleted(object sender, FileSystemEventArgs e)
        {
            string Path = e.FullPath.Replace("\\", "/");
            _ResourceList.Remove(Path);
        }

        private void OnFileRenamed(object sender, RenamedEventArgs e)
        {
            string OldPath = e.OldFullPath.Replace("\\", "/");
            _ResourceList.Remove(OldPath);
            string FullPath = e.FullPath.Replace("\\", "/");
            _ResourceList.Add(FullPath);
        }

        public ref Thread GetCollectingThread()
        {
            return ref _CollectingThread;
        }

        public void SuspendCollectingThread()
        {
            if (_AsyncCollectionReady)
            {
                return;
            }

            _CollectionThreadEvent.Reset();
        }

        public void ResumeCollectingThread()
        {
            if (_AsyncCollectionReady)
            {
                return;
            }

            _CollectionThreadEvent.Set();
        }

        public UIManager GetUIManager()
        {
            if (_Panel != null)
            {
                return _Panel.GetUIManager();
            }
            else
            {
                return UIManager.GetActiveUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        void InitializeUI(UIManager UIManager)
        {
            Control Root = UIManager.GetRoot();

            _BackgroundMask = new BackgroundMask(UIManager);
            _BackgroundMask.Mask(OnBackgroundMaskClose);

            int Width = 800;

            _Panel = new Panel();
            _Panel.SetBackgroundColor(Color.EDITOR_UI_MENU_BACK_COLOR);
            _Panel.SetBorderColor(Color.EDITOR_UI_MENU_HILIGHT_COLOR);
            _Panel.SetSize(Width, 400);
            _Panel.LeftMouseDownEvent += OnPanelLeftMouseDown;
            _Panel.MouseMoveEvent += OnPanelMouseMove;
            _Panel.SetVisible(false);
            Root.AddChild(_Panel);

            _EditPattern = new Edit();
            _EditPattern.SetFontSize(18);
            _EditPattern.Initialize(EditMode.Simple_SingleLine);
            _EditPattern.LoadSource("");
            _EditPattern.CharInputedEvent += OnEditPatternCharInpupted;
            _EditPattern.KeyDownEvent += OnEditPatternKeyDown;
            _Panel.AddChild(_EditPattern);
            EditContextUI.GetInstance().RegisterEdit(_EditPattern);
            _EditPattern.SetPosition(5, 5, Width - 10, 18);
            _EditPattern.SetFocus();

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.SetPosition(5, 28, Width - 10, 367);
            _Panel.AddChild(_ScrollView);

            _ScrollPanel = _ScrollView.GetScrollPanel();

            Background.SuspendBackgroundThreads();
            {
                FilterResourceList();
                UpdateResourceListUI();
            }
            Background.ResumeBackgroundThreads();
        }

        void CollectResources(string Directory)
        {
            ResourceTypeCache TypeCache = ResourceTypeCache.GetInstance();
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(Directory, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string EditorFilename = DirectoryWalkItem.Path;
                    if (EditorFilename.Contains(".fx.nda"))
                    {
                        Console.WriteLine();
                    }
                    string Extension = PathHelper.GetExtension(EditorFilename);
                    var FilterInclude = ResourceTypeCache.GetInstance().FilterIncludeList;
                    if (FilterInclude.Contains(Extension.ToLower()))
                    {
                        if (EditorFilename.Contains("EngineResource/Editor/") == false)
                        {
                            TypeCache.GetResourceType_Cache(EditorFilename);
                            _ResourceList.Add(EditorFilename);
                        }
                    }
                }
            }
        }

        void AsyncCollectResources(string Directory)
        {
            ResourceTypeCache TypeCache = ResourceTypeCache.GetInstance();
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(Directory, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string EditorFilename = DirectoryWalkItem.Path;
                    if (EditorFilename.Contains(".fx.nda"))
                    {
                        Console.WriteLine();
                    }
                    string Extension = PathHelper.GetExtension(EditorFilename);
                    var FilterInclude = ResourceTypeCache.GetInstance().FilterIncludeList;
                    if (FilterInclude.Contains(Extension.ToLower()))
                    {
                        if (EditorFilename.Contains("EngineResource/Editor/") == false)
                        {
                            _CollectionThreadEvent.WaitOne();
                            TypeCache.GetResourceType_Cache(EditorFilename);
                            _ResourceList.Add(EditorFilename);
                        }
                    }
                }
            }
        }

        void UpdateResourceList()
        {
            _ResourceList.Clear();

            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ContentsDirectory = ProjectDirectory + "/Contents";
            CollectResources(ContentsDirectory);

            string ResourceDirectory = EditorUtilities.GetResourceDirectory();

            string EngineResourceDirectory = ResourceDirectory + "/EngineResource";
            CollectResources(EngineResourceDirectory);

            string PipelineResourceDirectory = ResourceDirectory + "/PipelineResource";
            CollectResources(PipelineResourceDirectory);
        }

        public void AsyncUpdateResourceList()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ContentsDirectory = ProjectDirectory + "/Contents";
            AsyncCollectResources(ContentsDirectory);

            string ResourceDirectory = EditorUtilities.GetResourceDirectory();

            string EngineResourceDirectory = ResourceDirectory + "/EngineResource";
            AsyncCollectResources(EngineResourceDirectory);

            string PipelineResourceDirectory = ResourceDirectory + "/PipelineResource";
            AsyncCollectResources(PipelineResourceDirectory);

            _AsyncCollectionReady = true;
        }

        void FilterResourceList()
        {
            _FilteredResourceList.Clear();
            string Pattern = _EditPattern.GetText();
            if (_AsyncCollectionReady)
            {
                foreach (string EditorFilename in _ResourceList)
                {
                    ResourceTypeCache TypeCache = ResourceTypeCache.GetInstance();
                    ClassIDType ObjectClassID = TypeCache.GetCacheState() ? TypeCache.GetResourceType_CacheOnly(EditorFilename) : TypeCache.GetResourceType_Cache(EditorFilename);
                    if (EditorUtilities.IsSpecificAsset(ObjectClassID, _ObjectClassID1) ||
                        EditorUtilities.IsSpecificAsset(ObjectClassID, _ObjectClassID2) ||
                        EditorUtilities.IsSpecificAsset(ObjectClassID, _ObjectClassID3))
                    {
                        string ResourceName = PathHelper.GetNameOfPath(EditorFilename);
                        if (Pattern == "" ||
                            ResourceName.Contains(Pattern, StringComparison.OrdinalIgnoreCase))
                        {
                            _FilteredResourceList.Add(EditorFilename);
                        }
                    }
                }
            }
            else
            {
                string[] resList = _ResourceList.ToArray();
                foreach (string EditorFilename in resList)
                {
                    ResourceTypeCache TypeCache = ResourceTypeCache.GetInstance();
                    ClassIDType ObjectClassID = TypeCache.GetCacheState() ? TypeCache.GetResourceType_CacheOnly(EditorFilename) : TypeCache.GetResourceType_Cache(EditorFilename);
                    if (EditorUtilities.IsSpecificAsset(ObjectClassID, _ObjectClassID1) ||
                        EditorUtilities.IsSpecificAsset(ObjectClassID, _ObjectClassID2) ||
                        EditorUtilities.IsSpecificAsset(ObjectClassID, _ObjectClassID3))
                    {
                        string ResourceName = PathHelper.GetNameOfPath(EditorFilename);
                        if (Pattern == "" ||
                            ResourceName.Contains(Pattern, StringComparison.OrdinalIgnoreCase))
                        {
                            _FilteredResourceList.Add(EditorFilename);
                        }
                    }
                }
            }
        }

        void UpdateResourceListUI()
        {
            _ScrollPanel.ClearChildren();
            int Y = 3;
            for (int i = 0; i < _FilteredResourceList.Count; i++)
            {
                string EditorFilename = _FilteredResourceList[i];
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                string ResourceName = PathHelper.GetNameOfPath(StandardFilename);

                int Width = _ScrollView.GetWidth();

                Button ButtonResource = new Button();
                ButtonResource.Initialize();
                ButtonResource.SetPosition(0, Y, Width, 64);
                ButtonResource.SetFontSize(16);
                ButtonResource.SetTextAlign(TextAlign.CenterLeft);
                ButtonResource.SetTagString1(StandardFilename);
                ButtonResource.SetDownColor(Color.EDITOR_UI_MENU_BACK_COLOR);
                ButtonResource.ClickedEvent += OnButtonResourceClicked;
                //ButtonResource.RightMouseUpEvent += OnButtonResourceRightMouseUp;
                _ScrollPanel.AddChild(ButtonResource);

                Panel PanelResource = new Panel();
                PanelResource.SetBackgroundColor(new Color(0.3f, 0.3f, 0.3f, 1.0f));
                PanelResource.SetTagString1(StandardFilename);
                PanelResource.PaintEvent += (Sender) =>
                {
                    if (Sender.GetImage() == null)
                    {
                        Texture ThumbnailTexture = ThumbnailHelper.GetInstance().GetThumbnailTexture_CacheOnly(EditorFilename);
                        Sender.SetImage(ThumbnailTexture);
                    }
                    Sender.PaintThis();
                };
                PanelResource.SetPosition(0, Y, 64, 64);
                _ScrollPanel.AddChild(PanelResource);

                int X1 = 64 + 3;
                int Width1 = Width - X1 - 3;

                Label LabelResourceName = new Label();
                LabelResourceName.Initialize();
                LabelResourceName.SetPosition(X1, Y + 16, Width1, 28);
                LabelResourceName.SetText(ResourceName);
                LabelResourceName.SetTextAlign(TextAlign.CenterLeft);
                LabelResourceName.SetFontSize(18);
                _ScrollPanel.AddChild(LabelResourceName);

                Label LabelResourceFilename = new Label();
                LabelResourceFilename.Initialize();
                LabelResourceFilename.SetPosition(X1, Y + 38, Width1, 24);
                LabelResourceFilename.SetText(StandardFilename);
                LabelResourceFilename.SetTextAlign(TextAlign.CenterLeft);
                LabelResourceFilename.SetFontSize(14);
                _ScrollPanel.AddChild(LabelResourceFilename);

                Y += 64 + 3;
            }
            _ScrollPanel.SetSize(_ScrollView.GetWidth() - 22, Y);
            _ScrollView.UpdateScrollBar();
        }

        public void ShowUI(UIManager UIManager, ClassIDType ObjectClassID1, ClassIDType ObjectClassID2, ClassIDType ObjectClassID3, int X, int Y, int Width, int Height, ResourceSelectCallback ResourceSelectCallback)
        {
            _ObjectClassID1 = ObjectClassID1;
            _ObjectClassID2 = ObjectClassID2;
            _ObjectClassID3 = ObjectClassID3;
            _ResourceSelectCallback = ResourceSelectCallback;

            int X1 = X;
            int Y1 = Y + 1;
            int X2 = X + Width;
            int Y2 = Y + Height - 1;
            int MenuX = 0;
            int MenuY = 0;

            Control Root = GetUIManager().GetRoot();

            InitializeUI(UIManager);

            int MenuWidth = _Panel.GetWidth();
            int MenuHeight = _Panel.GetHeight();

            int RootWidth = Root.GetWidth();
            int RootHeight = Root.GetHeight();
            MenuX = X1;
            if (X1 + MenuWidth > RootWidth)
            {
                if (X2 - MenuWidth >= 0)
                {
                    MenuX = X2 - MenuWidth;
                }
                else
                {
                    MenuX = RootWidth - MenuWidth;
                }
                if (MenuX < 0)
                {
                    MenuX = 0;
                }
            }
            MenuY = Y2;
            if (Y2 + MenuHeight > RootHeight)
            {
                if (Y1 - MenuHeight >= 0)
                {
                    MenuY = Y1 - MenuHeight;
                }
                else
                {
                    MenuY = RootHeight - MenuHeight;
                }
                if (MenuY < 0)
                {
                    MenuY = 0;
                }
            }

            _Panel.SetPos(MenuX, MenuY);
            _Panel.SetVisible(true);
        }

        public void ShowUI(UIManager UIManager, ClassIDType ObjectClassID1, ClassIDType ObjectClassID2, ClassIDType ObjectClassID3, Control Control, ResourceSelectCallback ResourceSelectCallback)
        {
            int X = Control.GetScreenX();
            int Y = Control.GetScreenY();
            int Width = Control.GetWidth();
            int Height = Control.GetHeight();
            ShowUI(UIManager, ObjectClassID1, ObjectClassID2, ObjectClassID3, X, Y, Width, Height, ResourceSelectCallback);
        }

        public void HideUI()
        {
            OperationQueue.GetInstance().AddOperation(() =>
            {
                _BackgroundMask.Close();
                if (_Panel != null)
                {
                    UIManager UIManager = GetUIManager();
                    Control Root = UIManager.GetRoot();
                    _Panel.SetVisible(false);
                    Root.RemoveChild(_Panel);
                    _Panel = null;
                }
            });
        }

        public bool GetVisible()
        {
            if (_Panel != null)
            {
                return _Panel.GetVisible();
            }
            return false;
        }

        void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                bContinue = false;
            }
        }

        void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY))
            {
                bContinue = false;
            }
        }

        void OnEditPatternCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            FilterResourceList();
            UpdateResourceListUI();
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && (Char == '\r' || Char == '\n'))
            {
                if (_FilteredResourceList.Count == 1)
                {
                    string ResourceFilename = _FilteredResourceList[0];
                    TriggerSelection(ResourceFilename);
                }
            }
        }

        void OnEditPatternKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && Key == Key.Escape)
            {
                HideUI();
            }
        }

        void OnButtonResourceClicked(Button Sender)
        {
            Button ButtonResource = Sender;
            string ResourceFilename = ButtonResource.GetTagString1();
            TriggerSelection(ResourceFilename);
        }

        void TriggerSelection(string ResourceFilename)
        {
            if (ResourceFilename != "")
            {
                if (_ResourceSelectCallback != null)
                {
                    _ResourceSelectCallback(ResourceFilename);
                }
            }
            HideUI();
        }

        void OnBackgroundMaskClose(BackgroundMask Sender)
        {
            HideUI();
        }
    }
}