using EditorUI;

namespace CrossEditor
{
    public class ConsoleUI : DockingUI
    {
        static ConsoleUI _Instance = new ConsoleUI();

        VContainer _VContainer1;
        OperationBarUI _OperationBarUI;
        //Button _ButtonClear;
        //Button _ButtonClearOnPlay;
       // Button _ButtonCollapse;
        Button _ButtonErrorPause;
        Button _ButtonInformations;
        Button _ButtonWarnings;
        Button _ButtonErrors;
        Button _ButtonDebug;
        SearchUI _SearchUI;
        VSplitter _VSplitter;
        LogView _LogView;
        Edit _Edit;
        Edit _EditInputCommand;
        Panel _PanelStatusBarTips;
        Label _LabelInformations;
        Label _LabelWarnings;
        Label _LabelErrors;
        Label _LabelDebug;

        private string _LastCmdName = "";

        public static ConsoleUI GetInstance()
        {
            return _Instance;
        }

        public ConsoleUI()
        {
        }

        public bool Initialize()
        {
            _VContainer1 = new VContainer();
            _VContainer1.Initialize();
            _VContainer1.SetSize(500, 300);

            _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();
            _OperationBarUI.SetBarHeight(55);

            //_ButtonClear = OperationBarUI.CreateTextButton("Clear");
            //_ButtonClear.ClickedEvent += OnButtonClearClicked;
            //_OperationBarUI.AddLeft(_ButtonClear);

            //_ButtonClearOnPlay = OperationBarUI.CreateTextButton("Clear On Play");
            //_ButtonClearOnPlay.SetBorderChecked(true);
            //_ButtonClearOnPlay.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);
            //_ButtonClearOnPlay.ClickedEvent += OnButtonClearOnPlayClicked;
            //_OperationBarUI.AddLeft(_ButtonClearOnPlay);

            //_ButtonCollapse = OperationBarUI.CreateTextButton("Collapse");
            //_ButtonCollapse.SetBorderChecked(true);
            //_ButtonCollapse.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);
            //_ButtonCollapse.ClickedEvent += OnButtonCollapseClicked;
            //_OperationBarUI.AddLeft(_ButtonCollapse);

            _ButtonErrorPause = OperationBarUI.CreateTextButton("Error Pause");
            _ButtonErrorPause.SetBorderChecked(true);
            _ButtonErrorPause.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);
            _ButtonErrorPause.ClickedEvent += OnButtonErrorPauseClicked;
            //_OperationBarUI.AddLeft(_ButtonErrorPause);

            _EditInputCommand = new Edit();
            _EditInputCommand.SetFontSize(16);
            _EditInputCommand.Initialize(EditMode.Simple_SingleLine);
            _EditInputCommand.LoadSource("");
            _EditInputCommand.KeyDownEvent += OnEditInputCommandKeyDownEvent;
            _OperationBarUI.AddLeft(_EditInputCommand);
            _EditInputCommand.SetBackgroundColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            _EditInputCommand.SetHeight(OperationBarUI.BAR_HEIGHT - 2);
            _EditInputCommand.SetPosition(0, 1, 1000, OperationBarUI.BAR_HEIGHT - 2);
            EditContextUI.GetInstance().RegisterEdit(_EditInputCommand);

            _ButtonInformations = OperationBarUI.CreateTextButton("Informations");
            _ButtonInformations.SetBorderChecked(true);
            _ButtonInformations.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);
            _ButtonInformations.ClickedEvent += OnButtonInformationsClicked;
            _OperationBarUI.AddRight(_ButtonInformations);

            _ButtonWarnings = OperationBarUI.CreateTextButton("Warnings");
            _ButtonWarnings.SetBorderChecked(true);
            _ButtonWarnings.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);
            _ButtonWarnings.ClickedEvent += OnButtonWarningsClicked;
            _OperationBarUI.AddRight(_ButtonWarnings);

            _ButtonErrors = OperationBarUI.CreateTextButton("Errors");
            _ButtonErrors.SetBorderChecked(true);
            _ButtonErrors.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);
            _ButtonErrors.ClickedEvent += OnButtonErrorsClicked;
            _OperationBarUI.AddRight(_ButtonErrors);
            _ButtonDebug = OperationBarUI.CreateTextButton("Debugs");
            _ButtonDebug.SetBorderChecked(true);
            _ButtonDebug.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_BLUE);
            _ButtonDebug.ClickedEvent += OnButtonDebugClicked;
            _OperationBarUI.AddRight(_ButtonDebug);

            _SearchUI = new SearchUI();
            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 230, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);

            _OperationBarUI.Refresh();

            _VSplitter = new VSplitter();
            _VSplitter.Initialize();
            _VContainer1.AddSizableChild(_VSplitter, 1.0f);

            _LogView = new LogView();
            _LogView.Initialize();
            _LogView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _LogView.SetPosition(0, 0, 500, 200);
            _LogView.ItemSelectedEvent = OnLogViewItemSelected;
            _LogView.KeyDownEvent += OnLogViewKeyDown;
            _LogView.JumpFileEvent = OnLogViewJumpFile;
            _VSplitter.AddChild(_LogView);
            _LogView.UpdateScrollBar();
            _LogView.SetCollapse(false);
            UpdateCheckStateToLogView();

            _Edit = new Edit();
            _Edit.SetFontSize(18);
            _Edit.Initialize(EditMode.Code_Txt);
            _Edit.SetReadOnly(true);
            _Edit.SetHideLineModifyState(true);
            _Edit.LoadSource("");
            _Edit.LineDoubleClickedEvent += OnEditLineDoubleClicked;
            EditContextUI.GetInstance().RegisterEdit(_Edit);
            _VSplitter.AddChild(_Edit);
            _Edit.SetPosition(0, 0, 500, 50);
            _Edit.SetBackgroundColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            _VSplitter.SetPosition(0, 0, 500, 400);
            _VContainer1.AddChild(_OperationBarUI.GetPanelBar());
            FrameUI FrameUI = GetFrameUI();

            Label LabelStatusBar1 = FrameUI.GetLabelStatusBar1();
            LabelStatusBar1.LeftMouseDoubleClickedEvent += OnLabelStatusBar1LeftMouseDoubleClicked;

            Label LabelStatusBar2 = FrameUI.GetLabelStatusBar2();
            LabelStatusBar2.LeftMouseDoubleClickedEvent += OnLabelStatusBar2LeftMouseDoubleClicked;

            Panel PanelStatusBar = FrameUI.GetPanelStatusBar();

            _PanelStatusBarTips = new Panel();
            _PanelStatusBarTips.Initialize();
            _PanelStatusBarTips.SetPosition(0, 0, 240, 25);
            PanelStatusBar.AddChild(_PanelStatusBarTips);

            Panel PanelInformations = new Panel();
            PanelInformations.Initialize();
            PanelInformations.SetImage(UIManager.LoadUIImage("Editor/Log/InformationSmall.png"));
            PanelInformations.SetPosition(0, 5, 16, 25);
            _PanelStatusBarTips.AddChild(PanelInformations);

            _LabelInformations = new Label();
            _LabelInformations.Initialize();
            _LabelInformations.SetTextAlign(TextAlign.CenterLeft);
            _LabelInformations.SetText("0");
            _LabelInformations.SetFontSize(16);
            _LabelInformations.SetTextOffsetY(2);
            _LabelInformations.SetTextColor(Color.FromRGB(255, 255, 255));
            _LabelInformations.SetPosition(20, 5, 100, 25);
            _PanelStatusBarTips.AddChild(_LabelInformations);

            Panel PanelWarnings = new Panel();
            PanelWarnings.Initialize();
            PanelWarnings.SetImage(UIManager.LoadUIImage("Editor/Log/WarningSmall.png"));
            PanelWarnings.SetPosition(80, 5, 16, 16);
            _PanelStatusBarTips.AddChild(PanelWarnings);

            _LabelWarnings = new Label();
            _LabelWarnings.Initialize();
            _LabelWarnings.SetTextAlign(TextAlign.CenterLeft);
            _LabelWarnings.SetText("0");
            _LabelWarnings.SetFontSize(16);
            _LabelWarnings.SetTextOffsetY(2);
            _LabelWarnings.SetTextColor(Color.FromRGB(255, 255, 0));
            _LabelWarnings.SetPosition(80 + 20, 5, 100, 25);
            _PanelStatusBarTips.AddChild(_LabelWarnings);

            Panel PanelErrors = new Panel();
            PanelErrors.Initialize();
            PanelErrors.SetImage(UIManager.LoadUIImage("Editor/Log/ErrorSmall.png"));
            PanelErrors.SetPosition(160, 5, 16, 16);
            _PanelStatusBarTips.AddChild(PanelErrors);

            _LabelErrors = new Label();
            _LabelErrors.Initialize();
            _LabelErrors.SetTextAlign(TextAlign.CenterLeft);
            _LabelErrors.SetText("0");
            _LabelErrors.SetFontSize(16);
            _LabelErrors.SetTextOffsetY(2);
            _LabelErrors.SetTextColor(Color.FromRGB(255, 0, 0));
            _LabelErrors.SetPosition(160 + 20, 5, 100, 25);

            _LabelDebug = new Label();
            _LabelDebug.Initialize();
            _LabelDebug.SetTextAlign(TextAlign.CenterLeft);
            _LabelDebug.SetText("0");
            _LabelDebug.SetFontSize(16);
            _LabelDebug.SetTextOffsetY(2);
            _LabelDebug.SetTextColor(Color.FromRGB(255, 0, 0));
            _LabelDebug.SetPosition(240 + 20, 5, 100, 25);
            _PanelStatusBarTips.AddChild(_LabelDebug);

            UpdateLogCounters();

            base.Initialize("Output", _VContainer1);
            return true;
        }

        public void OnDeviceResize(int Width, int Height)
        {
            FrameUI FrameUI = GetFrameUI();
            Panel PanelStatusBar = FrameUI.GetPanelStatusBar();

            _PanelStatusBarTips.SetX(PanelStatusBar.GetWidth() - _PanelStatusBarTips.GetWidth());
        }

        public void ClearAll()
        {
            _LogView.ClearLogViewItems();
            if (_Edit != null)
            {
                _Edit.LoadSource("");
                _Edit.LocateToHome();
            }
            UpdateLogCounters();
        }

        void OnButtonClearClicked(Button Sender)
        {
            ClearAll();
        }

        public void ClearOnPlayIfNeeded()
        {

        }

        void OnButtonClearOnPlayClicked(Button Sender)
        {
            //bool bChecked = _ButtonClearOnPlay.GetBorderChecked();
           // _ButtonClearOnPlay.SetBorderChecked(!bChecked);
            //Sender.SetNormalColor(!bChecked ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color.EDITOR_UI_COLOR_KEY);
        }
        void OnButtonDebugClicked(Button Sender)
        {
            bool bChecked = _ButtonDebug.GetBorderChecked();
            bool bNewChecked = !bChecked;
            _LogView.SetShowDebug(bNewChecked);
            _LogView.DoFilter();
            _ButtonDebug.SetBorderChecked(bNewChecked);
            Sender.SetNormalColor(bNewChecked ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color.EDITOR_UI_COLOR_KEY);
        }

        void OnButtonCollapseClicked(Button Sender)
        {
            //bool bChecked = _ButtonCollapse.GetBorderChecked();
            //bool bNewChecked = !bChecked;
            //_LogView.SetCollapse(bNewChecked);
            //_ButtonCollapse.SetBorderChecked(bNewChecked);
            //Sender.SetNormalColor(bNewChecked ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color.EDITOR_UI_COLOR_KEY);
        }

        void OnButtonErrorPauseClicked(Button Sender)
        {
            bool bChecked = _ButtonErrorPause.GetBorderChecked();
            bool bNewChecked = !bChecked;
            _ButtonErrorPause.SetBorderChecked(bNewChecked);
            Sender.SetNormalColor(bNewChecked ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color.EDITOR_UI_COLOR_KEY);
        }

        void OnButtonInformationsClicked(Button Sender)
        {
            bool bChecked = _ButtonInformations.GetBorderChecked();
            bool bNewChecked = !bChecked;
            _LogView.SetShowInformations(bNewChecked);
            _LogView.DoFilter();
            _ButtonInformations.SetBorderChecked(bNewChecked);
            Sender.SetNormalColor(bNewChecked ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color.EDITOR_UI_COLOR_KEY);
        }

        void OnButtonWarningsClicked(Button Sender)
        {
            bool bChecked = _ButtonWarnings.GetBorderChecked();
            bool bNewChecked = !bChecked;
            _LogView.SetShowWarnings(bNewChecked);
            _LogView.DoFilter();
            _ButtonWarnings.SetBorderChecked(bNewChecked);
            Sender.SetNormalColor(bNewChecked ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color.EDITOR_UI_COLOR_KEY);
        }

        void OnButtonErrorsClicked(Button Sender)
        {
            bool bChecked = _ButtonErrors.GetBorderChecked();
            bool bNewChecked = !bChecked;
            _LogView.SetShowErrors(bNewChecked);
            _LogView.DoFilter();
            _ButtonErrors.SetBorderChecked(bNewChecked);
            Sender.SetNormalColor(bNewChecked ? Color.EDITOR_UI_HILIGHT_COLOR_BLUE : Color.EDITOR_UI_COLOR_KEY);
        }

        void UpdateSearchPattern()
        {
            string SearchPattern = _SearchUI.GetSearchPattern();
            _LogView.SetPattern(SearchPattern);
            UpdateLogCounters();
        }

        void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            UpdateSearchPattern();
        }

        void OnSearchUICancel(SearchUI Sender)
        {
            UpdateSearchPattern();
        }

        void OnLogViewItemSelected(LogView Sender)
        {
            LogViewItem SelectedItem = Sender.GetSelectedItem();
            if (SelectedItem != null)
            {
                _Edit.SetText(SelectedItem.LogMessage);
            }
        }

        void OnLogViewKeyDown(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && Key == Key.Escape)
            {
                _SearchUI.ClearSearchPattern();
                _SearchUI.TriggerCancelEvent();
            }
        }

        void MarkJumpableLines()
        {
            int LineCount = _Edit.GetLineCount();
            for (int i = 0; i < LineCount; i++)
            {
                EditLine Line = _Edit.GetLine(i);
                if (Line != null)
                {
                    Line.UpdateString();
                    string LineString = Line.LineString;
                    int Index1 = LineString.IndexOf("(at ");
                    if (Index1 != -1)
                    {
                        Line.TagObject = LineString;
                    }
                    else if (LineString.StartsWith("Error: Graph "))
                    {
                        Line.TagObject = LineString;
                    }
                }
            }
        }

        void ParseFilenameAndLine(string Message, out string Filename, out int Line)
        {
            Filename = "";
            Line = 0;
            int Index = Message.IndexOf("(at ");
            if (Index == -1)
            {
                return;
            }
            string Message1 = Message.Substring(Index + 4);
            int Index1 = Message1.IndexOf(')');
            if (Index1 == -1)
            {
                return;
            }
            string Message2 = Message1.Substring(0, Index1);
            int Index2 = Message2.IndexOf(':');
            if (Index2 == -1)
            {
                Filename = Message2;
            }
            else
            {
                Filename = Message2.Substring(0, Index2);
                string LineString = Message2.Substring(Index2 + 1);
                Line = MathHelper.ParseInt(LineString);
            }
        }

        void JumpToFileInString_Log(string String)
        {
            string Filename;
            int Line;
            ParseFilenameAndLine(String, out Filename, out Line);
            if (Filename != "")
            {
                string EditorFilename = EditorUtilities.StandardFilenameToEditorFilename(Filename);
                FileJumperManager.GetInstance().JumpFile(EditorFilename, Line);
            }
        }

        void JumpToFileInString_Graph(string String)
        {
            int Index1 = String.IndexOf('(');
            int Index2 = String.IndexOf(')');
            if (Index1 != -1 && Index2 != -1)
            {
                string NodeIDString = String.Substring(Index1 + 1, Index2 - Index1 - 1);
                int NodeID = int.Parse(NodeIDString);
                //NodeGraphUI.GetInstance().LocateToNode(NodeID);
            }
        }

        void JumpToFileInString(string String)
        {
            if (String.StartsWith("Error: Graph "))
            {
                JumpToFileInString_Graph(String);
            }
            else
            {
                JumpToFileInString_Log(String);
            }
        }

        void OnLogViewJumpFile(LogView Sender, LogViewItem LogViewItem)
        {
            if (LogViewItem != null)
            {
                JumpToFileInString(LogViewItem.LogMessage);
            }
        }

        void OnEditLineDoubleClicked(Edit Sender, EditLine EditLine)
        {
            EditLine.UpdateString();
            JumpToFileInString(EditLine.LineString);
        }

        void OnEditInputCommandKeyDownEvent(Control Sender, Key Key, ref bool bContinue)
        {
            Device Device = GetDevice();
            bool bNone = Device.IsNoneModifiersDown();
            if (bNone && Key == Key.Enter)
            {
                string TextCommand = _EditInputCommand.GetText();
                Clicross.GameWorldInterface.InGameTerminalManager_TriggerCommand(TextCommand);
                AddLogItem(LogMessageType.Command, TextCommand);
                _LastCmdName = TextCommand;
                _EditInputCommand.SetText("");
            }
            // Press Up button will use last command
            if (bNone && Key == Key.Up)
            {
                _EditInputCommand.SetText(_LastCmdName);
                AddLogItem(LogMessageType.Command, _LastCmdName);
            }
        }

        public void AddLogItem(LogMessageType LogMessageType, string LogMessage)
        {
            OperationQueue.GetInstance().AddOperation(() =>
            {
                ShaderLogTranslator ShaderLogTranslator = ShaderLogTranslator.GetInstance();
                if (ShaderLogTranslator.IsShaderLog(LogMessage))
                {
                    ShaderLogTranslator.TranslateShaderLog(LogMessage);
                }
                else
                {
                    RealAddLogItem(LogMessageType, LogMessage);
                }
            });
        }

        public void RealAddLogItem(LogMessageType LogMessageType, string LogMessage)
        {
            if (_LogView != null)
            {
                _LogView.AddLogViewItem(new LogViewItem(LogMessageType, LogMessage));
                _LogView.UpdateScrollBar();
                _LogView.ScrollToEnd();
                UpdateLogCounters();
            }
        }

        void UpdateLogCounterButton(Button Button, string Name, int Count)
        {
            string Text = string.Format("{0} {1}{2}", Count.ToString(), Name, Count > 1 ? "s" : "");
            Button.SetText(Text);
            int ButtonWidth = Button.CalculateTextWidth() + 8;
            Button.SetWidth(ButtonWidth);
        }

        void UpdateStatusBar(int InformationCount, int WarningCount, int ErrorCount, int debugCount)
        {
            if (_LabelInformations != null)
                _LabelInformations.SetText(InformationCount.ToString());

            if (_LabelWarnings != null)
            {
                _LabelWarnings.SetText(WarningCount.ToString());
                _LabelWarnings.SetTextColor(WarningCount > 0 ? Color.FromRGB(255, 255, 0) : Color.FromRGB(255, 255, 255));
            }

            if (_LabelErrors != null)
            {
                _LabelErrors.SetText(ErrorCount.ToString());
                _LabelErrors.SetTextColor(ErrorCount > 0 ? Color.FromRGB(255, 0, 0) : Color.FromRGB(255, 255, 255));
            }
            if (_LabelDebug != null)
            {
                _LabelDebug.SetText(debugCount.ToString());
                _LabelDebug.SetTextColor(debugCount > 0 ? Color.FromRGB(255, 0, 0) : Color.FromRGB(255, 255, 255));
            }
        }

        public LogCounter GetLogCounter()
        {
            if (_LogView == null)
            {
                return null;
            }
            return _LogView.GetLogCounter();
        }

        void UpdateLogCounters()
        {
            LogCounter LogCounter = _LogView.GetLogCounter();
            UpdateLogCounterButton(_ButtonInformations, "Information", LogCounter.InformationCount);
            UpdateLogCounterButton(_ButtonWarnings, "Warning", LogCounter.WarningCount);
            UpdateLogCounterButton(_ButtonErrors, "Error", LogCounter.ErrorCount);
            UpdateLogCounterButton(_ButtonDebug, "Debug", LogCounter.DebugCount);
            _OperationBarUI.Refresh();
            UpdateStatusBar(LogCounter.InformationCount, LogCounter.WarningCount, LogCounter.ErrorCount, LogCounter.DebugCount);
        }

        public void SaveUserConfig(Record RootRecord)
        {
            Record RecordConsoleUI = RootRecord.AddChild();
            RecordConsoleUI.SetTypeString("ConsoleUI");
            Record RecordConsoleUIState = RecordConsoleUI.AddChild();
            RecordConsoleUIState.SetTypeString("ConsoleUIState");
            RecordConsoleUIState.SetInt("LogViewHeight", _LogView.GetHeight());
            RecordConsoleUIState.SetInt("EditHeight", _Edit.GetHeight());
            //RecordConsoleUIState.SetBool("ButtonClearOnPlayBorderChecked", _ButtonClearOnPlay.GetBorderChecked());
            //RecordConsoleUIState.SetBool("ButtonCollapseBorderChecked", _ButtonCollapse.GetBorderChecked());
            RecordConsoleUIState.SetBool("ButtonErrorPauseBorderChecked", _ButtonErrorPause.GetBorderChecked());
            RecordConsoleUIState.SetBool("ButtonInformationsBorderChecked", _ButtonInformations.GetBorderChecked());
            RecordConsoleUIState.SetBool("ButtonWarningsBorderChecked", _ButtonWarnings.GetBorderChecked());
            RecordConsoleUIState.SetBool("ButtonErrorsBorderChecked", _ButtonErrors.GetBorderChecked());
            RecordConsoleUIState.SetBool("ButtonDebugBorderChecked", _ButtonDebug.GetBorderChecked());
        }

        void UpdateCheckStateToLogView()
        {

            bool bShowInformations = _ButtonInformations.GetBorderChecked();
            _LogView.SetShowInformations(bShowInformations);

            bool bButtonWarnings = _ButtonWarnings.GetBorderChecked();
            _LogView.SetShowWarnings(bButtonWarnings);

            bool bShowErrors = _ButtonErrors.GetBorderChecked();
            _LogView.SetShowErrors(bShowErrors);
            bool bShowDebug = _ButtonDebug.GetBorderChecked();
            _LogView.SetShowDebug(bShowDebug);
            _LogView.DoFilter();
        }

        public void LoadUserConfig(Record RootRecord)
        {
            Record RecordConsoleUI = RootRecord.FindByTypeString("ConsoleUI");
            if (RecordConsoleUI != null)
            {
                Record RecordConsoleUIState = RecordConsoleUI.FindByTypeString("ConsoleUIState");
                if (RecordConsoleUIState != null)
                {
                    _LogView.SetHeight(RecordConsoleUIState.GetInt("LogViewHeight"));
                    _Edit.SetHeight(RecordConsoleUIState.GetInt("EditHeight"));
                    //_ButtonClearOnPlay.SetBorderChecked(RecordConsoleUIState.GetBool("ButtonClearOnPlayBorderChecked"));
                    //_ButtonCollapse.SetBorderChecked(RecordConsoleUIState.GetBool("ButtonCollapseBorderChecked"));
                    _ButtonErrorPause.SetBorderChecked(RecordConsoleUIState.GetBool("ButtonErrorPauseBorderChecked"));
                    _ButtonInformations.SetBorderChecked(RecordConsoleUIState.GetBool("ButtonInformationsBorderChecked"));
                    _ButtonWarnings.SetBorderChecked(RecordConsoleUIState.GetBool("ButtonWarningsBorderChecked"));
                    _ButtonErrors.SetBorderChecked(RecordConsoleUIState.GetBool("ButtonErrorsBorderChecked"));
                    _ButtonDebug.SetBorderChecked(RecordConsoleUIState.GetBool("ButtonDebugBorderChecked"));
                    UpdateCheckStateToLogView();
                }
            }
        }

        void OnLabelStatusBar1LeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (_LogView != null && _LogView.GetLogCounter().ErrorCount > 0)
            {
                MainUI.GetInstance().ActivateDockingCard_Console();
            }
            bContinue = false;
        }

        void OnLabelStatusBar2LeftMouseDoubleClicked(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            MainUI.GetInstance().ActivateDockingCard_Console();
            bContinue = false;
        }
    }
}
