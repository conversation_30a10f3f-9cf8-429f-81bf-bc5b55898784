using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_SelectEntities : EditOperation
    {
        List<Entity> _SelectedEntitiesBefore;
        List<Entity> _SelectedEntitiesAfter;

        public EditOperation_SelectEntities()
        {
            _SelectedEntitiesBefore = new List<Entity>();
            _SelectedEntitiesAfter = new List<Entity>();
        }

        List<Entity> CopyEntityList(List<Entity> EntityList)
        {
            List<Entity> EntityList1 = new List<Entity>();
            foreach (Entity Entity in EntityList)
            {
                EntityList1.Add(Entity);
            }
            return EntityList1;
        }

        public void UpdateSelectedEntitiesBefore(List<Entity> SelectedEntitiesBefore)
        {
            _SelectedEntitiesBefore = CopyEntityList(SelectedEntitiesBefore);
        }

        public void UpdateSelectedEntitiesAfter(List<Entity> SelectedEntitiesAfter)
        {
            _SelectedEntitiesAfter = CopyEntityList(SelectedEntitiesAfter);
        }

        public override void Undo()
        {
            SelectEntities(_SelectedEntitiesBefore);
        }

        public override void Redo()
        {
            SelectEntities(_SelectedEntitiesAfter);
        }

        void SelectEntities(List<Entity> EntityList)
        {
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            InspectorUI.SetObjectInspected(EntityList);
            InspectorUI.InspectObject();

            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.ClearEntitySelectionNoEvent();
            foreach (Entity Entity in EntityList)
            {
                HierarchyUI.AddEntitySelectionNoEvent(Entity);
            }

            EditorScene EditorScene = EditorScene.GetInstance();
            List<Entity> SelectionList = new List<Entity>();
            foreach (Entity Entity in EntityList)
            {
                SelectionList.AddRange(EditorScene.EnumEntities(Entity));
            }
            EditorScene.SetSelectionList(SelectionList);
        }

        public bool IsIdentity()
        {
            if (_SelectedEntitiesBefore.Count == _SelectedEntitiesAfter.Count)
            {
                int Count = _SelectedEntitiesBefore.Count;
                for (int i = 0; i < Count; i++)
                {
                    if (_SelectedEntitiesBefore[i] != _SelectedEntitiesAfter[i])
                    {
                        return false;
                    }
                }
                return true;
            }
            return false;
        }
    }
}
