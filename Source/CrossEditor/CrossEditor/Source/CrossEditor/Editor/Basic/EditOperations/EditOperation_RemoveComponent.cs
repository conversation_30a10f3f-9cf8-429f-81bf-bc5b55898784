namespace CrossEditor
{
    class EditOperation_RemoveComponent : EditOperation
    {
        Entity Entity;
        Component Component;

        public EditOperation_RemoveComponent(Entity InEntity, Component InComponent)
        {
            Entity = InEntity;
            Component = InComponent;
        }

        public override void Undo()
        {
            Entity.AddComponent(Component);
            Component.RuntimeReapplyProperties();
            InspectorUI.GetInstance().InspectObject();
        }

        public override void Redo()
        {
            Entity.RemoveComponent(Component);
            InspectorUI.GetInstance().InspectObject();
        }
    }
}
