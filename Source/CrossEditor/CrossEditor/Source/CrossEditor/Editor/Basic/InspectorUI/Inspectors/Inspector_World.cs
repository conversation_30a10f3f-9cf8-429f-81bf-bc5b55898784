using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_World : Inspector
    {
        World _World;

        Panel _PanelIcon;
        Label _LabelName;

        public Inspector_World()
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _World = (World)Object;

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetImage(UIManager.LoadUIImage("Editor/Icons/AssetIcon_16/Root_16.png"));
            _SelfContainer.AddChild(_PanelIcon);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetFontSize(14);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);
            _LabelName.SetText("World Configuration");
            _SelfContainer.AddChild(_LabelName);

            ClearChildInspectors();
            Type Type = _World.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AddPropertyInspector(PropertyInfo, _World);
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public virtual object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public virtual void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            _SelfContainer.SetPosition(0, Y, Width, 24);
            Y += 24;

            _PanelIcon.SetPosition(SPAN_X, 2, 20, 20);
            int LabelNameWidth = _LabelName.CalculateTextWidth();
            _LabelName.SetPosition(_PanelIcon.GetEndX() + SPAN_X, 2, LabelNameWidth, 20);

            base.UpdateLayout(Width, ref Y);
        }
    }
}
