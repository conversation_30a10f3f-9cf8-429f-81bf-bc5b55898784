using CEngine;
using Clicegf;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class GameObjectComponent : ComponentBase
    {
        public GameObject mGameObject;

        public Dictionary<string, Component> mECSEditorComponents;

        public static List<Type> _AssociateComponents = new List<Type>();

        protected GameObjectComponent()
        {
            _Enable = true;
            mECSEditorComponents = new Dictionary<string, Component>();
        }

        public virtual void OnComponentAddToEntity()
        {
        }

        public static bool AddComponentCheck(Entity entity)
        {
            return true;
        }

        public GameObjectComponent Clone()
        {
            Type Type = GetType();
            return (GameObjectComponent)Activator.CreateInstance(Type);
        }

        protected virtual void CreateECSEditorComponent()
        {
        }

        public override bool Enable
        {
            get
            {
                foreach (KeyValuePair<string, Component> entry in mECSEditorComponents)
                {
                    return entry.Value.Enable;
                }

                return _Enable;
            }
            set
            {
                foreach (KeyValuePair<string, Component> entry in mECSEditorComponents)
                {
                    entry.Value.Enable = _Enable;
                    return;
                }
                _Enable = value;
            }
        }

        public override void RuntimeAddComponent()
        {
            CreateECSEditorComponent();
            foreach (string compNativeName in NativeNames())
            {
                var runtimeGameObject = GetRuntimeOwnerGameObject();
                if (runtimeGameObject != null)
                {
                    runtimeGameObject.CreateComponentByClassName(compNativeName);
                }
            }
            SyncDataFromEngine();
        }

        public override void RuntimeRemoveComponent()
        {
            foreach (string compNativeName in NativeNames())
            {
                var runtimeGameObject = GetRuntimeOwnerGameObject();
                if (runtimeGameObject != null)
                {
                    runtimeGameObject.RemoveComponentByClassName(compNativeName);
                }
            }
        }
        public override bool RuntimeHasComponent()
        {
            bool bResult = true;
            bool IsPIEWorld = mGameObject.mEntity.World.WorldType == WorldTypeTag.PIEWorld;
            if (IsPIEWorld && _IsEditorOnlyComponent)
            {
                return false;
            }

            foreach (string ComponentName in NativeNames())
            {
                if (ComponentName == "")
                {
                    return false;
                }
                GOContext GoContext = new GOContext(mGameObject.mEntity.World._WorldInterface);
                bResult = GoContext.GameObjectHasComponent(mGameObject.mEntity.EntityID, ComponentName);
                if (!bResult)
                {
                    return false;
                }

            }
            return true;
        }
        public override void RuntimeReapplyProperties()
        {
            base.RuntimeReapplyProperties();
            foreach (var mECSEditorComponent in mECSEditorComponents.Values)
            {
                var realecsComp = mGameObject.mEntity.GetComponent(mECSEditorComponent.GetType());
                if (realecsComp != mECSEditorComponent)
                {
                    EditorLogger.Log(LogMessageType.Error, "realecsComp != mECSEditorComponent");
                }
                if (!realecsComp.RuntimeHasComponent())
                {
                    EditorLogger.Log(LogMessageType.Error, "!realecsComp.RuntimeHasComponent()");
                }

                realecsComp.RuntimeReapplyProperties();
            }
        }

        public Clicegf.GameObject GetRuntimeOwnerGameObject()
        {
            return mGameObject.GetRuntimeGameObject();
        }
    }


    [ComponentAttribute(DisplayUINames = "Misc/InputComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class InputComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::InputComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
    }

    [ComponentAttribute(DisplayUINames = "Movement/CharacterMovementComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class CharacterMovementGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::CharacterMovementComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<CharacterMovementComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<CharacterMovementComponent>();
            }

            mECSEditorComponents["CharacterMovementComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["CharacterMovementComponent"] = mGameObject.mEntity.GetComponent<CharacterMovementComponent>();
        }
    }

    [ComponentAttribute(DisplayUINames = "Animation/AnimationComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class AnimationComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::AnimationComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        protected override void CreateECSEditorComponent()
        {
            var animComp = mGameObject.mEntity.GetComponent<Animator>();
            if (animComp == null)
            {
                animComp = mGameObject.mEntity.CreateComponent<Animator>();
            }
            var skeletonComp = mGameObject.mEntity.GetComponent<Skeleton>();
            if (skeletonComp == null)
            {
                skeletonComp = mGameObject.mEntity.CreateComponent<Skeleton>();
            }

            mECSEditorComponents["Animator"] = animComp;
            mECSEditorComponents["Skeleton"] = skeletonComp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["Animator"] = mGameObject.mEntity.GetComponent<Animator>();
            mECSEditorComponents["Skeleton"] = mGameObject.mEntity.GetComponent<Skeleton>();
        }
    }

    [ComponentAttribute(DisplayUINames = "Mesh/ModelComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class ModelGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::ModelComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<ModelComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<ModelComponent>();
            }
            var rpComp = mGameObject.mEntity.GetComponent<RenderProperty>();
            if (rpComp == null)
            {
                rpComp = mGameObject.mEntity.CreateComponent<RenderProperty>();
            }
            mECSEditorComponents["ModelComponent"] = comp;
            mECSEditorComponents["RenderPropertyComponent"] = rpComp;
        }
        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["ModelComponent"] = mGameObject.mEntity.GetComponent<ModelComponent>();
            mECSEditorComponents["RenderPropertyComponent"] = mGameObject.mEntity.GetComponent<RenderProperty>();
        }
    }

    [ComponentAttribute(DisplayUINames = "Misc/RootComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class RootComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::GameObjectComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        public override void SyncDataFromEngine()
        {
        }

    }
}