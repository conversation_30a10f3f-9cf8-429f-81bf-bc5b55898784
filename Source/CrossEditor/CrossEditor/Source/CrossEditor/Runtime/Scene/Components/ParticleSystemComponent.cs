using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(NeedRenderProperty = true)]
    class ParticleSystemComponent : Component
    {
        public static string _NativeSystemName = "cross::ParticleSimulationSystemG";

        static string[] _NativeNames = { "cross::ParticleSystemComponentG" };

        protected int _RandomSeed = 0;
        protected ParticleSystemResourceSlot _SystemInfo = new ParticleSystemResourceSlot();
        protected EntityIDStruct _SimulationCamera = EntityIDStruct.InvalidHandle();
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 2;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
        }

        public override void SyncDataFromEngine()
        {
            _SystemInfo.ParticleSystemPath = ParticleSimulationSystemG.GetSystemResourcePath(Entity.World.GetNativePointer(), Entity.EntityID);
            _SimulationCamera = ParticleSimulationSystemG.GetSimulationCamera(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this particle system execute.")]
        public override bool Enable
        {
            get { return _Enable; }
            set
            {
                _Enable = value;

                if (_Enable)
                {
                    ParticleSimulationSystemG.Activate(Entity.World.GetNativePointer(), Entity.EntityID);
                }
                else
                {
                    ParticleSimulationSystemG.Deactivate(Entity.World.GetNativePointer(), Entity.EntityID);
                }
            }
        }

        //[PropertyInfo(PropertyType = "Auto", ToolTips = "Global random seed for emitters without it.")]
        //public int RandomSeed
        //{
        //    get { return _RandomSeed; }
        //    set { _RandomSeed = value; }
        //}

        [PropertyInfo(PropertyType = "Struct", ToolTips = "")]
        public ParticleSystemResourceSlot SystemInfo
        {
            get { return _SystemInfo; }
            set
            {
                _SystemInfo.ParticleSystemPath = value.ParticleSystemPath;
                System.IntPtr World = Entity.World.GetNativePointer();
                var Event = ParticleResourceEvent.SystemChanged;
                ParticleSimulationSystemG.OnResourceChanged(World, Entity.EntityID, Event, 0, _SystemInfo.ParticleSystemPath);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Camera infomation used in particle simulation")]
        public EntityIDStruct SimulationCamera
        {
            get { return _SimulationCamera; }
            set
            {
                _SimulationCamera = value;
                System.IntPtr World = Entity.World.GetNativePointer();
                ParticleSimulationSystemG.SetSimulationCamera(World, Entity.EntityID, value);
            }
        }
    }
}