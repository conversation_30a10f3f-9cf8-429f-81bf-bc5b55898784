using CEngine;
using System;
using System.Reflection;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Light/[Point Light,Directional Light,Spot Light]")]
    class Light : Component
    {
        public LightComponentG ComponentData = new LightComponentG();

        static string[] _NativeNames = { "cross::LightComponentG" };
        static string _NativeSystemName = "cross::LightSystemG";

        public Light()
        {

        }

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 2;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override string NativeSystemName()
        {
            return _NativeSystemName;
        }

        public override void SyncDataFromEngine()
        {
            LightSystemG.GetLightComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
        }

        public override void Initialize(string initialization)
        {
            if (initialization.Contains("Directional"))
            {
                mLight.mType = LightType.Directional;
            }
            else if (initialization.Contains("Spot"))
            {
                mLight.mType = LightType.Spot;
            }
            else if (initialization.Contains("Point"))
            {
                mLight.mType = LightType.Point;
            }
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "Light")]

        public LightComponentG mLight
        {
            get
            {
                LightSystemG.GetLightComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
                return ComponentData;
            }
            set
            {
                ComponentData = value;
                LightSystemG.SetLightComponent(Entity.World.GetNativePointer(), Entity.EntityID, ComponentData);
            }
        }

        public PropertyInfoAttribute GetPropertyInfoAttributeCallBack(MemberInfo MemberInfo, PropertyInfoAttribute infoAttribute)
        {

            var type_string = Enum.GetName(typeof(LightType), mLight.mType);

            if (infoAttribute.Category != "")
            {
                if (!infoAttribute.Category.Contains(type_string))
                {
                    infoAttribute.bHide = true;
                }
                else
                {
                    infoAttribute.Category = type_string;
                }

            }
            return infoAttribute;
        }

    }
}
