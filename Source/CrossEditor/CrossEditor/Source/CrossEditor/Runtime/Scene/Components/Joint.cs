using CEngine;

namespace CrossEditor
{
    class Joint : Component
    {
        static string[] _NativeNames = { "cross::JointComponentG" };

        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        static int _ComponentOrder = 1;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 5;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public Joint()
        {

        }

        public override void Reset()
        {
            base.Reset();
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "Struct", ToolTips = "Test struct list.")]
        public vector_cross_JointConnection Connections
        {
            get
            {
                return JointSystemG.GetConnections(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                JointSystemG.SetConnections(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }
    }
}
