#pragma once
#include "TypeScriptObject.h"
#include "GameFramework/GameFrameworkTypes.h"
#include "reflection/objects/rtti_base.hpp"
#include "ValueConverter/Converter.h"

#define StaticMetaClassName(ClassName)                                                                                                                                                                                                         \
    CEMeta(ScriptCallable)                                                                                                                                                                                                                     \
    static const std::string& MetaClassName()                                                                                                                                                                                                  \
    {                                                                                                                                                                                                                                          \
        return gbf::reflection::query_meta_class<ClassName>()->name();                                                                                                                                                                         \
    }

namespace cegf
{

class GAMEFRAMEWORK_API CEMeta(Reflect, Script, Cli) ObjectBase : public gbf::reflection::RttiBase
{
    CEGameplayInternal()
public:
    StaticMetaClassName(ObjectBase);
    ObjectBase() {}

    virtual ~ObjectBase() {}
    
    virtual void BeginDestroy() { mIsPendingDestroy = true; }

    virtual void Destroyed() {}

    const gbf::reflection::MetaClass* GetMetaClass() const
    {
        return __rtti_meta();
    }

    gbf::reflection::UserObject GetUserObject() const
    {
        
        return gbf::reflection::make_user_object(const_cast<ObjectBase*>(this), gbf::reflection::remote_storage_policy{});
    }

    virtual const std::string& GetName() const { return mName; }

    virtual void SetName(const std::string& objName) { mName = objName; };

    bool IsPendingDestroy() const { return mIsPendingDestroy; }

    std::string MakeUniqueObjectName(const gbf::reflection::MetaClass* metaClass);
    
    std::string MakeUniqueObjectName(const std::string& className);

    template<typename T>
    bool CheckCast() const;

    auto& GetTsObject()const
    {
        return mTSObject;
    }

    CEMeta(Reflect)
    auto MixinTypescript(const std::string& typescriptClass) -> bool;

    cross::SerializeNode Serialize(cross::Context & context) const
    {
        return cross::SerializeNode();
    };
    void Serialize(cross::SerializeNode & node, cross::Context & context) const {};
    bool Deserialize(const cross::DeserializeNode& in, cross::Context& context)
    {
        return true;
    }
    bool PostDeserialize(const cross::DeserializeNode& in, cross::Context& context)
    {
        return true;
    }

protected:
    bool mIsPendingDestroy = false;

    std::string mName;
    cross::TypeScriptObject mTSObject;
private:
    static std::atomic<UInt32> ObjectNameIndex;
};

using ObjectBasePtr = std::shared_ptr<ObjectBase>;

template<typename T>
bool ObjectBase::CheckCast() const
{
    if constexpr (!std::is_base_of_v<ObjectBase, T>)
    {
        return false;
    }

    auto otherMetaClass = gbf::reflection::query_meta_class<T>();
    if (otherMetaClass == nullptr)
    {
        return false;
    }

    auto metaClass = GetMetaClass();
    if (metaClass == nullptr)
    {
        return false;
    }
    return metaClass->IsTypeMatch(otherMetaClass->id());
}

template<typename TargetType, typename SrcType>
TargetType* CastTo(SrcType* objPtr)
{
    if constexpr (!std::is_base_of_v<ObjectBase, SrcType> || !std::is_base_of_v<ObjectBase, TargetType>)
    {
        return nullptr;
    }

    if (objPtr)
    {
        return TYPE_CAST(TargetType*, objPtr);
    }
    return nullptr;
}

template<typename TargetType, typename SrcType>
TargetType* CheckCastTo(SrcType* objPtr)
{
    if constexpr (!std::is_base_of_v<ObjectBase, SrcType> || !std::is_base_of_v<ObjectBase, TargetType>)
    {
        return nullptr;
    }

    if (objPtr && objPtr->template CheckCast<TargetType>())
    {
        return TYPE_CAST(TargetType*, objPtr);
    }
    return nullptr;
}

inline bool IsValid(ObjectBase* InTest)
{
    return InTest && InTest->weak_from_this().use_count() >= 1;
}

template<class T>
inline const auto& GetNameSafe(T&& InTest)
{
    static std::string sInvalidName = "sInvalidName";
    if (InTest)
    {
        return InTest->GetName();
    }
    else
    {
        return sInvalidName;
    }
}

}
