#include "GameFramework/Components/PrimitiveComponent.h"
#include "GameFramework/GameWorld.h"
namespace cegf
{

void PrimitiveComponent::Init()
{
    GameObjectComponent::Init();
}

void PrimitiveComponent::Uninit(bool bShouldNotifyECS)
{
    GameObjectComponent::Uninit(bShouldNotifyECS);
}

void PrimitiveComponent::Tick(float deltaTime)
{
    GameObjectComponent::Tick(deltaTime);
}

void PrimitiveComponent::Serialize(SerializeNode& node, SerializeContext& context) const
{
    
}

bool PrimitiveComponent::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    return true;
}

//bool PrimitiveComponent::ShouldComponentAddToScene() const
//{
//    // TODO
//    auto owner = GetOwner();
//    return owner != nullptr;
//}


//void PrimitiveComponent::CreateRenderState()
//{
//    GameObjectComponent::CreateRenderState();
//
//    if (ShouldComponentAddToScene())
//    {
//        std::cerr << "Draw Once!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n";
//        Draw();
//    }
//}

//void PrimitiveComponent::MarkRenderStateDirty()
//{
//    if (IsRegistered() && (!mRenderStateDirty || !GetWorld()))
//    {
//        mRenderStateDirty = true;
//        MarkForNeedEndOfFrameUpdate();
//    }
//}

void PrimitiveComponent::BeginDestroy()
{
    GameObjectComponent::BeginDestroy();
}

void PrimitiveComponent::MarkForNeedEndOfFrameDraw()
{
    if (auto componentWorld = GetWorld())
    {
        componentWorld->MarkComponentForNeededEndOfFrameDraw(this);
    }
}

void PrimitiveComponent::MarkForNoNeedEndOfFrameDraw()
{
    if (auto componentWorld = GetWorld())
    {
        componentWorld->MarkComponentForNoNeedEndOfFrameDraw(this);
    }
}

}
