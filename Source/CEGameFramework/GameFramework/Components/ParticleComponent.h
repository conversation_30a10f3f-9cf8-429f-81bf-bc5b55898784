#pragma once
#include "GameFramework/Components/BasicComponent.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include "Runtime/GameWorld/ParticleSimulationSystemG.h"

#include "Runtime/Interface/ParticleSystemDebugHelper.h"

namespace cross {
class ParticleSimulationSystemG;
struct MinMaxCurve;
}

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) ParticleComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(ParticleComponent);
    CEMeta(Reflect)
    ParticleComponent();

    virtual ~ParticleComponent() override;
    
    virtual void Init() override;
    
    bool SetParticleAsset(const std::string& assetPath);

    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;

public:
    CEFunction(Reflect, Cli, ScriptCallable) void ActivatePS();
    CEFunction(Reflect, Cli, ScriptCallable) void DeactivatePS();
    CEFunction(Reflect, Cli) void OnResourceChanged(cross::fx::ParticleResourceEvent event, UInt32 index, const std::string& path);
    CEFunction(Reflect, Cli, ScriptCallable) void InitializeParticleSystem();
    CEFunction(Reflect, Cli, ScriptCallable) void StartParticleSystem();
    CEFunction(Reflect, Cli, ScriptCallable) void PauseParticleSystem();
    CEFunction(Reflect, Cli, ScriptCallable) void StopParticleSystem();
    CEFunction(Reflect, Cli, ScriptCallable) void RestartParticleSystem();
    CEFunction(Reflect, Cli, ScriptCallable) bool IsSystemCompleted(bool waitParticleClear = true) const;
    CEFunction(Reflect, Cli, ScriptCallable) bool IsSystemRunning() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetRandomSeed(UInt32 seed, bool unifiedSeed = false);
    CEFunction(Reflect, Cli, ScriptCallable) void SetParticleInitVar(SInt32 emitterIndex, const std::string& varName, const cross::MinMaxCurve& varValue);
    CEFunction(Reflect, Cli, ScriptCallable) float GetParticleParamFloat(SInt32 emitterIndex, UInt32 particleIndex, const std::string& varName);
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetTotalParticleCount();
    CEFunction(Reflect, Cli, ScriptCallable) UInt32 GetActiveParticleCount(UInt32 emitterIndex);
    CEFunction(Reflect, Cli, ScriptCallable) std::string GetSystemResourcePath() const;
    CEFunction(Reflect, Cli, ScriptCallable) size_t GetParticleSystemID() const;
    CEFunction(Reflect, Cli, ScriptCallable) void DisableEmitter(UInt32 emitterIndex);
    CEFunction(Reflect, Cli, ScriptCallable) void SetSimulationCamera(GameObject* camera);
    CEFunction(Reflect, Cli, ScriptCallable) void SetSystemResourcePath(const std::string& path);

    void RegisterParticleSpawnEventHandler(cross::fx::ParticleEventCallback callback);

    void RegisterParticleDeathEventHandler(cross::fx::ParticleEventCallback callback);
};

}
