#include "GameFramework/Components/SphereComponent.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"

namespace cegf
{

static constexpr float kDefaultSphereRadius = 20.0f;

SphereComponent::SphereComponent()
{
    mSphere = cross::PhysicsGeometrySphere(cross::Float3::Zero(), kDefaultSphereRadius);
}

float SphereComponent::GetSphereRadius() const
{
    cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();
    if (physSys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        auto const& spheres = physSys->GetExtraSpheres(comp.Read());
        if (!spheres.empty())
        {
            return spheres[0].radius;
        }
    }
    return kDefaultSphereRadius;
}

void SphereComponent::SetSphereRadius(float inRadius)
{
    cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();
    if (physSys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        auto collisions = physSys->GetExtraShape(comp.Read());
        if (collisions == nullptr)
        {
            return;
        }

        for (auto& sphere : collisions->mSphereGeometry)
        {
            sphere.radius = inRadius;
        }
    }
}

void SphereComponent::Init()
{
    PhysicsComponent::Init();
    cross::PhysicsSystemG* physSys = GetSystem<cross::PhysicsSystemG>();
    if (physSys)
    {
        auto comp = GetECSComponent<cross::PhysicsComponentG>();
        if (physSys->GetExtraSpheres(comp.Read()).empty())
        {
            physSys->AddExtraSphereShape(comp.Write(), mSphere);
        }
    }
}

}

