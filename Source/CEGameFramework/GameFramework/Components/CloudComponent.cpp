#include "CloudComponent.h"

#include "Runtime/GameWorld/CloudSystemG.h"

namespace cegf
{
void CloudComponent::SetPropertyCloudSettings(const cross::CloudSetting& setting)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        sys->SetPropertyCloudSettings(comp.Write(), setting);
    }
}

cross::CloudSetting CloudComponent::GetPropertyCloudSettings() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read());
    }
    return {};
}

void CloudComponent::SetPropertyCloudSpeed(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudRange.CloudSpeed = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudSpeed() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        return cloudSetting.CloudRange.CloudSpeed;
    }
    return {};
}

void CloudComponent::SetPropertyCloudNoiseFlow(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudRange.CloudNoiseFlow = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudNoiseFlow() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        return cloudSetting.CloudRange.CloudNoiseFlow;
    }
    return {};
}

void CloudComponent::SetPropertyCloudShadowScale(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShadow.CloudShadowIntensity = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudShadowScale() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        return cloudSetting.CloudShadow.CloudShadowIntensity;
    }
    return {};
}

void CloudComponent::SetPropertyCloudCutoffDistance(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudRange.CutOffDistance = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudCutoffDistance() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        return cloudSetting.CloudRange.CutOffDistance;
    }
    return {};
}

void CloudComponent::SetPropertyCloudSize(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudRange.Size = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyEnableThunderstorm(bool val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.ThunderDebug.enable = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyCloudMtl(cross::MaterialPtr mtl)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        // only support one component?
        auto comp = GetECSComponent<cross::CloudComponentG>();
        sys->SetCloudMaterialOverride(comp.Write(), mtl);
    }
}

// CloudData individual properties implementations

// Geometry
void CloudComponent::SetPropertyCloudBottomFade(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudGeometry.cloud_bottom_fade = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudBottomFade() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudGeometry.cloud_bottom_fade;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyCoverageCloudness(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudGeometry.coverage_cloudness = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCoverageCloudness() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudGeometry.coverage_cloudness;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyCoverageHeightRemap(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudGeometry.coverage_height_remap = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCoverageHeightRemap() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudGeometry.coverage_height_remap;
    }
    return 0.0f;
}

// Base Noise
void CloudComponent::SetPropertyBaseSize(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.base_size = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyBaseSize() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.base_size;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyNoiseThreshold(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.noise_threshold = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyNoiseThreshold() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.noise_threshold;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyNoiseThresholdExtent(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.noise_threshold_extent = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyNoiseThresholdExtent() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.noise_threshold_extent;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyBaseCurlDistortionScale(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.base_curl_distortion_scale = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyBaseCurlDistortionScale() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.base_curl_distortion_scale;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyBaseCloudDistortion(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.base_cloud_distortion = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyBaseCloudDistortion() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.base_cloud_distortion;
    }
    return 0.0f;
}

// Extra Noise
void CloudComponent::SetPropertyExtraNoiseSize(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.extra_noise_size = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyExtraNoiseSize() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.extra_noise_size;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyExtraNoiseIntensity(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.extra_noise_intensity = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyExtraNoiseIntensity() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.extra_noise_intensity;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyExtraNoiseThreshold(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.extra_noise_threshold = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyExtraNoiseThreshold() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.extra_noise_threshold;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyExtraNoiseThresholdExtent(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.extra_noise_threshold_extent = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyExtraNoiseThresholdExtent() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.extra_noise_threshold_extent;
    }
    return 0.0f;
}

// Detail Noise
void CloudComponent::SetPropertyDetailSize(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.detail_size = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyDetailSize() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.detail_size;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyCurlDistortionScale(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.curl_distortion_scale = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCurlDistortionScale() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.curl_distortion_scale;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyCloudDistortion(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.cloud_distortion = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudDistortion() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.cloud_distortion;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyDetailWispyBillowyGradient(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.detail_wispy_billowy_gradient = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyDetailWispyBillowyGradient() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.detail_wispy_billowy_gradient;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyDetailAffect(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudNoise.detail_affect = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyDetailAffect() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudNoise.detail_affect;
    }
    return 0.0f;
}

// Cloud Shading
void CloudComponent::SetPropertySunAttenuation(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.sun_attenuation = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertySunAttenuation() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.sun_attenuation;
    }
    return 0.0f;
}

void CloudComponent::SetPropertySunIntensity(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.sun_intensity = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertySunIntensity() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.sun_intensity;
    }
    return 0.0f;
}

void CloudComponent::SetPropertySunSaturation(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.sun_saturation = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertySunSaturation() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.sun_saturation;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyMultiscatteringIntensity(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.multiscattering_intensity = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyMultiscatteringIntensity() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.multiscattering_intensity;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyAttenuationCoefficient(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.attenuation_coefficient = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyAttenuationCoefficient() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.attenuation_coefficient;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyAmbientIntensityTop(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.ambient_intensity_top = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyAmbientIntensityTop() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.ambient_intensity_top;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyAmbientIntensityBottom(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.ambient_intensity_bottom = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyAmbientIntensityBottom() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.ambient_intensity_bottom;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyAmbientSaturation(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.ambient_saturation = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyAmbientSaturation() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.ambient_saturation;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyPhaseMainOctaveWeight(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.phase_main_octave_weight = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyPhaseMainOctaveWeight() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.phase_main_octave_weight;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyPhaseSecondaryOctaveWeight(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.phase_secondary_octave_weight = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyPhaseSecondaryOctaveWeight() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.phase_secondary_octave_weight;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyCloudAnisotropyMin(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.cloud_anisotropy_min = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudAnisotropyMin() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.cloud_anisotropy_min;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyCloudAnisotropyMax(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.cloud_anisotropy_max = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyCloudAnisotropyMax() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.cloud_anisotropy_max;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyDotVLMin(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.dotVL_min = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyDotVLMin() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.dotVL_min;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyDotVLMax(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShading.dotVL_Max = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyDotVLMax() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShading.dotVL_Max;
    }
    return 0.0f;
}

// Cloud Shadow
void CloudComponent::SetPropertyShadowBias(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShadow.CloudShadowBias = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyShadowBias() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShadow.CloudShadowBias;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyShadowIntensity(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShadow.CloudShadowIntensity = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyShadowIntensity() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShadow.CloudShadowIntensity;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyShadowSubtraction(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShadow.CloudShadowSubtraction = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyShadowSubtraction() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShadow.CloudShadowSubtraction;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyShadowContrast(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShadow.CloudShadowContrast = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyShadowContrast() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShadow.CloudShadowContrast;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyShadowMultiplier(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShadow.CloudShadowMultiplier = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyShadowMultiplier() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShadow.CloudShadowMultiplier;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyShadowApScale(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudShadow.CloudShadowForAPScale = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

float CloudComponent::GetPropertyShadowApScale() const
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        return sys->GetPropertyCloudSettings(comp.Read()).CloudShadow.CloudShadowForAPScale;
    }
    return 0.0f;
}

void CloudComponent::SetPropertyMipNearRatio(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudGeometry.mip_near_ratio = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyMipFarRatio(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudGeometry.mip_far_ratio = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyMipUVFarScale(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudGeometry.mip_uv_far_scale = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyMipContrast(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudGeometry.mip_contrast = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyCloudCoverage(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudCommon.coverage = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyCloudDensity(float val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudCommon.density = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::SetPropertyRefreshCloudHistory(bool val)
{
    cross::CloudSystemG* sys = GetSystem<cross::CloudSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::CloudComponentG>();
        auto cloudSetting = sys->GetPropertyCloudSettings(comp.Read());
        cloudSetting.CloudRendering.RefreshHistoryTex = val;
        sys->SetPropertyCloudSettings(comp.Write(), cloudSetting);
    }
}

void CloudComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::CloudComponentG::GetDesc()->GetMaskBitIndex(), true);
}
} // namespace cegf
