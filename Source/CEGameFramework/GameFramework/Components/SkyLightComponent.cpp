#include "SkyLightComponent.h"

#include "Runtime/GameWorld/SkyLightSystemG.h"

namespace cegf
{
void SkyLightComponent::SetLightMapIntensityDebug(float val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetLightMapIntensityDebug(comp.Write(), val);
    }
}

float SkyLightComponent::GetLightMapIntensityDebug() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetLightMapIntensityDebug(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetSkyLightIntensity(float val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetSkyLightIntensity(comp.Write(), val);
    }
}

float SkyLightComponent::GetSkyLightIntensity() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetSkyLightIntensity(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetEnable(bool val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetEnable(comp.Write(), val);
    }
}

bool SkyLightComponent::GetEnable() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetEnable(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetRealTimeCapture(bool val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetRealTimeCapture(comp.Write(), val);
    }
}

bool SkyLightComponent::GetRealTimeCapture() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetRealTimeCapture(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetRealTimeCaptureSliceCount(SInt32 val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetRealTimeCaptureSliceCount(comp.Write(), val);
    }
}

SInt32 SkyLightComponent::GetRealTimeCaptureSliceCount() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetRealTimeCaptureSliceCount(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetIsLowerHemisphereColor(bool val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetIsLowerHemisphereColor(comp.Write(), val);
    }
}

bool SkyLightComponent::GetIsLowerHemisphereColor() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetIsLowerHemisphereColor(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetTODLowerHemisphereColor(bool val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetTODLowerHemisphereColor(comp.Write(), val);
    }
}

bool SkyLightComponent::GetTODLowerHemisphereColor() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetTODLowerHemisphereColor(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetLowerHemisphereColor(cross::Float4 val)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetLowerHemisphereColor(comp.Write(), val);
    }
}

cross::Float4 SkyLightComponent::GetLowerHemisphereColor() const
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        return sys->GetLowerHemisphereColor(comp.Read());
    }
    return {};
}

void SkyLightComponent::SetLightColor(cross::Float3 inValue)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetLightColor(comp.Write(), inValue);
    }
}

void SkyLightComponent::SetDiffuseProbe(cross::Float4 param, int index)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetDiffuseProbe(comp.Write(), param, index);
    }
}

void SkyLightComponent::SetSpecularProbe(std::string path)
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::SkyLightComponentG>();
        sys->SetSpecularProbe(comp.Write(), path);
    }
}

void SkyLightComponent::ResetSystemState()
{
    cross::SkyLightSystemG* sys = GetSystem<cross::SkyLightSystemG>();
    if (sys)
    {
        sys->ResetState();
        sys->ForceUpdateSkyLight();
    }
}

void SkyLightComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::SkyLightComponentG::GetDesc()->GetMaskBitIndex(), true);
}
} // namespace cegf
