#include "AbilitySystemGlobals.h"
#include "GameplayEffect/GameplayEffectTypes.h"
#include "Utils/CrossSubclassOf.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayAbilitiesDeveloperSettings.h"

namespace cegf {
bool UAbilitySystemGlobals::IsAbilitySystemGlobalsInitialized() const
{
    return bInitialized;
}

bool UAbilitySystemGlobals::ShouldUseDebugTargetFromHud()
{

    return GetDefault<UGameplayAbilitiesDeveloperSettings>()->bUseDebugTargetFromHud;
}

void UAbilitySystemGlobals::InitGlobalData()
{
    // Make sure the user didn't try to initialize the system again (we call InitGlobalData automatically in UE5.3+).
    if (IsAbilitySystemGlobalsInitialized())
    {
        return;
    }
    bInitialized = true;

    GetGlobalCurveTable();
    GetGlobalAttributeMetaDataTable();

    InitAttributeDefaults();
    ReloadAttributeDefaults();

    GetGameplayCueManager();
    GetGameplayTagResponseTable();
    InitGlobalTags();
    PerformDeveloperSettingsUpgrade();

    InitTargetDataScriptStructCache();

    // Register for PreloadMap so cleanup can occur on map transitions
    //FCoreUObjectDelegates::PreLoadMapWithContext.AddUObject(this, &UAbilitySystemGlobals::HandlePreLoadMap);

#if WITH_EDITOR
    // Register in editor for PreBeginPlay so cleanup can occur when we start a PIE session
    if (GIsEditor)
    {
        FEditorDelegates::PreBeginPIE.AddUObject(this, &UAbilitySystemGlobals::OnPreBeginPIE);
    }
#endif
}

UCurveTable* UAbilitySystemGlobals::GetGlobalCurveTable()
{
    Assert(false);
    return GlobalCurveTable;
    //if (!GlobalCurveTable)
    //{
    //    const UGameplayAbilitiesDeveloperSettings* DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    //    if (DeveloperSettings->GlobalCurveTableName.IsValid())
    //    {
    //        GlobalCurveTable = Cast<UCurveTable>(DeveloperSettings->GlobalCurveTableName.TryLoad());
    //    }
    //}
    //return GlobalCurveTable;
}

UDataTable* UAbilitySystemGlobals::GetGlobalAttributeMetaDataTable()
{
    Assert(false);
    return GlobalAttributeMetaDataTable;
    //if (!GlobalAttributeMetaDataTable)
    //{
    //    const UGameplayAbilitiesDeveloperSettings* DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    //    if (DeveloperSettings->GlobalAttributeMetaDataTableName.IsValid())
    //    {
    //        GlobalAttributeMetaDataTable = Cast<UDataTable>(DeveloperSettings->GlobalAttributeMetaDataTableName.TryLoad());
    //    }
    //}
    //return GlobalAttributeMetaDataTable;
}

bool UAbilitySystemGlobals::DeriveGameplayCueTagFromAssetName(std::string AssetName, FGameplayTag& GameplayCueTag, cross::UniqueString& GameplayCueName)
{
    FGameplayTag OriginalTag = GameplayCueTag;

    // In the editor, attempt to infer GameplayCueTag from our asset name (if there is no valid GameplayCueTag already).
#if WITH_EDITOR
    if (GIsEditor)
    {
        if (GameplayCueTag.IsValid() == false)
        {
            AssetName.RemoveFromStart(GAS_TEXT("Default__"));
            AssetName.RemoveFromStart(GAS_TEXT("REINST_"));
            AssetName.RemoveFromStart(GAS_TEXT("SKEL_"));
            AssetName.RemoveFromStart(GAS_TEXT("GC_"));   // allow GC_ prefix in asset name
            AssetName.RemoveFromEnd(GAS_TEXT("_c"));

            AssetName.ReplaceInline(GAS_TEXT("_"), GAS_TEXT("."), ESearchCase::CaseSensitive);

            if (!AssetName.Contains(GAS_TEXT("GameplayCue")))
            {
                AssetName = std::string(GAS_TEXT("GameplayCue.")) + AssetName;
            }

            GameplayCueTag = UGameplayTagsManager::Get().RequestGameplayTag(cross::UniqueString(*AssetName), false);
        }
        GameplayCueName = GameplayCueTag.GetTagName();
    }
#endif
    return (OriginalTag != GameplayCueTag);
}

bool UAbilitySystemGlobals::ShouldAllowGameplayModEvaluationChannels() const
{
    //return false;
    return GetDefault<UGameplayAbilitiesDeveloperSettings>()->bAllowGameplayModEvaluationChannels;
}

bool UAbilitySystemGlobals::IsGameplayModEvaluationChannelValid(EGameplayModEvaluationChannel Channel) const
{
    // Only valid if channels are allowed and the channel has a game-specific alias specified or if not using channels and the channel is Channel0
    const bool bAllowChannels = ShouldAllowGameplayModEvaluationChannels();
    return bAllowChannels ? (!GetGameplayModEvaluationChannelAlias(Channel).GetStringView().empty()) : (Channel == EGameplayModEvaluationChannel::Channel0);
}

const cross::UniqueString& UAbilitySystemGlobals::GetGameplayModEvaluationChannelAlias(EGameplayModEvaluationChannel Channel) const
{
    return GetGameplayModEvaluationChannelAlias(static_cast<int>(Channel));
}

const cross::UniqueString& UAbilitySystemGlobals::GetGameplayModEvaluationChannelAlias(int Index) const
{
    const auto DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    Assert(Index >= 0 && Index < GAS_ARRAY_COUNT(DeveloperSettings->GameplayModEvaluationChannelAliases));
    return DeveloperSettings->GameplayModEvaluationChannelAliases[Index];
}

std::vector<std::string> UAbilitySystemGlobals::GetGameplayCueNotifyPaths()
{
    Assert(false);
    return {};
    // Use set so we can append just unique paths
    //std::unordered_set<std::string> ReturnPaths = std::unordered_set(GameplayCueNotifyPaths);
    //
    //ReturnPaths.Append(GetDefault<UGameplayAbilitiesDeveloperSettings>()->GameplayCueNotifyPaths);
    //return ReturnPaths.Array();
}

void UAbilitySystemGlobals::AddGameplayCueNotifyPath(const std::string& InPath)
{
    GameplayCueNotifyPaths.push_back(InPath);
}

int UAbilitySystemGlobals::RemoveGameplayCueNotifyPath(const std::string& InPath)
{
    auto newEnd = std::remove(GameplayCueNotifyPaths.begin(), GameplayCueNotifyPaths.end(), InPath);
    if (newEnd != GameplayCueNotifyPaths.end())
    {
        int num = GameplayCueNotifyPaths.end() - newEnd;
        // Use vector's erase method to remove the elements from newEnd to the actual end.
        GameplayCueNotifyPaths.erase(newEnd, GameplayCueNotifyPaths.end());
        return num;   // Value was found and removed
    }
    return 0;
}

#if WITH_EDITOR

void UAbilitySystemGlobals::OnTableReimported(UObject* InObject)
{
    if (GIsEditor && !IsRunningCommandlet() && InObject)
    {
        UCurveTable* ReimportedCurveTable = Cast<UCurveTable>(InObject);
        if (ReimportedCurveTable && GlobalAttributeDefaultsTables.Contains(ReimportedCurveTable))
        {
            ReloadAttributeDefaults();
        }
    }
}

#endif

FGameplayAbilityActorInfo* UAbilitySystemGlobals::AllocAbilityActorInfo() const
{
    return new FGameplayAbilityActorInfo();
}

FGameplayEffectContext* UAbilitySystemGlobals::AllocGameplayEffectContext() const
{
    return new FGameplayEffectContext();
}

/** Helping function to avoid having to manually cast */
UAbilitySystemComponent* UAbilitySystemGlobals::GetAbilitySystemComponentFromActor(const GameObject* Actor, bool LookForComponent)
{
   //Assert(false);
   //return nullptr;
   if (Actor == nullptr)
   {
       return nullptr;
   }
   
   const IAbilitySystemInterface* ASI = dynamic_cast<const IAbilitySystemInterface*>(Actor);
   if (ASI)
   {
       return ASI->GetAbilitySystemComponent();
   }
   
   if (LookForComponent)
   {
       // Fall back to a component search to better support BP-only actors
       return Actor->GetComponent<UAbilitySystemComponent>();
   }
   
   return nullptr;
}

bool UAbilitySystemGlobals::ShouldPredictTargetGameplayEffects() const
{
    return GetDefault<UGameplayAbilitiesDeveloperSettings>()->PredictTargetGameplayEffects;
}

bool UAbilitySystemGlobals::ShouldReplicateActivationOwnedTags() const
{
    Assert(false);
    return false;
    //return GetDefault<UGameplayAbilitiesDeveloperSettings>()->ReplicateActivationOwnedTags;
}

// --------------------------------------------------------------------

gbf::reflection::Function* UAbilitySystemGlobals::GetGameplayCueFunction(const FGameplayTag& ChildTag, gbf::reflection::MetaClass* Class, cross::UniqueString& MatchedTag)
{
    Assert(false);
    return nullptr;
    //SCOPE_CYCLE_COUNTER(STAT_GetGameplayCueFunction);
    //
    //// A global cached map to lookup these functions might be a good idea. Keep in mind though that FindFunctionByName
    //// is fast and already gives us a reliable map lookup.
    ////
    //// We would get some speed by caching off the 'fully qualified name' to 'best match' lookup. E.g. we can directly map
    //// 'GameplayCue.X.Y' to the function 'GameplayCue.X' without having to look for GameplayCue.X.Y first.
    ////
    //// The native remapping (Gameplay.X.Y to Gameplay_X_Y) is also annoying and slow and could be fixed by this as well.
    ////
    //// Keep in mind that any UFunction* cacheing is pretty unsafe. Classes can be loaded (and unloaded) during runtime
    //// and will be regenerated all the time in the editor. Just doing a single pass at startup won't be enough,
    //// we'll need a mechanism for registering classes when they are loaded on demand.
    //
    //FGameplayTagContainer TagAndParentsContainer = ChildTag.GetGameplayTagParents();
    //
    //for (auto InnerTagIt = TagAndParentsContainer.CreateConstIterator(); InnerTagIt; ++InnerTagIt)
    //{
    //    cross::UniqueString CueName = InnerTagIt->GetTagName();
    //    if (UFunction* Func = Class->FindFunctionByName(CueName, EIncludeSuperFlag::IncludeSuper))
    //    {
    //        MatchedTag = CueName;
    //        return Func;
    //    }
    //
    //    // Native functions cant be named with ".", so look for them with _.
    //    cross::UniqueString NativeCueFuncName = *CueName.ToString().Replace(GAS_TEXT("."), GAS_TEXT("_"));
    //    if (UFunction* Func = Class->FindFunctionByName(NativeCueFuncName, EIncludeSuperFlag::IncludeSuper))
    //    {
    //        MatchedTag = CueName;   // purposefully returning the . qualified name.
    //        return Func;
    //    }
    //}
    //
    //return nullptr;
}

void UAbilitySystemGlobals::InitGlobalTags()
{
    //Assert(false);
    auto TagFromDeprecatedName = [](FGameplayTag& Tag, cross::UniqueString DeprecatedName) {
        if (!Tag.IsValid() && !DeprecatedName.GetStringView().empty())
        {
            Tag = FGameplayTag::RequestGameplayTag(DeprecatedName);
            return true;
        }
    
        return false;
    };
    
    TagFromDeprecatedName(ActivateFailIsDeadTag, ActivateFailIsDeadName);
    TagFromDeprecatedName(ActivateFailCooldownTag, ActivateFailCooldownName);
    TagFromDeprecatedName(ActivateFailCostTag, ActivateFailCostName);
    TagFromDeprecatedName(ActivateFailTagsBlockedTag, ActivateFailTagsBlockedName);
    TagFromDeprecatedName(ActivateFailTagsMissingTag, ActivateFailTagsMissingName);
    TagFromDeprecatedName(ActivateFailNetworkingTag, ActivateFailNetworkingName);
}

void UAbilitySystemGlobals::PerformDeveloperSettingsUpgrade()
{
    //Assert(false);
    //return;
    auto SyncTag = [](FGameplayTag& DestinationTag, const FGameplayTag& OurTag) {
        if (OurTag.IsValid() && DestinationTag != OurTag)
        {
            DestinationTag = OurTag;
            return true;
        }
    
        return false;
    };
    
    auto DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    
    bool bUpgraded = false;
    bUpgraded |= SyncTag(DeveloperSettings->ActivateFailCooldownTag, ActivateFailCooldownTag);
    bUpgraded |= SyncTag(DeveloperSettings->ActivateFailCostTag, ActivateFailCostTag);
    bUpgraded |= SyncTag(DeveloperSettings->ActivateFailNetworkingTag, ActivateFailNetworkingTag);
    bUpgraded |= SyncTag(DeveloperSettings->ActivateFailTagsBlockedTag, ActivateFailTagsBlockedTag);
    bUpgraded |= SyncTag(DeveloperSettings->ActivateFailTagsMissingTag, ActivateFailTagsMissingTag);
    
    //if (bUpgraded)
    //{
    //    LOG_WARN("AbilitySystemGlobals' Tags did not agree with GameplayAbilitiesDeveloperSettings.  Updating GameplayAbilitiesDeveloperSettings Config to use Tags from AbilitySystemGlobals");
    //
    //    bool bSuccess = DeveloperSettings->TryUpdateDefaultConfigFile();
    //    if (!bSuccess)
    //    {
    //        LOG_WARN("AbilitySystemGlobals config file (DefaultGame.ini) couldn't be saved. Make sure the file is writable to update it."));
    //    }
    //}
    
    // Now that the upgrade is done, copy any settings set in the DeveloperSettings back to here (so calls to UAbilitySystemGlobals::Get().SomeTag work)
    SyncTag(ActivateFailCooldownTag, DeveloperSettings->ActivateFailCooldownTag);
    SyncTag(ActivateFailCostTag, DeveloperSettings->ActivateFailCostTag);
    SyncTag(ActivateFailNetworkingTag, DeveloperSettings->ActivateFailNetworkingTag);
    SyncTag(ActivateFailTagsBlockedTag, DeveloperSettings->ActivateFailTagsBlockedTag);
    SyncTag(ActivateFailTagsMissingTag, DeveloperSettings->ActivateFailTagsMissingTag);
}

void UAbilitySystemGlobals::InitTargetDataScriptStructCache()
{
    Assert(false);
    return;
    //TargetDataStructCache.InitForType(FGameplayAbilityTargetData::StaticStruct());
    //EffectContextStructCache.InitForType(FGameplayEffectContext::StaticStruct());
}

// --------------------------------------------------------------------

void UAbilitySystemGlobals::InitGameplayCueParameters(FGameplayCueParameters& CueParameters, const FGameplayEffectSpecForRPC& Spec)
{
    //Assert(false);
    return;
    //CueParameters.AggregatedSourceTags = Spec.AggregatedSourceTags;
    //CueParameters.AggregatedTargetTags = Spec.AggregatedTargetTags;
    //CueParameters.GameplayEffectLevel = Spec.GetLevel();
    //CueParameters.AbilityLevel = Spec.GetAbilityLevel();
    //InitGameplayCueParameters(CueParameters, Spec.GetContext());
}

void UAbilitySystemGlobals::InitGameplayCueParameters_GESpec(FGameplayCueParameters& CueParameters, const FGameplayEffectSpec& Spec)
{
    //Assert(false);
    return;
    //CueParameters.AggregatedSourceTags = *Spec.CapturedSourceTags.GetAggregatedTags();
    //CueParameters.AggregatedTargetTags = *Spec.CapturedTargetTags.GetAggregatedTags();
    //
    //// Look for a modified attribute magnitude to pass to the CueParameters
    //for (const FGameplayEffectCue& CueDef : Spec.Def->GameplayCues)
    //{
    //    bool FoundMatch = false;
    //    if (CueDef.MagnitudeAttribute.IsValid())
    //    {
    //        for (const FGameplayEffectModifiedAttribute& ModifiedAttribute : Spec.ModifiedAttributes)
    //        {
    //            if (ModifiedAttribute.Attribute == CueDef.MagnitudeAttribute)
    //            {
    //                CueParameters.RawMagnitude = ModifiedAttribute.TotalMagnitude;
    //                FoundMatch = true;
    //                break;
    //            }
    //        }
    //        if (FoundMatch)
    //        {
    //            break;
    //        }
    //    }
    //}
    //
    //CueParameters.GameplayEffectLevel = Spec.GetLevel();
    //CueParameters.AbilityLevel = Spec.GetEffectContext().GetAbilityLevel();
    //
    //InitGameplayCueParameters(CueParameters, Spec.GetContext());
}

void UAbilitySystemGlobals::InitGameplayCueParameters(FGameplayCueParameters& CueParameters, const FGameplayEffectContextHandle& EffectContext)
{
    if (EffectContext.IsValid())
    {
        // Copy Context over wholesale. Projects may want to override this and not copy over all data
        CueParameters.EffectContext = EffectContext;
    }
}

// --------------------------------------------------------------------

void UAbilitySystemGlobals::StartAsyncLoadingObjectLibraries()
{
    //Assert(false);
    return;
    //if (GlobalGameplayCueManager != nullptr)
    //{
    //    GlobalGameplayCueManager->InitializeRuntimeObjectLibrary();
    //}
}

// --------------------------------------------------------------------

/** Initialize FAttributeSetInitter. This is virtual so projects can override what class they use */
void UAbilitySystemGlobals::AllocAttributeSetInitter()
{
   Assert(false);
    return;
    //GlobalAttributeSetInitter = TSharedPtr<FAttributeSetInitter>(new FAttributeSetInitterDiscreteLevels());
}

FAttributeSetInitter* UAbilitySystemGlobals::GetAttributeSetInitter() const
{
    Assert(false);
    return nullptr;
    //Assert(GlobalAttributeSetInitter);
    //return GlobalAttributeSetInitter.get();
}

void UAbilitySystemGlobals::AddAttributeDefaultTables(const cross::UniqueString OwnerName, const std::vector<std::string>& AttribDefaultTableNames)
{
    Assert(false);
    return ;
    //bool bModified = false;
    //for (const FSoftObjectPath& TableName : AttribDefaultTableNames)
    //{
    //    if (std::vector<cross::UniqueString>* Found = GlobalAttributeSetDefaultsTableNamesWithOwners.Find(TableName))
    //    {
    //        Found->Add(OwnerName);
    //    }
    //    else
    //    {
    //        std::vector<cross::UniqueString> Owners = {OwnerName};
    //        GlobalAttributeSetDefaultsTableNamesWithOwners.Add(TableName, MoveTemp(Owners));
    //
    //        UCurveTable* AttribTable = Cast<UCurveTable>(TableName.TryLoad());
    //        if (AttribTable)
    //        {
    //            GlobalAttributeDefaultsTables.AddUnique(AttribTable);
    //            bModified = true;
    //        }
    //    }
    //}
    //
    //if (bModified)
    //{
    //    ReloadAttributeDefaults();
    //}
}

void UAbilitySystemGlobals::RemoveAttributeDefaultTables(const cross::UniqueString OwnerName, const std::vector<std::string>& AttribDefaultTableNames)
{
    Assert(false);
    return;
    //bool bModified = false;
    //const UGameplayAbilitiesDeveloperSettings* DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    //for (const FSoftObjectPath& TableName : AttribDefaultTableNames)
    //{
    //    if (TableName.IsValid())
    //    {
    //        if (std::vector<cross::UniqueString>* Found = GlobalAttributeSetDefaultsTableNamesWithOwners.Find(TableName))
    //        {
    //            Found->RemoveSingle(OwnerName);
    //
    //            // If no references remain, clear the pointer in GlobalAttributeDefaultsTables to allow GC
    //            if (Found->IsEmpty())
    //            {
    //                GlobalAttributeSetDefaultsTableNamesWithOwners.Remove(TableName);
    //
    //                // Only if not listed in config file
    //                if (!DeveloperSettings->GlobalAttributeSetDefaultsTableNames.Contains(TableName))
    //                {
    //                    // Remove reference to allow GC so package can be unloaded
    //                    if (UCurveTable* AttribTable = Cast<UCurveTable>(TableName.ResolveObject()))
    //                    {
    //                        if (GlobalAttributeDefaultsTables.Remove(AttribTable) > 0)
    //                        {
    //                            bModified = true;
    //                        }
    //                    }
    //                }
    //            }
    //        }
    //    }
    //}
    //
    //if (bModified)
    //{
    //    ReloadAttributeDefaults();
    //}
}

std::vector<std::string> UAbilitySystemGlobals::GetGlobalAttributeSetDefaultsTablePaths() const
{
    Assert(false);
    return {};

    //std::vector<FSoftObjectPath> AttribSetDefaultsTables;
    //
    //PRAGMA_DISABLE_DEPRECATION_WARNINGS
    //// Handle deprecated, single global table name
    //if (GlobalAttributeSetDefaultsTableName.IsValid())
    //{
    //    AttribSetDefaultsTables.Add(GlobalAttributeSetDefaultsTableName);
    //}
    //PRAGMA_ENABLE_DEPRECATION_WARNINGS
    //
    //const UGameplayAbilitiesDeveloperSettings* DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    //AttribSetDefaultsTables.Append(DeveloperSettings->GlobalAttributeSetDefaultsTableNames);
    //
    //return AttribSetDefaultsTables;
}

void UAbilitySystemGlobals::InitAttributeDefaults()
{
    Assert(false);
    return;
    //std::vector<FSoftObjectPath> AttribSetDefaultsTables = GetGlobalAttributeSetDefaultsTablePaths();
    //for (const FSoftObjectPath& AttribSetDefaultsTablePath : AttribSetDefaultsTables)
    //{
    //    if (AttribSetDefaultsTablePath.IsValid())
    //    {
    //        UCurveTable* AttribTable = Cast<UCurveTable>(AttribSetDefaultsTablePath.TryLoad());
    //        if (ensureMsgf(AttribTable, GAS_TEXT("Could not load Global AttributeSet Defaults Table: %s"), *AttribSetDefaultsTablePath.ToString()))
    //        {
    //            GlobalAttributeDefaultsTables.AddUnique(AttribTable);
    //        }
    //    }
    //}
}

void UAbilitySystemGlobals::ReloadAttributeDefaults()
{
    Assert(false);
    return;
//    if (!GlobalAttributeDefaultsTables.IsEmpty())
//    {
//        AllocAttributeSetInitter();
//        GetAttributeSetInitter()->PreloadAttributeSetData(GlobalAttributeDefaultsTables);
//
//        // Subscribe for reimports if in the editor
//#if WITH_EDITOR
//        if (GIsEditor && !RegisteredReimportCallback)
//        {
//            GEditor->GetEditorSubsystem<UImportSubsystem>()->OnAssetReimport.AddUObject(this, &UAbilitySystemGlobals::OnTableReimported);
//            RegisteredReimportCallback = true;
//        }
//#endif
//    }
}

// --------------------------------------------------------------------

UGameplayCueManager* UAbilitySystemGlobals::GetGameplayCueManager()
{
    Assert(false);
    return nullptr;
    //if (GlobalGameplayCueManager == nullptr)
    //{
    //    const UGameplayAbilitiesDeveloperSettings* DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    //    // Loads mud specific gameplaycue manager object if specified
    //    if (GlobalGameplayCueManager == nullptr && DeveloperSettings->GlobalGameplayCueManagerName.IsValid())
    //    {
    //        GlobalGameplayCueManager = LoadObject<UGameplayCueManager>(nullptr, *DeveloperSettings->GlobalGameplayCueManagerName.ToString(), nullptr, LOAD_None, nullptr);
    //        if (GlobalGameplayCueManager == nullptr)
    //        {
    //            ABILITY_LOG(Error, GAS_TEXT("Unable to Load GameplayCueManager %s"), *DeveloperSettings->GlobalGameplayCueManagerName.ToString());
    //        }
    //    }
    //
    //    // Load specific gameplaycue manager class if specified
    //    if (GlobalGameplayCueManager == nullptr && DeveloperSettings->GlobalGameplayCueManagerClass.IsValid())
    //    {
    //        UClass* GCMClass = LoadClass<UObject>(nullptr, *DeveloperSettings->GlobalGameplayCueManagerClass.ToString(), nullptr, LOAD_None, nullptr);
    //        if (GCMClass)
    //        {
    //            GlobalGameplayCueManager = NewObject<UGameplayCueManager>(this, GCMClass, NAME_None);
    //        }
    //    }
    //
    //    if (GlobalGameplayCueManager == nullptr)
    //    {
    //        // Fallback to base native class
    //        GlobalGameplayCueManager = NewObject<UGameplayCueManager>(this, UGameplayCueManager::StaticClass(), NAME_None);
    //    }
    //
    //    GlobalGameplayCueManager->OnCreated();
    //
    //    if (GetGameplayCueNotifyPaths().IsEmpty())
    //    {
    //        AddGameplayCueNotifyPath(GAS_TEXT("/Game"));
    //        ABILITY_LOG(Warning,
    //                    GAS_TEXT("No GameplayCueNotifyPaths were specified in DefaultGame.ini under [/Script/GameplayAbilities.AbilitySystemGlobals]. Falling back to using all of /Game/. This may be slow on large projects. Consider specifying "
    //                         "which paths are to be searched."));
    //    }
    //
    //    if (GlobalGameplayCueManager->ShouldAsyncLoadObjectLibrariesAtStart())
    //    {
    //        StartAsyncLoadingObjectLibraries();
    //    }
    //}
    //
    //check(GlobalGameplayCueManager);
    //return GlobalGameplayCueManager;
}

UGameplayTagReponseTable* UAbilitySystemGlobals::GetGameplayTagResponseTable()
{
    Assert(false);
    return nullptr;
    //const UGameplayAbilitiesDeveloperSettings* DeveloperSettings = GetDefault<UGameplayAbilitiesDeveloperSettings>();
    //if (GameplayTagResponseTable == nullptr && DeveloperSettings->GameplayTagResponseTableName.IsValid())
    //{
    //    GameplayTagResponseTable = LoadObject<UGameplayTagReponseTable>(nullptr, *DeveloperSettings->GameplayTagResponseTableName.ToString(), nullptr, LOAD_None, nullptr);
    //}
    //
    //return GameplayTagResponseTable;
}

void UAbilitySystemGlobals::GlobalPreGameplayEffectSpecApply(FGameplayEffectSpec& Spec, UAbilitySystemComponent* AbilitySystemComponent) {}

bool UAbilitySystemGlobals::ShouldIgnoreCooldowns() const
{
    //Assert(false);
    //return false;
    //return UE::AbilitySystemGlobals::bIgnoreAbilitySystemCooldowns;
    return false;
}

bool UAbilitySystemGlobals::ShouldIgnoreCosts() const
{
    //Assert(false);
    //return false;
    //return UE::AbilitySystemGlobals::bIgnoreAbilitySystemCosts;
    return false;
}

#if WITH_EDITOR
void UAbilitySystemGlobals::OnPreBeginPIE(const bool bIsSimulatingInEditor)
{
    ResetCachedData();
}
#endif   // WITH_EDITOR

void UAbilitySystemGlobals::ResetCachedData()
{
    Assert(false);
    return;
    //IGameplayCueInterface::ClearTagToFunctionMap();
    //FActiveGameplayEffectHandle::ResetGlobalHandleMap();
}

void UAbilitySystemGlobals::HandlePreLoadMap(const FWorldContext& WorldContext, const std::string& MapName)
{
    Assert(false);
    return;
    //// We don't want to reset for PIE since this is shared memory (which would have received OnPreBeginPIE).
    //if (WorldContext.PIEInstance > 0)
    //{
    //    return;
    //}
    //
    //// If we are preloading a map but coming from an existing map, then we should wait until the previous map is cleaned up,
    //// otherwise we'll end up stomping FActiveGameplayEffectHandle map.
    //if (const UWorld* InWorld = WorldContext.World())
    //{
    //    FWorldDelegates::OnPostWorldCleanup.AddWeakLambda(InWorld, [InWorld](UWorld* WorldParam, bool bSessionEnded, bool bCleanupResources) {
    //        if (WorldParam == InWorld)
    //        {
    //            ResetCachedData();
    //        }
    //    });
    //
    //    return;
    //}
    //
    //ResetCachedData();
}

void UAbilitySystemGlobals::Notify_OpenAssetInEditor(std::string AssetName, int AssetType)
{
    AbilityOpenAssetInEditorCallbacks.Broadcast(AssetName, AssetType);
}

void UAbilitySystemGlobals::Notify_FindAssetInEditor(std::string AssetName, int AssetType)
{
    AbilityFindAssetInEditorCallbacks.Broadcast(AssetName, AssetType);
}

void UAbilitySystemGlobals::NonShipping_ApplyGlobalAbilityScaler_Rate(float& Rate)
{
    Assert(false);
    return;
//#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
//    Rate *= UE::AbilitySystemGlobals::AbilitySystemGlobalScaler;
//#endif
}

void UAbilitySystemGlobals::NonShipping_ApplyGlobalAbilityScaler_Duration(float& Duration)
{
    Assert(false);
    return;
//#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
//    if (UE::AbilitySystemGlobals::AbilitySystemGlobalScaler > 0.f)
//    {
//        Duration /= UE::AbilitySystemGlobals::AbilitySystemGlobalScaler;
//    }
//#endif
}

//void FNetSerializeScriptStructCache::InitForType(UScriptStruct* InScriptStruct)
//{
//    ScriptStructs.Reset();
//
//    // Find all script structs of this type and add them to the list
//    // (not sure of a better way to do this but it should only happen once at startup)
//    for (TObjectIterator<UScriptStruct> It; It; ++It)
//    {
//        if (It->IsChildOf(InScriptStruct))
//        {
//            ScriptStructs.Add(*It);
//        }
//    }
//
//    ScriptStructs.Sort([](const UScriptStruct& A, const UScriptStruct& B) { return A.GetName().ToLower() > B.GetName().ToLower(); });
//}
//
//bool FNetSerializeScriptStructCache::NetSerialize(FArchive& Ar, UScriptStruct*& Struct)
//{
//    if (Ar.IsSaving())
//    {
//        int idx;
//        if (ScriptStructs.Find(Struct, idx))
//        {
//            check(idx < (1 << 8));
//            uint8 b = idx;
//            Ar.SerializeBits(&b, 8);
//            return true;
//        }
//        ABILITY_LOG(Error, GAS_TEXT("Could not find %s in ScriptStructCache"), *GetNameSafe(Struct));
//        return false;
//    }
//    else
//    {
//        uint8 b = 0;
//        Ar.SerializeBits(&b, 8);
//        if (ScriptStructs.IsValidIndex(b))
//        {
//            Struct = ScriptStructs[b];
//            return true;
//        }
//
//        ABILITY_LOG(Error, GAS_TEXT("Could not find script struct at idx %d"), b);
//        return false;
//    }
//}
}   // namespace cegf