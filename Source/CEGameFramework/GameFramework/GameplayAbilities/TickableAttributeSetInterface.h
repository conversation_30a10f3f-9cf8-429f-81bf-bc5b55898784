// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once
#include "Utils/GASMacros.h"

namespace cegf{

class ITickableAttributeSetInterface
{
	GENERATED_IINTERFACE_BODY()

public:
	/**
	 * Ticks the attribute set by DeltaTime seconds
	 * 
	 * @param DeltaTime Size of the time step in seconds.
	 */
	virtual void Tick(float DeltaTime) = 0;

	/**
	* Does this attribute set need to tick?
	*
	* @return true if this attribute set should currently be ticking, false otherwise.
	*/
	virtual bool ShouldTick() const = 0;
};

}