#pragma once
#include "GameObjects/GameObject.h"
#include "GameplayAbilities/AbilitySystemComponent.h"

namespace cegf {

class LocalTester
{
public:
    template<typename T>
    void TestEqual(const std::string& Message, T value, T valueShouldBe)
    {
        Assert(value == valueShouldBe);
        LOG_INFO("Test {} : current = {}, expected = {} ", Message, value, valueShouldBe);
    }
};

class GAMEFRAMEWORK_API CEMeta(Reflect) UTestExecutionCalculation : public UGameplayEffectExecutionCalculation
{
    int TestNum = 123;

public:
    StaticMetaClassName(UTestExecutionCalculation)
    CEMeta(Reflect)
    UTestExecutionCalculation();

    virtual void Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams, FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const override;
};

class GAMEFRAMEWORK_API CEMeta(Reflect, Puerts, Cli) GASTestObject : public GameObject
{
public:
    CEGameplayInternal() StaticMetaClassName(GASTestObject) GASTestObject();

    friend class ScopedTestLocker;
    virtual ~GASTestObject();
    virtual void Serialize(SerializeNode & node, SerializeContext & context) const override;
    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;
    virtual void InitializeComponents() override;
    virtual void PostInitializeComponents() override;
    virtual void Tick(float deltaTime) override;

protected:
    void TickWorld(float Time);

    void RunGameplayEffectTest();

    void RunGameplayAbilityTest();

    void RunGameplayGameplayTagTest();

private:
    UAbilitySystemComponent* SourceComponent;
    UAbilitySystemComponent* DestComponent;
    GameObjectPtr SourceObject;
    GameObjectPtr DestObject;
    std::shared_ptr<LocalTester> Test;
    GameWorld* World;

    void InitTestData();
    void RemoveTestData();

    std::atomic<int> NumTest = 0;
};

}   // namespace cegf
