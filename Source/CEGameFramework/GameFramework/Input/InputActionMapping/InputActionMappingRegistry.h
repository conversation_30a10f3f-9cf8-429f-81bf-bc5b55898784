#pragma once
#include "GameFramework/GameFrameworkTypes.h"
#include "Resource/InputActionMappingResource.h"

namespace cegf
{

// A singleton class to manually export input action mapping related data info to editor 
class GAMEFRAMEWORK_API CEMeta(Cli) InputActionMappingRegistry final
{
public:
    CEMeta(Cli)
    InputActionMappingRegistry() = default;
    ~InputActionMappingRegistry() = default;

    CEMeta(Cli)
    static InputActionMappingRegistry& Instance();

    void RegisterInputActionMappingClasses();

    void RegisterInputModifier(const gbf::reflection::MetaClass* meta, bool updateDefault = false);

    void RegisterInputTrigger(const gbf::reflection::MetaClass* meta, bool updateDefault = false);
    
    // TODO(hendrikwang): Current Cli code generation recognizes function container return type as UnknowKeeper^
    // Thus directly export container data struct to C#

    CEMeta(Cli)
    std::vector<std::string> Modifiers;

    CEMeta(Cli)
    std::vector<std::string> Triggers;

    CEMeta(Cli)
    std::string DefaultInputModifier = "";

    CEMeta(Cli)
    std::string DefaultInputTrigger = "";

private:
    std::unordered_map<std::string, const gbf::reflection::MetaClass*> mInputModifierMetaClasses;
    std::unordered_map<std::string, const gbf::reflection::MetaClass*> mInputTriggerMetaClasses;
};

}   // namespace cegf
