namespace cross::ecs::detail
{
template <size_t Mask,typename ...Components>
template <size_t Index, typename Tuple>
auto TQueryResult<Mask,Components...>::GetComponentDataPointerHandle(UInt32 PIIndex, UInt32 ChunkIndex, UInt32 ChunkOffset)
{
	using PointerType = std::add_pointer_t<typename std::tuple_element_t<Index, Tuple>::HandledType>;
	using ResultType =  std::tuple_element_t<Index, Tuple>;
	// decompose the dataInfos
	auto&[table, dataInfos] = mMatchDatas->At(PIIndex);

	// get the chunk data information for the specific index
	auto* dataInfo = dataInfos[Index];

	Chunk* chunk = table->Chunks()[ChunkIndex].get();
	UInt8* beginPostion = reinterpret_cast<UInt8*>(chunk);
	// return the pointer of begin with offset added
	auto pointer =  reinterpret_cast<PointerType>(beginPostion + dataInfo->Offset) + ChunkOffset;
	ResultType result;
	return result.From(pointer, GetDataRaceGuardPtr(chunk, ChunkOffset, dataInfo->Index), GetIDComponent(chunk,ChunkOffset));
}

template <size_t Mask, typename ...Components>
template <typename Tuple, size_t ... Indices>
auto TQueryResult<Mask,Components...>::GetComponentsDataPointerHandle(UInt32 PIIndex, UInt32 ChunkIndex, UInt32 ChunkOffset,std::index_sequence<Indices...>)
{
	static_assert(sizeof...(Indices) == Count);

	if constexpr (Count == 1)
	{
		return GetComponentDataPointerHandle<0, Tuple>(PIIndex,ChunkIndex,ChunkOffset);
	}
	else
	{
		return std::make_tuple(GetComponentDataPointerHandle<Indices, Tuple>(PIIndex, ChunkIndex, ChunkOffset)...);
	}
}

template <size_t Mask, typename ... Components>
inline void TQueryResult<Mask,Components...>::AddPrototype(const DataTable* table, DataInfoType const& dataInfos)
{
	mMatchDatas->EmplaceBack(const_cast<DataTable*>(table), dataInfos);
	UInt32 accumulate = table->GetPrototype()->CapacityInChunk * (UInt32)table->Chunks().size();
	if (mQueryInfo->GetSize()) accumulate += mQueryInfo->Back().EntityAccumulations;
	mQueryInfo->EmplaceBack(QueryInfo{table->GetPrototype()->CapacityInChunk, (UInt32)table->Chunks().size(), table->EntityCount(), accumulate});
	mEntityNum += table->EntityCount();
}

template <size_t Mask, typename ... Components>
inline auto TQueryResult<Mask,Components...>::begin()
{
    if (mMatchDatas->IsEmpty()) return end();
	auto prototypeItr = mMatchDatas->begin();
	if constexpr (Mask == 1) // TODO(yuanwan): Only support VisibilityMask for now...
	{
        int firstEntityIndexInChunk = -1;
        ChunkIterator chunkItr = prototypeItr->first->ChunkBegin();
        const ChunkIterator chunkEnd = std::prev(mMatchDatas->end())->first->ChunkEnd();
        while (prototypeItr != std::prev(mMatchDatas->end()) || chunkItr != chunkEnd)
        {
            if (chunkItr == prototypeItr->first->ChunkEnd())
            {
                prototypeItr++;
                chunkItr = prototypeItr->first->ChunkBegin();
            }
            firstEntityIndexInChunk = (*chunkItr)->mEntityMask[Mask].Findfirstbit(true);
            if (firstEntityIndexInChunk >= 0)
            {
                break;
            }
            chunkItr++;
        }
        if (firstEntityIndexInChunk < 0)
        {
            return end();
        }
        auto iter = Iterator{ ToUInt32(firstEntityIndexInChunk), chunkItr, prototypeItr, mMatchDatas->begin(), mMatchDatas->end(), mMask};
        return iter;
	}
	else
	{
		ChunkIterator chunkItr{};
		if (!mMatchDatas->IsEmpty())
		{
			chunkItr = prototypeItr->first->ChunkBegin();
		}
		return Iterator{ 0, chunkItr, prototypeItr, mMatchDatas->begin(), mMatchDatas->end(), mMask};
	}
}

template <size_t UseMask, typename ... Components>
inline auto TQueryResult<UseMask,Components...>::end()
{
	auto prototypeItr = mMatchDatas->end();
	ChunkIterator chunkItr{};
	if (!mMatchDatas->IsEmpty())
	{
		chunkItr = std::prev(prototypeItr)->first->ChunkEnd();
	}
	return Iterator{ 0, chunkItr, prototypeItr, mMatchDatas->begin(), mMatchDatas->end(), mMask};
}

template <size_t Mask, typename ... Components>
inline auto TQueryResult<Mask,Components...>::cbegin() const
{
    if (mMatchDatas->IsEmpty()) return cend();
	auto prototypeConstItr = mMatchDatas->cbegin();
	if constexpr (Mask == 1)
	{
        int firstEntityIndexInChunk = -1;
        ChunkConstIterator chunkItr = prototypeConstItr->first->ChunkConstBegin();
        const ChunkIterator chunkEnd = std::prev(mMatchDatas->cend())->first->ChunkConstEnd();
        while (prototypeConstItr != std::prev(mMatchDatas->cend()) || chunkItr != chunkEnd)
        {
            if (chunkItr == prototypeConstItr->first->ChunkConstEnd())
            {
                prototypeConstItr++;
                chunkItr = prototypeConstItr->first->ChunkConstBegin();
            }
            firstEntityIndexInChunk = (*chunkItr)->mEntityMask[Mask].Findfirstbit(true);
            if (firstEntityIndexInChunk >= 0)
            {
                break;
            }
            chunkItr++;
        }
        if (firstEntityIndexInChunk < 0)
        {
            return end();
        }
        auto iter = Iterator{ firstEntityIndexInChunk, chunkItr, prototypeConstItr, mMatchDatas->cbegin(), mMatchDatas->cend(), mMask };
        return iter;
	}
	else
	{
		ChunkConstIterator chunkConstItr{};
		if (!mMatchDatas->IsEmpty())
		{
			chunkConstItr = prototypeConstItr->first->ChunkConstBegin();
		}
		return ConstIterator{ 0, chunkConstItr, prototypeConstItr, mMatchDatas->cbegin(), mMatchDatas->cend(),mMask };
	}
}

template <size_t Mask, typename ... Components>
inline auto TQueryResult<Mask,Components...>::cend() const
{
	auto prototypeConstItr = mMatchDatas->cend();
	ChunkConstIterator chunkConstItr{};
	if (!mMatchDatas->IsEmpty())
	{
		chunkConstItr = std::prev(prototypeConstItr)->first->ChunkConstEnd();
	}
	return ConstIterator{ 0, chunkConstItr, prototypeConstItr, mMatchDatas->cbegin(), mMatchDatas->cend() ,mMask};
}

template <size_t Mask, typename ... Components>
inline auto TQueryResult<Mask,Components...>::begin() const
{
	return cbegin();
}

template <size_t Mask, typename ... Components>
inline auto TQueryResult<Mask,Components...>::end() const
{
	return cend();
}
	
template<size_t Mask, typename ... Components>
template<typename Const>
inline TQueryResult<Mask,Components...>::TBasicIterator<Const>::TBasicIterator(UInt32 index, ChunkIteratorType chunkItr, PrototypeIteratorType prototypeItr, PrototypeIteratorType const prototypeBegin, PrototypeIteratorType const prototypeEnd,RuntimeMaskType type) noexcept
	: mIndexInChunk(index)
	, mChunkItr(chunkItr)
	, mPrototypeItr(prototypeItr)
    , mPrototypeBegin(prototypeBegin)
	, mPrototypeEnd(prototypeEnd)
	, mMaskType(type)
{
	if (prototypeItr != prototypeEnd)
	{
		InitializeComponentPointers();
	}
    else
    {
        if constexpr (Count == 1)
        {
            mComponentPointerData = nullptr;
        }
        else
        {
            constexpr std::tuple<TAddPointerOrConstT<Components>...> AllEmptyResult{};
            mComponentPointerData = AllEmptyResult;
        }
    }
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline typename TQueryResult<Mask,Components...>::template TBasicIterator<Const>      // result type
	TQueryResult<Mask,Components...>::TBasicIterator<Const>::operator++()    // operator
{                                                                       // operator body
	MoveForward();
	return *this;
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline typename TQueryResult<Mask,Components...>::template TBasicIterator<Const>&              // result type
	TQueryResult<Mask,Components...>::TBasicIterator<Const>::operator++(int) // operator
{                                                                       // operator body
    TBasicIterator itr = *this;
	MoveForward();
	return itr;
    
}

template<size_t Mask, typename... Components>
template<typename Const>
inline typename TQueryResult<Mask, Components...>::template TBasicIterator<Const> TQueryResult<Mask, Components...>::TBasicIterator<Const>::operator+(int steps)
{
    TBasicIterator itr = *this;
    itr.MoveForward(steps);
    return itr;
}

template<size_t Mask, typename... Components>
template<typename Const>
inline typename TQueryResult<Mask, Components...>::template TBasicIterator<Const>& TQueryResult<Mask, Components...>::TBasicIterator<Const>::operator+=(int steps)
{
    MoveForward(steps);
    return *this;
}

template<size_t Mask, typename ...Components>
template<typename Const>
template<size_t ...Index>
inline auto TQueryResult<Mask,Components...>::TBasicIterator<Const>::ToDataHandle(std::index_sequence<Index...>) const &
{
	auto* idComponent = GetIDComponent(mChunkItr->get(), mIndexInChunk);
	if constexpr (Count == 1)
	{
		return THandleT<std::tuple_element_t<0, std::tuple<Components...>>>::From(mComponentPointerData, GetDataRaceGuardPtr(mChunkItr->get(), mIndexInChunk, mComponentIndex[0]), idComponent);
	}
	else
	{
		return std::make_tuple(THandleT<std::tuple_element_t<Index, std::tuple<Components...>>>::From(std::get<Index>(mComponentPointerData), GetDataRaceGuardPtr(mChunkItr->get(), mIndexInChunk, mComponentIndex[Index]), idComponent)...);
	}
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline typename TQueryResult<Mask,Components...>::template TBasicIterator<Const>               // result type
	TQueryResult<Mask,Components...>::TBasicIterator<Const>::operator--()    // operator
{                                                                       // operator body
    MoveBackward();
	return *this;
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline typename TQueryResult<Mask,Components...>::template TBasicIterator<Const>&              // result type
	TQueryResult<Mask,Components...>::TBasicIterator<Const>::operator--(int) // operator
{                                                                       // operator body
    TBasicIterator itr = *this;
	MoveBackward();
	return itr;
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline bool TQueryResult<Mask,Components...>::TBasicIterator<Const>::operator== (TBasicIterator const& other) const noexcept
{
	return mIndexInChunk == other.mIndexInChunk
		&& mPrototypeItr == other.mPrototypeItr
		&& mChunkItr == other.mChunkItr;
}

template<size_t Mask,typename ... Components>
template<typename Const>
inline bool TQueryResult<Mask,Components...>::TBasicIterator<Const>::operator!= (TBasicIterator const& other) const noexcept
{
	return !(*this == other);
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline auto TQueryResult<Mask,Components...>::TBasicIterator<Const>::operator*() const &
{
	return ToDataHandle(std::make_index_sequence<Count>{});
}

template <size_t Mask,typename ... Components>
TQueryResult<Mask, Components...>::TQueryResult(FrameAllocator* allocator, FrameStage stage)
{
	((RequiredMatcher |= Components::GetDesc()->GetMask()), ...);
    mMatchDatas = allocator->CreateFrameContainer<DataContainer>(stage, 8); // TODO(yuanwan): consider about capacity
    mQueryInfo = allocator->CreateFrameContainer<FrameVector<QueryInfo>>(stage, 8);
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline auto TQueryResult<Mask,Components...>::TBasicIterator<Const>::Get() const &
{
	return mComponentPointerData;
}

template<size_t UsedMask, typename ... Components>
template<typename Const>
inline void TQueryResult<UsedMask, Components...>::TBasicIterator<Const>::MoveForward()
{
    if constexpr (UsedMask == 0)
	{// check the iterator of the prototype is at the end
		if (mPrototypeItr == mPrototypeEnd)
		{
			DEBUG_ASSERT(mIndexInChunk == 0);
			return;
		}

		// get the prototype instance pointer
		auto table = mPrototypeItr->first;
		DEBUG_ASSERT(mChunkItr != table->ChunkEnd());


		auto* chunk = mChunkItr->get();
		// forward index
		mIndexInChunk++;// try advance first if not then use mask
		DEBUG_ASSERT(chunk);
		if (mIndexInChunk == chunk->ElementCount)
		{
			mIndexInChunk = 0;
			mChunkItr++;
			if (mChunkItr == table->ChunkEnd())
			{
				// update prototype iterator
				mPrototypeItr++;

				// return if prototype is at the end
				// which means that all data is iterated
				if (mPrototypeItr == mPrototypeEnd)
				{
					mIndexInChunk = 0;
					return;
				}
				// update chunk iterator
				table = mPrototypeItr->first;
				mChunkItr = table->ChunkBegin();

			}
			// since we have transfered to another chunk,
			// we need to make the data pointer to point at the memory in the new chunk
			InitializeComponentPointers();
		}
		else
		{
			MoveForwardAllComponentPointers();
		}
	}
#if 1 // Use bit comparison (faster when visible entities is more than 1/3)
    else
    {
        if (mPrototypeItr == mPrototypeEnd)
        {
            DEBUG_ASSERT(mIndexInChunk == 0);
            return;
        }

        // get the prototype instance pointer
        auto table = mPrototypeItr->first;
        DEBUG_ASSERT(mChunkItr != table->ChunkEnd());

        while (true)
        {
            auto* chunk = mChunkItr->get();
            // forward index
            mIndexInChunk++;
            DEBUG_ASSERT(chunk);
            if (mIndexInChunk == chunk->ElementCount)
            {
                mIndexInChunk = 0;
                mChunkItr++;
                if (mChunkItr == table->ChunkEnd())
                {
                    // update prototype iterator
                    mPrototypeItr++;

                    // return if prototype is at the end
                    // which means that all data is iterated
                    if (mPrototypeItr == mPrototypeEnd)
                    {
                        mIndexInChunk = 0;
                        return;
                    }
                    // update chunk iterator
                    table = mPrototypeItr->first;
                    mChunkItr = table->ChunkBegin();

                }
                // since we have transfered to another chunk,
                // we need to make the data pointer to point at the memory in the new chunk
                InitializeComponentPointers();
                if ((*mChunkItr)->mEntityMask[UsedMask].GetBit(mIndexInChunk))
                {
                    break;
                }
            }
            else
            {
                MoveForwardAllComponentPointers();
                if ((*mChunkItr)->mEntityMask[UsedMask].GetBit(mIndexInChunk))
                {
                    break;
                }
            }
        }
    }
#else // Use bit scan
	else
	{
        if (mPrototypeItr == mPrototypeEnd)
        {
            DEBUG_ASSERT(mIndexInChunk == 0);
            return;
        }

        // get the prototype instance pointer
        auto table = mPrototypeItr->first;
        DEBUG_ASSERT(mChunkItr != table->ChunkEnd());

        int nextIndexInChunk = -1;
        // int curIndexInChunk = mIndexInChunk;

        // find in this chunk
        nextIndexInChunk = (*mChunkItr)->mEntityMask[Mask].Findnextbit(mIndexInChunk % BitArray::BitsPerBlock, true);
        // find in this prototype
        if (nextIndexInChunk < 0)
        {
            ++mChunkItr;
            for (mChunkItr; mChunkItr != mPrototypeItr->first->ChunkEnd(); ++mChunkItr)
            {
                nextIndexInChunk = (*mChunkItr)->mEntityMask[Mask].Findfirstbit(true);
                if (nextIndexInChunk >= 0)
                {
                    break;
                }
            }
        }
        // find in others
        if (nextIndexInChunk < 0)
        {
            ++mPrototypeItr;
            for (mPrototypeItr; mPrototypeItr != mPrototypeEnd; ++mPrototypeItr)
            {
                for (mChunkItr = mPrototypeItr->first->ChunkBegin(); mChunkItr != mPrototypeItr->first->ChunkEnd(); ++mChunkItr)
                {
                    nextIndexInChunk = (*mChunkItr)->mEntityMask[Mask].Findfirstbit(true);
                    if (nextIndexInChunk >= 0)
                    {
                        break;
                    }
                }
                if (nextIndexInChunk >= 0)
                {
                    break;
                }
            }
        }
        if (nextIndexInChunk < 0)
        {
            mPrototypeItr = mPrototypeEnd;
            mIndexInChunk = 0;
            return;
        }
        mIndexInChunk = nextIndexInChunk;
        InitializeComponentPointers();
	}
#endif
}

template<size_t Mask, typename... Components>
template<typename Const>
inline void TQueryResult<Mask, Components...>::TBasicIterator<Const>::MoveForward(UInt32 steps)
{
    if constexpr (Mask == 0)
    {   // check the iterator of the prototype is at the end
        if (mPrototypeItr == mPrototypeEnd)
        {
            DEBUG_ASSERT(mIndexInChunk == 0);
            return;
        }

        // forward index
        while (steps)
        {
            // get the prototype instance pointer
            auto table = mPrototypeItr->first;
            DEBUG_ASSERT(mChunkItr != table->ChunkEnd());

            auto* chunk = mChunkItr->get();
            DEBUG_ASSERT(chunk);

            if (mIndexInChunk + steps >= chunk->ElementCount)
            {
                steps -= (chunk->ElementCount - mIndexInChunk);
                mIndexInChunk = 0;
                mChunkItr++;
                if (mChunkItr == table->ChunkEnd())
                {
                    // update prototype iterator
                    mPrototypeItr++;

                    // return if prototype is at the end
                    // which means that all data is iterated
                    if (mPrototypeItr == mPrototypeEnd)
                    {
                        mIndexInChunk = 0;
                        return;
                    }
                    // update chunk iterator
                    table = mPrototypeItr->first;
                    mChunkItr = table->ChunkBegin();
                }
                // since we have transfered to another chunk,
                // we need to make the data pointer to point at the memory in the new chunk
                InitializeComponentPointers();
            }
            else
            {
                mIndexInChunk += steps;
                MoveForwardAllComponentPointers(steps);
                steps = 0;
            }
        }
    }
    else
    {
        Assert(false);
    }
}

template<size_t Mask,typename ... Components>
template<typename Const>
inline void TQueryResult<Mask,Components...>::TBasicIterator<Const>::InitializeComponentPointers()
{
	mComponentPointerData = GetInitialComponentsDataPointer<std::tuple<Components...>>(std::make_index_sequence<Count>{});
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline void TQueryResult<Mask,Components...>::TBasicIterator<Const>::MoveForwardAllComponentPointers(UInt32 Steps)
{
	if constexpr (Count > 1)
	{
		DoOperationOnComponentsData([Steps](auto*& pointer) { pointer += Steps; }, std::make_index_sequence<Count>{});
	}
	else
	{
		mComponentPointerData += Steps;
	}
}

template<size_t Mask, typename ... Components>
template<typename Const>
inline void TQueryResult<Mask,Components...>::TBasicIterator<Const>::MoveBackwardAllComponentPointers(UInt32 Steps)
{
	if constexpr (Count > 1)
	{
		DoOperationOnComponentsData([Steps](auto*& pointer) { pointer -= Steps; }, std::make_index_sequence<Count>{});
	}
	else
	{
		mComponentPointerData -= Steps;
	}
}

template<size_t Mask, typename ... Components>
template<typename Const>
template <size_t Index, typename Tuple>
inline auto TQueryResult<Mask,Components...>::TBasicIterator<Const>::GetInitialComponentDataPointer(UInt8* begin)
{
	using ResultType = std::add_pointer_t<std::tuple_element_t<Index, Tuple>>;

	// decompose the dataInfos
	auto&[_, dataInfos] = *mPrototypeItr;

	// get the chunk data information for the specific index
	auto* dataInfo = dataInfos[Index];
		
	mOffsets[Index] = (UInt16)dataInfo->Offset;
	mComponentIndex[Index] = UInt8(dataInfo->Index);
	// return the pointer of begin with offset added
	return reinterpret_cast<ResultType>(begin + dataInfo->Offset) + mIndexInChunk;
}

template<size_t Mask,typename ... Components>
template<typename Const>
template <typename Tuple, size_t ... Indices>
inline auto TQueryResult<Mask,Components...>::TBasicIterator<Const>::GetInitialComponentsDataPointer(std::index_sequence<Indices...>)
{
	static_assert(sizeof...(Indices) == Count);

	// get the data part of the chunk
	auto chunkDataPointer = reinterpret_cast<UInt8*>(mChunkItr->get());
	if constexpr (Count == 1)
	{
		return GetInitialComponentDataPointer<0, Tuple>(chunkDataPointer);
	}
	else
	{
		return std::make_tuple(GetInitialComponentDataPointer<Indices, Tuple>(chunkDataPointer)...);
	}
}

template<size_t Mask, typename ... Components>
template<typename Const>
template <typename Operation, size_t ... Indices>
inline void TQueryResult<Mask,Components...>::TBasicIterator<Const>::DoOperationOnComponentsData(Operation operation, std::index_sequence<Indices...>)
{
	(operation(std::get<Indices>(mComponentPointerData)), ...);
}

template<size_t Mask, typename ... Components>
template <typename Func, size_t ... Indices>
inline void TQueryResult<Mask,Components...>::ApplyForEachFunc(Func operation, TBasicIterator<std::false_type> itr, std::index_sequence<Indices...>)
{
	operation(std::get<Indices>(*itr)...);
}
	
template<size_t Mask, typename ... Components>
template <typename Func, size_t ... Indices>
inline void TQueryResult<Mask,Components...>::ApplyForEachFunc(Func operation, ComponentDatas data, std::index_sequence<Indices...>)
{
	operation(std::get<Indices>(data)...);
}

template<size_t Mask, typename ...Components>
template<typename Func>
auto TQueryResult<Mask,Components...>::ForEach(Func func)
{
	///Assume all chunks are tight for now
	for (UInt32 i = 0; i < mMatchDatas->GetSize(); i++)
	{
		for (UInt32 j = 0; j < mQueryInfo->At(i).EntityCounts; j++)
		{
			UInt32 Chunkidx = j / mQueryInfo->At(i).CapacityChunk;
			UInt32 Chunkbias = j - (Chunkidx) * mQueryInfo->At(i).CapacityChunk;
			auto ComponentPointerDatas = GetComponentsDataPointerHandle<std::tuple<ComponentHandle<Components>...>>(i, Chunkidx, Chunkbias, std::make_index_sequence<Count>{});
			ApplyForEachFunc(func, ComponentPointerDatas, std::make_index_sequence< sizeof...(Components)>{});
		}
	}
}
	
template<size_t Mask, typename ...Components>
inline auto TQueryResult<Mask,Components...>::operator[] (UInt32 entityindex)
{
    UInt32 PIIndex = 1;
    UInt32 EntitiesNum = mQueryInfo->At(0).EntityCounts;
    while (entityindex >= EntitiesNum)
    {
        EntitiesNum += mQueryInfo->At(PIIndex).EntityCounts;
        PIIndex++;
    }
    PIIndex = PIIndex - 1;
    UInt32 j = entityindex - (EntitiesNum - mQueryInfo->At(PIIndex).EntityCounts);
    UInt32 Chunkidx = j / mQueryInfo->At(PIIndex).CapacityChunk;
    UInt32 Chunkbias = j - (Chunkidx)*mQueryInfo->At(PIIndex).CapacityChunk;
    return GetComponentsDataPointerHandle<std::tuple<ComponentHandle<Components>...>>(PIIndex, Chunkidx, Chunkbias, std::make_index_sequence<Count>{});
}
	
template<size_t Mask,typename ...Components>
template<typename ...FilterComponents, typename>
inline TQueryResult<Mask,Components...>& TQueryResult<Mask,Components...>::Exclude()
{
	((ExcludeMatcher |= FilterComponents::GetDesc()->GetMask()), ...);
	//mMatchDatas->Erase(std::remove_if(mMatchDatas->begin(), mMatchDatas->end(),
    //	[this](ValueType x) 
	//	{
    //		auto result = x.first->GetPrototype()->ComponentMask & ExcludeMatcher;
    //		return !result.IsZero();
    // }), mMatchDatas->end());

    Assert(mMatchDatas->GetSize() == mQueryInfo->GetSize());

    auto matchDataItr = mMatchDatas->begin();
    auto queryDataItr = mQueryInfo->begin();

    UInt32 accumulate = 0;
    mEntityNum = 0;

    while (matchDataItr != mMatchDatas->end())
    {
        auto result = (*matchDataItr).first->GetPrototype()->ComponentMask & ExcludeMatcher;
        if (!result.IsZero())
        {
            matchDataItr = mMatchDatas->Erase(matchDataItr, matchDataItr + 1);
            queryDataItr = mQueryInfo->Erase(queryDataItr, queryDataItr + 1);
        }
        else
        {
            auto& table = (*matchDataItr).first;
            UInt32 tableSize  = table->GetPrototype()->CapacityInChunk * (UInt32)table->Chunks().size();
            accumulate += tableSize;
            (*queryDataItr).EntityAccumulations = accumulate;
            mEntityNum += table->EntityCount();
            matchDataItr++;
            queryDataItr++;
        }
    }

	return *this;
}

template<size_t Mask, typename ...Components>
template<typename Func>
void TQueryResult<Mask, Components...>::ParallelForEach(Func func)
{
	threading::ParallelFor(mEntityNum, [this, func](auto Index)
	{
		UInt32 PIIndex = 1;
		UInt32 EntitiesNum = mQueryInfo->At(0).EntityCounts;
		while ((UInt32)Index >= EntitiesNum)
		{
			EntitiesNum += mQueryInfo->At(PIIndex).EntityCounts;
			PIIndex++;
		}
		PIIndex = PIIndex - 1;
		UInt32 j = Index - (EntitiesNum - mQueryInfo->At(PIIndex).EntityCounts);
		UInt32 Chunkidx = j / mQueryInfo->At(PIIndex).CapacityChunk;
		UInt32 Chunkbias = j - (Chunkidx)* mQueryInfo->At(PIIndex).CapacityChunk;
        if constexpr (Mask == 1)
        {
            auto& [table, dataInfos] = mMatchDatas->At(PIIndex);
            Chunk* chunk = table->Chunks()[Chunkidx].get();
            if (chunk->mEntityMask[Mask].GetBit(Chunkbias))
            {
                auto ComponentPointerDatas = GetComponentsDataPointerHandle<std::tuple<ComponentHandle<Components>...>>(PIIndex, Chunkidx, Chunkbias, std::make_index_sequence<Count>{});
                ApplyForEachFunc(func, ComponentPointerDatas, std::make_index_sequence<sizeof...(Components)>{});
            }
        }
		else
		{
            auto ComponentPointerDatas = GetComponentsDataPointerHandle<std::tuple<ComponentHandle<Components>...>>(PIIndex, Chunkidx, Chunkbias, std::make_index_sequence<Count>{});
            ApplyForEachFunc(func, ComponentPointerDatas, std::make_index_sequence<sizeof...(Components)>{});
		}
	}
	);
}
	
//template<size_t DirectBlock, typename ...Components>
//inline std::vector<ecs::EntityID> TQueryResult<DirectBlock,Components...>::ToEntityArray()
//{
//	std::vector<ecs::EntityID> result;
//	for (auto itr = begin(); itr != end(); itr++)
//	{
//		result.push_back((*itr).GetEntityID());
//	}
//	return std::move(result);
//}
}