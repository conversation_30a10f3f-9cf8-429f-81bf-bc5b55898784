#include "TLBSProcessor.h"
#include "Exporter/TLBSExporter.h"
#include "GPUBakingCore/ImportExport.h"
#include "GPUBakingCore/LightmassCommonData.h"
#include "GPUBakingCore/VolumetricLightMapEncoding.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/LightProbeVolumeSystemG.h"
#include "imageio.h"

#include <stdio.h>
#include <io.h>

#include <corecrt_io.h>
#include <corecrt_wdirect.h>
#include "CrossBase/Threading/Task.h"
#include "CECommon/Utilities/Timer.h"

#include "Editor/LightmapBaker/Prt/PrtCommon.h"

NS_GPUBAKING_BEGIN

/*-----------------------------------------------------------------------------
    FTLBSProcessor
-----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
    SwarmCallback function
-----------------------------------------------------------------------------*/
static std::mutex sSwarmCallback;
void FTLBSProcessor::SwarmCallback(NSwarm::FMessage* CallbackMessage, void* CallbackData)
{
    // panlele: thread safe for DebugCompletedTasks ???
    std::lock_guard lock(sSwarmCallback);
    char msg[4096];

    FTLBSProcessor* Processor = reinterpret_cast<FTLBSProcessor*>(CallbackData);

    switch (CallbackMessage->Type)
    {
    case NSwarm::MESSAGE_JOB_STATE:
    {
        NSwarm::FJobState* JobStateMessage = static_cast<NSwarm::FJobState*>(CallbackMessage);
        switch (JobStateMessage->JobState)
        {
        case NSwarm::JOB_STATE_INVALID:
            Processor->bProcessingFailed = true;
            break;
        case NSwarm::JOB_STATE_RUNNING:
            break;
        case NSwarm::JOB_STATE_COMPLETE_SUCCESS:
            Processor->bProcessingSuccessful = true;
            break;
        case NSwarm::JOB_STATE_COMPLETE_FAILURE:
            Processor->bProcessingFailed = true;
            break;
        case NSwarm::JOB_STATE_KILLED:
            Processor->bProcessingFailed = true;
            break;
        default:
            break;
        }
    }
    break;

    case NSwarm::MESSAGE_TASK_STATE:
    {
        NSwarm::FTaskState* TaskStateMessage = static_cast<NSwarm::FTaskState*>(CallbackMessage);
        // Processor->UpdateTaskState(TaskStateMessage->TaskGuid, TaskStateMessage->TaskState);
        switch (TaskStateMessage->TaskState)
        {
        case NSwarm::JOB_TASK_STATE_INVALID:
            // Consider this cause for failing the entire Job
            Processor->bProcessingFailed = true;
            break;
        case NSwarm::JOB_TASK_STATE_ACCEPTED:
            break;
        case NSwarm::JOB_TASK_STATE_REJECTED:
            Processor->bProcessingFailed = true;
            break;
        case NSwarm::JOB_TASK_STATE_RUNNING:
            break;
        case NSwarm::JOB_TASK_STATE_COMPLETE_SUCCESS:
        {
            if (TaskStateMessage->TaskGuid.B == 0)
            {
                Processor->CompletedLightmapTasks.insert(TaskStateMessage->TaskGuid.D);
            }
            else if (TaskStateMessage->TaskGuid.B == GPUBaking::EGUID_B_FOR_SHADOWMAP)
            {
                if (DebugUE4)
                {
                    Processor->CompletedSDFShadowmapTasks.insert(TaskStateMessage->TaskGuid.C);
                }
                else
                {
                    Processor->CompletedSDFShadowmapTasks.insert(TaskStateMessage->TaskGuid.D);
                }
            }
            else if (TaskStateMessage->TaskGuid.B == GPUBaking::EGUID_B_FOR_LOCALTRANSFER)
            {
                Processor->CompoletedLocalLightMapPRTTasks.insert(TaskStateMessage->TaskGuid.D);
            }
            break;
        }
        case NSwarm::JOB_TASK_STATE_COMPLETE_FAILURE:
        {
            // Add a mapping to the list of mapping GUIDs that have been completed
            TList<NSwarm::FGuid>* NewElement = new TList<NSwarm::FGuid>(TaskStateMessage->TaskGuid, NULL);
            Processor->CompletedMappingTasks.AddElement(NewElement);
            // FPlatformAtomics::InterlockedIncrement(&Processor->NumCompletedTasks);

            // Consider this cause for failing the entire Job
            Processor->bProcessingFailed = true;

            break;
        }
        case NSwarm::JOB_TASK_STATE_KILLED:
            break;
        default:
            break;
        }
    }
    break;

    case NSwarm::MESSAGE_INFO:
    {
        NSwarm::FInfoMessage* InfoMessage = static_cast<NSwarm::FInfoMessage*>(CallbackMessage);
        wchar2char(InfoMessage->TextMessage, msg, 4096);
        LOG_INFO("{}", msg);
    }
    break;

    case NSwarm::MESSAGE_ALERT:
    {
    }
    break;

    case NSwarm::MESSAGE_QUIT:
    {
        Processor->bQuitReceived = true;
    }
    break;
    }
}

bool FTLBSProcessor::Update()
{
    if (!bProcessingSuccessful)
    {
        return false;
    }
    float startTime = cross::Timer::GetFloatTime();
    LOG_INFO("Dawn Finish, and start import job = {}", startTime);
    assert(!bProcessingFailed);
    ImportJob();
    CloseJob();
    float endTime = cross::Timer::GetFloatTime();
    LOG_INFO("Dawn Finish, and finish import job = {}, import duration = {}", endTime, endTime - startTime);
    bProcessingSuccessful = false;
    bProcessingFailed = false;
    return true;
}

bool FTLBSProcessor::ImportJob()
{
    if (GPUBaking::LightingBakeConfig::GetInstance().GetLightmassDebug())
    {
        return false;
    }

    bool useVLM = false;
    if (BakingContext.HasLightProbeJob(useVLM))
    {
        if (useVLM)
        {
            ImportVolumetricLightmap();
        }
        else
        {
            ImportSparseVolumeSamples();
        }
    }

    cross::threading::TaskEventArray taskEventArray;

    mLightMapPack = std::make_shared<LightMapPack>(2048, 2048);
    for (auto lmTask : CompletedLightmapTasks)
    {
        NSwarm::FGuid guid;
        guid.A = guid.C = 0;
        guid.B = GPUBaking::EGUID_B_FOR_LIGHTMAP;
        guid.D = lmTask;
        FGuidInfo guidInfo = ToGuid(guid);

        if (DebugUE4)
        {
            std::shared_ptr<BakeTextureData> texData(new BakeTextureData);
            auto lmData = ImportLightMap2D(guid, texData->mTextureInfo, taskEventArray);
            texData->mLightMapData = lmData->QuantizedData;
            texData->mTransferLightMapData = lmData->QuantizedTransferData;
            bool isFind = false;
            for (auto i = 0; i < Exporter->ExportContext.LightMapJobs.size(); ++i)
            {
                if (Exporter->ExportContext.LightMapJobs[i].Width == static_cast<int>(lmData->SizeX) && Exporter->ExportContext.LightMapJobs[i].Height == static_cast<int>(lmData->SizeY))
                {
                    auto entityID = Exporter->ExportContext.GetLightMapJobEntityID(i);
                    const auto& meshInstInfo = Exporter->ExportContext.GetBakerMeshInstanceInfo(i);

                    texData->mTextureInfo.UseDirectionality = meshInstInfo.UseDirectionality;
                    texData->mTextureInfo.Filename = meshInstInfo.InstanceName;
                    texData->mTextureInfo.PackTextureIndex = mLightMapPack->AddTexture(texData);
                    LightMapOutMap[entityID] = &(texData->mTextureInfo);
                    isFind = true;
                }
            }
            Assert(isFind);
        }
        else
        {
            for (auto i = 0; i < Exporter->ExportContext.LightMapJobs.size(); ++i)
            {
                auto entityID = Exporter->ExportContext.GetLightMapJobEntityID(i);
                const auto& meshInstInfo = Exporter->ExportContext.GetBakerMeshInstanceInfo(i);
                if (guidInfo == Exporter->ExportContext.LightMapJobs[i].Guid)
                {
                    std::shared_ptr<BakeTextureData> texData(new BakeTextureData);
                    texData->mTextureInfo.UseDirectionality = meshInstInfo.UseDirectionality;
                    texData->mTextureInfo.Filename = meshInstInfo.InstanceName;
                    auto lmData = ImportLightMap2D(guid, texData->mTextureInfo, taskEventArray);
                    texData->mLightMapData = lmData->QuantizedData;
                    texData->mTransferLightMapData = lmData->QuantizedTransferData;
                    texData->mTextureInfo.PackTextureIndex = mLightMapPack->AddTexture(texData);
                    LightMapOutMap[entityID] = &(texData->mTextureInfo);
                }
            }
        }
    }
    Assert(mLightMapPack->GetTextureNum() == CompletedLightmapTasks.size());

    for (auto localLMTask : CompoletedLocalLightMapPRTTasks)
    {
        NSwarm::FGuid guid;
        guid.A = guid.C = 0;
        guid.B = GPUBaking::EGUID_B_FOR_LOCALTRANSFER;
        guid.D = localLMTask;
        FGuidInfo guidInfo = ToGuid(guid);

        const auto& ExportContext = Exporter->ExportContext;
        for (auto i = 0; i < ExportContext.LocalLightMapPRTJobs.size(); ++i)
        {
            auto&& [entityID, instanceName] = ExportContext.GetLocalLightMapJobEntityID(i);
            assert(LightMapOutMap.find(entityID) != LightMapOutMap.end());
            LightMapOutInfo* lmInfo = LightMapOutMap[entityID];

            if (guidInfo == ExportContext.LocalLightMapPRTJobs[i].Guid)
            {
                auto texData = mLightMapPack->GetTexture(lmInfo->PackTextureIndex);
                texData->mLocalTransferLightMapDataArray = ImportLocalTransferLightmap2D(guid, *lmInfo, taskEventArray);
            }
        }
    }

    for (auto smTask : CompletedSDFShadowmapTasks)
    {
        NSwarm::FGuid guid;
        guid.A = guid.C = 0;
        guid.B = GPUBaking::EGUID_B_FOR_SHADOWMAP;
        guid.D = smTask;
        FGuidInfo guidInfo = ToGuid(guid);

        if (DebugUE4)
        {
            LightMapOutInfo tmp;
            ImportSDFShadowMap(guid, tmp, taskEventArray);
        }
        else
        {
            for (auto i = 0; i < Exporter->JobInputs.SDFShadowJobs.Num(); ++i)
            {
                auto entityID = Exporter->ExportContext.GetShadowMapJobEntityID(i);
                LightMapOutInfo* lmInfo = nullptr;
                assert(LightMapOutMap.find(entityID) != LightMapOutMap.end());
                {
                    lmInfo = LightMapOutMap[entityID];
                }
                if (guidInfo == Exporter->JobInputs.SDFShadowJobs[i].JobID)
                {
                    auto texData = mLightMapPack->GetTexture(lmInfo->PackTextureIndex);
                    texData->mShadowMapData = ImportSDFShadowMap(guid, *lmInfo, taskEventArray);
                }
            }
        }
    }

    std::vector<int> usableFileIndex;
    for (auto i = 0; usableFileIndex.size() < mLightMapPack->GetTextureNum(); i++)
    {
        if (!Exporter->ExportContext.IsLockedFileIndex(i))
        {
            usableFileIndex.push_back(i);
        }
    }

    taskEventArray.WaitForCompletion();
    taskEventArray.Reset();

    mLightMapPack->PackLightMap(usableFileIndex);

    return true;
}

inline FColor GetColor(const GPUBaking::FLightmap2DSample& Sample, int32 Component)
{
    // return FLinearColor(Sample.IncidentLighting.x, Sample.IncidentLighting.y
    //, Sample.IncidentLighting.z).ToFColor(false);
    return FLinearColor(Sample.SHVector.R.V[Component], Sample.SHVector.G.V[Component], Sample.SHVector.B.V[Component]).ToFColor(false);
}

void WriteSkyOcclusionTexture(const std::string& filename, const FQuantizedLightmapData& lmData)
{
    uint32_t width = lmData.SizeX;
    uint32_t height = lmData.SizeY;
    imageio::image img(width, height);

    for (uint32 y = 0; y < height; ++y)
    {
        for (uint32 x = 0; x < width; ++x)
        {
            auto texelIdx = y * width + x;
            auto& sample = lmData.Data[texelIdx];
            auto& color = img(x, y);

#if ENABLE_PRT_API && ENABLE_OCCLUSION_FLOAT16F
            color.r = static_cast<UInt8>(sample.SkyOcclusion[0].GetFloat() * 255.f);
            color.g = static_cast<UInt8>(sample.SkyOcclusion[1].GetFloat() * 255.f);
            color.b = static_cast<UInt8>(sample.SkyOcclusion[2].GetFloat() * 255.f);
            color.a = static_cast<UInt8>(sample.SkyOcclusion[3].GetFloat() * 255.f);
#else
            color.r = sample.SkyOcclusion[0];
            color.g = sample.SkyOcclusion[1];
            color.b = sample.SkyOcclusion[2];
            color.a = sample.SkyOcclusion[3];
#endif
        }
    }
    imageio::save_png(filename.c_str(), img);
}

void WriteAOMaterialMask(const std::string& filename, const FQuantizedLightmapData& lmData)
{
    uint32_t width = lmData.SizeX;
    uint32_t height = lmData.SizeY;
    imageio::image img(width, height);

    for (uint32 y = 0; y < height; ++y)
    {
        for (uint32 x = 0; x < width; ++x)
        {
            auto texelIdx = y * width + x;
            auto& sample = lmData.Data[texelIdx];
            auto& color = img(x, y);

            color.r = sample.AOMaterialMask;
            color.g = sample.AOMaterialMask;
            color.b = sample.AOMaterialMask;
            color.a = 255;
        }
    }
    imageio::save_png(filename.c_str(), img);
}

void WriteLightMapPng(const std::string& filename, const FQuantizedLightmapData& lmData, bool UseDirectionality)
{
    uint32_t width = lmData.SizeX;
    uint32_t height = lmData.SizeY;

    uint32_t outputHeight = UseDirectionality ? lmData.SizeY * 2 : lmData.SizeY;
    imageio::image img(width, outputHeight);
    Assert(lmData.Data.size() == width * height);

    for (uint32 y = 0; y < height; ++y)
    {
        for (uint32 x = 0; x < width; ++x)
        {
            auto texelIdx = y * width + x;
            auto& sample = lmData.Data[texelIdx];

            auto& color = img(x, y);
            color.r = sample.Coefficients[LQ_LIGHTMAP_COEF_INDEX][0];
            color.g = sample.Coefficients[LQ_LIGHTMAP_COEF_INDEX][1];
            color.b = sample.Coefficients[LQ_LIGHTMAP_COEF_INDEX][2];
            color.a = 255;
        }

        if (UseDirectionality)
        {
            for (uint32 x = 0; x < width; ++x)
            {
                auto texelIdx = y * width + x;
                auto& sample = lmData.Data[texelIdx];

                auto imgIdx = (lmData.SizeY + y) * width + x;
                auto& color = img.get_pixels()[imgIdx];
                color.r = sample.Coefficients[LQ_LIGHTMAP_COEF_INDEX + 1][0];
                color.g = sample.Coefficients[LQ_LIGHTMAP_COEF_INDEX + 1][1];
                color.b = sample.Coefficients[LQ_LIGHTMAP_COEF_INDEX + 1][2];
                color.a = 255;
            }
        }
    }

    imageio::save_png(filename.c_str(), img);
}

void Filter(int x, int y, int width, int height, std::vector<FQuantizedSignedDistanceFieldShadowSample>& samples)
{
    const int neighborCount = 8;
    static int neighbor[neighborCount][2] = {{1, 0}, {0, 1}, {-1, 0}, {0, -1}, {1, 1}, {-1, 1}, {-1, -1}, {1, -1}};

    float filterComponents[2] = {0.0f, 0.0f};
    bool isFind = false;
    for (int neighborIndex = 0; neighborIndex < neighborCount; neighborIndex++)
    {
        int newY = y + neighbor[neighborIndex][0];
        int newX = x + neighbor[neighborIndex][1];
        if (newY >= 0 && newY < height && newX >= 0 && newX < width)
        {
            auto& sample = samples[static_cast<size_t>(newY) * width + newX];
            if (sample.Coverage > 0)
            {
                isFind = true;
                int secondY = y + neighbor[neighborIndex][0] * 2;
                int secondX = x + neighbor[neighborIndex][1] * 2;
                if (secondY >= 0 && secondY < height && secondX >= 0 && secondX < width)
                {
                    auto& secondSample = samples[static_cast<size_t>(secondY) * width + secondX];
                    filterComponents[0] = 2.0f * sample.GetFilterableComponent(0) - 1.0f * secondSample.GetFilterableComponent(0);
                    filterComponents[1] = 2.0f * sample.GetFilterableComponent(1) - 1.0f * secondSample.GetFilterableComponent(1);
                }
                else
                {
                    filterComponents[0] = sample.GetFilterableComponent(0);
                    filterComponents[1] = sample.GetFilterableComponent(1);
                }
                break;
            }
        }
    }

    if (isFind)
    {
        samples[static_cast<size_t>(y) * width + x].SetFilterableComponent(filterComponents[0], 0);
        samples[static_cast<size_t>(y) * width + x].SetFilterableComponent(filterComponents[1], 1);
    }
}

void WriteShadowMapPng(const std::string& filename, const FQuantizedShadowSignedDistanceFieldData2D& smData)
{
    auto FilpY = [height = smData.GetSizeY()](int y) -> int { return height - 1 - y; };
    auto SizeX = smData.GetSizeX();
    auto SizeY = smData.GetSizeY();

    std::vector<FQuantizedSignedDistanceFieldShadowSample> samples;
    samples.resize(SizeX * SizeY);
    memcpy(&samples[0], smData.GetData(), samples.size() * sizeof(samples[0]));

    for (uint32 y = 0; y < SizeY; ++y)
    {
        for (uint32 x = 0; x < SizeX; ++x)
        {
            if (samples[y * SizeX + x].Coverage == 0)
            {
                Filter(x, y, SizeX, SizeY, samples);
            }
        }
    }

    std::vector<FColor> RGBData;
    RGBData.resize(SizeY * SizeX);

    for (uint32 y = 0; y < SizeY; ++y)
    {
        for (uint32 x = 0; x < SizeX; ++x)
        {
            auto idx = y * SizeX + x;

            auto& color = RGBData[idx];
            // color.R = color.G = color.B = color.A = 255;
            color.R = color.G = color.B = samples[idx].Distance;
        }
    }
    WritePNG(filename, RGBData, SizeX, SizeY);
}

static NSwarm::TChannelFlags LM_TEXTUREMAPPING_CHANNEL_FLAGS = (NSwarm::TChannelFlags)(NSwarm::SWARM_JOB_CHANNEL_READ | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);
static NSwarm::TChannelFlags LM_LIGHTPROBE_CHANNEL_FLAGS = (NSwarm::TChannelFlags)(NSwarm::SWARM_JOB_CHANNEL_READ | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);
static NSwarm::TChannelFlags LM_VOLUMETRICLIGHTMAP_CHANNEL_FLAGS = (NSwarm::TChannelFlags)(NSwarm::SWARM_JOB_CHANNEL_READ | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);

bool FTLBSProcessor::ImportSparseVolumeSamples()
{
    std::wstring ChannelName = CreateChannelName(PrecomputedVolumeLightingGuid, LM_SCENE_VERSION, LM_LIGHTPROBE_EXTENSION);

    int32 Channel = BakingContext.Swarm->OpenChannel(ChannelName.c_str(), LM_LIGHTPROBE_CHANNEL_FLAGS);
    if (Channel >= 0)
    {
        FSwarmImportExportContext ImportContext(BakingContext.Swarm, Channel, false);
        FSparseLightProbeOutput SparseLightProbeOutput;
        SparseLightProbeOutput.Serialize(ImportContext);
        Assert(SparseLightProbeOutput.JobID == ToGuid(PrecomputedVolumeLightingGuid));

//         FileImportExportContext UE4ImportContext("D:/UE4/UnrealEngine/Engine/Binaries/Win64/DawnCache2Import/v1.CE97C5C3AB614FD3B2DA55C0E6C33FB4.lpgz", false);
//         FSparseLightProbeOutput UE4SparseLightProbeOutput;
//         UE4SparseLightProbeOutput.Serialize(UE4ImportContext);

        for (UInt32 i = 0; i < static_cast<UInt32>(SparseLightProbeOutput.LightProbesForLevel.Num()); i++)
        {
            auto& LightProbeOutput = SparseLightProbeOutput.LightProbesForLevel[i];
            Assert(LightProbeOutput.LevelID == DebugLevelGUID);
            // panlele: LightProbeOutpu.JobID is invalid, always zero
            FGuidInfo jobID{0, 2, 0, i};
            const BakerLightProbeJobInfo* jobInfo = BakingContext.GetLightProbeJob(jobID);
            auto cache = std::make_shared<cross::LightProbeCache>();

            ImportLightProbeSamples(cache, LightProbeOutput);

            auto rootPath = LightingBakeConfig::GetInstance().GetProjectionRootPath() + LightingBakeConfig::GetInstance().GetLightMapSavedRelPath();
            std::string filename = std::string("lightprobe_") + LightProbeOutput.JobID.ToString() + ".data";
            cache->SerializeCacheData(rootPath + filename);
            if (jobInfo)
            {
                mGameWorld->GetGameSystem<cross::LightProbeVolumeSystemG>()->SetLightProbeCache(jobInfo->VolumeEntityHandle, LightingBakeConfig::GetInstance().GetLightMapSavedRelPath() + filename, cache);
            }
        }
    }
    else
    {
        LOG_ERROR("Error, OpenChannel failed to open ImportSparseVolumeSamples with error code {}", Channel);
    }
    return true;
}

void GetUE4VLMCacheFiles(std::vector<std::string>& vlmgz, std::string& cachePath)
{
    std::string inPath = cachePath + "*";

    std::vector<std::string> allFiles;
    struct _finddata_t fileinfo;
    auto handle = _findfirst(inPath.c_str(), &fileinfo);
    do
    {
        allFiles.push_back(fileinfo.name);
    } while (!_findnext(handle, &fileinfo));

    for (auto filename : allFiles)
    {
        if (filename.rfind(".vlmgz") != std::string::npos)
        {
            vlmgz.push_back(filename);
        }
    }
    std::sort(vlmgz.begin(), vlmgz.end(), [](const auto& a, const auto& b) { return std::stoi(a) < std::stoi(b); });
}

bool GPUBAKING_NS_NAME::FTLBSProcessor::ImportVolumetricLightmap()
{
    GPUBaking::FAdaptiveVolumetricLightmapParameters VolumetricLightmapSettings;
    Exporter->ExportVolumetricLightmapSettings(VolumetricLightmapSettings);

    const FBakingJobInputs& JobInputs = Exporter->GetJobInput();
    const int BrickSize = VolumetricLightmapSettings.BrickSize;

    std::vector<FImportedVolumetricLightmapTaskData> TaskDataArray;

    bool DebugUE4Import = false;//panlele: for debug
    if (DebugUE4Import)
    {
        std::string cachePath = "D:/UE4/UnrealEngine/Engine/Binaries/Win64/DawnCache2Import/";
        std::vector<std::string> vlmgz;
        GetUE4VLMCacheFiles(vlmgz, cachePath);
        for (auto i = 0; i < vlmgz.size(); i++)
        {
            FileImportExportContext UE4ImportContext(cachePath + vlmgz[i], false);
            GPUBaking::FAdaptiveVolumetricLightmapOutput VolumetricLightmapOutput;
            VolumetricLightmapOutput.Serialize(UE4ImportContext);

            FImportedVolumetricLightmapTaskData NewTaskData;
            int32 NumBricks = VolumetricLightmapOutput.BrickData.NumElements;
            NewTaskData.Bricks.resize(NumBricks);
            for (int BrickIndex = 0; BrickIndex < NumBricks; BrickIndex++)
            {
                FImportedVolumetricLightmapBrick& NewBrick = NewTaskData.Bricks[BrickIndex];
                FVolumetricLightMapEncoder::ConvertVolumetricLightmapOutput(VolumetricLightmapOutput.BrickData[BrickIndex], NewBrick, BrickSize);
            }
            TaskDataArray.emplace_back(std::move(NewTaskData));
        }
    }
    else
    {
        for (const auto& TaskGuid : JobInputs.VolumetricLightmapTaskGuids.GetElements())
        {
            std::wstring ChannelName = CreateChannelName(ToGuid(TaskGuid), GPUBaking::LM_SCENE_VERSION, GPUBaking::LM_VLM_EXTENSION);
            int Channel = BakingContext.Swarm->OpenChannel(ChannelName.c_str(), LM_VOLUMETRICLIGHTMAP_CHANNEL_FLAGS);
            if (Channel > 0)
            {
                GPUBaking::FSwarmImportExportContext ImportContext(BakingContext.Swarm, Channel, false);
                GPUBaking::FAdaptiveVolumetricLightmapOutput VolumetricLightmapOutput;
                VolumetricLightmapOutput.Serialize(ImportContext);
                Assert(VolumetricLightmapOutput.JobID == TaskGuid);

                FImportedVolumetricLightmapTaskData NewTaskData;
                int32 NumBricks = VolumetricLightmapOutput.BrickData.NumElements;
                NewTaskData.Bricks.resize(NumBricks);
                for (int BrickIndex = 0; BrickIndex < NumBricks; BrickIndex++)
                {
                    FImportedVolumetricLightmapBrick& NewBrick = NewTaskData.Bricks[BrickIndex];
                    FVolumetricLightMapEncoder::ConvertVolumetricLightmapOutput(VolumetricLightmapOutput.BrickData[BrickIndex], NewBrick, BrickSize);
                }
                TaskDataArray.emplace_back(std::move(NewTaskData));
                BakingContext.Swarm->CloseChannel(Channel);
            }
        }
    }

    auto rootPath = LightingBakeConfig::GetInstance().GetProjectionRootPath() + LightingBakeConfig::GetInstance().GetLightMapSavedRelPath();
    //std::string filename = rootPath + std::string("vlm_ambientvector") + ".nda";
    FPrecomputedVolumetricLightmapData CurrentLevelData;
    FVolumetricLightMapEncoder::ImportVolumetricLightmap(VolumetricLightmapSettings, TaskDataArray, CurrentLevelData);

    cross::VolumetricLightmapData vlmData;
    FVolumetricLightMapEncoder::WriteVolumetricLightmap(CurrentLevelData, rootPath, vlmData);

    const BakerLightProbeJobInfo* jobInfo = BakingContext.GetLightProbeJob(0);
    if (jobInfo)
    {
        auto bakeSceneOrig = LightingBakeConfig::GetInstance().GetBakeSceneOriginal();
        auto ueBakeSceneOrig = Float3CE2UE(bakeSceneOrig);
        //Assert(bakeSceneOrig.x == 0 && bakeSceneOrig.y == 0 && bakeSceneOrig.z == 0);
        const auto& VolumeMin = VolumetricLightmapSettings.VolumeMin;
        const auto& VolumeSize = VolumetricLightmapSettings.VolumeSize;
        float3 VolumeExtent = VolumeSize / 2;
        float3 VolumeCenter = VolumeMin + VolumeExtent;
        cross::BoundingBox VolumeBounds(cross::Float3(VolumeCenter.x, VolumeCenter.y, VolumeCenter.z) + ueBakeSceneOrig, cross::Float3(VolumeExtent.x, VolumeExtent.y, VolumeExtent.z));
        mGameWorld->GetGameSystem<cross::LightProbeVolumeSystemG>()->SetVolumetricLightMap(jobInfo->VolumeEntityHandle, vlmData.mIndirectionTexFile,
            vlmData.mAmbientVector, vlmData.mSHCoefficients, vlmData.mSkyBentNormalTexFile, VolumeBounds, BrickSize);
    }
    return true;
}

bool FTLBSProcessor::ImportLightProbeSamples(std::shared_ptr<cross::LightProbeCache> cache, const FLightProbeOutput& lightProbeOutput)
{
    constexpr int NUM_INDIRECT_LIGHTING_SH_COEFFICIENTS = 9;
    auto& volumeSamples = lightProbeOutput.LightProbes;

#if ENABLE_PRT_API
    const bool bPrecomputedRadianceTranser = lightProbeOutput.TransferMatrices.Num() > 0;
    if (bPrecomputedRadianceTranser)
    {
        Assert(lightProbeOutput.TransferMatrices.Num() == volumeSamples.Num());
    }
    const bool bPrecomputedLocalTranser = lightProbeOutput.LocalTransferVectors.Num() > 0;
    if (bPrecomputedLocalTranser)
    {
        Assert(lightProbeOutput.LocalTransferVectors.Num() == volumeSamples.Num());
    }
#else
    const bool bPrecomputedRadianceTranser = false;
    const bool bPrecomputedLocalTranser = false;
#endif

    auto bakeSceneOrig = LightingBakeConfig::GetInstance().GetBakeSceneOriginal();

    for (auto sampleIndex = 0; sampleIndex < volumeSamples.Num(); sampleIndex++)
    {
        cross::LightProbeNode outSample;
        const FLightProbeInfo& currentSample = volumeSamples[sampleIndex];
        if (currentSample.Flags & GPUBaking::LIGHTPROBE_FLAGS_INSIDE_GEOMETRY)
        {
            continue;
        }
        outSample.mPosition = Float3UE2CE(currentSample.PostionAndRadius) + bakeSceneOrig;
        outSample.mRadius = currentSample.PostionAndRadius.w;
        outSample.mDirectionalLightShadowing = currentSample.DirectionalLightShadowing;
        // panlele: sh need convert from ue4 to ce
        float* RDestChannel[NUM_INDIRECT_LIGHTING_SH_COEFFICIENTS] = {
            &outSample.mSHSamplePacked0[0].x,
            &outSample.mSHSamplePacked0[0].y,
            &outSample.mSHSamplePacked0[0].z,
            &outSample.mSHSamplePacked0[0].w,
            &outSample.mSHSamplePacked1[0].x,
            &outSample.mSHSamplePacked1[0].y,
            &outSample.mSHSamplePacked1[0].z,
            &outSample.mSHSamplePacked1[0].w,
            &outSample.mSHSamplePacked2.x,
        };
        float* GDestChannel[NUM_INDIRECT_LIGHTING_SH_COEFFICIENTS] = {
            &outSample.mSHSamplePacked0[1].x,
            &outSample.mSHSamplePacked0[1].y,
            &outSample.mSHSamplePacked0[1].z,
            &outSample.mSHSamplePacked0[1].w,
            &outSample.mSHSamplePacked1[1].x,
            &outSample.mSHSamplePacked1[1].y,
            &outSample.mSHSamplePacked1[1].z,
            &outSample.mSHSamplePacked1[1].w,
            &outSample.mSHSamplePacked2.y,
        };
        float* BDestChannel[NUM_INDIRECT_LIGHTING_SH_COEFFICIENTS] = {
            &outSample.mSHSamplePacked0[2].x,
            &outSample.mSHSamplePacked0[2].y,
            &outSample.mSHSamplePacked0[2].z,
            &outSample.mSHSamplePacked0[2].w,
            &outSample.mSHSamplePacked1[2].x,
            &outSample.mSHSamplePacked1[2].y,
            &outSample.mSHSamplePacked1[2].z,
            &outSample.mSHSamplePacked1[2].w,
            &outSample.mSHSamplePacked2.z,
        };
        for (auto idx = 0; idx < NUM_INDIRECT_LIGHTING_SH_COEFFICIENTS; idx++)
        {
            *RDestChannel[idx] = currentSample.SampleValue.R.V[idx];
            *GDestChannel[idx] = currentSample.SampleValue.G.V[idx];
            *BDestChannel[idx] = currentSample.SampleValue.B.V[idx];

            outSample.mLighting.R.V[idx] = currentSample.SampleValue.R.V[idx];
            outSample.mLighting.G.V[idx] = currentSample.SampleValue.G.V[idx];
            outSample.mLighting.B.V[idx] = currentSample.SampleValue.B.V[idx];
        }
        outSample.mSHSamplePacked2.w = 1.0f;

        cross::Float3 inSkyBentNormal(currentSample.SkyOcclusion.x, currentSample.SkyOcclusion.y, currentSample.SkyOcclusion.z);
        float inSkyBentNormalLen = std::max(inSkyBentNormal.Length(), 0.0001f);
        inSkyBentNormal /= inSkyBentNormalLen;
        outSample.SetPackedSkyBentNormal(cross::Float4A(inSkyBentNormal.x, inSkyBentNormal.y, inSkyBentNormal.z, inSkyBentNormalLen));

#if ENABLE_PRT_API
        if (bPrecomputedRadianceTranser)
        {
            auto& TransferMatrix = lightProbeOutput.TransferMatrices[sampleIndex];
            static_assert(sizeof(TransferMatrix.MM) >= sizeof(cross::SHUtils::FPrtTransferMatrix4x9), "");

            #if ENABLE_PRTTRANSFERMATRIX_COMPRESS
            for (SInt32 idx0 = 0; idx0 < cross::SHUtils::FPrtTransferMatrix4x9::PRT_MATRIX_ELEMENT_NUM; ++idx0)
            {
                outSample.mTransferMatrix.M[idx0] = cross::SHUtils::FPrtVector(TransferMatrix.MM[idx0].x, TransferMatrix.MM[idx0].y, TransferMatrix.MM[idx0].z);
            }
            #else
            memcpy(&outSample.mTransferMatrix.M[0], &TransferMatrix.MM[0], sizeof(cross::SHUtils::FPrtTransferMatrix4x9));
            #endif

            outSample.mStaticLighting = outSample.mLighting;
        }
        if (bPrecomputedLocalTranser)
        {
            //TODO(timllpan):
        }
#endif

        cache->AddLightProbe(std::move(outSample));
    }
    cache->BuildOctree();
    return true;
}

std::shared_ptr<FMappingImportHelper> FTLBSProcessor::ImportLightMap2D(NSwarm::FGuid jobID, LightMapOutInfo& outInfo, cross::threading::TaskEventArray& taskEventArray)
{
    std::wstring ChannelName = CreateChannelName(jobID, LM_SCENE_VERSION, LM_LM_EXTENSION);

    int32 Channel = BakingContext.Swarm->OpenChannel(ChannelName.c_str(), LM_TEXTUREMAPPING_CHANNEL_FLAGS);
    if (Channel >= 0)
    {
        FSwarmImportExportContext ImportContext(BakingContext.Swarm, Channel, false);
        std::shared_ptr<FLightmap2DOutput> LightMapOutput = std::make_shared<FLightmap2DOutput>();

        LightMapOutput->Serialize(ImportContext);
        assert(LightMapOutput->JobID == ToGuid(jobID));

//         FileImportExportContext UE4ImportContext("D:/DawnPRT/PRT4UE/Engine/Binaries/Win64/00000000000000000000000000000001_LM", false);
//         LightMapOutput->Serialize(UE4ImportContext);

        auto filename = outInfo.Filename;
        outInfo.Filename = LightingBakeConfig::GetInstance().GetLightMapSavedRelPath() + std::string(filename + jobID.ToString()) + "_LM.nda";

        std::shared_ptr<FMappingImportHelper> ImportData(new FMappingImportHelper());
        ImportData->SizeX = LightMapOutput->Size.x;
        ImportData->SizeY = LightMapOutput->Size.y;
        ImportData->QuantizedData = std::make_shared<FQuantizedLightmapData>();
#if ENABLE_PRT_API
        ImportData->QuantizedTransferData = std::make_shared<FQuantizedTransferLightmapData>();
#endif   // ENABLE_PRT_API

        taskEventArray.Add(cross::threading::Dispatch([LightMapOutput, ImportData, jobID, &outInfo, filename](auto) 
            { 
                FJobResultHelper::ConvertLightmapOutput(*LightMapOutput, ImportData->QuantizedData.get(), ImportData->QuantizedTransferData.get(), false);

                memcpy(outInfo.Scale, ImportData->QuantizedData->Scale, sizeof(outInfo.Scale));
                memcpy(outInfo.Add, ImportData->QuantizedData->Add, sizeof(outInfo.Add));

                memcpy(outInfo.TransferLightMapScale, ImportData->QuantizedTransferData->Scale, sizeof(outInfo.TransferLightMapScale));
                memcpy(outInfo.TransferLightMapAdd, ImportData->QuantizedTransferData->Add, sizeof(outInfo.TransferLightMapAdd));
                
                /*auto lmDebugFileNameStr = LightingBakeConfig::GetInstance().GetProjectionRootPath() + LightingBakeConfig::GetInstance().GetLightMapSavedRelPath() + std::string(filename + jobID.ToString() + "_LM.png");
                WriteLightMapPng(lmDebugFileNameStr.c_str(), *ImportData->QuantizedData.get(), outInfo.UseDirectionality);

                // for debug output origin color bmp
                std::vector<FColor> lmDataColor;
                lmDataColor.resize(ImportData->QuantizedData->Data.size());
                for (auto i = 0; i < lmDataColor.size(); ++i)
                {
                    const FLightMapCoefficients& sample = ImportData->QuantizedData->Data[i];
                    // FColor Color = GetColor(sample, 1);
                    FColor Color;
#if ENABLE_PRT_API && ENABLE_OCCLUSION_FLOAT16F
                    Color.R = static_cast<UInt8>(sample.SkyOcclusion[0].GetFloat() * 255.f);
                    Color.G = static_cast<UInt8>(sample.SkyOcclusion[1].GetFloat() * 255.f);
                    Color.B = static_cast<UInt8>(sample.SkyOcclusion[2].GetFloat() * 255.f);
                    Color.A = static_cast<UInt8>(sample.SkyOcclusion[3].GetFloat() * 255.f);
#else
                    Color.R = sample.SkyOcclusion[0];
                    Color.G = sample.SkyOcclusion[1];
                    Color.B = sample.SkyOcclusion[2];
                    Color.A = sample.SkyOcclusion[3];
#endif
                    lmDataColor[i] = Color;
                }
                WriteBitmap(std::string(LightMapOutput->JobID.ToString() + "_LM_SKY_AO").c_str(),
                            lmDataColor,
                            LightMapOutput->Size.x,
                            LightMapOutput->Size.y,
                            LightingBakeConfig::GetInstance().GetProjectionRootPath() + LightingBakeConfig::GetInstance().GetLightMapSavedRelPath());*/
        }));

        BakingContext.Swarm->CloseChannel(Channel);
        return ImportData;
    }
    else
    {
        LOG_ERROR("Error, OpenChannel failed to open {} with error code {}", jobID.ToString(), Channel);
    }
    return nullptr;
}

std::shared_ptr<FLocalQuantizedTransferLightmapDataArray> FTLBSProcessor::ImportLocalTransferLightmap2D(NSwarm::FGuid jobID, LightMapOutInfo& outInfo, cross::threading::TaskEventArray& taskEventArray)
{
    std::vector<std::shared_ptr<FLocalQuantizedTransferLightmapData>> OutLocalLightMap;

    std::wstring ChannelName = CreateChannelName(jobID, LM_SCENE_VERSION, LM_LTLM_EXTENSION);
    int32 Channel = BakingContext.Swarm->OpenChannel(ChannelName.c_str(), LM_TEXTUREMAPPING_CHANNEL_FLAGS);
    if (Channel >= 0)
    {
        FSwarmImportExportContext ImportContext(BakingContext.Swarm, Channel, false);
        std::shared_ptr<FLocalTranferLightMapOutput> LocalTransferLightMapOutput = std::make_shared<FLocalTranferLightMapOutput>();

        LocalTransferLightMapOutput->Serialize(ImportContext);
        assert(LocalTransferLightMapOutput->JobID == ToGuid(jobID));

        std::shared_ptr<FLocalQuantizedTransferLightmapDataArray> LocalLightMap = std::make_shared<FLocalQuantizedTransferLightmapDataArray>();

        taskEventArray.Add(cross::threading::Dispatch([this, LocalTransferLightMapOutput, LocalLightMap, &outInfo](auto) 
        { 
            FJobResultHelper::ConvertLocalLightMapOutput(*LocalTransferLightMapOutput, LocalLightMap.get()); 

            memset(outInfo.LocalTransferLightMapScale, 0, sizeof(outInfo.LocalTransferLightMapScale));
            memset(outInfo.LocalTransferLightMapAdd, 0, sizeof(outInfo.LocalTransferLightMapAdd));

            auto lmNum = std::min(static_cast<uint32>(LocalLightMap->vData.size()), 4u);
            for (size_t lmIndex = 0; lmIndex < lmNum; lmIndex++)
            {
                auto& lmData = LocalLightMap->vData[lmIndex];
                memcpy(outInfo.LocalTransferLightMapScale[lmIndex], lmData.Scale, sizeof(lmData.Scale));
                memcpy(outInfo.LocalTransferLightMapAdd[lmIndex], lmData.Add, sizeof(lmData.Add));
                auto groudID = Exporter->ExportContext.FindLocalGroupID(lmData.LightGroupGuid);
                lmData.GroupID = groudID;
            }
        }));

        return LocalLightMap;
    }
    return nullptr;
}

std::shared_ptr<FFourDistanceFieldData2D> FTLBSProcessor::ImportSDFShadowMap(NSwarm::FGuid jobID, LightMapOutInfo& outInfo, cross::threading::TaskEventArray& taskEventArray)
{
    std::wstring ChannelName = CreateChannelName(jobID, LM_SCENE_VERSION, LM_SDF_EXTENSION);

    int32 Channel = BakingContext.Swarm->OpenChannel(ChannelName.c_str(), LM_TEXTUREMAPPING_CHANNEL_FLAGS);
    if (Channel >= 0)
    {
        std::shared_ptr<FSDFShadowOutput> ShadowMapOutput = std::make_shared<FSDFShadowOutput>();

        FSwarmImportExportContext ImportContext(BakingContext.Swarm, Channel, false);

        ShadowMapOutput->Serialize(ImportContext);
        assert(ShadowMapOutput->JobID == ToGuid(jobID));

        // bool hasShadowData = false;

        int32 ShadowMapCount = ShadowMapOutput->ShadowMaps.NumElements;

        std::shared_ptr<FFourDistanceFieldData2D> SDFData = nullptr;

        assert(ShadowMapCount <= 4);

        outInfo.SDFShadowFilename = LightingBakeConfig::GetInstance().GetLightMapSavedRelPath() + std::string(jobID.ToString() + "_SM.nda");

        if (ShadowMapCount > 0)
        {
            SDFData = std::make_shared<FFourDistanceFieldData2D>();
            SDFData->SizeX = ShadowMapOutput->Size.x;
            SDFData->SizeY = ShadowMapOutput->Size.y;
            taskEventArray.Add(cross::threading::Dispatch([ShadowMapOutput, SDFData, ShadowMapCount, jobID](auto) {
                SDFData->Data2D[0] = SDFData->Data2D[1] = SDFData->Data2D[2] = SDFData->Data2D[3] = nullptr;
                for (int32 SMIndex = 0; SMIndex < ShadowMapCount; SMIndex++)
                {
                    const FSDFShadowMap& SMData = ShadowMapOutput->ShadowMaps[SMIndex];

                    std::shared_ptr<FQuantizedShadowSignedDistanceFieldData2D> ShadowMapData(new FQuantizedShadowSignedDistanceFieldData2D(ShadowMapOutput->Size.x, ShadowMapOutput->Size.y));
                    FJobResultHelper::ConvertShadowOutput(SMData, ShadowMapData.get());

                    //WriteShadowMapPng(LightingBakeConfig::GetInstance().GetProjectionRootPath() + LightingBakeConfig::GetInstance().GetLightMapSavedRelPath() + std::string(jobID.ToString() + "_" + std::to_string(SMIndex) +
                    //"_SM.png").c_str(),
                    //                 *ShadowMapData.get());
                    SDFData->Data2D[SMIndex] = ShadowMapData;
                }
                SDFData->IsReady = true;
            }));
        }

        BakingContext.Swarm->CloseChannel(Channel);

        return SDFData;
    }
    return nullptr;
}

/**
 * Constructor
 *
 * @param bInDumpBinaryResults true if it should dump out raw binary lighting data to disk
 */
FTLBSProcessor::FTLBSProcessor(const FDawnBuildOptions& DawnOptions, cross::IGameWorld* InWorld, FTLBSExportContext& ExportContext)
    : BakingContext(ExportContext)
    , Exporter(nullptr)
    , bProcessingSuccessful(false)
    , bProcessingFailed(false)
    , bQuitReceived(false)
    , NumCompletedTasks(0)
    , bRunningTLBS(false)
{
#define DAWN_SWARM_INTERFACE_DIR  BAKING_TEXT("Dawn\\DotNET\\Dawn\\Win64")
#define DAWN_SWARN_INTERFACE_NAME BAKING_TEXT("DawnSwarmInterface.dll")

    wchar_t workPath[MAX_PATH];
    _wgetcwd(workPath, MAX_PATH);
    mWorkPath = workPath;
    mWorkPath += BAKING_TEXT("\\");

    bool ret = NSwarm::FTLBSSwarmInterface::Initialize((mWorkPath + DAWN_SWARM_INTERFACE_DIR + BAKING_TEXT("\\") + DAWN_SWARN_INTERFACE_NAME).c_str());
    if (ret)
    {
        NSwarm::TLogFlags LogFlags = NSwarm::SWARM_LOG_TIMINGS;

        BakingContext.Swarm = &NSwarm::FTLBSSwarmInterface::Get();
        std::wstring OptionsFolder = mWorkPath + DAWN_SWARM_INTERFACE_DIR;

        int ConnectionHandle = NSwarm::FTLBSSwarmInterface::Get().OpenConnection(SwarmCallback, this, LogFlags, OptionsFolder.c_str());
        bSwarmConnectionIsValid = (ConnectionHandle >= 0);

        Exporter = new FTLBSExporter(InWorld, BakingContext);
        BakingContext.bSwarmConnectionIsValid = bSwarmConnectionIsValid;
        mGameWorld = dynamic_cast<cross::GameWorld*>(InWorld);
        LOG_INFO("Swarm OpenConnection : ConnectionHandle = {}", ConnectionHandle);
    }
}

FTLBSProcessor::~FTLBSProcessor()
{
    // Note: the connection must be closed before deleting anything that SwarmCallback accesses
    BakingContext.Swarm->CloseConnection();

    if (Exporter != nullptr)
    {
        delete Exporter;
        Exporter = nullptr;
    }
}

/** Retrieve an exporter for the given channel name */
FTLBSExporter* FTLBSProcessor::GetLightmassExporter()
{
    return Exporter;
}

bool FTLBSProcessor::OpenJob()
{
    // Start the Job
    int32 ErrorCode = BakingContext.Swarm->OpenJob(DebugUE4 ? DebugUE4JobGuid : BakingContext.SceneGuid);
    if (ErrorCode < 0)
    {
        LOG_ERROR("Error, OpenJob failed with error code {}", ErrorCode);
        return false;
    }
    LOG_INFO("Swarm OpenJob Success, {}", BakingContext.SceneGuid.ToString().c_str());
    return true;
}

bool FTLBSProcessor::CloseJob()
{
    // All done, end the Job
    LOG_INFO("CloseJob ing");
    int32 ErrorCode = BakingContext.Swarm->CloseJob();
    LOG_INFO("CloseJob, ErrorCode={}", ErrorCode);
    if (ErrorCode < 0)
    {
        LOG_ERROR("Error, CloseJob failed with error code {}", ErrorCode);
        return false;
    }
    return true;
}

bool FTLBSProcessor::BeginRun()
{
#define TLBS_EXE64_PATH BAKING_TEXT("Dawn\\Win64\\Dawn.exe")
#define TLBS_PDB64_PATH BAKING_TEXT("Dawn\\Win64\\Dawn.pdb")
    std::wstring WorkPath = mWorkPath;
    std::wstring Dependency0 = WorkPath + BAKING_TEXT("Dawn/DotNET/Dawn/Win64/DawnSwarmInterface.dll");
    std::wstring Dependency1 = WorkPath + BAKING_TEXT("Dawn/DotNET/Dawn/Win64/DawnAgentInterface.dll");
    std::wstring Dependency2 = WorkPath + BAKING_TEXT("Dawn/Win64/optix.6.5.0.dll");
    std::wstring Dependency3 = WorkPath + BAKING_TEXT("Dawn/Win64/optix_prime.6.5.0.dll");
    std::wstring Dependency4 = WorkPath + BAKING_TEXT("Dawn/Win64/optixu.6.5.0.dll");
    std::wstring Dependency5 = WorkPath + BAKING_TEXT("Dawn/Win64/OpenImageDenoise.dll");
    std::wstring Dependency6 = WorkPath + BAKING_TEXT("Dawn/Win64/tbb.dll");
    std::wstring Dependency7 = WorkPath + BAKING_TEXT("Dawn/Win64/Dawn.data");
    std::wstring OptDependency0 = WorkPath + TLBS_PDB64_PATH;

    bProcessingFailed = false;

    std::wstring TLBSExecutable64 = WorkPath + TLBS_EXE64_PATH;
    const BAKING_TCHAR* RequiredDependencyPaths64[8] = {
        Dependency0.c_str(),
        Dependency1.c_str(),
        Dependency2.c_str(),
        Dependency3.c_str(),
        Dependency4.c_str(),
        Dependency5.c_str(),
        Dependency6.c_str(),
        Dependency7.c_str(),
    };
    const BAKING_TCHAR* OptionalDependencyPaths64[1] = {
        OptDependency0.c_str(),
    };

    // Set up the description for the Job
    const BAKING_TCHAR* DescriptionKeys[] = {BAKING_TEXT("MapName"), BAKING_TEXT("GameName")};
    const BAKING_TCHAR* DescriptionValues[] = {
        BAKING_TEXT("WorldMapName"),
        BAKING_TEXT("WorldProjectName"),
    };

    int32 JobFlags = NSwarm::JOB_FLAG_USE_DEFAULTS;
    // LOG_INFO("Swarm will be allowed to distribute this job");
    /*JobFlags |= NSwarm::JOB_FLAG_ALLOW_REMOTE;
    JobFlags |= NSwarm::JOB_FLAG_MINIMIZED;*/
    JobFlags |= NSwarm::JOB_FLAG_64BIT;
    bool LightmassDebug = LightingBakeConfig::GetInstance().GetLightmassDebug();
    if (LightmassDebug)
    {
        JobFlags |= NSwarm::JOB_FLAG_MANUAL_START;
    }
    else
    {
        JobFlags |= NSwarm::JOB_FLAG_ALLOW_REMOTE;
    }


    std::wstring CommandLineParameters = string2wstring(BakingContext.SceneGuid.ToString());
    CommandLineParameters += BAKING_TEXT(" -stats");

    NSwarm::FJobSpecification JobSpecification32;
    NSwarm::FJobSpecification JobSpecification64 = NSwarm::FJobSpecification(TLBSExecutable64.c_str(), CommandLineParameters.c_str(), (NSwarm::TJobTaskFlags)JobFlags);
    JobSpecification64.AddDependencies(RequiredDependencyPaths64, 8, OptionalDependencyPaths64, 1);
    JobSpecification64.AddDescription(DescriptionKeys, DescriptionValues, 2);

    int32 ErrorCode = BakingContext.Swarm->BeginJobSpecification(JobSpecification32, JobSpecification64);
    if (ErrorCode < 0)
    {
        LOG_ERROR("Error, BeginJobSpecification failed with error code {}", ErrorCode);
    }

    NumTotalSwarmTasks = 0;

    const FBakingJobInputs& JobInputs = Exporter->GetJobInput();
    if (DebugUE4)
    {
        // Count the number of tasks given to Swarm
        for (int MappingIdx = 0; (ErrorCode >= 0) && MappingIdx < DebugUE4TasksGUID.size(); ++MappingIdx)
        {
            NSwarm::FTaskSpecification NewTaskSpecification(DebugUE4TasksGUID[MappingIdx], DebugUE4TasksName[MappingIdx].c_str(), NSwarm::JOB_TASK_FLAG_USE_DEFAULTS);
            NewTaskSpecification.Cost = DebugUE4TasksCost[MappingIdx];
            ErrorCode = BakingContext.Swarm->AddTask(NewTaskSpecification);
            NumTotalSwarmTasks++;
        }
    }
    else
    {
        // Count the number of tasks given to Swarm
        for (int MappingIdx = 0; (ErrorCode >= 0) && MappingIdx < BakingContext.LightMapJobs.size(); ++MappingIdx)
        {
            const BakerLightMapJobInfo& lmJobInfo = BakingContext.LightMapJobs[MappingIdx];
            NSwarm::FGuid jobID = ToGuid(lmJobInfo.Guid);
            NSwarm::FTaskSpecification NewTaskSpecification(jobID, BAKING_TEXT("Lightmap2D"), NSwarm::JOB_TASK_FLAG_USE_DEFAULTS);
            NewTaskSpecification.Cost = 10000;
            ErrorCode = BakingContext.Swarm->AddTask(NewTaskSpecification);
            NumTotalSwarmTasks++;
        }

        for (int MappingIdx = 0; (ErrorCode >= 0) && MappingIdx < BakingContext.SDFShadowMapJobs.size(); ++MappingIdx)
        {
            const BakerSDFShadowMapJobInfo& smJobInfo = BakingContext.SDFShadowMapJobs[MappingIdx];
            NSwarm::FGuid jobID = ToGuid(smJobInfo.Guid);
            NSwarm::FTaskSpecification NewTaskSpecification(jobID, BAKING_TEXT("SdfShadow"), NSwarm::JOB_TASK_FLAG_USE_DEFAULTS);
            NewTaskSpecification.Cost = 10000;
            ErrorCode = BakingContext.Swarm->AddTask(NewTaskSpecification);
            NumTotalSwarmTasks++;
        }

        for (int MappingIdx = 0; (ErrorCode >= 0) && MappingIdx < BakingContext.LocalLightMapPRTJobs.size(); ++MappingIdx)
        {
            const BakerLocalLightMapPRTJobInfo& localLMJobInfo = BakingContext.LocalLightMapPRTJobs[MappingIdx];
            NSwarm::FGuid jobID = ToGuid(localLMJobInfo.Guid);
            NSwarm::FTaskSpecification NewTaskSpecification(jobID, BAKING_TEXT("LocalTransferLightMap2D"), NSwarm::JOB_TASK_FLAG_USE_DEFAULTS);
            NewTaskSpecification.Cost = 10000;
            ErrorCode = BakingContext.Swarm->AddTask(NewTaskSpecification);
            NumTotalSwarmTasks++;
        }

        if (JobInputs.LightProbeJobs.Num() > 0)
        {
            NSwarm::FTaskSpecification NewTaskSpecification(PrecomputedVolumeLightingGuid, BAKING_TEXT("VolumeSamples"), NSwarm::JOB_TASK_FLAG_USE_DEFAULTS);
            NewTaskSpecification.Cost = 10000;
            ErrorCode = BakingContext.Swarm->AddTask(NewTaskSpecification);
            if (ErrorCode >= 0)
            {
                NumTotalSwarmTasks++;
            }
            else
            {
                LOG_ERROR("Add Task Failed, PrecomputedVolumeLightingGuid");
            }
        }

        if (JobInputs.VolumetricLightmapTaskGuids.Num() > 0)
        {
            for (const auto& TaskGuid : JobInputs.VolumetricLightmapTaskGuids.GetElements()) 
            {
                NSwarm::FTaskSpecification NewTaskSpecification(GPUBaking::ToGuid(TaskGuid), BAKING_TEXT("VolumetricLightmap"), NSwarm::JOB_TASK_FLAG_USE_DEFAULTS);
                NewTaskSpecification.Cost = 10000;
                ErrorCode = BakingContext.Swarm->AddTask(NewTaskSpecification);
                if (ErrorCode >= 0)
                {
                    NumTotalSwarmTasks++;
                }
                else
                {
                    LOG_ERROR("Add Task Failed, VolumetricLightmapTaskGuids");
                }
            }
        }
    }

    int32 EndJobErrorCode = BakingContext.Swarm->EndJobSpecification();
    if (EndJobErrorCode < 0)
    {
        LOG_ERROR("Error, EndJobSpecification failed with error code {}", EndJobErrorCode);
    }

    if (ErrorCode < 0 || EndJobErrorCode < 0)
    {
        bProcessingFailed = true;
    }

    // Exporter->ClearSceneInfo();

    return !bProcessingFailed;
}

void FTLBSProcessor::InitiateExport()
{
    Exporter->ExportSceneInfo(BakingContext, DebugMappingGuid);
}

NS_GPUBAKING_END
