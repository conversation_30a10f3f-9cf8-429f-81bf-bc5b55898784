#include "ParticleEmitMesh.h"
#include "Resource/ParticleSystem/ParticleEmitterInfo.h"

namespace cross::fx {

ParticleEmitMesh::ParticleEmitMesh(resource::MeshAssetDataResource* meshAsset, UInt32 lodIndex) : mMeshAsset(meshAsset), mLodIndex(lodIndex)
{
    Assert(meshAsset);
    MeshAssetData* meshData = meshAsset->GetAssetData();
    const auto& position0Data = meshData->GetRawVertexAndIndexData(lodIndex, cross::VertexChannel::Position0);
    mPositions.resize(position0Data.vertexCount);
    memcpy(mPositions.data(), position0Data.vertexData, position0Data.vertexCount * sizeof(Float3));
    meshData->GetRawVertexData(lodIndex, VertexChannel::Normal0, mNormals);
    if (position0Data.is16BitIndex)
    {
        mIndices = std::vector<UInt16>(position0Data.indexCount);
        memcpy(std::get<std::vector<UInt16>>(mIndices).data(), position0Data.indexData, position0Data.indexCount * sizeof(UInt16));
    }
    else
    {
        mIndices = std::vector<UInt32>(position0Data.indexCount);
        memcpy(std::get<std::vector<UInt32>>(mIndices).data(), position0Data.indexData, position0Data.indexCount * sizeof(UInt32));
    }

    mIndexCount = position0Data.indexCount;
    mVertexCount = position0Data.vertexCount;

    if (mNormals.size() != mVertexCount)
    {
        mNormals.resize(mVertexCount);
        for (UInt32 i = 0; i < mVertexCount; i++)
        {
            mNormals[i] = mPositions[i].Normalized();
        }
    }
}

Float3 GetRandomBaryCoord(Rand& random)
{
    Float2 r = {random.GetFloat(), random.GetFloat()};
    float sqrt0 = sqrt(r.x);
    float sqrt1 = sqrt(r.y);
    return {1.0f - sqrt0, sqrt0 * (1.0f- r.y), r.y * sqrt0};
}

EmitPoint ParticleEmitMesh::GetEmitPoint(MeshSurfaceEmitType mode, Rand& random) const
{
    Float3 position, normal;
    if (mode == MeshSurfaceEmitType::Triangle)
    {
        UInt32 triangleCount = mIndexCount / 3;
        auto tri = RangedRandom(random, 0u, triangleCount);
        UInt32 indices[3];
        Float3 positions[3], normals[3];
        if (std::holds_alternative<std::vector<UInt32>>(mIndices))
        {
            auto& ptr = std::get<std::vector<UInt32>>(mIndices);
            indices[0] = ptr[tri * 3];
            indices[1] = ptr[tri * 3 + 1];
            indices[2] = ptr[tri * 3 + 2];
        }
        else
        {
            auto& ptr = std::get<std::vector<UInt16>>(mIndices);
            indices[0] = ptr[tri * 3];
            indices[1] = ptr[tri * 3 + 1];
            indices[2] = ptr[tri * 3 + 2];
        }

        positions[0] = mPositions[indices[0]];
        positions[1] = mPositions[indices[1]];
        positions[2] = mPositions[indices[2]];
        normals[0] = mNormals[indices[0]];
        normals[1] = mNormals[indices[1]];
        normals[2] = mNormals[indices[2]];
        auto baryCoord = GetRandomBaryCoord(random);
        position = baryCoord.x * positions[0] + baryCoord.y * positions[1] + baryCoord.z * positions[2];
        normal = baryCoord.x * normals[0] + baryCoord.y * normals[1] + baryCoord.z * normals[2];
    }
    else if (mode == MeshSurfaceEmitType::Vertex)
    {
        auto vertex = RangedRandom(random, 0u, mVertexCount);
        position = mPositions[vertex];
        normal = mNormals[vertex];
    }
    else if (mode == MeshSurfaceEmitType::Edge)
    {
        UInt32 triangleCount = mIndexCount / 3;
        auto tri = RangedRandom(random, 0u, triangleCount);
        UInt32 indices[3];
        Float3 positions[3], normals[3];
        if (std::holds_alternative<std::vector<UInt32>>(mIndices))
        {
            auto& ptr = std::get<std::vector<UInt32>>(mIndices);
            indices[0] = ptr[tri * 3];
            indices[1] = ptr[tri * 3 + 1];
            indices[2] = ptr[tri * 3 + 2];
        }
        else
        {
            auto& ptr = std::get<std::vector<UInt16>>(mIndices);
            indices[0] = ptr[tri * 3];
            indices[1] = ptr[tri * 3 + 1];
            indices[2] = ptr[tri * 3 + 2];
        }

        positions[0] = mPositions[indices[0]];
        positions[1] = mPositions[indices[1]];
        positions[2] = mPositions[indices[2]];
        normals[0] = mNormals[indices[0]];
        normals[1] = mNormals[indices[1]];
        normals[2] = mNormals[indices[2]];
        const int edgeIndex = RangedRandom(random, 0u, 3u);
        const int nextEdgeIndex = (edgeIndex + 1) % 3;
        const float t = random.GetFloat();
        position = Float3(std::lerp(positions[edgeIndex].x, positions[nextEdgeIndex].x, t),
                          std::lerp(positions[edgeIndex].y, positions[nextEdgeIndex].y, t),
                          std::lerp(positions[edgeIndex].z, positions[nextEdgeIndex].z, t));
        normal = Float3(std::lerp(normals[edgeIndex].x, normals[nextEdgeIndex].x, t), 
                        std::lerp(normals[edgeIndex].y, normals[nextEdgeIndex].y, t), 
                        std::lerp(normals[edgeIndex].z, normals[nextEdgeIndex].z, t));

    }
    else
    {
        Assert(false);
    }

    return std::make_tuple(position, normal);
}

}
