#pragma once
#include <array>
#include "ParticleSystemCommon.h"
#include "CECommon/Utilities/Random.h"
#include "Resource/ParticleSystem/ParticleEmitterInfo.h"

namespace cross::fx {

using EmitPoint = std::tuple<Float3, Float3>;

class ParticleEmitMesh
{
public:
    ~ParticleEmitMesh() = default;

    ParticleEmitMesh(resource::MeshAssetDataResource* meshAsset, UInt32 lodIndex);
    EmitPoint GetEmitPoint(MeshSurfaceEmitType mode, Rand& random) const;

public:
    inline void SetEmitType(MeshSurfaceEmitType dst) { mEmitType = dst; }
    inline MeshSurfaceEmitType GetEmitType() const { return mEmitType; }

private:
    MeshSurfaceEmitType mEmitType{ MeshSurfaceEmitType::Triangle};
    resource::MeshAssetDataResource* mMeshAsset = nullptr;
    UInt32 mLodIndex = 0, mIndexCount = 0, mVertexCount = 0;
    std::vector<Float3> mPositions;
    std::vector<Float3> mNormals;
    std::variant<std::vector<UInt32>, std::vector<UInt16>> mIndices;
};

}   // namespace cross::fx