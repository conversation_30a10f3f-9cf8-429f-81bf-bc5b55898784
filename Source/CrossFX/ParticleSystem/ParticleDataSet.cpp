#include "ParticleDataSet.h"

namespace cross::fx
{
gbf::allocator::RangeAllocatorResourceThreadSafe ParticleDataBuffer::mMemoryResource = gbf::allocator::RangeAllocatorResourceThreadSafe(1024 * 1024 * 4, 1024 * 1024 * 4);
ParticleDataBuffer::~ParticleDataBuffer()
{
    mSourceData.clear();
    mInstanceNum = 0u;
}

ParticleDataBuffer::ParticleDataBuffer(const ParticleDataBuffer& other)
    : mOwner(other.mOwner)
    , mSourceData(&mMemoryResource)
    , mInstanceNum(other.mInstanceNum)

{
    mSourceData.assign(other.mSourceData.begin(), other.mSourceData.end());
}

void ParticleDataBuffer::Allocate(UInt32 instanceNum)
{

    mSourceData.resize(instanceNum * mOwner->GetStride());
    mInstanceNum = instanceNum;
}

ParticleDataSet::ParticleDataSet(bool isGpu)
{
    mCurrentBuffer = isGpu ? nullptr : std::make_shared<ParticleDataBuffer>(this);
}

ParticleDataSet::~ParticleDataSet()
{
}

void ParticleDataSet::Allocate(UInt32 instanceNum)
{
    if (mCurrentBuffer)
    {
        mCurrentBuffer->Allocate(instanceNum);
    }
    mInstanceNum = instanceNum;
}

void ParticleDataSet::BindParameter(const ParticleVariable& param)
{
    auto& variable = mVariables.emplace_back(param);
    variable.Offset = mStride;
    mStride += static_cast<UInt32>(param.ParamType);
}
}   // namespace cross::fx