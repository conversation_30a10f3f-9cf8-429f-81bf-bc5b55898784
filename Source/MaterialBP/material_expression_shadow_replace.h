#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionShadowReplace : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "ShadowPassSwitch";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Default;

    CEProperty(Reflect)
    ExpressionInput m_Shadow;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross