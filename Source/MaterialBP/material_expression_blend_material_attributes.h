#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionBlendMaterialAttributes : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "BlendMaterialAttributes";
    }

public:
    CEProperty(Reflect)
    ExpressionAttributesInput m_A;

    CEProperty(Reflect)
    ExpressionAttributesInput m_B;

    CEProperty(Reflect)
    ExpressionInput m_Alpha;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross