#include "CrossBase/Log.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Interface/AssetPipeline.h"
#include "GamePlayBaseFramework/base/core/core_global.hpp"
#include "material_compiler.h"
#include "material_expression.h"
#include "material_expression_custom.h"
#include "material_expression_function_call.h"
#include "material_expression_surface_shader.h"
#include "material_expression_vertex_shader.h"
#include "material_expression_parameter.h"
#include "material_expression_custom_interpolator.h"
#include "material_expression_named_reroute.h"
#include <assert.h>
#include <fmt/format.h>
#include <fstream>
#include <stdarg.h>
#include "shader_template.h"
#include "Resource/Fx.h"
#include "Runtime/Animation/Animator/Parameter/exprtk_expression.hpp"
#pragma warning(push)
#pragma warning(disable : 4701)
#pragma warning(disable : 4703)
#ifndef LINE_TERMINATOR
#    define LINE_TERMINATOR "\n"
#endif
#include "Math/DoubleFloat.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
namespace cross {

static inline MaterialValueType GetVectorType(uint32_t numComponents)
{
    switch (numComponents)
    {
    case 1:
        return MaterialValueType::MCT_Float1;
    case 2:
        return MaterialValueType::MCT_Float2;
    case 3:
        return MaterialValueType::MCT_Float3;
    case 4:
        return MaterialValueType::MCT_Float4;
    default:
        return MaterialValueType::MCT_Unknown;
    };
}

MaterialCompiler::MaterialCompiler(InputParameters parameters, bool isCompileFunctionPreview)
    : m_InputParams(parameters)
    , m_IsCompileExpressionPreview(isCompileFunctionPreview)
{
    if (m_InputParams.clearMessage)
    {
        m_InputParams.clearMessage();
    }
    ResetCompileState();
}

MaterialCompiler::~MaterialCompiler()
{
    for (int i = 0; i < SF_NumFrequencies; i++)
    {
        for (auto* functionState : m_FunctionStacks[i])
        {
            delete functionState;
        }
    }
}

bool MaterialCompiler::Translate(CompiledShaderCodes& codes)
{
    switch (m_InputParams.defines.Domain)
    {
    case MaterialDomain::Surface:
        m_IsCompilePostProcess = false;
        codes.NeedCompileForward = true;
        codes.NeedCompileGPass = true;
        codes.NeedCompileVSMDepth = true;
        codes.NeedCompileShadow = true;
        codes.NeedCompileMeshDecal = false;
        return Translate_Surface(codes);
    case MaterialDomain::MeshDecal:
        m_IsCompilePostProcess = false;
        codes.NeedCompileForward = false;
        codes.NeedCompileGPass = false;
        codes.NeedCompileVSMDepth = false;
        codes.NeedCompileShadow = false;
        codes.NeedCompileMeshDecal = true;
        return Translate_MeshDecal(codes);
    case MaterialDomain::Foliage:
        m_IsCompilePostProcess = false;
        codes.NeedCompileForward = false;
        codes.NeedCompileGPass = true;
        codes.NeedCompileVSMDepth = true;
        codes.NeedCompileShadow = false;
        codes.NeedCompileMeshDecal = false;
        return Translate_Foliage(codes);
    case MaterialDomain::PostProcess:
        m_IsCompilePostProcess = true;
        codes.NeedCompileForward = true;
        codes.NeedCompileGPass = false;
        codes.NeedCompileVSMDepth = false;
        codes.NeedCompileShadow = false;
        codes.NeedCompileMeshDecal = false;
        return Translate_PostProcess(codes);
    default:
        Assert(false);
    }
    return false;
}
void MaterialCompiler::ClearVtIndex()
{
    if (m_InputParams.VTs)
    {
        m_InputParams.VTs->clear();
    }
    if (m_InputParams.overflowVTs)
    {
        m_InputParams.overflowVTs->clear();
    }
}
bool MaterialCompiler::Translate_Foliage(CompiledShaderCodes& codes)
{
    return TranslateDefaultImpl(codes,
                                
                                MaterialTemplateFileLocations{
                                    "Shader\\Material\\Lit\\SurfaceForward.template",
                                    "Shader\\Material\\Lit\\FoliageGPass.template",
                                    "Shader\\Material\\Lit\\SurfaceShadow.template",
                                    "Shader\\Material\\Lit\\FoliageVSMDepth.template",
                                    "",
                                });
}

bool MaterialCompiler::Translate_Surface(CompiledShaderCodes& codes)
{
    return TranslateDefaultImpl(
        codes,
        
        MaterialTemplateFileLocations {
            "Shader\\Material\\Lit\\SurfaceForward.template",
            "Shader\\Material\\Lit\\SurfaceGPass.template",
            "Shader\\Material\\Lit\\SurfaceShadow.template",
            "Shader\\Material\\Lit\\SurfaceVSMDepth.template",
            "",
        }
    );
}

bool MaterialCompiler::Translate_PostProcess(CompiledShaderCodes& codes)
{
    bool compileSuccess = true;

    ClearVtIndex();

    TranslateParams defaultParams{
        .usages = {{.Usage = MaterialUsage::USED_WITH_DEFAULT}},
    };

    auto& materialDefines = m_InputParams.defines;

    if (materialDefines.EnableDebugSymbol)
    {
        defaultParams.enables.emplace_back(material_literal::debug_symbol);
    }

    if (codes.NeedCompileForward)
    {
        auto params = defaultParams;
        codes.ForwardShaderCode.ShaderCode = TranslatePass_PostProcess(ForwardPassName, "Shader\\Material\\Lit\\PostProcessForward.template", params);
        compileSuccess &= !codes.ForwardShaderCode.ShaderCode.empty();
    }

    return compileSuccess;
}

bool MaterialCompiler::Translate_MeshDecal(CompiledShaderCodes& codes)
{
    return TranslateDefaultImpl(codes,
                                
                                MaterialTemplateFileLocations{
                                    "",
                                    "",
                                    "",
                                    "",
                                    "Shader\\Material\\Lit\\MeshDecal.template",
                                });
}

bool MaterialCompiler::TranslateDefaultImpl(CompiledShaderCodes& codes, MaterialTemplateFileLocations const& templateFileLocation)
{
    bool compileSuccess = true;

    ClearVtIndex();

    TranslateParams defaultParams{
        .usages = {{.Usage = MaterialUsage::USED_WITH_DEFAULT}},
    };

    auto& materialDefines = m_InputParams.defines;

    if (materialDefines.EnableDebugSymbol)
    {
        defaultParams.enables.emplace_back(material_literal::debug_symbol);
    }

    if (materialDefines.UsedWithLocalSpaceParticle || materialDefines.UsedWithGlobalSpaceParticle)
    {
        defaultParams.defines.emplace_back("PARTICLE_PASS");
    }

    auto HandleParticleUsage = [&](TranslateParams& params, bool enableMeshParticle, bool enableSpriteParticle) {
        auto AddSubKeyword = [&](auto& usage) {
            if (enableMeshParticle)
            {
                usage.Keywords.emplace_back("ENABLE_MESH_PARTICLE");
            }
            if (enableSpriteParticle)
            {
                usage.Keywords.emplace_back("ENABLE_SPRITE_PARTICLE");
            }
        };

        if (m_InputParams.defines.UsedWithLocalSpaceParticle)
        {
            AddSubKeyword(params.usages.emplace_back(MaterialUsage::USED_WITH_LOCAL_SPACE_PARTICLE));
        }

        if (m_InputParams.defines.UsedWithGlobalSpaceParticle)
        {
            AddSubKeyword(params.usages.emplace_back(MaterialUsage::USED_WITH_GLOBAL_SPACE_PARTICLE));
        }
    };

    if (m_InputParams.defines.UsedWithTerrain)
    {
        defaultParams.usages.emplace_back(MaterialUsage::USED_WITH_TERRAIN);
    }

    auto surfaceShaderExpression = m_InputParams.surfaceShaderExpression;
    m_CompilationOutput.mBaseColorConnencted     = surfaceShaderExpression && surfaceShaderExpression->m_BaseColor.m_LinkedExpressionOutput     != nullptr;
    m_CompilationOutput.mMetallicConnencted      = surfaceShaderExpression && surfaceShaderExpression->m_Metallic.m_LinkedExpressionOutput      != nullptr;
    m_CompilationOutput.mSpecularConnencted      = surfaceShaderExpression && surfaceShaderExpression->m_Specular.m_LinkedExpressionOutput      != nullptr;
    m_CompilationOutput.mRoughnessConnencted     = surfaceShaderExpression && surfaceShaderExpression->m_Roughness.m_LinkedExpressionOutput     != nullptr;
    m_CompilationOutput.mOpacityConnencted       = surfaceShaderExpression && surfaceShaderExpression->m_Opacity.m_LinkedExpressionOutput       != nullptr;
    m_CompilationOutput.mEmissiveColorConnencted = surfaceShaderExpression && surfaceShaderExpression->m_EmissiveColor.m_LinkedExpressionOutput != nullptr;
    m_CompilationOutput.mNormalConnencted        = surfaceShaderExpression && surfaceShaderExpression->m_Normal.m_LinkedExpressionOutput        != nullptr;

    if (codes.NeedCompileForward)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, true);
        codes.ForwardShaderCode.ShaderCode = TranslatePass_Surface(ForwardPassName, templateFileLocation.Forward, params);
        compileSuccess &= !codes.ForwardShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileGPass)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, false);
        if (m_InputParams.defines.UsedWithSkeletalMesh)
        {
            params.usages.emplace_back(MaterialUsage::USED_WITH_SKELETAL_MESH);
        }

        codes.GPassShaderCode.ShaderCode = TranslatePass_Surface(GPassName, templateFileLocation.GPass, params);
        compileSuccess &= !codes.GPassShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileShadow)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, false);
        codes.ShadowShaderCode.ShaderCode = TranslatePass_Surface(ShadowPassName, templateFileLocation.Shadow, params);
        compileSuccess &= !codes.ShadowShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileVSMDepth)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, false);
        codes.VSMDepthShaderCode.ShaderCode = TranslatePass_Surface(VSMPassName, templateFileLocation.VSMDepth, params);
        compileSuccess &= !codes.VSMDepthShaderCode.ShaderCode.empty();
    }

    if (codes.NeedCompileMeshDecal)
    {
        auto params = defaultParams;
        HandleParticleUsage(params, true, true);
        codes.MeshDecalShaderCode.ShaderCode = TranslatePass_Surface(MeshDecalPassName, templateFileLocation.MeshDecal, params);
        compileSuccess &= !codes.MeshDecalShaderCode.ShaderCode.empty();
    }

    return compileSuccess;
}

void MaterialCompiler::ResetCompileState()
{
    m_NextSymbolIndex = 0;

    for (int i = 0; i < SF_NumFrequencies; i++)
    {
        m_SharedPropertyCodeChunks[i].clear();

        for (auto* functionState : m_FunctionStacks[i])
        {
            delete functionState;
        }

        m_FunctionStacks[i].clear();
        m_FunctionStacks[i].push_back(new MaterialFunctionCompileState(nullptr));
    }

    m_ShaderConstSet.Clear();
    m_ExpressionCustomEntries.clear();
    m_Samplers.clear();
    m_MacroSet.Clear();
    m_CustomInterpolatorSet.Clear();
    m_ExternalCustomInterpolatorCodeIndices.clear();
    m_NumVirtualTextureFeedbackRequests = 0;
}

std::string MaterialCompiler::TranslatePass_Surface(std::string_view passName, std::string_view templateFileName, const TranslateParams& params)
{
    // Reset compile state
    ResetCompileState();

    // Add common macros
    if (passName == ForwardPassName || passName == GPassName || passName == MeshDecalPassName)
    {
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
        m_MacroSet.AddMacro(Macro_VertexNeedTangent);
    }
    else if (passName == ShadowPassName)
    {
        // ShadowMap need normal to do depth bias
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
    }
    else if (passName == VSMPassName)
    {
        m_MacroSet.AddMacro(Macro_VertexNeedClipDistance);
    }

    for (auto& define : params.defines)
    {
        m_MacroSet.AddMacro(std::string(define));
    }

    // when translate shadow pass, we will only compile OpacityMask and WorldPositionOffset
    m_IsCompileShadowPass = passName == ShadowPassName || passName == VSMPassName;

    int32_t result = CODE_CHUNK_INDEX_NONE;

    MaterialExpressionVertexShader* vertexShaderExpression = m_InputParams.vertexShaderExpression;
    VertexShaderCompileResult* vertexShaderCompileResult;
    MaterialExpressionSurfaceShader* surfaceShaderExpression = m_InputParams.surfaceShaderExpression;
    SurfaceShaderCompileResult* surfaceShaderCompileResult;

    MaterialExpression* previewExpression = m_InputParams.previewExpression;

    if (!previewExpression)
    {
        vertexShaderExpression->m_CompileResult.Clear();
        surfaceShaderExpression->m_CompileResult.Clear();
        CallRootExpression(vertexShaderExpression);
        GatherCustomVertexInterpolators(surfaceShaderExpression);
        CallRootExpression(surfaceShaderExpression);

        vertexShaderCompileResult = &vertexShaderExpression->m_CompileResult;
        surfaceShaderCompileResult = &surfaceShaderExpression->m_CompileResult;

        if (!surfaceShaderCompileResult->IsValid() || !vertexShaderCompileResult->IsValid() || !m_CustomInterpolatorSet.IsValid())
        {
            return "";
        }
    }
    else
    {
        AssignShaderFrequencyScope(SF_Surface);

        Assert(!previewExpression->GetOutputPins().empty());

        result = CallExpression(previewExpression, previewExpression->GetOutputPins()[0]);

        if (result == CODE_CHUNK_INDEX_NONE)
        {
            result = Constant3(0.0, 0.0, 0.0);
        }
    }

    if (HasVT())
    {
        m_MacroSet.AddMacro("OPEN_VT");
        m_MacroSet.AddMacro("NUM_VIRTUALTEXTURE_SAMPLES", std::to_string(m_NumVirtualTextureFeedbackRequests));
    }

    if (m_InputParams.defines.UsedWithTerrain)
    {
        m_MacroSet.AddMacro(Macro_NumMaterialTexcoords, "1");
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
        m_MacroSet.AddMacro(Macro_VertexNeedTangent);
        m_MacroSet.AddMacro(GetTerrainMacro(m_InputParams.defines.TerrainMode));
    }

    // Assemble hlsl code
    {
        ShaderTemplate shaderTemplate;

        // Keywords and enables
        {
            std::stringstream ss;
            for (auto& keyword : params.keywords)
            {
                ss << fmt::format("#pragma keyword {}\n", keyword);
            }
            for (auto& usage : params.usages)
            {
                ss << fmt::format("#pragma usage {}\n", usage.Usage);
                for (auto& keyword : usage.Keywords)
                {
                    ss << fmt::format("#pragma usage_keyword {}\n", keyword);
                }
            }
            for (auto& enable : params.enables)
            {
                ss << fmt::format("#pragma enable {}\n", enable);
            }

            shaderTemplate.PushCode("KeywordDefinitions", ss.str());
        }

        // Macro
        {
            std::stringstream ss;
            for (const auto& [key, value] : m_MacroSet.GetMacros())
            {
                ss << fmt::format("#define {} {}\n", key, value);
            }

            shaderTemplate.PushCode("MacroDefinitions", ss.str());
        }

        // ShaderConst
        {
            std::stringstream ss;
            for (auto& shaderConst : m_ShaderConstSet.GetShaderConsts())
            {
                ss << fmt::format("SHADER_CONST({}, {}, {});\n", HLSLTypeString(GetMaterialValueType(shaderConst.Type)), shaderConst.ShaderConstName, 1);
            }

            shaderTemplate.PushCode("ShaderConstDefinitions", ss.str());
        }

        // ObjectSceneData
        {
            std::stringstream ss;
            for (auto& [name, type] : m_GPUSceneObjectDataSet)
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(type), name);
            }

            shaderTemplate.PushCode("ObjectSceneDataDefinitions", ss.str());
        }

        // PrimitiveSceneData
        {
            std::stringstream ss;
            for (auto& [name, type] : m_GPUScenePrimitiveDataSet)
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(type), name);
            }

            shaderTemplate.PushCode("PrimitiveSceneDataDefinitions", ss.str());
        }

        // NumericParameter
        {
            std::stringstream ss;
            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (!IsTexture(parameter.m_Type))
                {
                    ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << ";\\\n";
                }
            }

            shaderTemplate.PushCode("NumericParameters", ss.str());
        }

        // TextureParameter
        {
            std::stringstream ss;

            for (auto& [sceneTextureId, lookUp] : m_SceneTextures)
            {
                if (lookUp)
                {
                    ss << fmt::format("Texture2D<float4> ce_{} : register(space0);\n", sceneTextureId);
                }
            }

            if (HasVT())
            {
                ss << "#include \"Features/VirtualTexture/VirtualTextureCommon.hlsl\"\n";
            }
            if (m_InputParams.defines.UsedWithTerrain)
            {
                ss << "#include \"Material/Lit/SurfaceData_MaterialEditor/Terrain.hlsl\"\n";
            }

            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (IsTexture(parameter.m_Type))
                {
                    if (int index = FindVtIndex(parameter.m_ParameterName); index != -1)
                    {
                        // Virtual texture
                        ss << fmt::format("DECLARVT(Texture2D<float4>, {}, {});\n", parameter.m_ParameterName, index);
                    }
                    else
                    {
                        // Not virtual texture
                        ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << " : register(space1);\n";
                    }
                }
            }

            shaderTemplate.PushCode("TextureParameters", ss.str());
        }

        // Sampler
        {
            std::stringstream ss;

            for (auto& sampler : m_Samplers)
            {
                ss << fmt::format("SamplerState {} : register(space1);\n", sampler.Name);
            }

            shaderTemplate.PushCode("SamplerParameters", ss.str());
        }

        // CustomFunction
        {
            std::stringstream ss;
            for (auto& expressionCustomEntry : m_ExpressionCustomEntries)
            {
                ss << expressionCustomEntry.Implementation << "\n";
            }

            shaderTemplate.PushCode("CustomFunctions", ss.str());
        }

        // CustomVSOutput
        {
            std::stringstream ss;
            int index = 1;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("{} {} : COLOR{}; \\\n", HLSLTypeString(GetMaterialValueType(type)), innerName, std::to_string(index++));
            }

            shaderTemplate.PushCode("CustomVSOutput", ss.str());
        }

        // CustomPSInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(GetMaterialValueType(type)), innerName);
            }

            shaderTemplate.PushCode("CustomPSInput", ss.str());
        }

        // CustomSurfaceInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("{} {}; \\\n", HLSLTypeString(GetMaterialValueType(type)), innerName);
            }

            shaderTemplate.PushCode("CustomSurfaceInput", ss.str());
        }

        // WorldPositionOffset
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_WorldPositionOffset);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams vtFeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_WorldPositionOffset])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            if (!m_InputParams.previewExpression)
            {
                if (vertexShaderCompileResult->WorldPositionOffset)
                {
                    ss << "return positionWS + " << GetParameterCode(*vertexShaderCompileResult->WorldPositionOffset) << ";\n";
                }
                else
                {
                    ss << "return positionWS;\n";
                }
            }
            else
            {
                ss << "return positionWS;\n";
            }

            shaderTemplate.PushCode("WorldPositionOffset", ss.str());
        }

        // Generate CustomInterpolators
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_GenerateCustomInterpolators);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams vtFeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_GenerateCustomInterpolators])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            const auto& customInnerInterpolators = m_CustomInterpolatorSet.GetCustomInterpolators();
            for (int i = 0; i < customInnerInterpolators.size(); i++)
            {
                ss << fmt::format("input.{} = {};\n", customInnerInterpolators[i].InnerName, GetParameterCode(customInnerInterpolators[i].CodeIndex).data());
            }
            
            shaderTemplate.PushCode("GenerateCustomInterpolators", ss.str());
        }

        // SetCustomPSInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("output.{} = input.{}; \\\n", innerName, innerName);
            }

            shaderTemplate.PushCode("SetCustomPSInput", ss.str());
        }

        // SetCustomPSInput
        {
            std::stringstream ss;
            for (const auto& [type, _, innerName, code] : m_CustomInterpolatorSet.GetCustomInterpolators())
            {
                ss << fmt::format("surfaceInput.{} = psInput.{}; \\\n", innerName, innerName);
            }

            shaderTemplate.PushCode("SetCustomSurfaceInput", ss.str());
        }

        // SurfaceData
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_Surface);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams vtFeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_Surface])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            ss << "SurfaceData surfaceData = (SurfaceData)0;\n";

            if (!m_InputParams.previewExpression)
            {
                if (surfaceShaderCompileResult->baseColor)
                {
                    ss << "surfaceData.baseColor = " + GetParameterCode(*surfaceShaderCompileResult->baseColor) << ";\n";
                }

                if (surfaceShaderCompileResult->metallic)
                {
                    ss << "surfaceData.metallic = " + GetParameterCode(*surfaceShaderCompileResult->metallic) << ";\n";
                }

                if (surfaceShaderCompileResult->specular)
                {
                    ss << "surfaceData.specular = " + GetParameterCode(*surfaceShaderCompileResult->specular) << ";\n";
                }

                if (surfaceShaderCompileResult->roughness)
                {
                    ss << "surfaceData.roughness = " + GetParameterCode(*surfaceShaderCompileResult->roughness) << ";\n";
                }

                if (surfaceShaderCompileResult->opacity)
                {
                    ss << "surfaceData.opacity = " + GetParameterCode(*surfaceShaderCompileResult->opacity) << ";\n";
                }

                if (surfaceShaderCompileResult->opacityMask)
                {
                    ss << "surfaceData.opacityMask = " + GetParameterCode(*surfaceShaderCompileResult->opacityMask) << ";\n";
                }

                ss << "surfaceData.opacityMaskClip = 0.3333;\n";

                if (surfaceShaderCompileResult->normal)
                {
                    ss << "surfaceData.normalTS = " + GetParameterCode(*surfaceShaderCompileResult->normal) << ";\n";
                }

                if (surfaceShaderCompileResult->ambientOcclusion)
                {
                    ss << "surfaceData.ambientOcclusion = " + GetParameterCode(*surfaceShaderCompileResult->ambientOcclusion) << ";\n";
                }

                if (surfaceShaderCompileResult->emissiveColor)
                {
                    ss << "surfaceData.emissiveColor = " + GetParameterCode(*surfaceShaderCompileResult->emissiveColor) << ";\n";
                }

                if (surfaceShaderCompileResult->subsurfaceColor)
                {
                    ss << "surfaceData.subsurfaceColor = " + GetParameterCode(*surfaceShaderCompileResult->subsurfaceColor) << ";\n";
                }

                if (surfaceShaderCompileResult->debugColor)
                {
                    ss << "surfaceData.debugColor = " + GetParameterCode(*surfaceShaderCompileResult->debugColor) << ";\n";
                }

                if (surfaceShaderCompileResult->temporalReactive)
                {
                    ss << "surfaceData.temporalReactive = " + GetParameterCode(*surfaceShaderCompileResult->temporalReactive) << ";\n";
                }

                if (HasVT())
                {
                    UInt32 FrameNumber = 1;
		            float Opacity = 1.0;
                    ss << "FinalizeVirtualTextureFeedback(vtFeedbackParams, input.positionNDC, " << Opacity << ", " << FrameNumber << ", VTFeedbackBuffer);\n";
                }

                ss << "surfaceData.materialType = MATERIAL_TYPE;\n";
            }
            else
            {
                ss << "surfaceData.opacity = 1.0f;\n";
                ss << "surfaceData.opacityMask = 1.0f;\n";
                ss << "surfaceData.opacityMaskClip = 0.333;\n";
                ss << "surfaceData.normalTS = float3(0, 0, 1);\n";
                ss << "surfaceData.emissiveColor = " + GetParameterCode(result) << ";\n";
                ss << "surfaceData.materialType = MaterialType_Unlit;\n";
            }

            ss << "return surfaceData;";

            shaderTemplate.PushCode("SurfaceData", ss.str());
        }

        return shaderTemplate.OutputGeneratedCode(templateFileName);
    }
}

std::string MaterialCompiler::TranslatePass_PostProcess(const std::string& passName, const std::string& templateFileName, const TranslateParams& params)
{
    // Reset compile state
    ResetCompileState();

    m_MacroSet.AddMacro(Macro_NumMaterialTexcoords, "1");

    for (auto& define : params.defines)
    {
        m_MacroSet.AddMacro(std::string(define));
    }

    int32_t result = CODE_CHUNK_INDEX_NONE;

    /*MaterialExpressionVertexShader* vertexShaderExpression = m_MaterialEditor->GetVertexShaderExpression();
    VertexShaderCompileResult* vertexShaderCompileResult;*/
    MaterialExpressionSurfaceShader* surfaceShaderExpression = m_InputParams.surfaceShaderExpression;
    SurfaceShaderCompileResult* surfaceShaderCompileResult;

    MaterialExpression* previewExpression = m_InputParams.previewExpression;

    if (!m_InputParams.previewExpression)
    {
        //vertexShaderExpression->m_CompileResult.Clear();
        surfaceShaderExpression->m_CompileResult.Clear();

        //CallRootExpression(vertexShaderExpression);
        CallRootExpression(surfaceShaderExpression);

        //vertexShaderCompileResult = &vertexShaderExpression->m_CompileResult;
        surfaceShaderCompileResult = &surfaceShaderExpression->m_CompileResult;

        if (!surfaceShaderCompileResult->IsValid())// || !vertexShaderCompileResult->IsValid())
        {
            return "";
        }
    }
    else
    {
        AssignShaderFrequencyScope(SF_Surface);

        if (previewExpression)
        {
            assert(!previewExpression->GetOutputPins().empty());

            result = CallExpression(previewExpression, previewExpression->GetOutputPins()[0]);
        }

        if (result == CODE_CHUNK_INDEX_NONE)
        {
            result = Constant3(0.0, 0.0, 0.0);
        }
    }

    if (HasVT())
    {
        m_MacroSet.AddMacro("OPEN_VT");
    }

    // Assemble hlsl code
    {
        ShaderTemplate shaderTemplate;

        // Keywords and enables
        {
            std::stringstream ss;
            for (auto& keyword : params.keywords)
            {
                ss << fmt::format("#pragma keyword {}\n", keyword);
            }
            for (auto& usage : params.usages)
            {
                ss << fmt::format("#pragma usage {}\n", usage.Usage);
                for (auto& keyword : usage.Keywords)
                {
                    ss << fmt::format("#pragma usage_keyword {}\n", keyword);
                }
            }
            for (auto& enable : params.enables)
            {
                ss << fmt::format("#pragma enable {}\n", enable);
            }

            shaderTemplate.PushCode("KeywordDefinitions", ss.str());
        }

        // Macro
        {
            std::stringstream ss;
            for (const auto& [key, value] : m_MacroSet.GetMacros())
            {
                ss << fmt::format("#define {} {}\n", key, value);
            }

            shaderTemplate.PushCode("MacroDefinitions", ss.str());
        }

        // ShaderConst
        {
            std::stringstream ss;
            for (auto& shaderConst : m_ShaderConstSet.GetShaderConsts())
            {
                ss << fmt::format("SHADER_CONST({}, {}, {});\n", HLSLTypeString(GetMaterialValueType(shaderConst.Type)), shaderConst.ShaderConstName, 1);
            }

            shaderTemplate.PushCode("ShaderConstDefinitions", ss.str());
        }

        // NumericParameter
        {
            std::stringstream ss;
            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (!IsTexture(parameter.m_Type))
                {
                    ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << ";\\\n";
                }
            }

            shaderTemplate.PushCode("NumericParameters", ss.str());
        }

        // TextureParameter
        {
            std::stringstream ss;

            for (auto& [sceneTextureId, lookUp] : m_SceneTextures)
            {
                if (lookUp)
                {
                    ss << fmt::format("Texture2D<float4> ce_{} : register(space0);\n", sceneTextureId);
                }
            }

            if (HasVT())
            {
                ss << "#include \"Features/VirtualTexture/VirtualTextureCommon.hlsl\"\n";
            }
            if (m_InputParams.defines.UsedWithTerrain)
            {
                ss << "#include \"Material/Lit/SurfaceData_MaterialEditor/Terrain.hlsl\"\n";
            }

            for (auto& parameter : m_AllUniformExpressionSet.GetParamters())
            {
                if (IsTexture(parameter.m_Type))
                {
                    if (int index = FindVtIndex(parameter.m_ParameterName); index != -1)
                    {
                        // Virtual texture
                        ss << fmt::format("DECLARVT(Texture2D<float4>, {}, {});\n", parameter.m_ParameterName, index);
                    }
                    else
                    {
                        // Not virtual texture
                        ss << HLSLTypeString(GetMaterialValueType(parameter.m_Type)) << " " << parameter.m_ParameterName << " : register(space1);\n";
                    }
                }
            }

            shaderTemplate.PushCode("TextureParameters", ss.str());
        }

        // Sampler
        {
            std::stringstream ss;

            for (auto& sampler : m_Samplers)
            {
                ss << fmt::format("SamplerState {} : register(space1);\n", sampler.Name);
            }

            shaderTemplate.PushCode("SamplerParameters", ss.str());
        }

        // CustomFunction
        {
            std::stringstream ss;
            for (auto& expressionCustomEntry : m_ExpressionCustomEntries)
            {
                ss << expressionCustomEntry.Implementation << "\n";
            }

            shaderTemplate.PushCode("CustomFunctions", ss.str());
        }

        // PostProcessData
        {
            // switch ShaderFrequency to make GetParameterCode() to get correct result
            AssignShaderFrequencyScope(SF_Surface);

            std::stringstream ss;

            if (HasVT())
            {
                ss << "FVirtualTextureFeedbackParams FeedbackParams = (FVirtualTextureFeedbackParams)0;\n";
                ss << "InitializeVirtualTextureFeedback(FeedbackParams, input.positionNDC);\n";
                ss << "#if ENABLE_STOCHASTIC_FILTERING\n";
                ss << "float4 randParams;\n";
                ss << "InitializeRandParams(randParams, input.positionNDC.xy);\n";
                ss << "#endif\n";
            }

            for (auto& codeChunk : m_SharedPropertyCodeChunks[SF_Surface])
            {
                if (!codeChunk.m_IsInlined)
                {
                    ss << codeChunk.m_Definition;
                }
            }

            ss << "float3 emissiveColor = " + GetParameterCode(*surfaceShaderCompileResult->emissiveColor) << ";\n";
            ss << "float opacity = " + (m_InputParams.defines.PostProcessEnableOutputAlpha ? GetParameterCode(*surfaceShaderCompileResult->opacity) : "1.f") << ";\n";
            ss << "return float4(emissiveColor.xyz, opacity);";

            shaderTemplate.PushCode("PostProcessData", ss.str());
        }

        return shaderTemplate.OutputGeneratedCode(templateFileName);
    }
}

void MaterialCompiler::CallExpressionSurfaceData(MaterialExpressionSurfaceShader* surfaceDataExpression) {}

void MaterialCompiler::CallExpression(MaterialExpression* expression)
{
    CallExpression(expression, nullptr);
}

const char* MaterialCompiler::GetTerrainMacro(TerrainMode terrainMode)
{
    switch (terrainMode)
    {
    case TerrainMode::Land:
        return Macro_TerrainLand;
        break;
    case TerrainMode::Ocean:
        return Macro_TerrainOcean;
        break;
    case TerrainMode::WeightBlend:
        return Macro_TerrainWeightBlend;
        break;
    default:
        Assert(false);
        return Macro_TerrainLand;
    }
}

void MaterialCompiler::AssignShaderFrequencyScope(ShaderFrequency shaderFrequency)
{
    m_CurrentScopeChunks = &m_SharedPropertyCodeChunks[shaderFrequency];
    m_ShaderFrequency = shaderFrequency;
}

void MaterialCompiler::BeginCompileShaderFrequency(MaterialExpression* expression, ShaderFrequency shaderFrequency)
{
    AssignShaderFrequencyScope(shaderFrequency);

    m_FunctionStacks[m_ShaderFrequency].back()->m_ExpressionStack.push_back({expression, nullptr});
}

void MaterialCompiler::EndCompileShaderFrequency()
{
    m_FunctionStacks[m_ShaderFrequency].back()->m_ExpressionStack.pop_back();
}

int32_t MaterialCompiler::CallExpression(MaterialExpression* expression, ExpressionOutput* output)
{
    EMaterialProperty currentProperty = this->GetMaterialAttribute();
    MaterialExpressionKey expressionKey{expression, output, currentProperty};

    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];
    MaterialFunctionCompileState* currentFuncitonState = currentFunctionStack.back();

    bool expressionCompiled = false;
    int32_t existingCodeIndex = CODE_CHUNK_INDEX_NONE;
    if (auto iter = currentFuncitonState->m_ExpressionCodeMap.find(expressionKey); iter != currentFuncitonState->m_ExpressionCodeMap.end())
    {
        expressionCompiled = true;
        existingCodeIndex = iter->second;
    }

    if (expressionCompiled)
    {
        return existingCodeIndex;
    }
    else
    {
        // Disallow reentrance.
        if (std::find(currentFuncitonState->m_ExpressionStack.begin(), currentFuncitonState->m_ExpressionStack.end(), expressionKey) != currentFuncitonState->m_ExpressionStack.end())
        {
            return Error("Reentrant expression");
        }

        // The first time this expression is called, translate it.
        currentFuncitonState->m_ExpressionStack.push_back(expressionKey);

        // Attempt to share function states between function calls
        MaterialExpressionFunctionCall* functionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression);
        if (functionCall)
        {
            MaterialExpressionKey reuseCompileStateExpressionKey = expressionKey;
            expressionKey.output = nullptr;

            MaterialFunctionCompileState* sharedFunctionState = currentFuncitonState->FindOrAddSharedFunctionState(reuseCompileStateExpressionKey, functionCall);
            functionCall->SetSharedCompileState(sharedFunctionState);
        }

        int32_t result = expression->Compile(*this, output);

        if (auto* expressionParam = dynamic_cast<MaterialExpressionParameter*>(expression))
        {
            expressionParam->m_IsVisibleInMaterialInstanceEditor = true;
        }

        // Restore state
        if (functionCall)
        {
            functionCall->SetSharedCompileState(nullptr);
        }

        MaterialExpressionKey poppedExpressionKey = currentFuncitonState->m_ExpressionStack.back();
        currentFuncitonState->m_ExpressionStack.pop_back();

        // Cache the translation
        currentFuncitonState->m_ExpressionCodeMap[expressionKey] = result;

        //Assert(result >= 0);
        return result;
    }
}

int32_t MaterialCompiler::CallRootExpression(MaterialExpression* expression)
{
    return expression->Compile(*this, nullptr);
}

void MaterialCompiler::CallMaterialFunctionOutputs(const std::vector<MaterialExpression*>& functionOutputs)
{
    AssignShaderFrequencyScope(SF_Surface);

    for (auto* functionOutput : functionOutputs)
    {
        CallExpression(functionOutput);
    }
}

void MaterialCompiler::PushFunction(MaterialFunctionCompileState* functionState)
{
    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];
    currentFunctionStack.push_back(functionState);
}

MaterialFunctionCompileState* MaterialCompiler::PopFunction()
{
    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];
    auto* functionState = currentFunctionStack.back();
    currentFunctionStack.pop_back();
    return functionState;
}

void MaterialCompiler::AddCustomInterpolator(MaterialCustomInterpolatorType type, std::string_view name, int32_t code)
{
    m_CustomInterpolatorSet.AddInterpolator(type, name, code);
}

int32_t MaterialCompiler::FindCustomInterpolator(std::string_view name)
{
    if (!IsShaderFrequency(SF_Surface))
    {
        return Errorf("Current shader state don't support CustomInterpolator");
    }

    MaterialCustomInterpolatorInfo* interpolatorInfo = interpolatorInfo = m_CustomInterpolatorSet.FindInterpolator(name);

    if (!interpolatorInfo)
    {
        return Errorf("CustomInterpolator:{} doesn't exist", name);
    }

    return AddInlinedCodeChunk(GetMaterialValueType(interpolatorInfo->Type), "input.{}", interpolatorInfo->InnerName);
}

size_t MaterialCompiler::GetCustomInterpolatorsSize()
{
    return m_CustomInterpolatorSet.GetCustomInterpolators().size();
}

void MaterialCompiler::GatherCustomVertexInterpolators(MaterialExpression* surfaceExpression)
{
    std::function<void(MaterialExpression*, std::unordered_set<MaterialExpression*>&, std::vector<MaterialExpression*>&)> GetAllExpressionsImpl;
    std::function<void(std::vector<MaterialExpression*>)> PreCompileVSInterpolators;

    GetAllExpressionsImpl = [&](MaterialExpression* expr, std::unordered_set<MaterialExpression*>& visited, std::vector<MaterialExpression*>& result)
    {
        if (!expr || visited.count(expr))
        {
            return;
        }
        visited.insert(expr);
        result.push_back(expr);
        for (auto* input : expr->m_Inputs)
        {
            if (input && input->m_LinkedExpressionOutput)
            {
                GetAllExpressionsImpl(input->m_LinkedExpressionOutput->m_ParentExpression, visited, result);
            }
        }
        if (auto expressionRerouteUsage = dynamic_cast<MaterialExpressionNamedRerouteUsage*>(expr))
        {
            GetAllExpressionsImpl(expressionRerouteUsage->m_Declaration, visited, result);
        }
    };

    auto GetAllExpressions = [&](MaterialExpression* rootExpression) 
    {
        std::unordered_set<MaterialExpression*> visited;
        std::vector<MaterialExpression*> result;
        GetAllExpressionsImpl(rootExpression, visited, result);
        return result;
    };

    auto GetFunctionCallExpressions = [&](MaterialExpressionFunctionCall* functionCall)
    {
        std::vector<MaterialExpression*> expressions;
        for (auto& expression : functionCall->m_MaterialFunctionEditorData.Expressions)
        {
            expressions.push_back(expression.get());
        }
        return expressions;
    };

    PreCompileVSInterpolators = [&](std::vector<MaterialExpression*> expressions) {
        for (MaterialExpression* expression : expressions)
        {
            if (auto* customInterpolator = dynamic_cast<MaterialExpressionCustomInterpolator*>(expression))
            {
                if (!customInterpolator->m_Input.m_LinkedExpressionOutput)
                {
                    continue;
                }
                static std::map<MaterialValueType, MaterialCustomInterpolatorType> interpolatorTypeMap = {
                    // {MaterialValueType::MCT_Float, MaterialCustomInterpolatorType::Float4},
                    {MaterialValueType::MCT_Float1, MaterialCustomInterpolatorType::Float1},
                    {MaterialValueType::MCT_Float2, MaterialCustomInterpolatorType::Float2},
                    {MaterialValueType::MCT_Float3, MaterialCustomInterpolatorType::Float3},
                    {MaterialValueType::MCT_Float4, MaterialCustomInterpolatorType::Float4},
                    {MaterialValueType::MCT_UInt1, MaterialCustomInterpolatorType::UInt1},
                    {MaterialValueType::MCT_UInt2, MaterialCustomInterpolatorType::UInt2},
                    {MaterialValueType::MCT_UInt3, MaterialCustomInterpolatorType::UInt3},
                    {MaterialValueType::MCT_UInt4, MaterialCustomInterpolatorType::UInt4},
                };
                int32_t Ret = customInterpolator->m_Input.Compile(*this);
                if (Ret != CODE_CHUNK_INDEX_NONE)
                {
                    customInterpolator->m_Name = std::format("CustomVertexInterpolatorExternal{}", static_cast<int32_t>(GetCustomInterpolatorsSize()));
                AddCustomInterpolator(interpolatorTypeMap.find(GetParameterType(Ret))->second, customInterpolator->m_Name, Ret);
            }
            }
            else if (auto* functionCall = dynamic_cast<MaterialExpressionFunctionCall*>(expression))
            {
                auto& currentFunctionStack = m_FunctionStacks[SF_GenerateCustomInterpolators].back();
                if (!functionCall->m_MaterialFunction.empty())
                {
                    MaterialExpressionKey expressionKey{expression, nullptr, EMaterialProperty::None};
                    MaterialFunctionCompileState* sharedFunctionState = currentFunctionStack->FindOrAddSharedFunctionState(expressionKey, functionCall);
                    functionCall->SetSharedCompileState(sharedFunctionState);
                    PushFunction(sharedFunctionState);
                    PreCompileVSInterpolators(GetFunctionCallExpressions(functionCall));
                    PopFunction();
                    functionCall->SetSharedCompileState(nullptr);
                }
            }
        }
    };

    BeginCompileShaderFrequency(surfaceExpression, SF_GenerateCustomInterpolators);
    PreCompileVSInterpolators(GetAllExpressions(surfaceExpression));
    EndCompileShaderFrequency();
}

int32_t MaterialCompiler::ScalarParameter(std::string_view parameterName, float value)
{
    m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Scalar, parameterName, value);

    return AddCodeChunk(MaterialValueType::MCT_Float, "ce_PerMaterial[primitiveData.ce_MaterialIndex].{}", parameterName);
}

int32_t MaterialCompiler::VectorParameter(std::string_view parameterName, Float4 value)
{
    m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Vector, parameterName, value);

    return AddCodeChunk(MaterialValueType::MCT_Float4, "ce_PerMaterial[primitiveData.ce_MaterialIndex].{}", parameterName);
}

int32_t MaterialCompiler::TextureParameter(std::string_view parameterName, MaterialValueType type, std::string_view textureGuid)
{
    m_AllUniformExpressionSet.FindOrAddParameter(ToParameterType(type), parameterName, std::string(textureGuid));

    return AddInlinedCodeChunk(type, "{}", parameterName);
}

int32_t MaterialCompiler::ShaderConstBool(std::string_view shaderConstName, std::string_view shaderConstDisplayName, bool value)
{
    m_ShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Bool, shaderConstName, shaderConstDisplayName, value);
    m_AllShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Bool, shaderConstName, shaderConstDisplayName, value);

    return AddInlinedCodeChunk(MaterialValueType::MCT_Bool, "{}", shaderConstName);
}

int32_t MaterialCompiler::ShaderConstFloat(std::string_view shaderConstName, std::string_view shaderConstDisplayName, float value)
{
    m_ShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Float, shaderConstName, shaderConstDisplayName, value);
    m_AllShaderConstSet.FindOrAddShaderConst(MaterialShaderConstType::Float, shaderConstName, shaderConstDisplayName, value);

    return AddInlinedCodeChunk(MaterialValueType::MCT_Float1, "{}", shaderConstName);
}

int32_t MaterialCompiler::GPUSceneData(GPUSceneDataLevel level, MaterialValueType type, std::string_view GPUSceneDataName)
{
    switch (level)
    {
    case GPUSceneDataLevel::Object:
        m_GPUSceneObjectDataSet.emplace(GPUSceneDataName, type);
        return AddCodeChunk(type, "objectData.{}", GPUSceneDataName);
    case GPUSceneDataLevel::Primitive:
        m_GPUScenePrimitiveDataSet.emplace(GPUSceneDataName, type);
        return AddCodeChunk(type, "primitiveData.{}", GPUSceneDataName);
    default:
        Assert(false);
        return CODE_CHUNK_INDEX_NONE;
    }
}

int32_t MaterialCompiler::ObjectLocalBounds(BoundsOutputIndex outputIndex)
{
    uint32_t boundsCenter = AddCodeChunk(MaterialValueType::MCT_Float3, "primitiveData.ce_LocalBoundsCenter");
    uint32_t halfBoundsExtent = AddCodeChunk(MaterialValueType::MCT_Float3, "primitiveData.ce_LocalBoundsExtent");
    switch (outputIndex)
    {
    case BoundsOutputIndex::BoundsHalfExtent:
        return halfBoundsExtent;
    case BoundsOutputIndex::BoundsExtent:
        return Mul(halfBoundsExtent, Constant(2.f));
    case BoundsOutputIndex::BoundsMin:
        return Sub(boundsCenter, halfBoundsExtent);
    case BoundsOutputIndex::BoundsMax:
        return Add(boundsCenter, halfBoundsExtent);
    default:
        Assert(false);
        return CODE_CHUNK_INDEX_NONE;
    }
}

void MaterialCompiler::PushMaterialAttribute(EMaterialProperty attribute)
{
    m_MaterialAttributesStack.push_back(attribute);
}

void MaterialCompiler::PopMaterialAttribute()
{
    m_MaterialAttributesStack.pop_back();
}

EMaterialProperty MaterialCompiler::GetMaterialAttribute()
{
    if (m_MaterialAttributesStack.empty())
    {
        return EMaterialProperty::None;
    }
    return m_MaterialAttributesStack.back();
}

int32_t MaterialCompiler::ParticleColor()
{
    return AddCodeChunk(MaterialValueType::MCT_Float4, "GetParticleColor(objectData)");
}

int32_t cross::MaterialCompiler::ParticleUVScale()
{
    return AddCodeChunk(MaterialValueType::MCT_Float4, "GetParticleUVScale(objectData)");
}

int32_t cross::MaterialCompiler::ParticleAnimatedVelocity()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetParticleAnimatedVelocity(objectData)");
}

int32_t cross::MaterialCompiler::ParticlePosition()
{
    return AddCodeChunk(MaterialValueType::MCT_Double3, "DFFromTileAndPosition(GetTilePosition(objectData, primitiveData), GetParticlePosition(objectData))");
}

int32_t cross::MaterialCompiler::ParticleRotation()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetParticleRotation(objectData)");
}

int32_t cross::MaterialCompiler::ParticleSizeScale()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetParticleSizeScale(objectData)");
}

int32_t MaterialCompiler::Constant(float x)
{
    return AddCodeChunk(MaterialValueType::MCT_Float1, "{}", x);
}

int32_t MaterialCompiler::Constant2(float x, float y)
{
    return AddCodeChunk(MaterialValueType::MCT_Float2, "float2({}, {})", x, y);
}

int32_t MaterialCompiler::Constant3(float x, float y, float z)
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "float3({}, {}, {})", x, y, z);
}

int32_t MaterialCompiler::Constant4(float x, float y, float z, float w)
{
    return AddCodeChunk(MaterialValueType::MCT_Float4, "float4({}, {}, {}, {})", x, y, z, w);
}

int32_t MaterialCompiler::ConstantDouble(double value)
{
    FDFScalar DFValue(value);
    return AddCodeChunk(MaterialValueType::MCT_Double4, "MakeDFVector4({}, {})", DFValue.High, DFValue.Low);
}

int32_t MaterialCompiler::Sine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFSin({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "sin({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Cosine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFCos({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "cos({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Tangent(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFTan({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "tan({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Arcsine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFASin({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "asin({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Arccosine(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFACos({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "acos({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Arctangent(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFATan({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "atan({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Arctangent2(int32_t y, int32_t x)
{
    if (y == CODE_CHUNK_INDEX_NONE || x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto YType = GetParameterType(y), XType = GetParameterType(x); IsMaterialValueType(YType, MaterialValueType::MCT_Float) && XType == YType)
    {
        return AddCodeChunk(GetParameterType(y), "atan2({}, {})", GetParameterCode(y).data(), GetParameterCode(x).data());
    }
    else
    {
        return Errorf("Type not supported for atan2: {} and {}", GetParameterType(y), GetParameterType(x));
    }
}

int32_t MaterialCompiler::Floor(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFFloor({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "floor({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Ceil(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFCeil({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "ceil({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Round(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFRound({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "round({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Truncate(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFTrunc({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "trunc({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Sign(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }
    
    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFSign({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "sign({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Frac(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFFracDemote({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(ResultType, "frac({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Fmod(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetArithmeticResultType(a, b); IsDoubleType(ResultType))
    {
        return AddCodeChunk(ResultType, "DFFmod({}, {})", GetParameterCode(a), GetParameterCode(b));        
    }
    else
    {
        return AddCodeChunk(ResultType, "fmod({}, {})", GetParameterCode(a), GetParameterCode(b));
    }
}

int32_t MaterialCompiler::ViewProperty(ExposedViewProperty Property, bool InvProperty)
{
    struct MaterialExposedViewPropertyMeta
    {
        ExposedViewProperty EnumValue;
        MaterialValueType Type;
        std::string_view PropertyCode;
        std::string_view InvPropertyCode;
    };

    static const MaterialExposedViewPropertyMeta ViewPropertyMetaArray[] = {
        {ExposedViewProperty::WorldSpaceCameraPosition, MaterialValueType::MCT_Double3, MCDFBuiltins::ce_CameraPos },
        {ExposedViewProperty::TileCameraPosition, MaterialValueType::MCT_Float3, "ce_CameraTilePosition" },
        {ExposedViewProperty::CameraVectorWS, MaterialValueType::MCT_Float3, "normalize(ce_CameraPos.xyz - input.positionWS)" },
        {ExposedViewProperty::TanHalfVieldOfView, MaterialValueType::MCT_Float2, "float2(ce_InvProjection[0][0], ce_InvProjection[1][1])" },
        {ExposedViewProperty::ViewSize, MaterialValueType::MCT_Float2, "ce_ScreenParams.xy", "ce_ScreenParams.zw"},
    };
    auto& PropertyMeta = ViewPropertyMetaArray[ToUnderlying(Property)];
    return AddCodeChunk(PropertyMeta.Type, "{}", PropertyMeta.PropertyCode);
}

int32_t MaterialCompiler::Exponential(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(ResultType, "DFExp({})", GetParameterCode(x));        
    }
    else
    {
        return AddCodeChunk(ResultType, "exp({})", GetParameterCode(x));
    }
}

int32_t MaterialCompiler::Exponential2(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(ResultType, "DFExp2({})", GetParameterCode(x));        
    }
    else
    {
        return AddCodeChunk(ResultType, "exp2({})", GetParameterCode(x));
    }
}

int32_t MaterialCompiler::DynamicBranch(int32_t condition, int32_t a, int32_t b)
{
    if (condition == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (b == CODE_CHUNK_INDEX_NONE)
    {
        return a;
    }

    if (a == CODE_CHUNK_INDEX_NONE)
    {
        return b;
    }

    if (GetParameterType(condition) != MaterialValueType::MCT_StaticBool)
    {
        return Errorf("Value is not type Bool");
    }

    MaterialValueType typeA = GetParameterType(a);
    MaterialValueType typeB = GetParameterType(b);
    MaterialValueType resultType = MaterialValueType::MCT_Unknown;
    if (typeA == typeB)
    {
        resultType = typeA;
    }
    else if (!IsFloatNumericType(typeA) || !IsFloatNumericType(typeB))
    {
        return Errorf("Cannot branch on non float numeric Types if they are not equal: {} {}", HLSLTypeString(typeA), HLSLTypeString(typeB));
    }
    else
    {
        resultType = GetNumComponents(typeA) > GetNumComponents(typeB) ? typeA : typeB;
    }

    a = ForceCast(a, resultType, MaterialCastFlags::ReplicateScalar);
    b = ForceCast(b, resultType, MaterialCastFlags::ReplicateScalar);
    std::string symbolName = CreateSymbolName("static");

    AddCodeChunk(MaterialValueType::MCT_VoidStatement, "{} {};", HLSLTypeString(resultType), symbolName);
    AddCodeChunk(MaterialValueType::MCT_VoidStatement, "switch (int({})){{ default: {} = {}; break; case 0: {} = {}; break;}}", GetParameterCode(condition).data(), symbolName.data(), GetParameterCode(a).data(), symbolName.data(), GetParameterCode(b).data());
    return AddCodeChunk(resultType, "{}", symbolName);
}

int32_t MaterialCompiler::If(int32_t a, int32_t b, int32_t aGreaterThanB, int32_t aEqualsB, int32_t aLessThanB)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE || aGreaterThanB == CODE_CHUNK_INDEX_NONE || aLessThanB == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (!IsFloatNumericType(GetParameterType(a)))
    {
        return Errorf("If input A must be of type float");
    }

    if (!IsFloatNumericType(GetParameterType(b)))
    {
        return Errorf("If input B must be of type float");
    }

    if (aEqualsB != CODE_CHUNK_INDEX_NONE)
    {
        MaterialValueType resultType = GetArithmeticResultType(GetParameterType(aGreaterThanB), GetArithmeticResultType(GetParameterType(aEqualsB), GetParameterType(aLessThanB)));

        int32_t coercedAGreaterThanB = ForceCast(aGreaterThanB, resultType);
        int32_t coercedAEqualsB = ForceCast(aEqualsB, resultType);
        int32_t coercedALessThanB = ForceCast(aLessThanB, resultType);

        if (coercedAGreaterThanB == CODE_CHUNK_INDEX_NONE || coercedAEqualsB == CODE_CHUNK_INDEX_NONE || coercedALessThanB == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }

        return AddCodeChunk(resultType,
                            "select(abs({} - {}) > 0.00001f, select({} >= {}, {}, {}), {})",
                            GetParameterCode(a).data(),
                            GetParameterCode(b).data(),
                            GetParameterCode(a).data(),
                            GetParameterCode(b).data(),
                            GetParameterCode(coercedAGreaterThanB).data(),
                            GetParameterCode(coercedALessThanB).data(),
                            GetParameterCode(coercedAEqualsB).data());
    }
    else
    {
        MaterialValueType resultType = GetArithmeticResultType(GetParameterType(aGreaterThanB), GetParameterType(aLessThanB));

        int32_t coercedAGreaterThanB = ForceCast(aGreaterThanB, resultType);
        int32_t coercedALessThanB = ForceCast(aLessThanB, resultType);

        if (coercedAGreaterThanB == CODE_CHUNK_INDEX_NONE || coercedALessThanB == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }

        return AddCodeChunk(resultType, "select({} >= {}, {}, {})", GetParameterCode(a).data(), GetParameterCode(b).data(), GetParameterCode(coercedAGreaterThanB).data(), GetParameterCode(coercedALessThanB).data());
    }
}

int32_t MaterialCompiler::StaticBool(bool Value)
{
    // TODO: optimize fmt to std::format
    if (Value)
    {
        return AddInlinedCodeChunk(MaterialValueType::MCT_StaticBool, "true");
    }
    else
    {
        return AddInlinedCodeChunk(MaterialValueType::MCT_StaticBool, "false");
    }
    //return AddInlinedCodeChunk(MCT_StaticBool, Value ? "true" : "false");
}

int32_t MaterialCompiler::PixelDepth()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support PixelDepth, use SceneTextureLookup");
    }
    return AddCodeChunk(MaterialValueType::MCT_Float1, "input.positionNDC.z");
}

int32_t MaterialCompiler::SceneDepth()
{
    return SceneTextureLookup(CODE_CHUNK_INDEX_NONE, SceneTextureId::SceneDepth, false, false);
}
int32_t MaterialCompiler::SceneLinearDepth()
{
    return SceneTextureLookup(CODE_CHUNK_INDEX_NONE, SceneTextureId::SceneDepth, false, true);
}

int32_t MaterialCompiler::PixelLinearDepth()
{
    int32_t pixelDepth = PixelDepth();
    return AddCodeChunk(MaterialValueType::MCT_Float1, ("GetLinearDepth({})"), GetParameterCode(pixelDepth));
}

int32_t MaterialCompiler::SkyAtmosphereLightDirection(uint32_t lightIndex)
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "ce_AtmosphereLightData[{}].LightDirection", lightIndex);
}

int32_t MaterialCompiler::SkyAtmosphereLightDiskLuminance(uint32_t lightIndex, int32_t diskAngularDiameterOverride)
{
    return AddCodeChunk(MaterialValueType::MCT_Float3,
                        ("GetLightDiskLuminance(normalize(input.positionWS - ce_CameraPos.xyz), {}, {})"),
                        lightIndex,
                        diskAngularDiameterOverride == INDEX_NONE ? GetParameterCode(Constant(-1.0f)) : GetParameterCode(diskAngularDiameterOverride)
        );
}

int32_t MaterialCompiler::SkyAtmosphereLightIlluminanceOnGround(uint32_t lightIndex)
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "ce_LightIlluminanceOnGroundPostTransmittance[{}]", lightIndex);
}

int32_t MaterialCompiler::SkyAtmosphereViewLuminance()
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetSkyAtmosphereViewLuminance(normalize(input.positionWS - ce_CameraPos.xyz))");
}

int32_t MaterialCompiler::VertexColor()
{
    m_MacroSet.AddMacro(Macro_VertexNeedVertexColor);
    return AddCodeChunk(MaterialValueType::MCT_Float4, "input.vertexColor");
}

int32_t MaterialCompiler::Comment(Float4 color)
{
    return AddCodeChunk(MaterialValueType::MCT_Float3, "float4({}, {}, {})", color.x, color.y, color.z, color.w);
}

int32_t MaterialCompiler::Abs(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto Type = GetParameterType(x); IsDoubleType(Type))
    {
        return AddCodeChunk(Type, "DFAbs({})", GetParameterCode(x).data());        
    }
    else
    {
        return AddCodeChunk(GetParameterType(x), "abs({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Add(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(resultType, "DFAdd({}, {})", GetParameterCode(a).data(), GetParameterCode(b).data());
    }
    else
    {
    return AddCodeChunk(resultType, "({} + {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}
}

int32_t MaterialCompiler::Sobol(int32_t Cell, int32_t Index, int32_t Seed)
{
    if (Cell == CODE_CHUNK_INDEX_NONE || Index == CODE_CHUNK_INDEX_NONE || Seed == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(MaterialValueType::MCT_Float2, "(floor({}) + float2(SobolIndex(SobolPixel(uint2({})), uint({})) ^ uint2({} * 0x10000) & 0xffff) / 0x10000)", 
                        GetParameterCode(Cell).data(), 
                        GetParameterCode(Cell).data(),
                        GetParameterCode(Index).data(),
                        GetParameterCode(Seed).data());
}

int32_t MaterialCompiler::Sub(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(resultType, "DFSubtract({}, {})", GetParameterCode(a).data(), GetParameterCode(b).data());
    }
    else
    {
    return AddCodeChunk(resultType, "({} - {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}
}

int32_t MaterialCompiler::Mul(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType ResultType = GetArithmeticResultType(a, b);

    if (ResultType == MaterialValueType::MCT_Unknown)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (IsDoubleType(ResultType))
    {
        return AddCodeChunk(ResultType, "DFMultiply({}, {})", GetParameterCode(a).data(), GetParameterCode(b).data());
    }
    else
    {
    return AddCodeChunk(ResultType, "({} * {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}
}

int32_t MaterialCompiler::Div(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(resultType, "DFDivide({}, {})", GetParameterCode(a).data(), GetParameterCode(b).data());
    }
    else
    {
    return AddCodeChunk(resultType, "({} / {})", GetParameterCode(a).data(), GetParameterCode(b).data());
}
}

int32_t MaterialCompiler::Dot(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);

    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(MaterialValueType::MCT_Double1, "DFDot({}, {})", GetParameterCode(a), GetParameterType(b));        
    }
    else
    {
        return AddCodeChunk(MaterialValueType::MCT_Float1, "dot({}, {})", GetParameterCode(a), GetParameterCode(b));
    }
}

int32_t MaterialCompiler::Cross(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(a, b);
    
    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(resultType, "DFCross({}, {})", GetParameterCode(a), GetParameterCode(b));        
    }
    else
    {
        return AddCodeChunk(resultType, "cross({}, {})", GetParameterCode(a), GetParameterCode(b));
    }
}

int32_t MaterialCompiler::Power(int32_t base, int32_t exponent)
{
    if (base == CODE_CHUNK_INDEX_NONE || exponent == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(DoubleTypeToFloatType(GetParameterType(base)), "pow(DFDemote({}), DFDemote({}))", GetParameterCode(base).data(), GetParameterCode(exponent).data());
}

int32_t MaterialCompiler::Logarithm2(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(DoubleTypeToFloatType(GetParameterType(x)), "log2(DFDemote({}))", GetParameterCode(x).data());
}

int32_t MaterialCompiler::Logarithm10(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(DoubleTypeToFloatType(GetParameterType(x)), "log10(DFDemote({}))", GetParameterCode(x).data());
}

int32_t MaterialCompiler::SquareRoot(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (auto ResultType = GetParameterType(x); IsDoubleType(ResultType))
    {
        return AddCodeChunk(DoubleTypeToFloatType(ResultType), "DFSqrtDemote({})", GetParameterCode(x).data());
}
    else
    {
        return AddCodeChunk(ResultType, "sqrt({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Length(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetParameterType(x);
    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(MaterialValueType::MCT_Double1, "DFLength({})", GetParameterCode(x).data());
}
    else
    {
    return AddCodeChunk(MaterialValueType::MCT_Float1, "length({})", GetParameterCode(x).data());
}
}

int32_t MaterialCompiler::Normalize(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetParameterType(x);

    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(resultType, "DFNormalize({})", GetParameterCode(x).data());
}
    else
    {
        return AddCodeChunk(resultType, "normalize({})", GetParameterCode(x).data());
    }
}

int32_t MaterialCompiler::Step(int32_t y, int32_t x)
{
    if (y == CODE_CHUNK_INDEX_NONE || x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialValueType resultType = GetArithmeticResultType(y, x);

    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(resultType, "DFStep({}, {})", GetParameterCode(y).data(), GetParameterCode(x).data());
    }
    else
    {
    return AddCodeChunk(resultType, "step({}, {})", GetParameterCode(y).data(), GetParameterCode(x).data());
}
}

int32_t MaterialCompiler::SmoothStep(int32_t x, int32_t y, int32_t a)
{
    if (x == CODE_CHUNK_INDEX_NONE || y == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    MaterialValueType resultType = GetArithmeticResultType(y, x);
    MaterialValueType alphaType = resultType == (*m_CurrentScopeChunks)[x].m_Type ? resultType : MaterialValueType::MCT_Float1;

    if (IsDoubleType(resultType))
    {
        return AddCodeChunk(resultType, "DFSmoothStep({}, {}, {})", GetParameterCode(y).data(), GetParameterCode(x).data(), CoerceParameter(a, alphaType).data());
    }
    else
    {
    return AddCodeChunk(resultType, "smoothstep({}, {}, {})", GetParameterCode(y).data(), GetParameterCode(x).data(), CoerceParameter(a, alphaType).data());
}
}

int32_t MaterialCompiler::InvLerp(int32_t x, int32_t y, int32_t a)
{
    if (x == CODE_CHUNK_INDEX_NONE || y == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    int32_t numerator = Sub(a, x);
    int32_t denominator = Sub(y, x);

    return Div(numerator, denominator);
}

int32_t MaterialCompiler::Lerp(int32_t x, int32_t y, int32_t a)
{
    if (x == CODE_CHUNK_INDEX_NONE || y == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    auto resultType = GetArithmeticResultType(x, y);
    auto nonDoubleResultType = DoubleTypeToFloatType(resultType);
    auto alphaType = nonDoubleResultType == DoubleTypeToFloatType(GetParameterType(a)) ? nonDoubleResultType : MaterialValueType::MCT_Float1;

    if (CoerceParameter(x, resultType) == "")
        Assert(false);
    
    return AddCodeChunk(resultType,
                        "{}({}, {}, {})",
                        IsDoubleType(resultType) ? "DFLerp" : "lerp", 
                        CoerceParameter(x, resultType),
                        CoerceParameter(y, resultType),
                        CoerceParameter(a, alphaType));
}

int32_t MaterialCompiler::Min(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "{}({}, {})", IsDoubleType(GetParameterType(a)) ? "DFMin" : "min", GetParameterCode(a).data(), CoerceParameter(b, GetParameterType(a)).data());
}

int32_t MaterialCompiler::Max(int32_t a, int32_t b)
{
    if (a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "{}({}, {})", IsDoubleType(GetParameterType(a)) ? "DFMax" : "max", GetParameterCode(a).data(), CoerceParameter(b, GetParameterType(a)).data());
}

int32_t MaterialCompiler::Clamp(int32_t x, int32_t a, int32_t b)
{
    if (x == CODE_CHUNK_INDEX_NONE || a == CODE_CHUNK_INDEX_NONE || b == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (IsDoubleType(GetParameterType(x)))
    {
        return AddCodeChunk(GetParameterType(x), "DFMin(DFMax({}, {}), {})", GetParameterCode(x).data(), CoerceParameter(a, GetParameterType(x)).data(), CoerceParameter(b, GetParameterType(x)).data());
    }
    
    return AddCodeChunk(GetParameterType(x), "min(max({}, {}), {})", GetParameterCode(x).data(), CoerceParameter(a, GetParameterType(x)).data(), CoerceParameter(b, GetParameterType(x)).data());
}

int32_t MaterialCompiler::Saturate(int32_t x)
{
    if (x == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    if (IsDoubleType(GetParameterType(x)))
    {
        return AddCodeChunk(DoubleTypeToFloatType(GetParameterType(x)), "DFSaturateDemote({})", GetParameterCode(x).data());
    }
    
    return AddCodeChunk(GetParameterType(x), "saturate({})", GetParameterCode(x).data());
}

int32_t MaterialCompiler::AppendVector(int32_t A, int32_t B)
{
	if(A == INDEX_NONE || B == INDEX_NONE)
	{
		return INDEX_NONE;
	}

	const MaterialValueType TypeA = GetParameterType(A);
	const MaterialValueType TypeB = GetParameterType(B);
	const bool bIsDoubleFloat = IsDoubleType(TypeA) || IsDoubleType(TypeB);
	const int32_t NumComponentsA = GetNumComponents(TypeA);
	const int32_t NumComponentsB = GetNumComponents(TypeB);
	const int32_t NumResultComponents = NumComponentsA + NumComponentsB;
	if (NumResultComponents > 4)
	{
		return Errorf(("Can't append {} to {}"), TypeA, TypeB);
	}

	const MaterialValueType ResultType = bIsDoubleFloat ? GetDoubleFloatType(NumResultComponents) : GetVectorType(NumResultComponents);

    // TODO(peterwjma): used to reduce code, ignored here due to lacking of many infrastructures
	// if(GetParameterUniformExpression(A) && GetParameterUniformExpression(B))
	// {
	// 	FAddUniformExpressionScope Scope(this);
	// 	return AddUniformExpression(Scope, new FMaterialUniformExpressionAppendVector(GetParameterUniformExpression(A),GetParameterUniformExpression(B),GetNumComponents(GetParameterType(A))),ResultType,("MaterialFloat%u(%s,%s)"),NumResultComponents,*GetParameterCode(A),*GetParameterCode(B));
	// }
	// else
	{
		std::string FiniteCode;
		if (bIsDoubleFloat)
		{
		    // just ststistics
			// AddLWCFuncUsage(ELWCFunctionKind::Promote, 2);
			// AddLWCFuncUsage(ELWCFunctionKind::Constructor);
			FiniteCode = fmt::format("MakeDFVector(DFPromote({}),DFPromote({}))", GetParameterCode(A), GetParameterCode(B));
		}
		else
		{
			FiniteCode = fmt::format("float{}({},{})", NumResultComponents, GetParameterCode(A), GetParameterCode(B));
		}

	    // nanite derivitive related
		// const EDerivativeStatus ADerivativeStatus = GetDerivativeStatus(A);
		// const EDerivativeStatus BDerivativeStatus = GetDerivativeStatus(B);
		// if (IsAnalyticDerivEnabled() && IsDerivativeValid(ADerivativeStatus) && IsDerivativeValid(BDerivativeStatus))
		// {
		// 	if (ADerivativeStatus == EDerivativeStatus::Zero && BDerivativeStatus == EDerivativeStatus::Zero)
		// 	{
		// 		return AddInlinedCodeChunkZeroDeriv(ResultType, *FiniteCode);
		// 	}
		// 	else
		// 	{
		// 		FString A_DDX = GetFloatZeroVector(NumComponentsA);
		// 		FString A_DDY = GetFloatZeroVector(NumComponentsA);
		// 		FString B_DDX = GetFloatZeroVector(NumComponentsB);
		// 		FString B_DDY = GetFloatZeroVector(NumComponentsB);
		//
		// 		if (ADerivativeStatus == EDerivativeStatus::Valid)
		// 		{
		// 			FString Deriv = *GetParameterCodeDeriv(A, CompiledPDV_Analytic);
		// 			A_DDX = Deriv + ".Ddx";
		// 			A_DDY = Deriv + ".Ddy";
		// 		}
		//
		// 		if (BDerivativeStatus == EDerivativeStatus::Valid)
		// 		{
		// 			FString Deriv = *GetParameterCodeDeriv(B, CompiledPDV_Analytic);
		// 			B_DDX = Deriv + ".Ddx";
		// 			B_DDY = Deriv + ".Ddy";
		// 		}
		//
		// 		FString DDXCode = FString::Printf(("MaterialFloat%u(%s, %s)"), NumResultComponents, *A_DDX, *B_DDX);
		// 		FString DDYCode = FString::Printf(("MaterialFloat%u(%s, %s)"), NumResultComponents, *A_DDY, *B_DDY);
		// 		FString AnalyticCode = DerivativeAutogen.ConstructDeriv(FiniteCode, *DDXCode, *DDYCode, GetDerivType(ResultType));
		// 		return AddCodeChunkInnerDeriv(*FiniteCode, *AnalyticCode, ResultType, false, EDerivativeStatus::Valid);
		// 	}
		// }
		// else
		{
			return AddInlinedCodeChunk(ResultType, "{}", FiniteCode);
		}
	}
}

int32_t MaterialCompiler::DDX(int32_t a)
{
    if (a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "ddx({})", GetParameterCode(a).data());
}

int32_t MaterialCompiler::DDY(int32_t a)
{
    if (a == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    return AddCodeChunk(GetParameterType(a), "ddy({})", GetParameterCode(a).data());
}

int32_t MaterialCompiler::TextureCoordinate(uint32_t coordinateIndex)
{
    const uint32_t maxNumCoordinates = 3;

    if (coordinateIndex >= maxNumCoordinates)
    {
        return Errorf("Only {} texture coordinate sets can be used by this feature level, currently using {}", maxNumCoordinates, coordinateIndex + 1);
    }

    if (m_IsCompilePostProcess)
    {
        if (coordinateIndex!=0)
        return Errorf("Post process only support TextureCoordinate-0");
    }

    auto macros = m_MacroSet.GetMacros();
    std::string texCoordCode;
    int texNum = coordinateIndex + 1;
    switch (coordinateIndex)
    {
    case 0:
        texCoordCode = "input.uvs[0].xy";
        break;
    case 1:
        texCoordCode = "input.uvs[0].zw";
        break;
    case 2:
        texCoordCode = "input.uvs[1].xy";
        break;
    }

    if (auto pair = macros.find(Macro_NumMaterialTexcoords); pair != macros.end())
    {
        texNum = std::max(texNum, std::stoi(pair->second));
    }
    m_MacroSet.AddMacro(Macro_NumMaterialTexcoords, std::to_string(texNum));

    return AddCodeChunk(MaterialValueType::MCT_Float2, "{}", texCoordCode);
}

int32_t MaterialCompiler::ScreenUV()
{
    if (!IsShaderFrequency(SF_Surface))
    {
        return Errorf("Current shader state don't support ScreenUV");
    }

    return AddCodeChunk(MaterialValueType::MCT_Float2, "input.screenUV");
}

int32_t MaterialCompiler::PixelPosition()
{
    return AddCodeChunk(MaterialValueType::MCT_Float2, "(input.positionNDC.xy + 1.0) * 0.5 * ce_ScreenParams.xy");
}

int32_t MaterialCompiler::WorldPosition()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support WorldPosition, use SceneTextureLookup to get depth");
    }

    int32_t worldPos = CODE_CHUNK_INDEX_NONE;
    
    if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
    {
        worldPos = AddInlinedCodeChunk(MaterialValueType::MCT_Float3, "positionWS");
    }
    else
    {
        worldPos = AddInlinedCodeChunk(MaterialValueType::MCT_Float3, "input.positionWS");
    }

    return AddInlinedCodeChunk(MaterialValueType::MCT_Double3, "DFFromTileAndPosition({}, {})", GetParameterCode(TilePosition()), GetParameterCode(worldPos));
}

int32_t MaterialCompiler::ObjectPositionWS()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support ObjectPosition, use SceneTextureLookup to get depth");
    }

    int32_t A = AddCodeChunk(MaterialValueType::MCT_Float3, "primitiveData.ce_LocalBoundsCenter");
    int32_t res = Transform(MaterialCommonBasis::Instance, MaterialCommonBasis::World, A, cross::TransformElementType::Position);
    return ComponentMask(res, true, true, true, false);
}

int32_t MaterialCompiler::TilePosition()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support TilePosition");
    }

    return AddCodeChunk(MaterialValueType::MCT_Float3, "GetTilePosition(objectData, primitiveData)");
}

int32_t MaterialCompiler::WorldGeometryNormal()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support WorldGeometryNormal");
    }

    m_MacroSet.AddMacro(Macro_VertexNeedNormal);

    return AddCodeChunk(MaterialValueType::MCT_Float3, "input.normalWS");
}

int32_t MaterialCompiler::WorldTangent()
{
    if (m_IsCompilePostProcess)
    {
        return Errorf("Post process doesn't support WorldTangent");
    }

    m_MacroSet.AddMacro(Macro_VertexNeedTangent);
    return AddCodeChunk(MaterialValueType::MCT_Float3, "input.tangentWS");
}

int32_t MaterialCompiler::Time()
{
    return AddCodeChunk(MaterialValueType::MCT_Float1, "ce_Time");
}

int32_t MaterialCompiler::EyeAdaption()
{
    return AddCodeChunk(MaterialValueType::MCT_Float1, "ce_PreExposure");
}

int32_t MaterialCompiler::RotateAboutAxis(int32_t NormalizedRotationAxisAndAngleIndex, int32_t PositionOnAxisIndex, int32_t PositionIndex)
{
    if (NormalizedRotationAxisAndAngleIndex == INDEX_NONE || PositionOnAxisIndex == INDEX_NONE || PositionIndex == INDEX_NONE)
    {
        return INDEX_NONE;
    }
    else
    {
        const MaterialValueType PositionOnAxisType = GetParameterType(PositionOnAxisIndex);
        const MaterialValueType PositionType = GetParameterType(PositionIndex);
        const MaterialValueType InputType = IsDoubleType(PositionOnAxisType) || IsDoubleType(PositionType) ? MaterialValueType::MCT_Double3 : MaterialValueType::MCT_Float3;
        return AddCodeChunk(MaterialValueType::MCT_Float3, "RotateAboutAxis({}, {}, {})", CoerceParameter(NormalizedRotationAxisAndAngleIndex, MaterialValueType::MCT_Float4), CoerceParameter(PositionOnAxisIndex, InputType), CoerceParameter(PositionIndex, InputType));
    }
}

int32_t MaterialCompiler::MaterialParameterCollection(const std::string& mpcPath, const MpcProperty& property)
{
    uint32_t mpcIndex = 0;
    MaterialValueType valueType = MaterialValueType::MCT_Float1;
    MaterialParameterType parameterType = MaterialParameterType::None;

    if (property.first == MpcDynamicEnum::PropertyNone)
    {
        return Errorf("Select invalid parameter in material parameter collection");
    }

    if (const auto v = std::get_if<float>(&property.second))
    {
        mpcIndex = m_AllParameterFromMpcSet.AddParameterCollectionParameter(mpcPath, property.first);
        valueType = MaterialValueType::MCT_Float1;
        parameterType = MaterialParameterType::Scalar;
        const std::string propertyName = "MPC" + std::to_string(mpcIndex) + "_" + property.first;
        m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Scalar, propertyName, *v);
    }
    else if (const auto v2 = std::get_if<Float4>(&property.second))
    {
        mpcIndex = m_AllParameterFromMpcSet.AddParameterCollectionParameter(mpcPath, property.first);
        valueType = MaterialValueType::MCT_Float4;
        parameterType = MaterialParameterType::Vector;
        const std::string propertyName = "MPC" + std::to_string(mpcIndex) + "_" + property.first;
        m_AllUniformExpressionSet.FindOrAddParameter(MaterialParameterType::Vector, propertyName, *v2);
    }
    else
    {
        Assert(false);
    }

    return AddCodeChunk(valueType, "MPC{}_{}", mpcIndex, property.first);
}

int32_t MaterialCompiler::TerrainColor()
{
    auto textureCoordinate = TextureCoordinate(0);
    return AddCodeChunk(MaterialValueType::MCT_Float4, "GetTerrainColor({}, objectData)", GetParameterCode(textureCoordinate));
}

int32_t MaterialCompiler::TerrainCoords(int32_t terrainCoords)
{
    return AddCodeChunk(MaterialValueType::MCT_Float2, "GetTerrainLayerCoords({}, objectData)", GetParameterCode(terrainCoords));
}

int32_t MaterialCompiler::StaticTerrainLayerWeight(uint32_t layerIndex)
{
    auto textureCoordinate = TextureCoordinate(0);
    return AddCodeChunk(MaterialValueType::MCT_Float1, "GetStaticTerrainLayerWeight{}({}, objectData, primitiveData)[{}]", layerIndex / 4, GetParameterCode(textureCoordinate), layerIndex % 4);
}

int32_t MaterialCompiler::VertexID()
{
    if (!IsShaderFrequency(SF_WorldPositionOffset) && !IsShaderFrequency(SF_GenerateCustomInterpolators))
    {
        return Errorf("Current shader state don't support VertexID");
    }

    return AddInlinedCodeChunk(MaterialValueType::MCT_UInt1, "vsInput.vertexID");
}

int32_t MaterialCompiler::TransformByMatrix(std::string_view matrixName, std::string_view invMatrixName, int32_t A, TransformElementType elementType)
{
    switch (elementType)
    {
    case TransformElementType::Normal:
        return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(float4({}, 0), {}).xyz", GetParameterCode(A), invMatrixName);
    case TransformElementType::Vector:
        return AddCodeChunk(MaterialValueType::MCT_Float3, "mul({}, float4({}, 0)).xyz", matrixName, GetParameterCode(A));
    default:
        return Errorf("Invalid element type: {}", elementType);
    }
}

int32_t MaterialCompiler::TransformPosition(MaterialCommonBasis SourceCoordBasis, MaterialCommonBasis DestCoordBasis, int32_t A)
{
    if (SourceCoordBasis == DestCoordBasis)
    {
        return A;
    }

    if (SourceCoordBasis == MaterialCommonBasis::World)
    {
        if (!IsDoubleType(GetParameterType(A)))
        {
            A = AddCodeChunk(MaterialValueType::MCT_Double3, "DFPromote({})", GetParameterCode(A));
        }
    }
    else
    {
        if (IsDoubleType(GetParameterType(A)))
        {
            A = AddCodeChunk(MaterialValueType::MCT_Float3, "DFDemote({})", GetParameterCode(A));
        }
    }
    
    switch (SourceCoordBasis)
    {
    case MaterialCommonBasis::Local:
        switch (DestCoordBasis)
        {
        case MaterialCommonBasis::World:
        case MaterialCommonBasis::Camera:
            A = AddCodeChunk(MaterialValueType::MCT_Double3, "DFMultiply({}, {})", MCDFBuiltins::ce_RootToWorld, GetParameterCode(A));
            break;
        case MaterialCommonBasis::Instance:
            return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(objectData.ce_InvWorld, mul(primitiveData.ce_RootToWorld, float4({}, 1))).xyz", MCDFBuiltins::ce_World, GetParameterCode(A));
        default:
            return Errorf("Invalid destination coordinate basis: {}", DestCoordBasis);
        }
        break;
    case MaterialCommonBasis::World:
        switch (DestCoordBasis)
        {
        case MaterialCommonBasis::Local:
            return AddCodeChunk(MaterialValueType::MCT_Float3, "DFMultiplyDemote({}, {})", MCDFBuiltins::ce_WorldToRoot, GetParameterCode(A));
        case MaterialCommonBasis::Camera:
            return AddCodeChunk(MaterialValueType::MCT_Float3, "DFMultiplyDemote({}, {})", MCDFBuiltins::ce_View, GetParameterCode(A));
        case MaterialCommonBasis::Instance:
            return AddCodeChunk(MaterialValueType::MCT_Float3, "DFMultiplyDemote({}, {})", MCDFBuiltins::ce_InvWorld, GetParameterCode(A));
        default:
            return Errorf("Invalid destination coordinate basis: {}", DestCoordBasis);
        }
        Assert(false);
        break;
    case MaterialCommonBasis::Camera:
        A = AddCodeChunk(MaterialValueType::MCT_Double3, "DFMultiply({}, {})", MCDFBuiltins::ce_InvView, GetParameterCode(A));
        break;
    case MaterialCommonBasis::Instance:
        switch (DestCoordBasis)
        {
        case MaterialCommonBasis::World:
        case MaterialCommonBasis::Camera:
            A = AddCodeChunk(MaterialValueType::MCT_Double3, "DFMultiply({}, {})", MCDFBuiltins::ce_World, GetParameterCode(A));
            break;
        case MaterialCommonBasis::Local:
            return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(objectData.ce_WorldToRoot, mul(primitiveData.ce_World, float4({}, 1))).xyz", MCDFBuiltins::ce_World, GetParameterCode(A));
        default:
            return Errorf("Invalid destination coordinate basis: {}", DestCoordBasis);
        }
        break;
    default:
        return Errorf("Invalid source coordinate basis: {}", SourceCoordBasis);
    }

    return TransformPosition(MaterialCommonBasis::World, DestCoordBasis, A);
}

int32_t MaterialCompiler::TransformNonePosition(MaterialCommonBasis SourceCoordBasis, MaterialCommonBasis DestCoordBasis, int32_t A, TransformElementType elementType)
{
    if (SourceCoordBasis == DestCoordBasis)
    {
        return A;
    }
    
    int32_t intermediateResult = CODE_CHUNK_INDEX_NONE;
    
    switch (SourceCoordBasis)
    {
    case cross::MaterialCommonBasis::Tangent:
        if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
        {
            intermediateResult = AddCodeChunk(MaterialValueType::MCT_Float3, "mul({}, GetTangentToWorldMatrix(input))", GetParameterCode(A));
        }
        else
        {
            intermediateResult = AddCodeChunk(MaterialValueType::MCT_Float3, "mul({}, input.TangentToWorld)", GetParameterCode(A));
        }
        break;
    case cross::MaterialCommonBasis::Local:
        intermediateResult = TransformByMatrix("primitiveData.ce_RootToWorld", "primitiveData.ce_WorldToRoot", A, elementType);
        break;
    case cross::MaterialCommonBasis::World:
        switch (DestCoordBasis)
        {
        case cross::MaterialCommonBasis::Tangent:
            if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
            {
                return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(GetTangentToWorldMatrix(input), {})", GetParameterCode(A));
            }
            else
            {
                return AddCodeChunk(MaterialValueType::MCT_Float3, "mul(input.TangentToWorld, {})", GetParameterCode(A));
            }
        case cross::MaterialCommonBasis::Local:
            return TransformByMatrix("primitiveData.ce_WorldToRoot", "primitiveData.ce_RootToWorld", A, elementType);
        case cross::MaterialCommonBasis::Camera:
            return TransformByMatrix("ce_View", "ce_InvView", A, elementType);
        case cross::MaterialCommonBasis::Instance:
            return TransformByMatrix("objectData.ce_InvWorld", "objectData.ce_World", A, elementType);
        default:
            return Errorf("Can't transform form {} to {}", SourceCoordBasis, DestCoordBasis);
        }
        break;
    case cross::MaterialCommonBasis::Camera:
        intermediateResult = TransformByMatrix("ce_InvView", "ce_View", A, elementType);
        break;
    case cross::MaterialCommonBasis::Instance:
        intermediateResult = TransformByMatrix("objectData.ce_World", "objectData.ce_InvWorld", A, elementType);
        break;
    default:
        return Errorf("Can't transform form {} to {}", SourceCoordBasis, DestCoordBasis);
    }
    return TransformNonePosition(MaterialCommonBasis::World, DestCoordBasis, intermediateResult, elementType);
}

int32_t MaterialCompiler::Transform(MaterialCommonBasis SourceCoordBasis, MaterialCommonBasis DestCoordBasis, int32_t A, TransformElementType elementType)
{
    if (A == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    {
        // validation
        // if (m_ShaderFrequency != SF_Surface && m_ShaderFrequency != SF_WorldPositionOffset)
        //{
        //    return CODE_CHUNK_INDEX_NONE;
        //}

        // if (m_ShaderFrequency != SF_Surface && m_ShaderFrequency != SF_WorldPositionOffset)
        //{
        // if (SourceCoordBasis == MaterialCommonBasis::Local || DestCoordBasis == MaterialCommonBasis::Local)
        //{
        //     return Errorf(("Local space is only supported for vertex, compute or pixel shader"));
        // }
        // }

        if (elementType == TransformElementType::Position && (SourceCoordBasis == MaterialCommonBasis::Tangent || DestCoordBasis == MaterialCommonBasis::Tangent))
        {
            return Errorf(("Tangent basis not available for position transformations"));
        }

        // Construct float3(0,0,x) out of the input if it is a scalar
        // This way artists can plug in a scalar and it will be treated as height, or a vector displacement
        const auto SourceType = GetParameterType(A);

        // type cast
        if ((elementType == TransformElementType::Normal || elementType == TransformElementType::Vector) && IsDoubleType(SourceType))
        {
             A = AddInlinedCodeChunk(DoubleTypeToFloatType(SourceType), "DFDemote({})", GetParameterCode(A));
        }

        // change component count
        const auto NumInputComponents = GetNumComponents(SourceType);
        if (NumInputComponents == 0)
        {
            return Errorf("Input has 0 component");
        }

        if (NumInputComponents == 1u && SourceCoordBasis == MaterialCommonBasis::Tangent)
        {
            A = AppendVector(Constant2(0, 0), A);
        }
        else if (NumInputComponents < 3u)
        {
            return Errorf(("input must be a 3-component vector (current: {}: {}) or a scalar (if source is Tangent)"), GetParameterCode(A), SourceType);
        }

        if (elementType == TransformElementType::Position)
        {
            switch (NumInputComponents)
            {
            case 1:
                A = AppendVector(A, Constant2(0, 0));
                break;
            case 2:
                A = AppendVector(A, Constant(0));
                break;
            case 4:
                A = AddCodeChunk(MaterialValueType::MCT_Double3, "DFGetXYZ({})", GetParameterCode(A));
                break;
            }
        }
        else
        {
            if (NumInputComponents == 4)
            {
                A = ValidCast(A, MaterialValueType::MCT_Float3);
            }
        }
    }

    if (SourceCoordBasis == DestCoordBasis)
    {
        // no transformation needed
        return A;
    }
    
    if (SourceCoordBasis == cross::MaterialCommonBasis::Tangent || DestCoordBasis == cross::MaterialCommonBasis::Tangent)
    {
        m_MacroSet.AddMacro(Macro_VertexNeedNormal);
        m_MacroSet.AddMacro(Macro_VertexNeedTangent);
    }

    if (elementType == TransformElementType::Position)
    {
        return TransformPosition(SourceCoordBasis, DestCoordBasis, A);
    }
    else
    {
        return TransformNonePosition(SourceCoordBasis, DestCoordBasis, A, elementType);
    }
}

int32_t MaterialCompiler::TextureSample(SamplerState samplerState, int32_t texture, int32_t coordinate, int32_t level, int32_t bias, int32_t ddx, int32_t ddy)
{
    if (texture == CODE_CHUNK_INDEX_NONE || coordinate == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    // Sampling with implicit lod is only allowed in pixel shaders
    if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
    {
        samplerState.MipValueMode = TextureMipValueMode::MipLevel;
        level = Constant(0);
    }

    MaterialValueType textureType = GetParameterType(texture);

    if (!EnumHasAnyFlags(textureType, MaterialValueType::MCT_Texture))
    {
        Errorf("Sampling unknown texture type: {}", HLSLTypeString(textureType));
        return CODE_CHUNK_INDEX_NONE;
    }

    auto mipValueMode = samplerState.MipValueMode;
    if (mipValueMode == TextureMipValueMode::MipLevel)
    {
        if (level == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }
        else if (!IsFloatNumericType(GetParameterType(level)))
        {
            return Errorf("Invalid Level parameter");
        }
    }
    else if (mipValueMode == TextureMipValueMode::MipBias)
    {
        if (bias == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }
        else if (!IsFloatNumericType(GetParameterType(bias)))
        {
            return Errorf("Invalid Bias parameter");
        }
    }
    else if (mipValueMode == TextureMipValueMode::Derivative)
    {
        if (ddx == CODE_CHUNK_INDEX_NONE || ddy == CODE_CHUNK_INDEX_NONE)
        {
            return CODE_CHUNK_INDEX_NONE;
        }
        else if (!IsFloatNumericType(GetParameterType(ddx)))
        {
            return Errorf("Invalid DDX(UVs) parameter");
        }
        else if (!IsFloatNumericType(GetParameterType(ddy)))
        {
            return Errorf("Invalid DDY(UVs) parameter");
        }
    }

    // Get TextureName
    std::string textureName = GetParameterCode(texture);

    // Get Sampler
    std::string samplerName;
    {
        for (auto& sampler : m_AllSamplers)
        {
            if (sampler.SamplerState == samplerState)
            {
                samplerName = sampler.Name;
                break;
            }
        }

        if (samplerName.empty())
        {
            samplerName = "Sampler" + std::to_string(m_AllSamplers.size());

            m_AllSamplers.push_back(MaterialSampler{samplerName, samplerState});
        }

        bool isSamplerFound = false;
        for (auto& sampler : m_Samplers)
        {
            if (sampler.Name == samplerName)
            {
                isSamplerFound = true;
                break;
            }
        }

        if (!isSamplerFound)
        {
            m_Samplers.push_back(MaterialSampler{samplerName, samplerState});
        }
    }

    MaterialValueType UVsType;
    switch (textureType)
    {
    case MaterialValueType::MCT_TextureCubeArray:
        UVsType = MaterialValueType::MCT_Float4;
        break;
    case MaterialValueType::MCT_TextureCube:
    case MaterialValueType::MCT_Texture2DArray:
    case MaterialValueType::MCT_VolumeTexture:
        UVsType = MaterialValueType::MCT_Float3;
        break;
    default:
        UVsType = MaterialValueType::MCT_Float2;
        break;
    }

    int32_t NonDFCoordinateIndex = coordinate;
    const bool bDoubleCoordinates = IsDoubleType(GetParameterType(coordinate));
    if (bDoubleCoordinates)
    {
        // Apply texture address math manually, using LWC-scale operations, then convert the result to float
        // This could potentially cause problems if content is relying on SSM_FromTextureAsset, and having texture parameters change address mode in MI
        // Trade-off would be skip manual address mode in this case, and just accept precision loss
        auto AddressModeToString = [](TextureAddressMode InAddress) {
            switch (InAddress)
            {
            case TextureAddressMode::Clamp:
                return "LWCADDRESSMODE_CLAMP";
            case TextureAddressMode::Wrap:
                return "LWCADDRESSMODE_WRAP";
            case TextureAddressMode::Mirror:
                return "LWCADDRESSMODE_MIRROR";
            default:
                Assert(false);
                return "";
            }
        };
        //AddLWCFuncUsage(ELWCFunctionKind::Other, 1);
        const uint32_t NumComponents = GetNumComponents(UVsType);
        switch (NumComponents)
        {
        case 1u:
            NonDFCoordinateIndex = AddCodeChunk(UVsType, 
                                                 ("DFApplyAddressMode({}, {})"), 
                                                 CoerceParameter(coordinate, MaterialValueType::MCT_Double), 
                                                 AddressModeToString(samplerState.AddressMode));
            break;
        case 2u:
            NonDFCoordinateIndex = AddCodeChunk(UVsType, 
                                                 ("DFApplyAddressMode({}, {}, {})"), 
                                                 CoerceParameter(coordinate, MaterialValueType::MCT_Double2), 
                                                 AddressModeToString(samplerState.AddressMode), 
                                                 AddressModeToString(samplerState.AddressMode));
            break;
        case 3u:
            NonDFCoordinateIndex = AddCodeChunk(UVsType,
                                                 ("DFApplyAddressMode({}, {}, {}, {})"),
                                                 CoerceParameter(coordinate, MaterialValueType::MCT_Double3),
                                                 AddressModeToString(samplerState.AddressMode),
                                                 AddressModeToString(samplerState.AddressMode),
                                                 AddressModeToString(samplerState.AddressMode));
            break;
        default:
            AssertMsg(false, ("Invalid number of components {}"), NumComponents);
            break;
        }

        // Explicitly compute the derivatives for LWC UVs
        // This is needed for 100% correct functionality, otherwise filtering seams are possible where there is discontinuity in LWC->float UV conversion
        // This is expensive though, and discontinuities can be minimized by carefully choosing conversion operation
        // Disabled for now, may enable as an option in the future
        //const bool bExplicitLWCDerivatives = false;
        //if (bExplicitLWCDerivatives && (MipValueMode == TMVM_None || MipValueMode == TMVM_MipBias))
        //{
        //    int32 MipScaleIndex = INDEX_NONE;
        //    if (MipValueMode == TMVM_MipBias)
        //    {
        //        MipScaleIndex = AddCodeChunkZeroDeriv(UVsType, ("exp2(%s)"), *CoerceParameter(MipValue0Index, MCT_Float1));
        //    }

        //    AddLWCFuncUsage(ELWCFunctionKind::Other, 2);
        //    MipValue0Index = AddCodeChunkZeroDeriv(UVsType, ("WSDdxDemote(%s)"), *GetParameterCode(CoordinateIndex));
        //    MipValue1Index = AddCodeChunkZeroDeriv(UVsType, ("WSDdyDemote(%s)"), *GetParameterCode(CoordinateIndex));
        //    if (MipScaleIndex != INDEX_NONE)
        //    {
        //        MipValue0Index = Mul(MipValue0Index, MipScaleIndex);
        //        MipValue1Index = Mul(MipValue1Index, MipScaleIndex);
        //    }

        //    MipValueMode = TMVM_Derivative;
        //}
    }
    else
    {
        // Get UV
        if (textureType == MaterialValueType::MCT_TextureCube)
        {
            NonDFCoordinateIndex = ValidCast(coordinate, MaterialValueType::MCT_Float3);
        }
        else
        {
            NonDFCoordinateIndex = ValidCast(coordinate, MaterialValueType::MCT_Float2);
        }
    }

    if (NonDFCoordinateIndex == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    // Assemble SampleCode
    std::string sampleCodeFinite;
    if (textureType == MaterialValueType::MCT_TextureVirtual || textureType == MaterialValueType::MCT_TextureVirtualNormal)
    {
        auto SamplerAddressModeToString = [](TextureAddressMode addressMode) {
            switch (addressMode)
            {
            case TextureAddressMode::Clamp:
                return "VTADDRESSMODE_CLAMP";
            case TextureAddressMode::Wrap:
                return "VTADDRESSMODE_WRAP";
            case TextureAddressMode::Mirror:
                return "VTADDRESSMODE_MIRROR";
            default:
                return "VTADDRESSMODE_WRAP";
            }
        };

        int index = FindOrAddVtIndex(textureName);
        if (index < 0)
        {
            Errorf("There cannot be more than 4 virtual textures in this material");
            return CODE_CHUNK_INDEX_NONE;
        }
        else
        {
            auto addressMode = SamplerAddressModeToString(samplerState.AddressMode);

            if (IsShaderFrequency(SF_WorldPositionOffset) || IsShaderFrequency(SF_GenerateCustomInterpolators))
            {
                sampleCodeFinite =
                    fmt::format("CustomizedSampleVT_VS({}, input.positionNDC.xy, VT_PARA({}, {}), {}, {}, {}, vtFeedbackParams, {}, {} \
                                \n#if ENABLE_STOCHASTIC_FILTERING \
                                \n    , randParams                \
                                \n#endif                          \
                        \n)",
                                GetParameterCode(NonDFCoordinateIndex),
                                textureName,
                                index,
                                samplerName,
                                level,
                                m_NumVirtualTextureFeedbackRequests,
                                addressMode,
                                addressMode);
            }
            else
            {
                sampleCodeFinite = fmt::format("CustomizedSampleVT({}, input.positionNDC.xy, VT_PARA({}, {}), {}, {}, vtFeedbackParams, {}, {} \
                                                \n#if ENABLE_STOCHASTIC_FILTERING \
                                                \n    , randParams                \
                                                \n#endif                          \
                        \n)",
                                               GetParameterCode(NonDFCoordinateIndex),
                                               textureName,
                                               index,
                                               samplerName,
                                               m_NumVirtualTextureFeedbackRequests,
                                               addressMode,
                                               addressMode);
            }

            ++m_NumVirtualTextureFeedbackRequests;
        }
    }
    else if (mipValueMode == TextureMipValueMode::None)
    {
        sampleCodeFinite = fmt::format("{}.Sample({}, {})", textureName, samplerName, GetParameterCode(NonDFCoordinateIndex));
    }
    else if (mipValueMode == TextureMipValueMode::MipLevel)
    {
        sampleCodeFinite = fmt::format("{}.SampleLevel({}, {}, {})", textureName, samplerName, GetParameterCode(NonDFCoordinateIndex), GetParameterCode(level));
    }
    else if (mipValueMode == TextureMipValueMode::MipBias)
    {
        sampleCodeFinite = fmt::format("{}.SampleBias({}, {}, {})", textureName, samplerName, GetParameterCode(NonDFCoordinateIndex), GetParameterCode(bias));
    }
    else if (mipValueMode == TextureMipValueMode::Derivative)
    {
        sampleCodeFinite = fmt::format("{}.SampleGrad({}, {}, {}, {})", textureName, samplerName, GetParameterCode(NonDFCoordinateIndex), GetParameterCode(ddx), GetParameterCode(ddy));
    }

    if (textureType == MaterialValueType::MCT_Texture2DNormal || textureType == MaterialValueType::MCT_TextureVirtualNormal)
    {
        return AddCodeChunk(MaterialValueType::MCT_Float4, "float4(UnpackNormalmapRGorAG({}), 0.0f)", sampleCodeFinite);
    }
    else
    {
        return AddCodeChunk(MaterialValueType::MCT_Float4, "{}", sampleCodeFinite);
    }
}

int32_t MaterialCompiler::CustomExpression(MaterialExpressionCustom* expressionCustom, ExpressionOutput* output, std::vector<int32_t> compiledInputs)
{
    MaterialExpressionCustomEntry* customEntry = nullptr;
    for (auto& entry : m_ExpressionCustomEntries)
    {
        if (entry.Expression == expressionCustom)
        {
            customEntry = &entry;
        }
    }

    // Create Definition
    if (!customEntry)
    {
        std::string outputTypeString;
        MaterialValueType outputType;
        switch (expressionCustom->m_OutputType)
        {
        case CustomMaterialOutputType::Float1:
            outputType = MaterialValueType::MCT_Float1;
            outputTypeString = "float1";
            break;
        case CustomMaterialOutputType::Float2:
            outputType = MaterialValueType::MCT_Float2;
            outputTypeString = "float2";
            break;
        case CustomMaterialOutputType::Float3:
            outputType = MaterialValueType::MCT_Float3;
            outputTypeString = "float3";
            break;
        case CustomMaterialOutputType::Float4:
            outputType = MaterialValueType::MCT_Float4;
            outputTypeString = "float4";
            break;
        default:
            AssertMsg(false, "Unsupported output type. {}", expressionCustom->m_OutputType);
            // outputType = MaterialValueType::MCT_Float1;
            // outputTypeString = "float1";
            // break;
        }

        // Declare implementation function
        std::string inputParamDecl;
        for (int i = 0; i < expressionCustom->m_CustomInputs.size(); i++)
        {
            if (expressionCustom->m_CustomInputs[i].Name.empty())
            {
                continue;
            }

            if (i != 0)
            {
                inputParamDecl += ", ";
            }

            std::string inputNameString = expressionCustom->m_CustomInputs[i].Name;
            switch (GetParameterType(compiledInputs[i]))
            {
            case MaterialValueType::MCT_StaticBool:
                inputParamDecl += "bool ";
                inputParamDecl += inputNameString;
                break;
            // case MaterialValueType::MCT_Float:
            case MaterialValueType::MCT_Float1:
                inputParamDecl += "float ";
                inputParamDecl += inputNameString;
                break;
            case MaterialValueType::MCT_Float2:
                inputParamDecl += "float2 ";
                inputParamDecl += inputNameString;
                break;
            case MaterialValueType::MCT_Float3:
                inputParamDecl += "float3 ";
                inputParamDecl += inputNameString;
                break;
            case MaterialValueType::MCT_Float4:
                inputParamDecl += "float4 ";
                inputParamDecl += inputNameString;
                break;
            // case MaterialValueType::MCT_UInt:
            case MaterialValueType::MCT_UInt1:
                inputParamDecl += "uint ";
                inputParamDecl += inputNameString;
                break;
            case MaterialValueType::MCT_UInt2:
                inputParamDecl += "uint2 ";
                inputParamDecl += inputNameString;
                break;
            case MaterialValueType::MCT_UInt3:
                inputParamDecl += "uint3 ";
                inputParamDecl += inputNameString;
                break;
            case MaterialValueType::MCT_UInt4:
                inputParamDecl += "uint4 ";
                inputParamDecl += inputNameString;
                break;
            case MaterialValueType::MCT_Texture2D:
                // should provide a texture sampler in shader code
                inputParamDecl += "Texture2D ";
                inputParamDecl += inputNameString;
                break;
            default:
                return Errorf("Bad type {} for {} input {}", HLSLTypeString(GetParameterType(compiledInputs[i])), expressionCustom->m_Name, inputNameString);
                break;
            }
        }

        for (const auto& customOutput : expressionCustom->m_AdditionalCustomOutputs)
        {
            if (customOutput.Name.empty())
            {
                continue;
            }

            inputParamDecl += ", inout ";
            std::string outputNameString = customOutput.Name;
            switch (customOutput.OutputType)
            {
            case CustomMaterialOutputType::Float1:
                inputParamDecl += "float ";
                inputParamDecl += outputNameString;
                break;
            case CustomMaterialOutputType::Float2:
                inputParamDecl += "float2 ";
                inputParamDecl += outputNameString;
                break;
            case CustomMaterialOutputType::Float3:
                inputParamDecl += "float3 ";
                inputParamDecl += outputNameString;
                break;
            case CustomMaterialOutputType::Float4:
                inputParamDecl += "float4 ";
                inputParamDecl += outputNameString;
                break;
            default:
                return Errorf("Bad type {} for {} output {}", customOutput.OutputType, expressionCustom->m_Name, outputNameString);
                break;
            }
        }

        int32_t expressionCustomEntryIndex = static_cast<int32_t>(m_ExpressionCustomEntries.size());
        std::string code = expressionCustom->m_Code.empty() ? "0" : expressionCustom->m_Code;
        if (code.find("return") == std::string::npos)
        {
            code = "return " + code + ";";
        }

        MaterialExpressionCustomEntry& entry = m_ExpressionCustomEntries.emplace_back();
        entry.Expression = expressionCustom;
        entry.Implementation += fmt::format("{0} CustomExpression{1}({2})\n{{\n\t{3}\n}}\n", outputTypeString, expressionCustomEntryIndex, inputParamDecl, code);
        entry.FunctionName = fmt::format("CustomExpression{}", expressionCustomEntryIndex);
        customEntry = &entry;
    }

    auto& outputCodeIndices = customEntry->OutputCodeIndices[m_ShaderFrequency];
    auto& outputCodeIndicesVec = customEntry->OutputCodeIndicesVec[m_ShaderFrequency];
    if (auto iter = outputCodeIndices.find(output); iter != outputCodeIndices.end())
    {
        return iter->second;
    }
    else
    {
        // Create local temp variables to hold results of additional outputs
        for (auto& customOutput : expressionCustom->m_AdditionalCustomOutputs)
        {
            if (customOutput.Name.empty())
            {
                continue;
            }

            const std::string outputName = customOutput.Name;
            int32_t outputCodeIndex = CODE_CHUNK_INDEX_NONE;
            switch (customOutput.OutputType)
            {
            case CustomMaterialOutputType::Float1:
                outputCodeIndex = AddCodeChunk(MaterialValueType::MCT_Float1, "0.0f");
                break;
            case CustomMaterialOutputType::Float2:
                outputCodeIndex = AddCodeChunk(MaterialValueType::MCT_Float2, "float2(0.0f, 0.0f)");
                break;
            case CustomMaterialOutputType::Float3:
                outputCodeIndex = AddCodeChunk(MaterialValueType::MCT_Float3, "float3(0.0f, 0.0f, 0.0f)");
                break;
            case CustomMaterialOutputType::Float4:
                outputCodeIndex = AddCodeChunk(MaterialValueType::MCT_Float4, "float4(0.0f, 0.0f, 0.0f, 0.0f)");
                break;
            }
            outputCodeIndices[&customOutput.Output] = (outputCodeIndex);
            outputCodeIndicesVec.push_back(outputCodeIndex);
        }

        // Add call to implementation function
        std::string codeChunk = fmt::format("{}(", customEntry->FunctionName);
        for (int i = 0; i < compiledInputs.size(); i++)
        {
            if (expressionCustom->m_CustomInputs[i].Name.empty())
            {
                continue;
            }

            std::string paramCode = GetParameterCode(compiledInputs[i]);
            MaterialValueType paramType = GetParameterType(compiledInputs[i]);

            if (i != 0)
            {
                codeChunk += ", ";
            }

            codeChunk += paramCode;

        }

        for (const auto& codeChunkIndex : outputCodeIndicesVec)
        {
            codeChunk += ", ";
            codeChunk += GetParameterCode(codeChunkIndex);
        }

        codeChunk += ")";

        MaterialValueType outputType;
        switch (expressionCustom->m_OutputType)
        {
        case CustomMaterialOutputType::Float1:
            outputType = MaterialValueType::MCT_Float1;
            break;
        case CustomMaterialOutputType::Float2:
            outputType = MaterialValueType::MCT_Float2;
            break;
        case CustomMaterialOutputType::Float3:
            outputType = MaterialValueType::MCT_Float3;
            break;
        case CustomMaterialOutputType::Float4:
            outputType = MaterialValueType::MCT_Float4;
            break;
        default:
            AssertMsg(false, "Unsupported output type. {}", expressionCustom->m_OutputType);
        }

        outputCodeIndices[&expressionCustom->m_Output] = AddCodeChunk(outputType, "{}", codeChunk);

        return outputCodeIndices[output];
    }
}

std::string ComponentMaskLWC(const std::string& SourceString, MaterialValueType SourceType, MaterialValueType ResultType, bool R, bool G, bool B, bool A)
{
    const char* ComponentAccess[] = {
        "DFGetX",
        "DFGetY",
        "DFGetZ",
        "DFGetW",
    };

    bool bNeedClosingParen = false;
    bool bNeedComma = false;
    std::string Result;
    if (ResultType != MaterialValueType::MCT_Double1)
    {
        Result = "MakeDFVector(";
        bNeedClosingParen = true;
    }
    if (R)
    {
        Result += fmt::format("{}({})", ComponentAccess[0], SourceString);
        bNeedComma = true;
    }
    if (G)
    {
        if (bNeedComma)
            Result += (", ");
        Result += fmt::format("{}({})", ComponentAccess[SourceType == MaterialValueType::MCT_Double1 ? 0 : 1], SourceString);
        bNeedComma = true;
    }
    if (B)
    {
        if (bNeedComma)
            Result += (", ");
        Result += fmt::format("{}({})", ComponentAccess[SourceType == MaterialValueType::MCT_Double1 ? 0 : 2], SourceString);
        bNeedComma = true;
    }
    if (A)
    {
        if (bNeedComma)
            Result += (", ");
        Result += fmt::format("{}({})", ComponentAccess[SourceType == MaterialValueType::MCT_Double1 ? 0 : 3], SourceString);
        bNeedComma = true;
    }
    if (bNeedClosingParen)
    {
        Result += ")";
    }
    return Result;
}

int32_t MaterialCompiler::ComponentMask(int32_t Vector, bool R, bool G, bool B, bool A)
{
    if (Vector == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    MaterialValueType VectorType = GetParameterType(Vector);
    uint32_t NumValidComponents = GetNumComponents(VectorType);

    if ((A && NumValidComponents < 4u) || (B && NumValidComponents < 3u) || (G && NumValidComponents < 2u) || (R && NumValidComponents < 1u))
    {
        return Errorf(("Not enough components in ({}: {}) for component mask {}{}{}{}"), GetParameterCode(Vector), VectorType, R, G, B, A);
    }

    const bool bIsLWC = IsDoubleType(VectorType);
    MaterialValueType ResultType = MakeMaterialValueType(bIsLWC ? MaterialValueType::MCT_Double : MaterialValueType::MCT_Float, (R ? 1 : 0) + (G ? 1 : 0) + (B ? 1 : 0) + (A ? 1 : 0));
    if (ResultType == MaterialValueType::MCT_Unknown)
    {
        return Errorf(("Couldn't determine result type of component mask {}{}{}{}"), R, G, B, A);
    }

    auto MaskString = fmt::format("{}{}{}{}",
                                  R ? "r" : "",
                                  G ? "g" : "",
                                  B ? "b" : "",
                                  A ? "a" : "");

    // auto* Expression = GetParameterUniformExpression(Vector);
    // if (Expression)
    // {
    //     int8_t Mask[4] = {-1, -1, -1, -1};
    //     for (int32_t Index = 0; Index < MaskString.Len(); ++Index)
    //     {
    //         Mask[Index] = SwizzleComponentToIndex(MaskString[Index]);
    //     }
    //     FAddUniformExpressionScope Scope(this);
    //     return AddUniformExpression(Scope, new FMaterialUniformExpressionComponentSwizzle(Expression, Mask[0], Mask[1], Mask[2], Mask[3]), ResultType, ("%s.%s"), *GetParameterCode(Vector), *MaskString);
    // }

    std::string SourceString = GetParameterCode(Vector);

    // const EDerivativeStatus VectorDerivStatus = GetDerivativeStatus(Vector);
    std::string CodeFinite;
    if (bIsLWC)
    {
        CodeFinite = ComponentMaskLWC(SourceString, VectorType, ResultType, R, G, B, A);
    }
    else
    {
        CodeFinite = fmt::format("{}.{}", SourceString, MaskString);
    }

    // if (IsAnalyticDerivEnabled() && IsDerivativeValid(VectorDerivStatus))
    // {
    //     const std::string VectorDeriv = GetParameterCodeDeriv(Vector, CompiledPDV_Analytic);
    //
    //     std::string CodeAnalytic;
    //     if (VectorDerivStatus == EDerivativeStatus::Valid)
    //     {
    //         std::string ValueDeriv;
    //         if (bIsLWC)
    //         {
    //             ValueDeriv = ComponentMaskLWC(VectorDeriv + ".Value", VectorType, ResultType, R, G, B, A);
    //         }
    //         else
    //         {
    //             ValueDeriv = fmt::format(("%s.Value.%s"), *VectorDeriv, *MaskString);
    //         }
    //
    //         CodeAnalytic = DerivativeAutogen.ConstructDeriv(ValueDeriv, fmt::format(("%s.Ddx.%s"), *VectorDeriv, *MaskString), FString::Printf(("%s.Ddy.%s"), *VectorDeriv, *MaskString), GetDerivType(ResultType));
    //
    //         return AddCodeChunkInnerDeriv(*CodeFinite, *CodeAnalytic, ResultType, false, EDerivativeStatus::Valid);
    //     }
    //     else
    //     {
    //         return AddInlinedCodeChunkZeroDeriv(ResultType, *CodeFinite);
    //     }
    // }
    // else
    {
        return AddInlinedCodeChunk(ResultType, "{}", CodeFinite);
    }
}

int32_t MaterialCompiler::SceneTextureLookup(int32_t ViewportUV, SceneTextureId InSceneTextureId, bool bFiltered, bool bLinear)
{
    if (m_ShaderFrequency != SF_Surface)
    {
        // we can relax this later if needed
        return Error("Can't use Scene Texture Lookup in Non Pixel Shader");
    }

    UseSceneTextureId(InSceneTextureId, true);

    int32_t BufferUV;
    if (ViewportUV != CODE_CHUNK_INDEX_NONE)
    {
        BufferUV = AddCodeChunk(MaterialValueType::MCT_Float2, "clamp({}, 0, 1)", CoerceParameter(ViewportUV, MaterialValueType::MCT_Float2));
    }
    else
    {
        BufferUV = ScreenUV();
    }

    if (InSceneTextureId == SceneTextureId::SceneColor)
    {
        return AddCodeChunk(MaterialValueType::MCT_Float4, ("ce_{}.SampleLevel(ce_Sampler_{}, {}, 0)"), InSceneTextureId, bFiltered ? "Clamp" : "Point", CoerceParameter(BufferUV, MaterialValueType::MCT_Float2));
    }
    else if (InSceneTextureId == SceneTextureId::SceneDepth)
    {
        if (bLinear)
        {
            return AddCodeChunk(MaterialValueType::MCT_Float1, ("GetLinearDepth(ce_{}.SampleLevel(ce_Sampler_{}, {}, 0).x)"), InSceneTextureId, bFiltered ? "Clamp" : "Point", CoerceParameter(BufferUV, MaterialValueType::MCT_Float2));
        }
        else
        {
            return AddCodeChunk(MaterialValueType::MCT_Float1, ("ce_{}.SampleLevel(ce_Sampler_{}, {}, 0).x"), InSceneTextureId, bFiltered ? "Clamp" : "Point", CoerceParameter(BufferUV, MaterialValueType::MCT_Float2));
        }
    }
    return Error("Invalid Texture ID");
}

int32_t MaterialCompiler::GetSceneTextureViewSize(SceneTextureId SceneTextureId, bool InvProperty)
{
    // if (m_ShaderFrequency != SF_Surface && m_ShaderFrequency != SF_WorldPositionOffset)
    //{
    //     // we can relax this later if needed
    //     return CODE_CHUNK_INDEX_NONE;
    // }

    UseSceneTextureId(SceneTextureId, false);

    if (InvProperty)
    {
        return AddInlinedCodeChunk(MaterialValueType::MCT_Float2, "ce_ScreenParams.zw");
    }
    else
    {
        return AddInlinedCodeChunk(MaterialValueType::MCT_Float2, "ce_ScreenParams.xy");
    }
}

int32_t MaterialCompiler::AddCodeChunkInner(MaterialValueType type, const char* formattedCode, bool isInlined)
{
    if (type == MaterialValueType::MCT_Unknown)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    int32_t codeIndex = CODE_CHUNK_INDEX_NONE;

    if (type == MaterialValueType::MCT_VoidStatement)
    {
        codeIndex = static_cast<int32_t>(m_CurrentScopeChunks->size());
        const std::string statement = std::string(formattedCode) + LINE_TERMINATOR;
        m_CurrentScopeChunks->push_back(ShaderCodeChunk(statement.data(), statement.data(), type, false));
    }
    else if (isInlined)
    {
        codeIndex = static_cast<int32_t>(m_CurrentScopeChunks->size());
        m_CurrentScopeChunks->push_back(ShaderCodeChunk(formattedCode, formattedCode, type, true));
    }
    else if (EnumHasAnyFlags(type,
             MaterialValueType::MCT_Float | 
             MaterialValueType::MCT_Double | 
             MaterialValueType::MCT_VTPageTableResult | 
             MaterialValueType::MCT_UInt | 
             MaterialValueType::MCT_ShadingModel |
             MaterialValueType::MCT_MaterialAttributes | 
             MaterialValueType::MCT_Strata))
    {
        codeIndex = static_cast<int32_t>(m_CurrentScopeChunks->size());
        std::string symbolName = CreateSymbolName("local");
        std::string localVariableDefinition = std::string(HLSLTypeString(type)) + " " + symbolName + " = " + formattedCode + ";" + LINE_TERMINATOR;
        m_CurrentScopeChunks->push_back(ShaderCodeChunk(localVariableDefinition.data(), symbolName.data(), type, false));
    }
    else
    {
        assert(false);
        return CODE_CHUNK_INDEX_NONE;
    }

    return codeIndex;
}

std::string MaterialCompiler::CastValue(std::string_view Code, MaterialValueType SourceType, MaterialValueType DestType, MaterialCastFlags Flags)
{
	if (SourceType == DestType)
	{
		return std::string{Code};
	}
	
	const bool bAllowTruncate = EnumHasAnyFlags(Flags, MaterialCastFlags::AllowTruncate);
	const bool bAllowAppendZeroes = EnumHasAnyFlags(Flags, MaterialCastFlags::AllowAppendZeroes);
	const bool bAllowReplicateScalar = EnumHasAnyFlags(Flags, MaterialCastFlags::ReplicateScalar);
	const bool bAllowInteger = EnumHasAnyFlags(Flags, MaterialCastFlags::AllowInteger);

	const MaterialValueType AllowedTypes = MaterialValueType::MCT_Float | MaterialValueType::MCT_Double | (bAllowInteger ? MaterialValueType::MCT_UInt : MaterialValueType{0});

	if (IsMaterialValueType(SourceType, AllowedTypes) && IsMaterialValueType(DestType, AllowedTypes))
	{
		const uint32_t NumSourceComponents = GetNumComponents(SourceType);
		const uint32_t NumDestComponents = GetNumComponents(DestType);
		const bool bReplicateScalar = bAllowReplicateScalar && (NumSourceComponents == 1);
		if (!bReplicateScalar && !bAllowAppendZeroes && NumDestComponents > NumSourceComponents)
		{
			Errorf(("Cannot cast from smaller type {} to larger type {}."), (SourceType), (DestType));
			return "";
		}
		if (!bReplicateScalar && !bAllowTruncate && NumDestComponents < NumSourceComponents)
		{
			Errorf(("Cannot cast from larger type {} to smaller type {}."), (SourceType), (DestType));
			return "";
		}

		const bool bIsDoubleFloat = IsDoubleType(DestType);
		if (bIsDoubleFloat != IsDoubleType(SourceType))
		{
			if (bIsDoubleFloat)
			{
				// float->LWC
				// AddLWCFuncUsage(ELWCFunctionKind::Promote);
				return fmt::format("DFPromote({})", CastValue(Code, SourceType, DoubleTypeToFloatType(DestType), Flags));
			}
			else
			{
				//LWC->float
				// AddLWCFuncUsage(ELWCFunctionKind::Demote);
				return CastValue(fmt::format("DFDemote({})", Code), DoubleTypeToFloatType(SourceType), DestType, Flags);
			}
		}

		std::string Result;
		uint32_t NumComponents = 0u;
		bool bNeedClosingParen = false;
		if (bIsDoubleFloat)
		{
			// AddLWCFuncUsage(ELWCFunctionKind::Constructor);
			Result = "MakeDFVector(";
			bNeedClosingParen = true;
		}
		else
		{
			if (NumSourceComponents == NumDestComponents)
			{
				NumComponents = NumDestComponents;
				Result += Code;
			}
			else if (bReplicateScalar)
			{
				NumComponents = NumDestComponents;
				// Cast the scalar to the correct type, HLSL language will replicate the scalar when performing this cast
				Result += fmt::format("(({}){})", HLSLTypeString(DestType), Code);
			}
			else
			{
				NumComponents = std::min(NumSourceComponents, NumDestComponents);
				if (NumComponents < NumDestComponents)
				{
					Result = std::string(HLSLTypeString(DestType)) + "(";
					bNeedClosingParen = true;
				}
				if (NumComponents == NumSourceComponents)
				{
					// If we're taking all the components from the source, can avoid adding a swizzle
					Result += Code;
				}
				else
				{
					static const char* Mask[] = { "<ERROR>", "x", "xy", "xyz", "xyzw" };
					Assert(NumComponents <= 4);
					Result += fmt::format("{}.{}", Code, Mask[NumComponents]);
				}
			}
		}

		if (bNeedClosingParen)
		{
			for (uint32_t ComponentIndex = NumComponents; ComponentIndex < NumDestComponents; ++ComponentIndex)
			{
				if (ComponentIndex > 0u)
				{
					Result += (",");
				}
				if (bIsDoubleFloat)
				{
					if (!bReplicateScalar && ComponentIndex >= NumSourceComponents)
					{
						Assert(bAllowAppendZeroes);
						// AddLWCFuncUsage(ELWCFunctionKind::Promote);
						Result += "DFPromote(0.0f)";
					}
					else
					{
						Result += fmt::format(("DFGetComponent({}, {})"), Code, bReplicateScalar ? 0 : ComponentIndex);
					}
				}
				else
				{
					// Non-LWC case should only be zero-filling here, other cases should have already been handled
					Assert(bAllowAppendZeroes);
					Assert(!bReplicateScalar);
					Assert(ComponentIndex >= NumSourceComponents);
					Result += "0.0f";
				}
			}
			NumComponents = NumDestComponents;
			Result += ")";
		}
		Assert(NumComponents == NumDestComponents);
		return Result;
	}

	// If the type came from a texture collection, we'll need to remove that flag to resolve the cast.
	if (EnumHasAnyFlags(SourceType, MaterialValueType::MCT_TextureCollection) && SourceType != MaterialValueType::MCT_TextureCollection)
	{
		return CastValue(Code, MaterialValueType(SourceType & ~MaterialValueType::MCT_TextureCollection), DestType, Flags);
	}

	Errorf(("Cannot cast between non-numeric types {} to {}."), SourceType, DestType);
	return "";
}

std::string MaterialCompiler::CoerceValue(std::string_view code, MaterialValueType srcType, MaterialValueType dstType)
{
    MaterialCastFlags castFlags = MaterialCastFlags::ReplicateScalar;
    if (dstType == MaterialValueType::MCT_Float1 || dstType == MaterialValueType::MCT_Double1)
    {
        // CoerceValue allows truncating to scalar types only
        castFlags |= MaterialCastFlags::AllowTruncate;
    }
    return CastValue(code, srcType, dstType, castFlags);
}

std::string MaterialCompiler::CoerceParameter(int32_t index, MaterialValueType dstType)
{
    return CoerceValue(GetParameterCode(index), GetParameterType(index), dstType);
}

int32_t MaterialCompiler::ValidCast(int32_t Code, MaterialValueType DestType)
{
	if(Code == CODE_CHUNK_INDEX_NONE)
	{
		return CODE_CHUNK_INDEX_NONE;
	}

	const MaterialCastFlags Flags = MaterialCastFlags::ValidCast;
	const MaterialValueType SourceType = GetParameterType(Code);
	int32_t CompiledResult = CODE_CHUNK_INDEX_NONE;

	if (EnumHasAnyFlags(SourceType, DestType))
	{
		CompiledResult = Code;
	}
	// else if(GetParameterUniformExpression(Code) && !GetParameterUniformExpression(Code)->IsConstant())
	// {
	// 	if ((SourceType & MCT_TextureVirtual) && (DestType & MCT_Texture2D))
	// 	{
	// 		return Code;
	// 	}
	// 	else
	// 	{
	// 		return ValidCast(AccessUniformExpression(Code), DestType);
	// 	}
	// }
	// else if ((SourceType & (MCT_TextureMeshPaint | MCT_TextureMaterialCache)) && (DestType & MCT_Texture2D))
	// {
	// 	return Code;
	// }
	else if(IsFloatNumericType(SourceType) && IsFloatNumericType(DestType))
	{
		// const FDerivInfo CodeDerivInfo = GetDerivInfo(Code);
		//
		auto FiniteCode = CastValue(GetParameterCode(Code), SourceType, DestType, Flags);
		// if (IsAnalyticDerivEnabled() && IsDerivativeValid(CodeDerivInfo.DerivativeStatus))
		// {
		// 	if (CodeDerivInfo.DerivativeStatus == EDerivativeStatus::Valid)
		// 	{
		// 		FString DerivString = *GetParameterCodeDeriv(Code, CompiledPDV_Analytic);
		// 		FString DDXCode = CastValue(DerivString + ".Ddx", MakeNonLWCType(SourceType), MakeNonLWCType(DestType), Flags);
		// 		FString DDYCode = CastValue(DerivString + ".Ddy", MakeNonLWCType(SourceType), MakeNonLWCType(DestType), Flags);
		// 		FString AnalyticCode = DerivativeAutogen.ConstructDeriv(FiniteCode, DDXCode, DDYCode, GetDerivType(DestType));
		// 		return AddCodeChunkInnerDeriv(*FiniteCode, *AnalyticCode, DestType, false, EDerivativeStatus::Valid);
		// 	}
		// 	else
		// 	{
		// 		return AddInlinedCodeChunkZeroDeriv(DestType, *FiniteCode);
		// 	}
		// }
		// else
		{
			return AddInlinedCodeChunk(DestType, "{}", FiniteCode);
		}
	}
	else
	{
		//We can feed any type into a material attributes socket as we're really just passing them through.
		if( DestType == MaterialValueType::MCT_MaterialAttributes )
		{
			CompiledResult = Code;
		}
		else
		{
			CompiledResult = Errorf(("Cannot cast from {} to {}."), SourceType, DestType);
		}
	}

	return CompiledResult;
}

int32_t MaterialCompiler::ForceCast(int32_t codeIndex, MaterialValueType dstType, MaterialCastFlags forceCastFlags)
{
    if (codeIndex == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    const MaterialCastFlags flags = MaterialCastFlags::ValidCast | MaterialCastFlags::AllowAppendZeroes | forceCastFlags;
    MaterialValueType srcType = GetParameterType(codeIndex);

    if (EnumHasAnyFlags(srcType, dstType))
    {
        return codeIndex;
    }
    else if (IsFloatNumericType(srcType) && IsFloatNumericType(dstType))
    {
        std::string finiteCode = CastValue(GetParameterCode(codeIndex), srcType, dstType, flags);
        return AddInlinedCodeChunk(dstType, "{}", finiteCode);
    }
    else
    {
        return Errorf("Cannot force a cast from {} to {}.", HLSLTypeString(srcType), HLSLTypeString(dstType));
    }
}

int32_t MaterialCompiler::BaseTypeCast(int32_t index, MaterialValueType dstType)
{
    assert(dstType == MaterialValueType::MCT_Float || dstType == MaterialValueType::MCT_UInt || dstType == MaterialValueType::MCT_SInt);

    if (index == CODE_CHUNK_INDEX_NONE)
    {
        return CODE_CHUNK_INDEX_NONE;
    }

    MaterialValueType srcType = GetParameterType(index);
    if (EnumHasAnyFlags(srcType, dstType))
    {
        return index;
    }

    uint32_t validComponentCount = GetNumComponents(srcType);
    MaterialValueType dstTypeActual = MakeMaterialValueType(dstType, validComponentCount);

    return AddInlinedCodeChunk(dstTypeActual, "(({}){})", HLSLTypeString(dstTypeActual), GetParameterCode(index));
}

MaterialValueType MaterialCompiler::GetParameterType(int32_t index) const
{
    return m_CurrentScopeChunks->at(index).m_Type;
}

bool MaterialCompiler::GetStaticBoolValue(int32_t boolIndex, bool& bSucceed)
{
    bSucceed = true;
    if (boolIndex == INDEX_NONE)
    {
        bSucceed = false;
        return false;
    }

    if (GetParameterType(boolIndex) != MaterialValueType::MCT_StaticBool)
    {
        Errorf("Failed to cast {} input to static bool type", HLSLTypeString(GetParameterType(boolIndex)));
        bSucceed = false;
        return false;
    }

    if (GetParameterCode(boolIndex).find("true") != std::string::npos)
    {
        return true;
    }

    return false;
}

std::string MaterialCompiler::GetParameterCode(int32_t index, const char* defaultCode)
{
    if (index == -1 && defaultCode)
    {
        return defaultCode;
    }

    const ShaderCodeChunk& codeChunk = m_CurrentScopeChunks->at(index);
    if (codeChunk.m_IsInlined)
    {
        return codeChunk.m_Definition;
    }
    else
    {
        return codeChunk.m_SymbolName;
    }
}

std::string MaterialCompiler::CreateSymbolName(const char* symbolNameHint)
{
    return std::string(symbolNameHint) + std::to_string(m_NextSymbolIndex++);
}

const char* MaterialCompiler::HLSLTypeString(MaterialValueType type) const
{
    switch (type)
    {
    case MaterialValueType::MCT_Float1:
        return "float";
    case MaterialValueType::MCT_Float2:
        return "float2";
    case MaterialValueType::MCT_Float3:
        return "float3";
    case MaterialValueType::MCT_Float4:
        return "float4";
    // case MaterialValueType::MCT_Float:
    //     return "float";
    case MaterialValueType::MCT_Texture2D:
        return "Texture2D";
    case MaterialValueType::MCT_Texture2DNormal:
        return "Texture2DNormal";
    case MaterialValueType::MCT_TextureCube:
        return "TextureCube";
    case MaterialValueType::MCT_Texture2DArray:
        return "Texture2DArray";
    case MaterialValueType::MCT_TextureCubeArray:
        return "TextureCubeArray";
    case MaterialValueType::MCT_VolumeTexture:
        return "volumeTexture";
    case MaterialValueType::MCT_StaticBool:
        return "static bool";
    case MaterialValueType::MCT_Bool:
        return "bool";
    case MaterialValueType::MCT_MaterialAttributes:
        return "FMaterialAttributes";
    case MaterialValueType::MCT_TextureExternal:
        return "TextureExternal";
    case MaterialValueType::MCT_TextureVirtual:
        return "TextureVirtual";
    case MaterialValueType::MCT_VTPageTableResult:
        return "VTPageTableResult";
    case MaterialValueType::MCT_ShadingModel:
        return "uint";
    // case MaterialValueType::MCT_UInt:
    //     return "uint";
    case MaterialValueType::MCT_UInt1:
        return "uint";
    case MaterialValueType::MCT_UInt2:
        return "uint2";
    case MaterialValueType::MCT_UInt3:
        return "uint3";
    case MaterialValueType::MCT_UInt4:
        return "uint4";
    // case MaterialValueType::MCT_SInt:
    //     return "int";
    case MaterialValueType::MCT_SInt1:
        return "int";
    case MaterialValueType::MCT_SInt2:
        return "int2";
    case MaterialValueType::MCT_SInt3:
        return "int3";
    case MaterialValueType::MCT_SInt4:
        return "int4";
    case MaterialValueType::MCT_Strata:
        return "FStrataData";
    case MaterialValueType::MCT_Double1:
        return "FDFScalar";
    case MaterialValueType::MCT_Double2:
        return "FDFVector2";
    case MaterialValueType::MCT_Double3:
        return "FDFVector3";
    case MaterialValueType::MCT_Double4:
        return "FDFVector4";
    default:
        return "unknown";
    };
}

MaterialValueType MaterialCompiler::GetArithmeticResultType(MaterialValueType TypeA, MaterialValueType TypeB)
{
    if (!IsPrimitiveType(TypeA) || !IsPrimitiveType(TypeB))
    {
        Errorf(("Attempting to perform arithmetic on non-primitive types: {} {}"), TypeA, TypeB);
        return MaterialValueType::MCT_Unknown;
    }

    if(TypeA == TypeB)
    {
        return TypeA;
    }
    else if (IsDoubleType(TypeA) || IsDoubleType(TypeB))
    {
        const MaterialValueType DoubleTypeA = FloatTypeToDoubleType(TypeA);
        const MaterialValueType DoubleTypeB = FloatTypeToDoubleType(TypeB);
        if (DoubleTypeA == DoubleTypeB)
        {
            return DoubleTypeA;
        }
        else if (DoubleTypeA == MaterialValueType::MCT_Double1 && IsFloatNumericType(DoubleTypeB))
        {
            return DoubleTypeB;
        }
        else if (DoubleTypeB == MaterialValueType::MCT_Double1 && IsFloatNumericType(DoubleTypeA))
        {
            return DoubleTypeA;
        }
    }
    else if (TypeA == MaterialValueType::MCT_Float1)
    {
        return TypeB;
    }
    else if(TypeB == MaterialValueType::MCT_Float1)
    {
        return TypeA;
    }

    Errorf(("Arithmetic between types {} and {} are undefined"), TypeA, TypeB);
    return MaterialValueType::MCT_Unknown;
}

MaterialValueType MaterialCompiler::GetArithmeticResultType(int32_t a, int32_t b)
{
    MaterialValueType typeA = m_CurrentScopeChunks->at(a).m_Type;
    MaterialValueType typeB = m_CurrentScopeChunks->at(b).m_Type;

    return GetArithmeticResultType(typeA, typeB);
}

MaterialParameterType MaterialCompiler::ToParameterType(MaterialValueType valueType)
{
    switch (valueType)
    {
    case MaterialValueType::MCT_Float1:
        return MaterialParameterType::Scalar;
    case MaterialValueType::MCT_Float4:
        return MaterialParameterType::Vector;
    case MaterialValueType::MCT_Texture2D:
    case MaterialValueType::MCT_Texture2DNormal:
        return MaterialParameterType::Texture2D;
    case MaterialValueType::MCT_TextureCube:
        return MaterialParameterType::TextureCube;
    case MaterialValueType::MCT_TextureVirtual:
    case MaterialValueType::MCT_TextureVirtualNormal:
        return MaterialParameterType::VirtualTexture;
    }

    Assert(false);
    return MaterialParameterType::None;
}

void MaterialCompiler::UseSceneTextureId(SceneTextureId SceneTextureId, bool bTextureLookup)
{
    m_SceneTextures[SceneTextureId] |= bTextureLookup;

    if (SceneTextureId == SceneTextureId::SceneColor && m_InputParams.defines.Domain != MaterialDomain::Surface && m_InputParams.defines.Domain != MaterialDomain::PostProcess)
    {
        Errorf(("SceneColor lookups are only available when MaterialDomain = Surface or PostProcess."));
    }
}

int MaterialCompiler::FindOrAddVtIndex(const std::string& vtParameterName)
{
    for (UInt32 i = 0; i < m_InputParams.VTs->size(); ++i)
    {
        if ((*m_InputParams.VTs)[i] == vtParameterName)
        {
            return i;
        }
    }
    if (m_InputParams.VTs->size() >= 4)
    {
        m_InputParams.overflowVTs->insert(vtParameterName);
        return -1;
    }
    m_InputParams.VTs->push_back(vtParameterName);
    return static_cast<int>(m_InputParams.VTs->size() - 1);
}

int MaterialCompiler::FindVtIndex(const std::string& vtParameterName)
{
    for (UInt32 i = 0; i < m_InputParams.VTs->size(); ++i)
    {
        if ((*m_InputParams.VTs)[i] == vtParameterName)
        {
            return i;
        }
    }
    return -1;
}

bool MaterialCompiler::HasVT() const
{
    return !m_InputParams.VTs->empty();
}

int32_t MaterialCompiler::Error(const char* text)
{
    auto& currentFunctionStack = m_FunctionStacks[m_ShaderFrequency];

    if (currentFunctionStack.size() > 1)
    {
        // we are inside a function
        auto* errorFunction = currentFunctionStack[1]->m_FunctionCall;
        errorFunction->m_ErrorMessage = text;
    }
    else if (currentFunctionStack.back()->m_ExpressionStack.size() > 0)
    {
        MaterialExpression* errorExpression = currentFunctionStack.back()->m_ExpressionStack.back().expression;
        assert(errorExpression);

        if (errorExpression->m_ErrorMessage.empty())
        {
            errorExpression->m_ErrorMessage = text;
        }
    }

    if (m_InputParams.addErrorMessage)
    {
        m_InputParams.addErrorMessage(text);
    }
    

    return CODE_CHUNK_INDEX_NONE;
}

}
