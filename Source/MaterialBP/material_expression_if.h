#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionIf : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "If";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_A;

    CEProperty(Reflect)
    ExpressionInput m_B;

    CEProperty(Reflect, meta(DisplayName = "A > B"))
    ExpressionInput m_AGreaterThanB;

    CEProperty(Reflect, meta(DisplayName = "A == B"))
    ExpressionInput m_AEqualsB;

    CEProperty(Reflect, meta(DisplayName = "A < B"))
    ExpressionInput m_ALessThanB;

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect, meta(OverrideInputProperty = m_B))
    float m_ConstB = 0;
};
}   // namespace cross