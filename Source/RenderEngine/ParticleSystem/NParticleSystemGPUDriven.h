#pragma once
#include "NParticleInstanceCountManager.h"
#include "CrossFX/ParticleSystem/NParticleSystemGPUComputeProxy.h"
#include "RenderEngine/GPUScene/GPUScene.h"

namespace cross {

struct NParticleGPUDispatchInfo
{
    NParticleGPUDispatchInfo(const fx::NParticleEmitterGPUComputeContext* context, const fx::NParticleSystemGPUTick& tick, const fx::NParticleEmitterGPUComputeData& emitterGPUComputeData)
        : mContext(context), mTick(tick), mEmitterGPUComputeData(emitterGPUComputeData)
    {}

    const fx::NParticleEmitterGPUComputeContext* mContext = nullptr;
    const fx::NParticleSystemGPUTick& mTick;
    const fx::NParticleEmitterGPUComputeData& mEmitterGPUComputeData;
    NParticleGPUDataBuffer* mSrcBuffer = nullptr;
    NParticleGPUDataBuffer* mDstBuffer = nullptr;

    UInt32 mSrcCountOffset = 0;
    UInt32 mDstCountOffset = 0;

    UInt32 mSrcNumInstances = 0;
    UInt32 mDstNumInstances = 0;

    // GPUScene
    SInt32 mGPUSceneBufferOffset = -1;
    const GPUSceneBufferView* mGPUSceneBufferView = nullptr;
};

class NParticleSystemGPUDriven
{
public:
    inline void AddGPUComputeProxy(fx::NParticleSystemGPUComputeProxy* computeProxy)
    {
        mComputeProxies.push_back(computeProxy);
    }

    inline void RemoveGPUComputeProxy(fx::NParticleSystemGPUComputeProxy* computeProxy)
    {
        mPendingRemovedProxies.push_back(computeProxy);
    }

    void ProcessPendingTicks(RenderWorld* renderWorld);

    void PrepareAllTicks(RenderWorld* renderWorld);

    void PrepareTicksForProxy(fx::NParticleSystemGPUComputeProxy* proxy, RenderWorld* renderWorld);

    void ExecuteTicks();

    NParticleInstanceCountManager& GetParticleInstanceCountManager()
    {
        return mParticleInstanceCountManager;
    }

    void ReleaseInstanceCount(UInt32 countOffset)
    {
        mInstanceCountToRelease.push_back(countOffset);
    }

private:

    void UpdateInstanceCountManager();

    void FinishDispatches();

    void ProcessPendingProxies();

    std::vector<NParticleGPUDispatchInfo> mDispatchInfos;

    std::vector<fx::NParticleSystemGPUComputeProxy*> mPendingRemovedProxies;
    std::vector<fx::NParticleSystemGPUComputeProxy*> mComputeProxies;
    std::vector<UInt32> mInstanceCountToRelease;
    NParticleInstanceCountManager mParticleInstanceCountManager;

    static UInt32 sTickCounter;
};

}