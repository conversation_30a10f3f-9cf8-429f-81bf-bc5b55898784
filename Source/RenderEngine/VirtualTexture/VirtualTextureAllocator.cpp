#include "VirtualTextureAllocator.h"
#include "AllocatedVirtualTexture.h"
#include "VirtualTexturing.h"
#include "VirtualTextureMath.h"

namespace cross {
VTMemoryAllocator::VTMemoryAllocator() 
    : mDimensions(0)
{}
VTMemoryAllocator::VTMemoryAllocator(UInt32 dimensions) 
    : mDimensions(dimensions)
    , mAllocatedWidth(0u)
    , mAllocatedHeight(0u)
    , mNumAllocations(0u)
    , mNumAllocatedPages(0u)
{}
void VTMemoryAllocator::Initialize(UInt32 maxSize) 
{
    const UInt32 vLogSize = VTMath::CeilLogTwo(maxSize);
    Assert(vLogSize <= VIRTUALTEXTURE_LOG2_MAX_PAGETABLE_SIZE);
    Assert(mNumAllocations == 0);

    mAddressBlocks.reserve(1);
    mSortedAddresses.reserve(1);
    mSortedIndices.reserve(1);
    mFreeList.reserve(vLogSize + 1);
    mPartiallyFreeList.reserve(vLogSize + 1);

    // Start with one empty block
    FAddressBlock defaultBlock(static_cast<UInt8>(vLogSize));
    defaultBlock.state = EBlockState::FreeList;
    mAddressBlocks.push_back(defaultBlock);

    mSortedAddresses.emplace_back(0u);
    UInt16 index = 0;
    mSortedIndices.emplace_back(index);

    // Init free list
    mFreeList.resize(vLogSize + 1);
    mPartiallyFreeList.resize(vLogSize + 1);
    for (UInt8 i = 0; i < vLogSize; i++)
    {
        mFreeList[i] = 0xffff;
        std::memset(&mPartiallyFreeList[i], 0xffff, sizeof(PartiallyFreeMip));
    }
    mFreeList[vLogSize] = 0;
    std::memset(&mPartiallyFreeList[vLogSize], 0xffff, sizeof(PartiallyFreeMip));

    // Init global free list
    mGlobalFreeList = 0xffff;
}
AllocatedVirtualTexture* VTMemoryAllocator::Find(UInt32 vAddress, UInt32& OutLocal_vAddress) const
{
    const UInt32 sortedIndex = FindAddressBlock(vAddress);

    const UInt16 index = mSortedIndices[sortedIndex];
    const FAddressBlock& addressBlock = mAddressBlocks[index];
    Assert(mSortedAddresses[sortedIndex] == addressBlock.vAddress);

    AllocatedVirtualTexture* mAllocatedVT = nullptr;
    const UInt32 blockSize = 1 << (mDimensions * addressBlock.vLogSize);
    if (vAddress >= addressBlock.vAddress && vAddress < addressBlock.vAddress + blockSize)
    {
        mAllocatedVT = addressBlock.VT;
        if (mAllocatedVT)
        {
            OutLocal_vAddress = vAddress - mAllocatedVT->GetVirtualAddress();
        }
    }

    return mAllocatedVT;
}
UInt32 VTMemoryAllocator::Alloc(AllocatedVirtualTexture* VT)
{
    const UInt32 widthInTiles = VT->GetWidthInTiles();
    const UInt32 heightInTiles = VT->GetHeightInTiles();
    //const UInt32 minSize = std::min(widthInTiles, heightInTiles);
    const UInt32 maxSize = std::max(widthInTiles, heightInTiles);
   // const SInt32 vLogMinSize = VTMath::CeilLogTwo(minSize);
    const SInt32 vLogMaxSize = VTMath::CeilLogTwo(maxSize);

    // Tile must be aligned to match the max level of the VT, otherwise tiles at lower mip levels may intersect neighboring regions
    //const UInt32 maxLevel = VT->GetMaxLevel();
    //const UInt32 vAddressAlignment = 1u << (mDimensions * maxLevel);

    if (vLogMaxSize >= mFreeList.size())
    {
        // VT is larger than the entire page table
        return ~0u;
    }
    UInt16 allocIndex = 0xffff;
    UInt32 vAddress = ~0u;
    
    // See if we have any completely free blocks big enough
    // Here we search all free blocks, including ones that are too large (large blocks will still be subdivided to fit)
    for (UInt32 vLogSize = vLogMaxSize; vLogSize < mFreeList.size(); ++vLogSize)
    {
        // Could avoid this loop if FreeList was kept sorted by vAddress
        UInt16 freeIndex = mFreeList[vLogSize];
        while (freeIndex != 0xffff)
        {
            const FAddressBlock& allocBlock = mAddressBlocks[freeIndex];
            Assert(allocBlock.state == EBlockState::FreeList);
            if (allocBlock.vAddress < vAddress)
            {
                allocIndex = freeIndex;
                vAddress = allocBlock.vAddress;
            }
            freeIndex = allocBlock.nextFree;
        }
    }
    
    if (allocIndex != 0xffff)
    {
        Assert(vAddress != ~0u);
        const UInt32 vTileX = VTMath::ReverseMortonCode2(vAddress);
        const UInt32 vTileY = VTMath::ReverseMortonCode2(vAddress >> 1);

        MarkBlockAllocated(allocIndex, vTileX, vTileY, VT);

        Assert(mAddressBlocks[allocIndex].state != EBlockState::FreeList);

        // Make sure we allocate enough space in the backing texture so all the mip levels fit
        //const UInt32 SizeAlign = 1u << maxLevel;
        //const UInt32 AlignedWidthInTiles = Align(widthInTiles, SizeAlign);
        //const UInt32 AlignedHeightInTiles = Align(heightInTiles, SizeAlign);

        mAllocatedWidth = std::max(mAllocatedWidth, vTileX + widthInTiles);
        mAllocatedHeight = std::max(mAllocatedHeight, vTileY + heightInTiles);
    }
    return vAddress;
}
void VTMemoryAllocator::Free(AllocatedVirtualTexture* VT) 
{
    if (mHashTable.size() == 0)
    {
        LOG_ERROR("Free a empty VTMemoryAllocator size");
        return; 
    }
    UInt16 key = static_cast<UInt16>(reinterpret_cast<UInt64>(VT) / 16);
    auto range = mHashTable.equal_range(key);
    for (auto it = range.first; it != range.second; ) 
    {
        UInt16 index = static_cast<UInt16>(it->second);
        FAddressBlock& addressBlock = mAddressBlocks[index];
        if (addressBlock.VT == VT)
        {
            // Assert(addressBlock.state == EBlockState::AllocatedTexture);
            // Assert(addressBlock.firstChild == 0xffff);   // texture allocation should be leaf
            addressBlock.state = EBlockState::None;
            addressBlock.VT = nullptr;

            FreeMipUpdateParents(addressBlock.parent);

            Assert(mNumAllocations > 0u);
            --mNumAllocations;

            const UInt32 NumPagesForBlock = 1u << (mDimensions * addressBlock.vLogSize);
            Assert(mNumAllocatedPages >= NumPagesForBlock);
            mNumAllocatedPages -= NumPagesForBlock;

            // Add block to free list
            // This handles merging free siblings
            FreeAddressBlock(index, true);

            // Remove the index from the hash table as it may be reused later
            it = mHashTable.erase(it);
        }
        else
        {
            it++;
        }
    }
}

void VTMemoryAllocator::LinkFreeList(UInt16& inOutListHead, EBlockState state, UInt16 index) 
{
    FAddressBlock& addressBlock = mAddressBlocks[index];
    Assert(addressBlock.state == EBlockState::None);
    Assert(addressBlock.nextFree == 0xffff);
    Assert(addressBlock.prevFree == 0xffff);

    // Only the PartiallyFreeList free list is allowed to have children
    Assert(state == EBlockState::PartiallyFreeList || addressBlock.firstChild == 0xffff);

    addressBlock.state = state;
    addressBlock.nextFree = inOutListHead;
    if (addressBlock.nextFree != 0xffff)
    {
        mAddressBlocks[addressBlock.nextFree].prevFree = index;
    }
    inOutListHead = index;
}
void VTMemoryAllocator::UnlinkFreeList(UInt16& inOutListHead, EBlockState state, UInt16 index) 
{
    FAddressBlock& addressBlock = mAddressBlocks[index];
    Assert(addressBlock.state == state);
    const UInt16 prevFreeIndex = addressBlock.prevFree;
    const UInt16 nextFreeIndex = addressBlock.nextFree;
    if (prevFreeIndex != 0xffff)
    {
        mAddressBlocks[prevFreeIndex].nextFree = nextFreeIndex;
        addressBlock.prevFree = 0xffff;
    }
    if (nextFreeIndex != 0xffff)
    {
        mAddressBlocks[nextFreeIndex].prevFree = prevFreeIndex;
        addressBlock.nextFree = 0xffff;
    }
    if (inOutListHead == index)
    {
        inOutListHead = nextFreeIndex;
    }
    addressBlock.state = EBlockState::None;
}
UInt16 VTMemoryAllocator::AcquireBlock()
{
    UInt16 index = mGlobalFreeList;
    if (index == 0xffff)
    {
        FAddressBlock newBlock(0);
        index = static_cast<UInt16>(mAddressBlocks.size());
        mAddressBlocks.emplace_back(newBlock);
        Assert(index <= 0x8000);   // make sure we're not getting close
        Assert(index <= 0xffff);    // make sure we fit in 16bit index
    }
    else
    {
        UnlinkFreeList(mGlobalFreeList, EBlockState::GlobalFreeList, index);
    }

    // Debug fill memory to invalid value
    FAddressBlock& addressBlock = mAddressBlocks[index];
    std::memset(&addressBlock, 0xCC, sizeof(FAddressBlock));

    return index;
}
void VTMemoryAllocator::FreeAddressBlock(UInt16 index, bool bTopLevelBlock) 
{
    FAddressBlock& addressBlock = mAddressBlocks[index];
    if (bTopLevelBlock)
    {
        // block was freed directly, should already be removed froms lists
        Assert(addressBlock.state == EBlockState::None);
    }
    else
    {
        // block was freed by consolidating children
        UnlinkFreeList(mPartiallyFreeList[addressBlock.vLogSize].mips[addressBlock.freeMip], EBlockState::PartiallyFreeList, index);
    }

    Assert(addressBlock.VT == nullptr);
    Assert(addressBlock.nextFree == 0xffff);
    Assert(addressBlock.prevFree == 0xffff);

    // If we got here, the block's children have already been consolidated/removed
    addressBlock.firstChild = 0xffff;

    // If all siblings are free then we can merge them
    UInt32 siblingIndex = addressBlock.firstSibling;
    bool bConsolidateSiblings = siblingIndex != 0xffff;
    while (bConsolidateSiblings && siblingIndex != 0xffff)
    {
        const FAddressBlock& siblingBlock = mAddressBlocks[siblingIndex];
        if (siblingIndex != index)
        {
            Assert(siblingBlock.state != EBlockState::None);
            Assert(siblingBlock.state != EBlockState::GlobalFreeList);
            bConsolidateSiblings &= (siblingBlock.state == EBlockState::FreeList);
        }
        siblingIndex = siblingBlock.nextSibling;
    }

    if (!bConsolidateSiblings)
    {
        // Simply place this block on the free list
        LinkFreeList(mFreeList[addressBlock.vLogSize], EBlockState::FreeList, index);
    }
    else
    {
        // Remove all of this block's siblings from free list and add to global free list
        UInt16 freeIndex = addressBlock.firstSibling;
        while (freeIndex != 0xffff)
        {
            FAddressBlock& freeBlock = mAddressBlocks[freeIndex];

            if (freeIndex != index)
            {
                // All our siblings must be free (we Asserted above to get into this case)
                UnlinkFreeList(mFreeList[addressBlock.vLogSize], EBlockState::FreeList, freeIndex);
            }

            LinkFreeList(mGlobalFreeList, EBlockState::GlobalFreeList, freeIndex);

            freeIndex = freeBlock.nextSibling;
        }

        Assert(addressBlock.state == EBlockState::GlobalFreeList);

        // Remove this block and its siblings from the sorted lists
        // We can assume that the sibling blocks are sequential in the sorted list since they are free and so have no children
        // FirstSibling will be the last in the range of siblings in the sorted lists
        const UInt32 sortedIndexRangeEnd = FindAddressBlock(mAddressBlocks[addressBlock.firstSibling].vAddress);
        Assert(mSortedAddresses[sortedIndexRangeEnd] == mAddressBlocks[addressBlock.firstSibling].vAddress);
        const UInt32 numSiblings = 1 << mDimensions;
        Assert(sortedIndexRangeEnd + 1 >= numSiblings);
        const UInt32 SortedIndexRangeStart = sortedIndexRangeEnd + 1 - numSiblings;

        // Remove all but one siblings because...
        mSortedAddresses.erase(mSortedAddresses.begin() + SortedIndexRangeStart, mSortedAddresses.begin() + SortedIndexRangeStart + numSiblings - 1);
        mSortedIndices.erase(mSortedIndices.begin() + SortedIndexRangeStart, mSortedIndices.begin() + SortedIndexRangeStart + numSiblings - 1);
        // ... we replace first sibling with parent
        mSortedIndices[SortedIndexRangeStart] = addressBlock.parent;
        Assert(mSortedAddresses[SortedIndexRangeStart] == mAddressBlocks[addressBlock.parent].vAddress);

        // Add parent block to free list (and possibly consolidate)
        FreeAddressBlock(addressBlock.parent, false);
    }
}
UInt32 VTMemoryAllocator::FindAddressBlock(UInt32 vAddress) const
{
    UInt32 min = 0;
    UInt32 max = static_cast<UInt32>(mSortedAddresses.size());

    // Binary search for lower bound
    while (min != max)
    {
        const UInt32 mid = min + (max - min) / 2;
        const UInt32 key = mSortedAddresses[mid];

        if (vAddress < key)
            min = mid + 1;
        else
            max = mid;
    }
    return min;
}
void VTMemoryAllocator::SubdivideBlock(UInt16 parentIndex)
{
    const UInt32 numChildren = (1 << mDimensions);

    const UInt8 vParentLogSize = mAddressBlocks[parentIndex].vLogSize;
    Assert(vParentLogSize > 0u);
    const UInt8 vChildLogSize = vParentLogSize - 1u;

    // Only free blocks can be subdivided, move to the partially free list
    Assert(mAddressBlocks[parentIndex].firstChild == 0xffff);
    UnlinkFreeList(mFreeList[vParentLogSize], EBlockState::FreeList, parentIndex);
    mAddressBlocks[parentIndex].freeMip = 0;
    LinkFreeList(mPartiallyFreeList[vParentLogSize].mips[0], EBlockState::PartiallyFreeList, parentIndex);

    const UInt32 vAddress = mAddressBlocks[parentIndex].vAddress;
    const UInt32 sortedIndex = FindAddressBlock(vAddress);
    Assert(vAddress == mSortedAddresses[sortedIndex]);

    // Make room for newly added
    mSortedAddresses.insert(mSortedAddresses.begin() + sortedIndex, numChildren - 1u, 0u);
    mSortedIndices.insert(mSortedIndices.begin() + sortedIndex, numChildren - 1u, 0u);
    Assert(mSortedAddresses.size() == mSortedIndices.size());

    UInt16 firstSiblingIndex = 0xffff;
    UInt16 prevChildIndex = 0xffff;
    for (UInt32 sibling = 0; sibling < numChildren; sibling++)
    {
        const UInt16 childBlockIndex = AcquireBlock();
        const UInt32 vChildAddress = vAddress + (sibling << (mDimensions * vChildLogSize));

        const UInt32 sortedIndexOffset = numChildren - 1 - sibling;
        mSortedAddresses[sortedIndex + sortedIndexOffset] = vChildAddress;
        mSortedIndices[sortedIndex + sortedIndexOffset] = childBlockIndex;

        if (sibling == 0u)
        {
            firstSiblingIndex = childBlockIndex;
            mAddressBlocks[parentIndex].firstChild = childBlockIndex;
        }
        else
        {
            mAddressBlocks[prevChildIndex].nextSibling = childBlockIndex;
        }

        FAddressBlock childBlock(vChildLogSize);
        childBlock.vAddress = vChildAddress;
        childBlock.parent = parentIndex;
        childBlock.firstSibling = firstSiblingIndex;
        childBlock.nextSibling = 0xffff;
        mAddressBlocks[childBlockIndex] = childBlock;

        // New child blocks start out on the free list
        LinkFreeList(mFreeList[vChildLogSize], EBlockState::FreeList, childBlockIndex);

        prevChildIndex = childBlockIndex;
    }
}
void VTMemoryAllocator::MarkBlockAllocated(UInt16 index, UInt32 vAllocatedTileX0, UInt32 vAllocatedTileY0, AllocatedVirtualTexture* VT)
{
    FAddressBlock* allocBlock = &mAddressBlocks[index];
    Assert(allocBlock->state != EBlockState::None);
    Assert(allocBlock->state != EBlockState::GlobalFreeList);

    const UInt32 vLogSize = allocBlock->vLogSize;

    // Assert to see if block is in the correct position
    const UInt32 vAllocatedTileX1 = vAllocatedTileX0 + VT->GetWidthInTiles();
    const UInt32 vAllocatedTileY1 = vAllocatedTileY0 + VT->GetHeightInTiles();
    const UInt32 blockSize = (1u << vLogSize);
    const UInt32 vBlockAddress = allocBlock->vAddress;
    const UInt32 vBlockTileX0 = VTMath::ReverseMortonCode2(vBlockAddress);
    const UInt32 vBlockTileY0 = VTMath::ReverseMortonCode2(vBlockAddress >> 1);
    const UInt32 vBlockTileX1 = vBlockTileX0 + blockSize;
    const UInt32 vBlockTileY1 = vBlockTileY0 + blockSize;

    if (vAllocatedTileX1 > vBlockTileX0 && vAllocatedTileX0 < vBlockTileX1 && vAllocatedTileY1 > vBlockTileY0 && vAllocatedTileY0 < vBlockTileY1)
    {
        // block overlaps the VT we're trying to allocate
        if (vBlockTileX0 >= vAllocatedTileX0 && vBlockTileX1 <= vAllocatedTileX1 && vBlockTileY0 >= vAllocatedTileY0 && vBlockTileY1 <= vAllocatedTileY1)
        {
            // block is entirely contained within the VT we're trying to allocate
            // In this case, block must be completely free (or else there's an error somewhere else)
            Assert(allocBlock->firstChild == 0xffff);
            UnlinkFreeList(mFreeList[vLogSize], EBlockState::FreeList, index);

            ++mNumAllocations;
            mNumAllocatedPages += 1u << (mDimensions * vLogSize);

            // Add to hash table
            UInt16 key = static_cast<UInt16>(reinterpret_cast<UInt64>(VT) / 16);
            mHashTable.emplace(key, index);

            allocBlock->VT = VT;
            allocBlock->state = EBlockState::AllocatedTexture;
        }
        else
        {
            // block intersects the VT
            if (allocBlock->state == EBlockState::FreeList)
            {
                // If block is completely free, need to subdivide further
                SubdivideBlock(index);
            }
            // otherwise block is already subdivided
            allocBlock = nullptr;   // list will be potentially reallocated
            Assert(mAddressBlocks[index].state == EBlockState::PartiallyFreeList);

            UInt32 numChildren = 0u;
            UInt16 childIndex = mAddressBlocks[index].firstChild;
            Assert(childIndex == mAddressBlocks[childIndex].firstSibling);
            while (childIndex != 0xffff)
            {
                Assert(mAddressBlocks[childIndex].parent == index);

                MarkBlockAllocated(childIndex, vAllocatedTileX0, vAllocatedTileY0, VT);

                childIndex = mAddressBlocks[childIndex].nextSibling;
                numChildren++;
            }
            Assert(numChildren == (1u << mDimensions));
        }
    }
}
void VTMemoryAllocator::FreeMipUpdateParents(UInt16 parentIndex) 
{
    for (UInt32 parentDepth = 0; parentDepth < PartiallyFreeMipDepth; parentDepth++)
    {
        if (parentIndex == 0xffff)
        {
            break;
        }

        FAddressBlock& parentBlock = mAddressBlocks[parentIndex];
        UInt8 oldFreeMip = parentBlock.freeMip;
        UInt8 newFreeMip = ComputeFreeMip(parentIndex);

        if (newFreeMip != oldFreeMip)
        {
            UnlinkFreeList(mPartiallyFreeList[parentBlock.vLogSize].mips[oldFreeMip], EBlockState::PartiallyFreeList, parentIndex);
            parentBlock.freeMip = newFreeMip;
            LinkFreeList(mPartiallyFreeList[parentBlock.vLogSize].mips[newFreeMip], EBlockState::PartiallyFreeList, parentIndex);
        }

        parentIndex = parentBlock.parent;
    }
}

UInt8 VTMemoryAllocator::ComputeFreeMip(UInt16 BlockIndex) const
{
    // First we need to generate a map of the block, where data is allocated.  This is an
    // 8x8 pixel map in bits in a single 64-bit word.

    UInt64 blockMap = 0;
    RecurseComputeFreeMip(BlockIndex, 0, blockMap);

    // Mapping that specifies pixels that need to be covered by child blocks to block any allocation at the
    // given alignment.  If the corner pixel at a given resolution alone is covered, we can't allocate,
    // because aligned block addresses always start at pixel corners.  But if not, there is potential to
    // squeeze an allocation in there (at least a 1x1 if nothing else).  For finer granularity alignments,
    // we need to Assert multiple pixel corners to cover all allocation offsets.  Allocation offsets
    // correspond to steps by the "vAddressAlignment" variable in FVirtualTextureAllocator::Alloc.  Here's
    // a diagram of the pixels we are looking at, and the corresponding bytes.  X increases as you go left
    // in bits, and Y increases as you go left in bytes:
    //
    //                               1x1     2x2     4x4     8x8
    //        8 1 2 1 4 1 2 1        0xff    0x33    0x11    0x01
    //        1 1 1 1 1 1 1 1        0xff    0x00    0x00    0x00
    //        2 1 2 1 2 1 2 1        0xff    0x33    0x00    0x00
    //        1 1 1 1 1 1 1 1        0xff    0x00    0x00    0x00
    //        4 1 2 1 4 1 2 1        0xff    0x33    0x11    0x00
    //        1 1 1 1 1 1 1 1        0xff    0x00    0x00    0x00
    //        2 1 2 1 2 1 2 1        0xff    0x33    0x00    0x00
    //        1 1 1 1 1 1 1 1        0xff    0x00    0x00    0x00
    //
    static const UInt64 blockOverlapByDepth[4] = {
        0x0000000000000001ull,   // 8x8 block
        0x0000001100000011ull,   // 4x4 block
        0x0033003300330033ull,   // 2x2 block
        0xffffffffffffffffull,   // 1x1 block (unused by code, just here for reference)
    };

    UInt8 freeMip;
    for (freeMip = 0; freeMip < 3; freeMip++)
    {
        // Are all the corner pixels at this alignment resolution covered?  If not,
        // break out of the loop.
        if ((blockOverlapByDepth[freeMip] & blockMap) != blockOverlapByDepth[freeMip])
        {
            break;
        }
    }

    return freeMip;
}
void VTMemoryAllocator::RecurseComputeFreeMip(UInt16 blockIndex, UInt32 depth, UInt64& ioBlockMap) const
{
    const FAddressBlock& block = mAddressBlocks[blockIndex];

    // Add children first...
    if (depth < 3)
    {
        UInt16 childIndex = block.firstChild;
        while (childIndex != 0xffff)
        {
            RecurseComputeFreeMip(childIndex, depth + 1, ioBlockMap);
            childIndex = mAddressBlocks[childIndex].nextSibling;
        }
    }

    if (block.state == EBlockState::AllocatedTexture)
    {
        // Bit mask of pixels covered by a block of the given log2 size.  Think of each byte
        // as a row of 8 single bit pixels, moving left in bits representing increasing X, and bytes
        // increasing Y, similar to how a linear 2D array of texels is usually arranged.  Here's a
        // diagram of pixels that are covered by increasing powers of 2, and the corresponding bytes,
        // to show where the entries in the table come from:
        //
        //                               1x1     2x2     4x4     8x8
        //        1 2 4 4 8 8 8 8        0x01    0x03    0x0f    0xff
        //        2 2 4 4 8 8 8 8        0x00    0x03    0x0f    0xff
        //        4 4 4 4 8 8 8 8        0x00    0x00    0x0f    0xff
        //        4 4 4 4 8 8 8 8        0x00    0x00    0x0f    0xff
        //        8 8 8 8 8 8 8 8        0x00    0x00    0x00    0xff
        //        8 8 8 8 8 8 8 8        0x00    0x00    0x00    0xff
        //        8 8 8 8 8 8 8 8        0x00    0x00    0x00    0xff
        //        8 8 8 8 8 8 8 8        0x00    0x00    0x00    0xff
        //
        static const UInt64 blockMaskByDepth[4] = {
            0xffffffffffffffffull,   // 8x8 block
            0x000000000f0f0f0full,   // 4x4 block
            0x0000000000000303ull,   // 2x2 block
            0x0000000000000001ull,   // 1x1 block
        };

        // Absolute address
        UInt32 X = VTMath::ReverseMortonCode2(block.vAddress);
        UInt32 Y = VTMath::ReverseMortonCode2(block.vAddress >> 1);

        // Parent block relative address.  Let's say the original parent block was 32 by 32 -- these
        // bit mask operations would mask out the bottom 5 bits of the address for any given block,
        // producing offsets in the range [0..32).  For the original parent, depth will be zero, and
        // block.vLogSize would be 5, so the mask generated would be (1 << (5 + 0)) - 1 == 0x1f.  For
        // the next child, block.vLogSize would be 4, and depth 1, generating the same 0x1f mask, and
        // so on.
        UInt32 relativeX = X & ((1 << (block.vLogSize + depth)) - 1);
        UInt32 relativeY = Y & ((1 << (block.vLogSize + depth)) - 1);

        // Mapping address (8x8).  For our 32x32 case, we want to map our [0..32) range to [0..8), which
        // requires us to divide by 32 and multiply by 8.  In bit shifts, that means a right shift by
        // block.vLogSize + depth == 5, and a left shift by 3.  If the original tile is smaller than 8x8,
        // the shift could go negative, so we need to handle that case.
        SInt32 mapShift = (block.vLogSize + depth - 3);
        UInt32 mapX;
        UInt32 mapY;
        if (mapShift >= 0)
        {
            mapX = relativeX >> mapShift;
            mapY = relativeY >> mapShift;
        }
        else
        {
            mapX = relativeX << (-mapShift);
            mapY = relativeY << (-mapShift);
        }

        // Set bits in our bitmap
        UInt32 bitIndex = mapX + mapY * 8;
        ioBlockMap |= blockMaskByDepth[depth] << bitIndex;
    }
}
}   // namespace cross
