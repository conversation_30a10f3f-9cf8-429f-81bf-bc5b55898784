#pragma once
#include "NativeGraphicsInterface/NGI.h"
/*-----------------------------------------------------------------------------
    Binary Heap, used to index another data structure.

    Also known as a priority queue. Smallest key at top.
    KeyType must implement operator<
-----------------------------------------------------------------------------*/
template<typename KeyType = UInt32, typename IndexType = UInt16>
class BinaryHeap
{
public:
    BinaryHeap();
    BinaryHeap(KeyType InHeapSize, IndexType InIndexSize);
    ~BinaryHeap();

    BinaryHeap(const BinaryHeap&) = delete;
    void operator=(const BinaryHeap&) = delete;
    BinaryHeap(BinaryHeap&& Other);

    void Clear();
    void Free();
    void Resize(KeyType NewHeapSize, IndexType NewIndexSize);

    bool IsEmpty() const
    {
        return HeapNum == 0;
    }
    KeyType Num() const
    {
        return HeapNum;
    }
    KeyType GetHeapSize() const
    {
        return HeapSize;
    }
    IndexType GetIndexSize() const
    {
        return IndexSize;
    }

    bool IsPresent(IndexType Index) const;
    KeyType GetKey(IndexType Index) const;
    IndexType Peek(IndexType Index) const;

    IndexType Top() const;
    void Pop();

    void Add(KeyType Key, IndexType Index);
    void Update(KeyType Key, IndexType Index);
    void Remove(IndexType Index);

private:
    void ResizeHeap(KeyType NewHeapSize);
    void ResizeIndexes(IndexType NewIndexSize);

    void UpHeap(IndexType HeapIndex);
    void DownHeap(IndexType HeapIndex);

    /**
     * Reset internal variables to a cleared state, does not free data.
     */
    void ResetInternal();

    KeyType HeapNum;
    KeyType HeapSize;
    IndexType IndexSize;

    IndexType* Heap;

    KeyType* Keys;
    IndexType* HeapIndexes;
};


#include "VirtualTextureMath.h"

template<typename KeyType, typename IndexType>
BinaryHeap<KeyType, IndexType>::BinaryHeap()
{
    ResetInternal();
}

template<typename KeyType, typename IndexType>
BinaryHeap<KeyType, IndexType>::BinaryHeap(KeyType InHeapSize, IndexType InIndexSize)
    : HeapNum(0)
    , HeapSize(InHeapSize)
    , IndexSize(InIndexSize)
{
    Heap = new IndexType[HeapSize];
    Keys = new KeyType[IndexSize];
    HeapIndexes = new IndexType[IndexSize];

    std::memset(HeapIndexes, 0xff, IndexSize * sizeof(IndexType));
}

template<typename KeyType, typename IndexType>
BinaryHeap<KeyType, IndexType>::BinaryHeap(BinaryHeap<KeyType, IndexType>&& Other)
{
    HeapNum = Other.HeapNum;
    HeapSize = Other.HeapSize;
    IndexSize = Other.IndexSize;

    Heap = Other.Heap;
    Keys = Other.Keys;
    HeapIndexes = Other.HeapIndexes;

    Other.ResetInternal();
}

template<typename KeyType, typename IndexType>
BinaryHeap<KeyType, IndexType>::~BinaryHeap()
{
    Free();
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::Clear()
{
    HeapNum = 0;
    std::memset(HeapIndexes, 0xff, IndexSize * sizeof(IndexType));
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::Free()
{
    delete[] Heap;
    delete[] Keys;
    delete[] HeapIndexes;

    ResetInternal();
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::ResizeHeap(KeyType NewHeapSize)
{
    Assert(NewHeapSize != HeapSize);

    if (NewHeapSize == 0)
    {
        HeapNum = 0;
        HeapSize = 0;

        delete[] Heap;
        Heap = nullptr;

        return;
    }

    IndexType* NewHeap = new IndexType[NewHeapSize];

    if (HeapSize != 0)
    {
        std::memcpy(NewHeap, Heap, HeapSize * sizeof(IndexType));
        delete[] Heap;
    }

    HeapNum = std::min(HeapNum, NewHeapSize);
    HeapSize = NewHeapSize;
    Heap = NewHeap;
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::ResizeIndexes(IndexType NewIndexSize)
{
    Assert(NewIndexSize != IndexSize);

    if (NewIndexSize == 0)
    {
        IndexSize = 0;

        delete[] Keys;
        delete[] HeapIndexes;

        Keys = nullptr;
        HeapIndexes = nullptr;

        return;
    }

    KeyType* NewKeys = new KeyType[NewIndexSize];
    IndexType* NewHeapIndexes = new IndexType[NewIndexSize];

    if (IndexSize != 0)
    {
        Assert(NewIndexSize >= IndexSize);

        for (UInt32 i = 0; i < IndexSize; i++)
        {
            NewKeys[i] = Keys[i];
            NewHeapIndexes[i] = HeapIndexes[i];
        }
        delete[] Keys;
        delete[] HeapIndexes;
    }

    for (UInt32 i = IndexSize; i < NewIndexSize; i++)
    {
        NewHeapIndexes[i] = (IndexType)-1;
    }

    IndexSize = NewIndexSize;
    Keys = NewKeys;
    HeapIndexes = NewHeapIndexes;
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::Resize(KeyType NewHeapSize, IndexType NewIndexSize)
{
    if (NewHeapSize != HeapSize)
    {
        ResizeHeap(NewHeapSize);
    }

    if (NewIndexSize != IndexSize)
    {
        ResizeIndexes(NewIndexSize);
    }
}

template<typename KeyType, typename IndexType>
bool BinaryHeap<KeyType, IndexType>::IsPresent(IndexType Index) const
{
    if (Index >= IndexSize)
    {
        return false;
    }
    return HeapIndexes[Index] != (IndexType)-1;
}

template<typename KeyType, typename IndexType>
KeyType BinaryHeap<KeyType, IndexType>::GetKey(IndexType Index) const
{
    Assert(IsPresent(Index));
    return Keys[Index];
}

template<typename KeyType, typename IndexType>
IndexType BinaryHeap<KeyType, IndexType>::Peek(IndexType Index) const
{
    Assert(Index < HeapNum);
    return Heap[Index];
}

template<typename KeyType, typename IndexType>
IndexType BinaryHeap<KeyType, IndexType>::Top() const
{
    Assert(Heap);
    Assert(HeapNum > 0);
    return Heap[0];
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::Pop()
{
    Assert(Heap);
    Assert(HeapNum > 0);

    IndexType Index = Heap[0];

    Heap[0] = Heap[--HeapNum];
    HeapIndexes[Heap[0]] = 0;
    HeapIndexes[Index] = (IndexType)-1;

    DownHeap(0);
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::Add(KeyType Key, IndexType Index)
{
    if (HeapNum == HeapSize)
    {
        ResizeHeap(std::max<UInt32>(32u, HeapSize * 2));
    }

    if (Index >= IndexSize)
    {
        ResizeIndexes(std::max<IndexType>(32u, static_cast<IndexType>(VTMath::RoundUpToPowerOfTwo(Index + 1))));
    }

    Assert(!IsPresent(Index));

    IndexType HeapIndex = static_cast<IndexType>(HeapNum++);
    Heap[HeapIndex] = Index;

    Keys[Index] = Key;
    HeapIndexes[Index] = HeapIndex;

    UpHeap(HeapIndex);
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::Update(KeyType Key, IndexType Index)
{
    Assert(Heap);
    //Assert(IsPresent(Index));
    if (!IsPresent(Index))
    {
        return;
    }

    Keys[Index] = Key;

    IndexType HeapIndex = HeapIndexes[Index];
    IndexType Parent = (HeapIndex - 1) >> 1;
    if (HeapIndex > 0 && Key < Keys[Heap[Parent]])
    {
        UpHeap(HeapIndex);
    }
    else
    {
        DownHeap(HeapIndex);
    }
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::Remove(IndexType Index)
{
    if (!IsPresent(Index))
    {
        return;
    }

    KeyType Key = Keys[Index];
    IndexType HeapIndex = HeapIndexes[Index];

    Heap[HeapIndex] = Heap[--HeapNum];
    HeapIndexes[Heap[HeapIndex]] = HeapIndex;
    HeapIndexes[Index] = (IndexType)-1;

    if (Key < Keys[Heap[HeapIndex]])
    {
        DownHeap(HeapIndex);
    }
    else
    {
        UpHeap(HeapIndex);
    }
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::UpHeap(IndexType HeapIndex)
{
    IndexType Moving = Heap[HeapIndex];
    IndexType i = HeapIndex;
    IndexType Parent = (i - 1) >> 1;

    while (i > 0 && Keys[Moving] < Keys[Heap[Parent]])
    {
        Heap[i] = Heap[Parent];
        HeapIndexes[Heap[i]] = i;

        i = Parent;
        Parent = (i - 1) >> 1;
    }

    if (i != HeapIndex)
    {
        Heap[i] = Moving;
        HeapIndexes[Heap[i]] = i;
    }
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::DownHeap(IndexType HeapIndex)
{
    IndexType Moving = Heap[HeapIndex];
    IndexType i = HeapIndex;
    IndexType Left = (i << 1) + 1;
    IndexType Right = Left + 1;

    while (Left < HeapNum)
    {
        IndexType Smallest = Left;
        if (Right < HeapNum)
        {
            Smallest = (Keys[Heap[Left]] < Keys[Heap[Right]]) ? Left : Right;
        }

        if (Keys[Heap[Smallest]] < Keys[Moving])
        {
            Heap[i] = Heap[Smallest];
            HeapIndexes[Heap[i]] = i;

            i = Smallest;
            Left = (i << 1) + 1;
            Right = Left + 1;
        }
        else
        {
            break;
        }
    }

    if (i != HeapIndex)
    {
        Heap[i] = Moving;
        HeapIndexes[Heap[i]] = i;
    }
}

template<typename KeyType, typename IndexType>
void BinaryHeap<KeyType, IndexType>::ResetInternal()
{
    HeapNum = 0;
    HeapSize = 0;
    IndexSize = 0;
    Heap = nullptr;
    Keys = nullptr;
    HeapIndexes = nullptr;
}
