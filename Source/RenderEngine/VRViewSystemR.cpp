#include "EnginePrefix.h"
#include "VRViewSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/CameraSystemR.h"
#include "NativeGraphicsInterface/NGIXRRuntime.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RendererSystemR.h"

cross::ecs::ComponentDesc* cross::VRViewComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::VRViewComponentR>(false);
}

void cross::VRViewSystemR::SetViewID(ecs::EntityID entityID, UInt32 viewID)
{
    auto vrViewH = mRenderWorld->GetComponent<VRViewComponentR>(entityID);
    vrViewH.Write()->mViewID = viewID;
}

void cross::VRViewSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [this]
    {
        SCOPED_CPU_TIMING(GroupRendering, "VRViewSystemRUpdate");
        auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        if (rdrSys->GetXRFrameInfo().Running)
        {
            auto* red = rdrSys->GetRenderingExecutionDescriptor();
            auto* camSys = mRenderWorld->GetRenderSystem<CameraSystemR>();
            auto xrRuntime = GetXRRuntime();

            auto queryResult = mRenderWorld->Query<CameraComponentR, VRViewComponentR>();
            for (auto [camH, vrViewH] : queryResult)
            {
                auto viewID = vrViewH.Read()->mViewID;
                if (viewID < xrRuntime->GetViewCount())
                {
                    auto camReader = camH.Read();
                    auto renderCam = camSys->GetRenderCamera(camReader);
                    auto renderPipeline = camSys->GetRenderPipeline(camReader);
                    {
                        auto swapchain = xrRuntime->GetColorSwapchain(viewID);
                        auto bufferIdx = swapchain->GetCurrentBackBufferIndex();
                        auto targetTex = swapchain->GetBuffer(bufferIdx);
                        auto redTex = red->AllocateTexture(fmt::format("VRView: {}", viewID), targetTex);
                        NGITextureViewDesc targetViewDesc
                        {
                            NGITextureUsage::CopyDst | NGITextureUsage::RenderTarget,
                            targetTex->GetDesc().Format, 
                            NGITextureType::Texture2D, 
                            { 
                                NGITextureAspect::Color, 0, 1, 0, 1,
                            }
                        };
                        auto redTexView = red->AllocateTextureView(redTex, targetViewDesc);
                        redTexView->SetExternalState(NGIResourceState::Present);
                        renderPipeline->SetTargetView(redTexView);
                    }
                    {
                        auto swapchain = xrRuntime->GetDepthSwapchain(viewID);
                        auto bufferIdx = swapchain->GetCurrentBackBufferIndex();
                        auto targetTex = swapchain->GetBuffer(bufferIdx);
                        auto redTex = red->AllocateTexture(fmt::format("VRViewDepth: {}", viewID), targetTex);
                        NGITextureViewDesc targetViewDesc
                        {
                            NGITextureUsage::CopyDst | NGITextureUsage::DepthStencil,
                            targetTex->GetDesc().Format,
                            NGITextureType::Texture2D,
                            {
                                NGITextureAspect::Depth | NGITextureAspect::Stencil, 0, 1, 0, 1,
                            }
                        };
                        auto redTexView = red->AllocateTextureView(redTex, targetViewDesc);

                        redTexView->SetExternalState(NGIResourceState::TargetReadWrite);
                        renderPipeline->GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>() = redTexView;
                    }
                    xrRuntime->SetViewClipRange(viewID, renderCam->GetNearPlane() / 1000, renderCam->GetFarPlane() / 1000);
                }
            }
        }
    });
}