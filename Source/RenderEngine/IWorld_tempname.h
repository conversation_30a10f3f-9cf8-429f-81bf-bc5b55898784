#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "ECS/StoreProxy.h"

namespace cross {
template<typename StoreType>
class IWorld_tempname : public StoreProxy<StoreType>
{
public:
    template<typename Component>
    ecs::ComponentHandle<Component> GetSingletonComponent();

    /*template<typename Component>
    void RuntimeAddSingletonComponent();*/

    template<typename Component>
    void RegisterSingletonComponent()
    {
        static_assert(std::is_same_v<StoreType, ecs::GameStore>, "GameWorld call InitializeSingletonComponents to SetWorldSingletonEntity for RenderWorld.");

        mRegisteredMask.Set(Component::GetDesc()->GetMaskBitIndex(), true);
    }
    
    virtual void InitializeSingletonComponents() = 0;
    
    void SetWorldSingletonEntity(ecs::EntityID entity)
    {
        static_assert(std::is_same_v<StoreType, ecs::RenderStore>, "GameWorld call InitializeSingletonComponents to SetWorldSingletonEntity for RenderWorld.");

        mWorldSingletonEntity = entity;
    }

protected:
    ecs::EntityID mWorldSingletonEntity;
    ecs::ComponentBitMask mRegisteredMask;
};

template<typename StoreType>
template<typename Component>
ecs::ComponentHandle<Component> IWorld_tempname<StoreType>::GetSingletonComponent()
{
    return StoreProxy<StoreType>::template GetComponent<Component>(mWorldSingletonEntity);
}

//template<typename StoreProxyType>
//template<typename Component>
//void IWorld_tempname<StoreProxyType>::RuntimeAddSingletonComponent()
//{
//    reinterpret_cast<StoreProxyType*>(this)->CreateComponents<Component>(mWorldSingletonEntity);
//}
}
