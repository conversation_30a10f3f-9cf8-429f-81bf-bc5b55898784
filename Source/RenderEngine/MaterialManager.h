#pragma once

#ifndef MATERIAL_GPU_SCENE_H
#define MATERIAL_GPU_SCENE_H

// #include "GPUScene/ObjectIndexAllocator.h"
#include "RenderMaterial.h"
#include "GPUScene/GPUScene.h"

namespace cross {

class MaterialManager
{
public:
    UInt32 Allocate(UInt32 byteStride);

    void Free(UInt32 byteStride, UInt32 materialIndexStart);

    void* UploadData(UInt32 sizeInByte, UInt32 dstBufferOffsetInByte, bool clearMemory = true);

    void ResizeGPUBuffer();

    void UploadDataToGPUBuffer();
    
    void MarkMaterialCacheDirty(MaterialR* dirtyMaterial);

    void UpdateMaterialCache();

    void AddMaterialCacheByteSize(int byteSizeChange);
    
    REDResidentBufferView* GetMaterialBufferView(UInt32 stride)
    {
        Assert(mMaterialBufferViews.contains(stride));
        return mMaterialBufferViews[stride].get();
    }
    
    REDResidentBufferView* GetMaterialBufferViewForRayTracing() const
    {
        return mMaterialBufferViews.empty() ? mDummyBufferView.get() : mMaterialBufferViews.begin()->second.get();
    }

private:
    int mMtlScratchByte = 0;
    int mMtlVariantByte = 0;
    std::mutex mMtlAddMutex;
    std::mutex mMarkMaterialCacheByteSizeMutex;
    std::mutex mMarkMaterialDirtyMutex;
    std::set<MaterialR*> mDirtyCacheMaterial;
    
    ObjectIndexAllocator mGPUMaterialBufferIndexAllocator;
    std::mutex mGPUMaterialBufferIndexAllocatorMutex;
    ScatterBytesUploadBuffer mGPUMaterialUploadBuffer;

    REDUniquePtr<REDResidentBuffer> mMaterialBuffer;
    std::unordered_map<UInt32, REDUniquePtr<REDResidentBufferView>> mMaterialBufferViews;
    REDUniquePtr<REDResidentBuffer> mDummyBuffer;
    REDUniquePtr<REDResidentBufferView> mDummyBufferView;
};

}  // namespace cross

#endif
