#include "ShadowCamera.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"

namespace cross {

void DirectionalLightShadowCamera::CalculateOrthBox()
{
    BoundingOrientedBox orthBox = BoundingOrientedBox({0, 0, (mMaxSubjectZ + mMinSubjectZ) / 2}, {mBounds.GetRadius(), mBounds.GetRadius(), /*(mMaxSubjectZ - mMinSubjectZ) / 2*/ 10000000}, {0, 0, 0, 1});
    orthBox.Transform(orthBox, mCameraView.mOrthInvertViewMatrix);
    mCameraView.mOrthBox = orthBox;
}

float DirectionalLightShadowCamera::ComputeTransitionSize() const
{
    float transitionSize = 1.0f;

    if (isDirectionalLight)
    {
        transitionSize = mLightShadowBias / (mMaxSubjectZ - mMinSubjectZ);

        transitionSize *= mWorldSpaceTexelScale * 0.5f;
    }

    const float minTransitionSize = 0.00001f;
    return std::max(transitionSize, minTransitionSize);
}

float DirectionalLightShadowCamera::GetShadowReceiverBias() const
{
    float shadowReceiverBias = 1.0f;

    switch (mLightType)
    {
    case LightType::Directional:
        shadowReceiverBias = 0.9f;
        break;
    case LightType::Spot:
        shadowReceiverBias = 0.5f;
        break;
    case LightType::Point:
        shadowReceiverBias = mShadowSlopeBias;
        break;
    default:
        break;
    }

    return 1.0f - Clamp(shadowReceiverBias, 0.0f, 1.0f);
}

void DirectionalLightShadowCamera::SetupWholeSceneProjection(Float4x4 lightWorldMatrix, UInt32 resolution)
{
    Float3 lightAxisX = Float3(lightWorldMatrix.m00, lightWorldMatrix.m01, lightWorldMatrix.m02).Normalized();
    Float3 lightAxisY = Float3(lightWorldMatrix.m10, lightWorldMatrix.m11, lightWorldMatrix.m12).Normalized();
    Float3 lightAxisZ = Float3(lightWorldMatrix.m20, lightWorldMatrix.m21, lightWorldMatrix.m22).Normalized();
    Float4x4A shadowInvertViewMatrix(Float4(lightAxisX, 0), Float4(lightAxisY, 0), Float4(lightAxisZ, 0), Float4(0, 0, 0, 1));

    mMinSubjectZ = -mBounds.GetRadius();
    mMaxSubjectZ = mBounds.GetRadius();
    if (isDirectionalLight)
    {
        const float depthRangeClamp = 5000;
        mMaxSubjectZ = std::max(mMaxSubjectZ, depthRangeClamp);
        mMinSubjectZ = std::min(mMinSubjectZ, -depthRangeClamp);
    }
    if (std::isnan(mBounds.GetCenter().x) || std::isnan(mBounds.GetCenter().y) || std::isnan(mBounds.GetCenter().z) || mBounds.GetRadius() < 0.0001f)
    {
        // it's denormalized
        return;
    }
    {
        Float4x4A shadowProjMatrix = Float4x4::CreateOrthographic(mBounds.GetRadius() * 2.0f, mBounds.GetRadius() * 2.0f, mMinSubjectZ, mMaxSubjectZ, NGIPlatform::OpenGLES3 == EngineGlobal::GetSettingMgr()->GetRenderMode());

        Float3 shadowWorldPosition = mBounds.GetCenter();

        if (isDirectionalLight)
        {
            const Float4x4A tempShadowVPMatrix = shadowInvertViewMatrix.Inverted() * shadowProjMatrix;

            Float4 transformedPosition = Float4x4::Transform(tempShadowVPMatrix, Float4(shadowWorldPosition, 1.0f));
            transformedPosition /= transformedPosition.w;

            const UInt32 maxDownsampleFactor = 4;

            const float snapX = fmodf(transformedPosition.x, 2.0f / resolution * maxDownsampleFactor);
            const float snapY = fmodf(transformedPosition.y, 2.0f / resolution * maxDownsampleFactor);

            Float4 snappedWorldPosition = Float4x4::Transform(tempShadowVPMatrix.Inverted(), transformedPosition - Float4(snapX, snapY, 0.0f, 0.0f));
            snappedWorldPosition /= snappedWorldPosition.w;

            shadowWorldPosition = Float3(snappedWorldPosition.x, snappedWorldPosition.y, snappedWorldPosition.z);
        }

        shadowInvertViewMatrix.m30 = shadowWorldPosition.x;
        shadowInvertViewMatrix.m31 = shadowWorldPosition.y;
        shadowInvertViewMatrix.m32 = shadowWorldPosition.z;

        mLastFrameCameraView = mCameraView;

        mProjectionMode = CameraProjectionMode::Orthogonal;
        mCameraView.mOrthProjMatrix = shadowProjMatrix;
        mCameraView.mOrthViewMatrix = shadowInvertViewMatrix.Inverted();
        mCameraView.mOrthInvertViewMatrix = shadowInvertViewMatrix;
        mCameraView.mOrthViewProjMatrix = mCameraView.mOrthViewMatrix * mCameraView.mOrthProjMatrix;

        mWorldSpaceTexelScale = mBounds.GetRadius() * 2.0f / resolution;

        UpdateShadowDepthBias();
    }
}

void DirectionalLightShadowCamera::UpdateShadowDepthBias()
{
    float depthBias = 0;
    float slopeScaleDepthBias = 1.0f;

    if (isDirectionalLight)
    {
        depthBias = mLightShadowBias * 10.0f;
        depthBias = depthBias / (mMaxSubjectZ - mMinSubjectZ);
        depthBias = Lerp(depthBias, depthBias * mWorldSpaceTexelScale * 0.5f, mCascadeBiasDistribution);

        slopeScaleDepthBias = mLightShadowSlopeBias * 3.0f;
    }

    mShadowBias = std::max(depthBias, 0.0f);
    mShadowSlopeBias = std::max(depthBias * slopeScaleDepthBias, 0.0f);
    mMaxShadowSlopeBias = 1.0f;
}

}   // namespace cross

bool cross::DirectionalLightShadowCamera::IsBoundingBoxVisible(const BoundingBox& boundingBox) const
{
    Float3 objectCenter, objectExtent;
    boundingBox.GetCenter(&objectCenter);
    boundingBox.GetExtent(&objectExtent);

    const Float3 lightDirection = mLightDirection;
    const Float3 objectToShadowCenter = mBounds.GetCenter() - objectCenter;
    const float projectedDistanceFromShadowOriginAlongLightDir = lightDirection.Dot(objectToShadowCenter);
    const float objectDistanceFromCylinderAxisSq = (-lightDirection * projectedDistanceFromShadowOriginAlongLightDir + objectToShadowCenter).LengthSquared();
    const float combinedRadius = mBounds.GetRadius() + objectExtent.Length();
    const float combinedRadiusSq = combinedRadius * combinedRadius;

    if (mCullingType == CullingType::Shadow)
    {
        return !(objectDistanceFromCylinderAxisSq >= combinedRadiusSq || (projectedDistanceFromShadowOriginAlongLightDir < 0 && objectToShadowCenter.LengthSquared() > combinedRadiusSq) ||
               !mBoundsAccurate.IntersectBox(objectCenter, objectExtent));
    }
    else if (mCullingType == CullingType::Shadow_SimpleCulling)
    {
        return !(objectDistanceFromCylinderAxisSq >= combinedRadiusSq || (projectedDistanceFromShadowOriginAlongLightDir < 0 && objectToShadowCenter.LengthSquared() > combinedRadiusSq));
    }
    else
    {
        return false;
    }
}