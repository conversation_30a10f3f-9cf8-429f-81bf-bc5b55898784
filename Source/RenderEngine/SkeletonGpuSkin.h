#pragma once
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/RenderSystemBase.h"

#include "RenderEngine/SkeletonComponentR.h"
#include "RenderEngine/ModelSystemR.h"

namespace cross 
{ 

namespace GeomertySkinnedMode 
{
    enum Type
    {
        /* The mode indicate the entity skin failed for some reason */
        Dummy,
        /* The mode is used to modify staging buffer before skinned result is copied into mesh vbo directly */
        CpuSkin,
        /* The mode require all materials in IndividualModel, which persist all sub model part & lod information, 'GPU_SKIN' execute in vertex shader */
        GpuGraphicSkin,
        /* The mode */
        GpuComputeSkin,
        /* The mode not implement yet */
        GpuUniformSkin
    };
}

typedef UInt64 PoseMatricesBufferOffset;

struct PoseMatricesStagingBuffer;
using PoseStagingBufferH = THandle<PoseMatricesStagingBuffer>;

class VertexStreamLayoutSkinParameter;

static inline UniqueString Skin_Senmatic_Marco                          = "GPU_SKIN";
static inline UniqueString Skin_Senmatic_Main_Function                  = "ExecuteSkinning";

static inline UniqueString Skin_Semantic_Compute_VertexPosInBuffer = "staticPosIn";
static inline UniqueString Skin_Semantic_Compute_VertexNormalInBufer = "staticNormalIn";
static inline UniqueString Skin_Semantic_Compute_VertexTangentInBuffer = "staticTangentIn";
static inline UniqueString Skin_Semantic_Compute_VertexSkeletonIDInBuffer = "skeletonIDIn";
static inline UniqueString Skin_Semantic_Compute_VertexSkeletonWtInBuffer = "skeletonWtIn";
static inline UniqueString Skin_Semantic_Compute_VertexPosOutBuffer = "staticPosOut";
static inline UniqueString Skin_Semantic_Compute_VertexNormalOutBuffer = "staticNormalOut";
static inline UniqueString Skin_Semantic_Compute_VertexTangentOutBuffer = "staticTangentOut";
static inline UniqueString Skin_Semantic_Compute_VertexSkeletonIDOutBuffer = "skeletonIDOut";
static inline UniqueString Skin_Semantic_Compute_VertexSkeletonWtOutBuffer = "skeletonWtOut";

static inline UniqueString Skin_Senmatic_Compute_PoseStorageBuffer      = "skinningPosesIn";
static inline UniqueString skin_senmatic_compute_IS_FIRST_SKINNING      = "isFirstSkinning";

static inline UniqueString Skin_Senamtic_Graphic_PoseStorageBufferRange  = "skinningVertexCount";
static inline UniqueString Skin_Senamtic_Graphic_PoseStorageBufferOffset = "skinningPosesOffset";

struct PoseMatricesBufferHandle final
{
    PoseMatricesBufferHandle(VertexStreamLayoutSkinParameter const* inSkinParam);
    PoseMatricesBufferHandle(PoseMatricesBufferHandle const& other);

    inline friend bool operator==(PoseMatricesBufferHandle lhs, PoseMatricesBufferHandle rhs) noexcept
    {
        return lhs.mHash.ConvertTo32() == rhs.mHash.ConvertTo32();
    }

    struct Hash
    {
        cross::StringHash32 operator()(const PoseMatricesBufferHandle& x) const
        {
            return x.mHash.ConvertTo32();
        }
    };

    //
    const ecs::EntityID Target = ecs::EntityID::InvalidHandle();
    //
    const PrimaryModelCompH ModelHandle = PrimaryModelCompH::InvalidHandle();
    // 
    const VertexStreamLayoutSkinParameter* SkinParam = nullptr;

private:
    StringHash64 mHash;
    friend struct Hash;
};

struct PoseMatricesStagingBuffer final
{
    StagingBufferWrap BufferWrap{};
    PoseStagingBufferH Cursor = {0};
};

class PoseMatricesFrameBuffer final
{
public:
    static constexpr UInt8 sRingSize = 2;
    static constexpr UInt32 sMatrixStrideInFloat = 16;
    static constexpr UInt32 sPoseBufferArrayWidth = 128 * 128;
    static constexpr UInt32 minSkinnedMeshStreamCount = 5;
    static constexpr UInt32 sPoseBufferArraySize = sPoseBufferArrayWidth * sizeof(float) * sMatrixStrideInFloat;
    static constexpr UInt32 sPoseMatrixCapacity = (UInt32)(sPoseBufferArrayWidth / sMatrixStrideInFloat);

    static std::array<NGIBufferView*, minSkinnedMeshStreamCount> AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, SInt32 inLodlevel);
    //static cross::NGIBufferView* AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, SecondaryModelCompH inSubModelH);
    static std::array<NGIBufferView*, minSkinnedMeshStreamCount> AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, UInt32 inVertexStart, UInt32 inVertexEnd);
    static std::array<NGIBufferView*, minSkinnedMeshStreamCount> AssembleComputeSkinnedVertexBufferView(ModelComponentR::IndividualModel const* inPrimaryModel, UInt32 inMeshPartStart, UInt32 inMeshPartCount, bool isMeshPart);

    // TODO(scolu): refactor this
    static cross::NGIBufferView* AssembleComputeSkinnedPoseBufferView(VertexStreamLayoutSkinParameter const* inSkinParameter, PoseMatricesBufferOffset inPoseBufferOffset, NGIBuffer* inPoseBuffer);

public:
    PoseMatricesFrameBuffer();
    ~PoseMatricesFrameBuffer();

    void Initialize();

    void OnRenderFrameBegin();
    void OnRenderFrameEnd();

    // Upload inPose's matrix into Staging buffer start from writing_offset and return writing_offset + sizeof(inPose)
    PoseStagingBufferH UpdatePoseStagingBuffer(FrameVector<SIMDMatrix>* inPose, FrameVector<MaterialR*>* inAllSubModelMaterials, GeomertySkinnedMode::Type inMode, threading::TaskEventArray& outTaskEventArray);
    // Get pose SSBO which executed in Compute Stage || Vertex Stage depends on their skin mode
    FORCE_INLINE NGIBuffer* GetPoseStorageBuffer() const { return mPoseStorageBuffers[mCurFrameCount % sRingSize].get(); }
    // Get staging buffer writing into pose ssbo buffer
    FORCE_INLINE PoseMatricesStagingBuffer const& GetPoseStagingBuffer() const { return mStagingBuffer; };

private:
    //
    UInt32 mCurFrameCount;
    //
    PoseMatricesStagingBuffer mStagingBuffer;
    //
    std::array<std::unique_ptr<NGIBuffer>, sRingSize> mPoseStorageBuffers;
    //
    std::array<std::unique_ptr<NGIBufferView>, sRingSize> mPoseStorageBufferViews;
};

}