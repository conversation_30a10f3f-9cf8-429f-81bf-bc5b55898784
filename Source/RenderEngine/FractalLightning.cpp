#include "FractalLightning.h"
#include "CECommon/Common/MeshDefines.h"

namespace cross
{

inline Float3 Lerp(const Float3& pos1, const Float3& pos2, float t)
{
    return pos1 + t * (pos2 - pos1);
}

void FractalLightning::Init()
{
    mSubdivideVertexBuffers[0].clear();
    mSubdivideVertexBuffers[1].clear();
    SubdivideVertex tmp;

    for (size_t i = 0; i < mChainTargetPositions.size(); i++)
    {
        tmp.start = i == 0 ? mChainSource : mChainTargetPositions[i - 1];
        tmp.end = mChainTargetPositions[i];
        tmp.up = GetUp(tmp.start, tmp.end, {0, 0, 1});
        if (tmp.up.LengthSquared() < MathUtils::MathEps)
            tmp.up = GetUp(tmp.start, tmp.end, {0, 1, 0});
        tmp.level = 0;
        tmp.parent = static_cast<SInt32>(i) - 1;
        tmp.startTexcoord = 0;
        tmp.endTexcoord = 1;
        mSubdivideVertexBuffers[0].emplace_back(tmp);
    }
    mChainDest = mChainTargetPositions.back();
    mStrikeLength = (mChainDest - mChainSource).LengthSquared();
    if (!MathUtils::IsNearlyZero(mStrikeLength))
    {
        mStrikeLength = sqrt(mStrikeLength) * 0.01f;
    }
}

SInt32 FractalLightning::PatternZigZag(Segment& segment, size_t target, SInt32 parent)
{
    Float2 delta = mStrikeLength * Decay(mSettings.ZigZagDeviationDecay) * Float2(RangedRandom(mRand, mSettings.ZigZagDeviationRight.x, mSettings.ZigZagDeviationRight.y), RangedRandom(mRand, mSettings.ZigZagDeviationUp.x, mSettings.ZigZagDeviationUp.y));
    Float3 jittered = Lerp(segment.start, segment.end, RangedRandom(mRand, mSettings.ZigZagFraction.x, mSettings.ZigZagFraction.y)) + delta.x * segment.right + delta.y * segment.up;
    float midTexcoord = (segment.startTexcoord + segment.endTexcoord) * 0.5f;
    DrawLineRight(segment.start, jittered, segment.right, segment.level, target, parent, segment.startTexcoord, midTexcoord);
    DrawLineRight(jittered, segment.end, segment.right, segment.level, target, static_cast<SInt32>(mSubdivideVertexBuffers[target].size()) - 1, midTexcoord, segment.endTexcoord);
    return static_cast<SInt32>(mSubdivideVertexBuffers[target].size()) - 1;
}

SInt32 FractalLightning::PatternFork(Segment& segment, size_t target, SInt32 parent)
{
    Float2 delta = mStrikeLength * Decay(mSettings.ForkZigZagDeviationDecay) *
                   Float2(RangedRandom(mRand, mSettings.ForkZigZagDeviationRight.x, mSettings.ForkZigZagDeviationRight.y), RangedRandom(mRand, mSettings.ForkZigZagDeviationUp.x, mSettings.ForkZigZagDeviationUp.y));
    Float3 jittered = Lerp(segment.start, segment.end, RangedRandom(mRand, mSettings.ForkFraction.x, mSettings.ForkFraction.y)) + delta.x * segment.right + delta.y * segment.up;
    float midTexcoord = (segment.startTexcoord + segment.endTexcoord) * 0.5f;

    DrawLineRight(segment.start, jittered, segment.right, segment.level, target, parent, segment.startTexcoord, midTexcoord);
    SInt32 forkParent = static_cast<SInt32>(mSubdivideVertexBuffers[target].size()) - 1;
    DrawLineRight(jittered, segment.end, segment.right, segment.level, target, forkParent, midTexcoord, segment.endTexcoord);

    //Float3 forkDir = segment.right.Normalized();

    Float3 f_delta = Decay(mSettings.ForkDeviationDecay) * Float3(RangedRandom(mRand, mSettings.ForkDeviationRight.x, mSettings.ForkDeviationRight.y),
                                                                  RangedRandom(mRand, mSettings.ForkDeviationUp.x, mSettings.ForkDeviationUp.y),
                                                                  RangedRandom(mRand, mSettings.ForkDeviationForward.x, mSettings.ForkDeviationForward.y));
    float f_length = 0.0f;   // RangedRandom(mRand, mForkLength.x, mForkLength.y) * Decay(mForkLengthDecay);
    f_length = Decay({(mChainDest - jittered).Length(), mSettings.ForkLengthDecay}, segment.level) * RangedRandom(mRand, mSettings.ForkLength.x, mSettings.ForkLength.y);
    Float3 f_jittered = jittered + f_length * (f_delta.x * segment.right + f_delta.y * segment.up + f_delta.z * segment.forward).Normalized();
    //draw a fork branch
    DrawLineRight(jittered, f_jittered, segment.forward, segment.level + 1, target, forkParent, 0, 1);
    return static_cast<SInt32>(mSubdivideVertexBuffers[target].size()) - 2;
}

void FractalLightning::Subdivide(bool bFork)
{
    auto target = mLastTarget ^ 1;
    mSubdivideVertexBuffers[target].clear();

    for (size_t i = 0; i < mSubdivideVertexBuffers[mLastTarget].size(); i++)
    {
        Segment seg;
        seg.start = mSubdivideVertexBuffers[mLastTarget][i].start;
        seg.end = mSubdivideVertexBuffers[mLastTarget][i].end;
        seg.center = 0.5 * (seg.start + seg.end);
        seg.up = mSubdivideVertexBuffers[mLastTarget][i].up;
        seg.forward = (seg.end - seg.start).Normalized();
        seg.right = seg.forward.Cross(seg.up).Normalized();
        seg.up = seg.right.Cross(seg.forward).Normalized();
        seg.level = mSubdivideVertexBuffers[mLastTarget][i].level;
        seg.startTexcoord = mSubdivideVertexBuffers[mLastTarget][i].startTexcoord;
        seg.endTexcoord = mSubdivideVertexBuffers[mLastTarget][i].endTexcoord;

        auto oldParent = mSubdivideVertexBuffers[mLastTarget][i].parent;
        Assert(oldParent < static_cast<SInt32>(i));
        auto parent = oldParent;
        if (parent >= 0)
            parent = mSubdivideVertexBuffers[mLastTarget][oldParent].parent;
        SInt32 endPos = -1;
        if (bFork)
            endPos = PatternFork(seg, target, parent);
        else
            endPos = PatternZigZag(seg, target, parent);
        mSubdivideVertexBuffers[mLastTarget][i].parent = endPos;
    }
}

void FractalLightning::Loop(int seed)
{
    mRand = Rand(seed);
    mLastTarget = 0;
    for (auto i = 0u; i < mSettings.Subdivisions; i++)
    {
        mSubdivisionLevel = i;
        //i == 3 || i == 1 || i == 2 || i == 5
        Subdivide(mSettings.PatternMask & (1 << i));
        mLastTarget ^= 1;
    }
}

void FractalLightning::CreateLightningGeometry(GeometryPacketPtr geometryPack, RendererSystemR* rendererSystem, NGIDevice& ngiDevice, UInt32& vertexNum, UInt32& indexNum, UInt32& primitiveNum, Float3 cameraForward)
{
    const auto quadNum = static_cast<UInt32>(mSubdivideVertexBuffers[mLastTarget].size());
    primitiveNum = quadNum * 2;
    indexNum = 6u * quadNum;
    const UInt32 vertexStride = static_cast<UInt32>(sizeof(Float3) + sizeof(Float2) + sizeof(Float2));
    const UInt32 indexSize = static_cast<UInt32>(sizeof(UInt16)) * indexNum;
    VertexStreamLayout layout;
    layout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3, 0);
    layout.AddVertexChannelLayout(VertexChannel::TexCoord0, VertexFormat::Float2, sizeof(Float3));
    layout.AddVertexChannelLayout(VertexChannel::TexCoord1, VertexFormat::Float2, sizeof(Float3) + sizeof(Float2));

    mVertexAttributes.clear();
    mVertexIndex.clear();
    
    UInt16 offset = 0;
    std::vector<Float3> positions(quadNum);
    const float chainLength = (mChainDest - mChainSource).Length();
    auto emplaceVertexAttributes = [chainLength, this](const Float3& position, float animationInfo, const Float2& uv) {
        mVertexAttributes.emplace_back(position.x);
        mVertexAttributes.emplace_back(position.y);
        mVertexAttributes.emplace_back(position.z);
        mVertexAttributes.emplace_back(animationInfo);
        mVertexAttributes.emplace_back(Clamp((position - mChainSource).Length() / chainLength, 0.0f, 1.0f));

        mVertexAttributes.emplace_back(uv.x);
        mVertexAttributes.emplace_back(uv.y);
    };

    for (UInt32 i = 0; i < quadNum; i++)
    {
        auto start = mSubdivideVertexBuffers[mLastTarget][i].start;
        auto end = mSubdivideVertexBuffers[mLastTarget][i].end;
        auto forward = (end - start).Normalized();
        auto right = cameraForward.Cross(forward).SafeNormal();
        if (right.LengthSquared() < MathUtils::MathEps)
            right = mSubdivideVertexBuffers[mLastTarget][i].up.Cross(forward).SafeNormal();
        positions[i] = right;
        auto pa = mSubdivideVertexBuffers[mLastTarget][i].parent;
        if (pa >= 0 && mSubdivideVertexBuffers[mLastTarget][pa].level == mSubdivideVertexBuffers[mLastTarget][i].level)
        {
            auto oldRight = positions[pa];
            auto lerpRight = (oldRight + right).SafeNormal();
            if (lerpRight != Float3::Zero())
                positions[pa] = lerpRight;
        }
    }

    for (UInt32 i = 0; i < quadNum; i++)
    {
        auto start = mSubdivideVertexBuffers[mLastTarget][i].start;
        auto end = mSubdivideVertexBuffers[mLastTarget][i].end;
        auto startTexcoord = mSubdivideVertexBuffers[mLastTarget][i].startTexcoord;
        auto endTexcoord = mSubdivideVertexBuffers[mLastTarget][i].endTexcoord;
        Float3 startRight;
        //positions[i] = end + right;
        auto width = Decay(mSettings.BoltWidth, mSubdivideVertexBuffers[mLastTarget][i].level);
        auto pa = mSubdivideVertexBuffers[mLastTarget][i].parent;
        if (pa >= 0)
        {
            auto parentEnd = mSubdivideVertexBuffers[mLastTarget][pa].end;
            startRight = start + positions[pa] * width;
            Assert(parentEnd == start);
        }
        else
        {
            auto forward = (end - start).Normalized();
            auto right = cameraForward.Cross(forward).SafeNormal();
            if (right.LengthSquared() < MathUtils::MathEps)
                right = mSubdivideVertexBuffers[mLastTarget][i].up.Cross(forward).SafeNormal();
            startRight = start + right * width;
        }
        
        float animationInfo = 0.0f;
        if (mSubdivideVertexBuffers[mLastTarget][i].level == 0)
            animationInfo = 1.0f;

        Float3 pos;
        if (pa < 0 || (mSubdivideVertexBuffers[mLastTarget][i].level != mSubdivideVertexBuffers[mLastTarget][pa].level))
        {
            pos = start;
            emplaceVertexAttributes(pos, animationInfo, {startTexcoord, 0});

            pos = startRight;   // start + right;
            emplaceVertexAttributes(pos, animationInfo, {startTexcoord, 1});

            pos = end;
            emplaceVertexAttributes(pos, animationInfo, {endTexcoord, 0});

            pos = end + positions[i] * width;   // end + right;
            emplaceVertexAttributes(pos, animationInfo, {endTexcoord, 1});

            mVertexIndex.emplace_back(offset + 0);
            mVertexIndex.emplace_back(offset + 1);
            mVertexIndex.emplace_back(offset + 3);
            mVertexIndex.emplace_back(offset + 0);
            mVertexIndex.emplace_back(offset + 3);
            mVertexIndex.emplace_back(offset + 2);

            mSubdivideVertexBuffers[mLastTarget][i].parent = offset + 2;
            offset += 4;
        }
        else
        {
            pos = end;
            emplaceVertexAttributes(pos, animationInfo, {endTexcoord, 0});

            pos = end + positions[i] * width;   // end + right;
            emplaceVertexAttributes(pos, animationInfo, {endTexcoord, 1});

            auto parentOffset = mSubdivideVertexBuffers[mLastTarget][pa].parent;

            mVertexIndex.emplace_back(parentOffset + 0);
            mVertexIndex.emplace_back(parentOffset + 1);
            mVertexIndex.emplace_back(offset + 1);
            mVertexIndex.emplace_back(parentOffset + 0);
            mVertexIndex.emplace_back(offset + 1);
            mVertexIndex.emplace_back(offset + 0);

            mSubdivideVertexBuffers[mLastTarget][i].parent = offset;
            offset += 2;
        }
    }

    vertexNum = offset;
    const UInt32 vertexSize = vertexStride * vertexNum;
    auto vertexBufferNGIWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, vertexSize);
    vertexBufferNGIWrap.MemWrite(0,mVertexAttributes.data(), vertexSize);

    NGIBufferDesc vertexBufferDesc{vertexSize, NGIBufferUsage::VertexBuffer | NGIBufferUsage::CopyDst};
    auto vertexBuffer = ngiDevice.CreateBuffer(vertexBufferDesc, "FractalLightningVertexBuffer");
    rendererSystem->InitializeBuffer(vertexBuffer, vertexBufferNGIWrap.GetNGIBuffer(), NGICopyBuffer{vertexBufferNGIWrap.GetNGIOffset(), 0u, vertexSize}, NGIResourceState::VertexBuffer);
    geometryPack->AddVertexStream(vertexBuffer, vertexSize, 0u, layout);

    auto indexBufferNGIWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, indexSize);
    indexBufferNGIWrap.MemWrite(0, mVertexIndex.data(), indexSize);

    NGIBufferDesc indexBufferDesc{indexSize, NGIBufferUsage::IndexBuffer | NGIBufferUsage::CopyDst};
    auto indexBuffer = ngiDevice.CreateBuffer(indexBufferDesc, "FractalLightningIndexBuffer");
    rendererSystem->InitializeBuffer(indexBuffer, indexBufferNGIWrap.GetNGIBuffer(), NGICopyBuffer{indexBufferNGIWrap.GetNGIOffset(), 0u, indexSize}, NGIResourceState::IndexBuffer);
    geometryPack->SetIndexStream(indexBuffer, indexSize, indexNum);
}

}