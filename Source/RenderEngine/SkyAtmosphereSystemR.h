#pragma once
#include "Math/CrossMath.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "CECommon/SkyAtmosphere/Model.h"

#include "Runtime/GameWorld/SkyAtmosphereSystemG.h"

#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"


namespace cross 
{
struct REDPass;
struct REDTextureView;
struct REDTexture;
struct RenderingExecutionDescriptor;
class ComputeShaderR;

struct SkyAtmoShaderParamBase
{
    PassRawDataParam AtmosphereModel{NAME_ID("ATMOSPHERE"), nullptr, sizeof(atmosphere::AtmosphereParameter)};
    PassFloat3Param SkySpectralRadianceToLuminance{NAME_ID("SKY_SPECTRAL_RADIANCE_TO_LUMINANCE"), Float3::Zero()};
    PassFloat3Param SunSpectralRadianceToLuminance{NAME_ID("SUN_SPECTRAL_RADIANCE_TO_LUMINANCE"), Float3::Zero()};
    PassFloat4x4Param LuminanceFromRadiance{NAME_ID("LUMINANCE_FROM_RADIANCE"), Float4x4::Identity()};
    PassFloatParam AerialPerspStartDepthKM{NAME_ID("AerialPerspStartDepthKM"), 0.001f};
    PassFloat3Param SkyLuminanceFactor{NAME_ID("SkyLuminanceFactor"), Float3::One()};
    PassFloatParam MultiScatteringFactor{NAME_ID("MultiScatteringFactor"), 0.f};
    PassFloatParam Exposure{ NAME_ID("Exposure"), 1.0f };

    PassBoolParam PlanetTopAtWorldOrigin{NAME_ID("PLANET_TOP_AT_ORIGIN"), true};
    PassBoolParam MoonPhase{NAME_ID("USE_MOON_PHASE"), true};
    PassBoolParam RenderSunDisk{NAME_ID("USE_SUN_DISK"), true};
    PassBoolParam RenderMoonDisk{NAME_ID("USE_MOON_DISK"), true};
    PassBoolParam UnitM{NAME_ID("UnitM"), true};
    PassBoolParam HighQualityAP{NAME_ID("HIGH_QUALITY_AP"), true};
    PassBoolParam UseVSM{NAME_ID("ATMOSPHERE_USE_VSM"), false};
    PassBoolParam SampleCloudShadow{NAME_ID("SAMPLE_CLOUD_SHADOW"), false};
    //PassBoolParam UseDoubleTransform{NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true};
    PassBoolParam IlluminanceIsOne{NAME_ID("ILLUMINANCE_IS_ONE"), false};
    PassBoolParam MultiScatterApproxEnabled{NAME_ID("MULTISCATAPPROX_ENABLED"), true};
};

struct SkyAtmoShaderAdvancedVarsParam
{
    PassFloatParam SkySampleCountMin{NAME_ID("SkySampleCountMin"), 4.0f};
    PassFloatParam SkySampleCountMax{NAME_ID("SkySampleCountMax"), 32.0f};
    PassFloatParam SkyDistanceToSampleCountMaxInv{NAME_ID("SkyDistanceToSampleCountMaxInv"), 1 / 150.0f};
    PassFloatParam SkyViewLUTSampleCountMin{NAME_ID("SkyViewLUTSampleCountMin"), 2.0f};
    PassFloatParam SkyViewLUTSampleCountMax{NAME_ID("SkyViewLUTSampleCountMax"), 32.0f};
    PassFloatParam SkyViewLUTDistanceToSampleCountMaxInv{NAME_ID("SkyViewLUTDistanceToSampleCountMaxInv"), 1 / 150.0f};
};
struct SkyRenderingPSParamNGI
{
    PassNGITextureParam TransmittanceLutTexture{NAME_ID("_TransmittanceLutTextureReadOnly"), nullptr};
    PassNGITextureParam MultiScatTexture{NAME_ID("_MultiScatTextureReadOnly"), nullptr};
    PassNGITextureParam SkyViewLutTexture{NAME_ID("_SkyViewLutTextureReadOnly"), nullptr};
    PassNGITextureParam DistantSkyLightTexture{NAME_ID("_DistantSkyLightTextureReadOnly"), nullptr};
    PassNGITextureParam AtmosphereCameraScatteringVolume{NAME_ID("_AtmosphereCameraScatteringVolumeReadOnly"), nullptr};
    //PassFloatParam SunSize{NAME_ID("SunSize"), 0.99999f};
    PassFloatParam Exposure{NAME_ID("Exposure"), 40.0f};
    PassFloatParam APScale{NAME_ID("APScale"), 1.0f};
    PassFloatParam ColorTrans{NAME_ID("ColorTrans"), 1.0f};
};

struct SkyRenderingPSParamRED
{
    PassREDTextureParam TransmittanceLutTexture{ NAME_ID("_TransmittanceLutTextureReadOnly"), nullptr };
    PassREDTextureParam MultiScatTexture{ NAME_ID("_MultiScatTextureReadOnly"), nullptr };
    PassREDTextureParam SkyViewLutTexture{ NAME_ID("_SkyViewLutTextureReadOnly"), nullptr };
    PassREDTextureParam DistantSkyLightTexture{ NAME_ID("_DistantSkyLightTextureReadOnly"), nullptr };
    PassREDTextureParam AtmosphereCameraScatteringVolume{ NAME_ID("_AtmosphereCameraScatteringVolumeReadOnly"), nullptr };
    //PassFloatParam SunSize{NAME_ID("SunSize"), 0.99999f};
    PassFloatParam Exposure{ NAME_ID("Exposure"), 40.0f };
    PassFloatParam APScale{ NAME_ID("APScale"), 1.0f };
    PassFloatParam ColorTrans{NAME_ID("ColorTrans"), 1.0f};
};


struct SkyTransmittanceCSParam
{
    PassREDTextureParam TransmittanceLutTexture{NAME_ID("_TransmittanceLutTexture"), nullptr};
};

struct SkyMultiScatteringCSParam
{
    PassREDTextureParam TransmittanceLutTexture{NAME_ID("_TransmittanceLutTextureReadOnly"), nullptr};
    PassREDTextureParam MultiScatTexture{NAME_ID("_MultiScatTexture"), nullptr};
};

struct DistantSkyLightCSParam
{
    PassREDTextureParam TransmittanceLutTexture{NAME_ID("_TransmittanceLutTextureReadOnly"), nullptr};
    PassREDTextureParam MultiScatTexture{NAME_ID("_MultiScatTextureReadOnly"), nullptr};
    PassREDTextureParam DistantSkyLightTexture{NAME_ID("_DistantSkyLightTexture"), nullptr};
};

struct SkyViewCSParam
{
    PassREDTextureParam TransmittanceLutTexture{NAME_ID("_TransmittanceLutTextureReadOnly"), nullptr};
    PassREDTextureParam MultiScatTexture{NAME_ID("_MultiScatTextureReadOnly"), nullptr};
    PassREDTextureParam SkyViewLutTexture{NAME_ID("_SkyViewLutTexture"), nullptr};
};

struct SkyCameraVolumeCSParam
{
    PassREDTextureParam TransmittanceLutTexture{NAME_ID("_TransmittanceLutTextureReadOnly"), nullptr};
    PassREDTextureParam MultiScatTexture{NAME_ID("_MultiScatTextureReadOnly"), nullptr};
    PassREDTextureParam SkyViewLutTexture{NAME_ID("_SkyViewLutTextureReadOnly"), nullptr};
    PassREDTextureParam AtmosphereCameraScatteringVolume{NAME_ID("_AtmosphereCameraScatteringVolume"), nullptr};
};

struct SkyAtmosphereComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

private:
    bool enable = false;
    bool dirty = true;
    SkyAtmosphereOuterParam outerParam;
    Float3 skyLuminanceFactor;
    float MultiScatteringFactor;
    float HeightFogContribution;
    std::shared_ptr<atmosphere::Model> model = nullptr;
    friend class SkyAtmosphereSystemR;
};

using RenderSkyAtmosphereComponentHandle = ecs::ComponentHandle<SkyAtmosphereComponentR>;
using RenderSkyAtmosphereComponentReader = ecs::ScopedComponentRead<SkyAtmosphereComponentR>;
using RenderSkyAtmosphereComponentWriter = ecs::ScopedComponentWrite<SkyAtmosphereComponentR>;

class SkyAtmosphereSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    RENDER_ENGINE_API static SkyAtmosphereSystemR* CreateInstance();

    virtual void Release() override;

protected:
    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    SkyAtmosphereSystemR();

    ~SkyAtmosphereSystemR();

    RENDER_ENGINE_API void SetAtmosphereEnable(ecs::EntityID entity, bool enable);
    RENDER_ENGINE_API void SetAtmosphereModel(ecs::EntityID entity, std::shared_ptr<atmosphere::Model> inAtmosphere);
    RENDER_ENGINE_API void SetAtmosphereOuterParam(ecs::EntityID entity, SkyAtmosphereOuterParam outerParam, SkyAtmosphereConfig config);

private:
    void PreComputeSingleWavelength(const RenderSkyAtmosphereComponentReader& inComp, cross::RenderingExecutionDescriptor* RED, const Float4x4& mat, const Double3& lambdas, bool blend = false, UInt32 numScattering = 4);

    void CreateTexture(std::string_view name, UInt32 width, UInt32 height, UInt16 depth, REDUniquePtr<REDResidentTexture>& texPtr, REDUniquePtr<REDResidentTextureView>& texViewPtr, REDUniquePtr<REDResidentTextureView>& texViewReadOnlyPtr);

public:
    auto GetSkyAtmoRenderContextFunc()
    {
        return mSetRenderingPassContextFunc;
    }

    bool GetContextReady() const 
    {
        return mSkyAtmoRenderContextReady;
    }

    void SetupGlobalSkyAtmosphereContext(RenderingExecutionDescriptor* inContext, bool useREDView = false)
    {
        mSetGlobalRenderContextFunc(inContext, false);
    }

    Float3 GetTransmittanceTowardsSunAtRenderCamera(const Float3& inLightDir, const RenderCamera* cam);
    Float3 GetTransmittanceTowardsSunAtMainCamera(const Float3& inLightDir);

    Float3 GetTransmittanceTowardsSunAtGroundLevel(const Float3& inLightDir, const Float3& inCameraPosDir)
    {
        if (!GetContextReady())
        {
            return Float3::One();
        }
        return mComputeTransmittanceTowardsSunAtGroundLevelFunc(inLightDir, inCameraPosDir);
    }

    // pre-compute View related textures
    void PreComputeTextures(const RenderCamera* Camera);

    // pre-compute DistantSkyLight textures
    void PreComputeDistantSkyLightTextures();

    // pre-compute view sky view reflection realTime capture
    void PrecomputeSkyViewReflectionRealTimeCaptureLutTexture();

    // pre-compute view sky view reflection realTime capture
    void RenderSkyWithComputeShader(REDTextureView* RenderTarget);

    //Clear sky with (0, 0, 0, 1)
    void ClearSkyWithComputeShader(REDTextureView* RenderTarget);

    void UpdateCloudShadowContext(REDPass* pass) const;

    // Return isWGS84 Mode
    bool GetPlanetTopAtWorldOrigin() const {
        return mPlanetTopAtWorldOrigin;
    }

    REDResidentTextureView* GetSkyViewReflectionRealTimeCaptureLutTextureView()
    {
        return mSkyViewReflectionRealTimeCaptureLutTextureView.get();
    }

    REDResidentTexture* GetDistantSkyLightTexture()
    {
        return mDistantSkyLightTexture.get();
    }

    float GetHeightFogContribution() const
    {
        auto AtmoQueryResult = mRenderWorld->Query<SkyAtmosphereComponentR>();
        if (AtmoQueryResult.GetEntityNum() > 0)
        {
            return AtmoQueryResult[0].Read()->HeightFogContribution;
        }
        return 1.f;
    }
    
    void PrepareRenderSkyAtmosphere();

    // Set view dependent shader parameters
    void SetRenderCameraCaptureViewContext(REDPass* Pass, const RenderCamera* Camera);

    // Set view independent shader parameters
    void SetSkyCameraCaptureViewContext(REDPass* Pass);
    
    void SetSkyViewCaptureViewContext(REDPass* Pass, const Float3& CapturePos, const Float3& ViewForward,
        Float3& SkyCameraOrigin, Float4& SkyPlanetCenterAndViewHeight, Float4x4& SkyViewLutReferential);

private:
    // pre-compute common textures
    void PreComputeTransmittanceAndScattering();

    /*
    *   Get VSM enable
    */
    bool GetVSMAvailiable() const;

    template<typename Vec3>
    Vec3 CEVec3ToUE(const Vec3& InVec3)
    {
        return {
            InVec3.x,
            -InVec3.z,
            InVec3.y
        };
    }

protected:
    friend class SkyAtmosphereSystemG;

    ComputeShaderR* mPrecomputeShader = nullptr;
    ComputeShaderR* mPrecomputeAPShader = nullptr;
    RenderContext mSkyAtmoRenderContext = RenderContext(nullptr);
    bool mSkyAtmoRenderContextReady = false;
    // false represents for PlanetTCenterAtComponentTransform, TODO: if UE use ESkyAtmosphereTransformMode::PlanetTopAtComponentTransform mode, should do a translation (0.f, 0.f, -BottomRadius)
    bool mPlanetTopAtWorldOrigin = true;

    mutable std::function<bool(SkyAtmoShaderParamBase& passParam)> mSetBasePassContextFunc;
    mutable std::function<bool(cross::RenderingExecutionDescriptor* globalRenderContext, bool realTimeCapture, bool useREDView, bool useDisk, bool useVSM)> mSetRenderingPassContextFunc;
    mutable std::function<Float3(const Float3& LightDir, const Float3& inCameraPosDir)> mComputeTransmittanceTowardsSunAtGroundLevelFunc;
    mutable std::function<bool(cross::RenderingExecutionDescriptor* globalRenderContext, bool realTimeCapture)> mSetGlobalRenderContextFunc;

    /*
    *   target pre-computed textures:
    */

    REDUniquePtr<REDResidentTexture> mTransmittanceTexture;
    REDUniquePtr<REDResidentTextureView> mTransmittanceTextureView;
    REDUniquePtr<REDResidentTextureView> mTransmittanceTextureViewReadOnly;

    REDUniquePtr<REDResidentTexture> mMultiScatTexture;
    REDUniquePtr<REDResidentTextureView> mMultiScatTextureView;
    REDUniquePtr<REDResidentTextureView> mMultiScatTextureViewReadOnly;

    REDUniquePtr<REDResidentTexture> mSkyViewLutTexture;
    REDUniquePtr<REDResidentTextureView> mSkyViewLutTextureView;
    REDUniquePtr<REDResidentTextureView> mSkyViewLutTextureViewReadOnly;

    REDUniquePtr<REDResidentTexture> mDistantSkyLightTexture;
    REDUniquePtr<REDResidentTextureView> mDistantSkyLightTextureView;
    REDUniquePtr<REDResidentTextureView> mDistantSkyLightTextureViewReadOnly;

    REDUniquePtr<REDResidentTexture> mSkyViewReflectionRealTimeCaptureLutTexture;
    REDUniquePtr<REDResidentTextureView> mSkyViewReflectionRealTimeCaptureLutTextureView;
    REDUniquePtr<REDResidentTextureView> mSkyViewReflectionRealTimeCaptureLutTextureViewReadOnly;

    REDUniquePtr<REDResidentTexture> mAtmosphereCameraScatteringVolume;
    REDUniquePtr<REDResidentTextureView> mAtmosphereCameraScatteringVolumeView;
    REDUniquePtr<REDResidentTextureView> mAtmosphereCameraScatteringVolumeViewReadOnly;
};


}   // namespace cross
