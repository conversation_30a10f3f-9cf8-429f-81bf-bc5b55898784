#pragma once
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/BindlessResource/BindlessResourceManager.h"

#if CROSSENGINE_WIN
#include <concurrent_vector.h>
#else
#include <vector>
#endif

namespace cross {

class MeshR;

enum class RayTracingInstanceChangeType
{
    Create,
    Skeleton,
    Material,
    Mesh
};

class RayTracingScene
{
public:
    RayTracingScene() = default;

    ~RayTracingScene();

    void Initialize(RenderWorld* renderWorld,
        RenderingExecutionDescriptor* red,
        GPUScene* gpuScene);

    void SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting);
    
    void Update();

    void PostUpdate();

    void RefreshIndex();

    void SetRayTracingSceneDirty(ecs::EntityID entity, RayTracingInstanceChangeType changeType);

    NGIAccelStruct* GetTopLevelAccelStruct() const
    {
        return mAccelStruct.get();
    }

    auto GetSubInstanceBuffer() const
    {
        return mSubInstanceBuffer.get();
    }

    auto GetSubInstanceBufferView() const
    {
        return mSubInstanceBufferView.get();
    }

    void RemoveEntity(ecs::EntityID entity);

    void SetRayTracingResources(REDPass* pass) const;
    
private:
    void CreateTLAS();

    void DestroyTLAS();

    void BuildBLASes();
    
    void BuildTLAS();

    void UpdateSubInstanceData();
    
    void AddUpdateEntity(ecs::EntityID entity, RayTracingInstanceChangeType changeType);

    bool IsEntityValidForRayTracing(ecs::EntityID entity) const;

    UInt32 GetSubInstanceCount() const;

    void CreateBLASPlaceHolder();

    
private:
    RenderWorld* mRenderWorld = nullptr;
    RenderingExecutionDescriptor* mRED = nullptr;
    const FFSRenderPipelineSetting* mFFSRenderPipelineSetting = nullptr;
    GPUScene* mGPUScene = nullptr;

    struct RayTracingInstance
    {
        ecs::EntityID Entity = ecs::EntityID::InvalidHandle();
        RayTracingInstanceChangeType ChangeType;
        UInt32 InstanceID = 0xffffffff;
        UInt32 SubInstanceID = 0xffffffff;

        RayTracingInstance(ecs::EntityID entity, RayTracingInstanceChangeType changeType) : Entity(entity), ChangeType(changeType) {}
    };
    std::vector<RayTracingInstance> mRayTracingInstances;
    std::unordered_set<ecs::EntityID> mRayTracingInstancesSet;
    bool mIsInstanceDataDirty = true;  // true to make sure first frame will update sub instance data
    
    std::unique_ptr<NGIAccelStruct> mAccelStruct = nullptr;
    std::unordered_set<std::tuple<ecs::EntityID, RayTracingInstanceChangeType>> mFrameUpdateEntitySet;
    std::recursive_mutex mMutex;
    
    std::vector<SubInstanceData> mSubInstanceData;
    std::unique_ptr<NGIBuffer> mSubInstanceBuffer = nullptr;
    std::unique_ptr<NGIBufferView> mSubInstanceBufferView = nullptr;

private:
    RendererSystemR* mRendererSystem = nullptr;
    ModelSystemR* mModelSystem = nullptr;
    TransformSystemR* mTransformSystem = nullptr;
    Float3 mMainCameraTilePosition{0.f, 0.f, 0.f};
    EntityLifeCycleRenderDataSystemR* mEntityLifeCycleRenderDataSystem = nullptr;
    NGICommandList* mCmd = nullptr;

    // A single triangle BLAS for creating TLAS empty world
    std::unique_ptr<NGIAccelStruct> mSingleTriangleBLAS;
    std::unique_ptr<NGIBuffer> mSingleTriangleVertexBuffer;
    std::unique_ptr<NGIBuffer> mSingleTriangleIndexBuffer;
};

}
