#pragma once
#include "RenderEngine/RenderEngineForward.h"
#include "NativeGraphicsInterface/NGI.h"
#include "Runtime/Input/LagacyInput.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#include "IMGUINodeEditor/imgui.h"
#include "imgui_extra_math.h"
#include "Resource/Shader.h"
#include <vector>

namespace cross
{
class RendererSystemR;
struct REDThumbnailInfo;

inline NameID gREDGuiViewTextureName = "REDImGuiViewTexture";
inline std::string_view gREDGuiPassName = "REDImGui";

struct RENDER_ENGINE_API REDImGui
{
    REDImGui();

    REDImGui(RendererSystemR* rendererSys);

    virtual ~REDImGui();

    void SetShader(resource::Shader* IMGUIDisplayShader)
    { 
        mIMGUIDisplayShader = IMGUIDisplayShader;
    }
    void InitializeGui();

    void DestroyGui();

    void SetName(NameID gREDViewTextureName, std::string_view gREDPassName);

    virtual void OnMouseEvent(const MouseEvent& e);

    virtual void OnKeyboardEvent(const ButtonEvent& e);

    virtual void GenerateGUI() = 0;

    void OnPaint(const UInt2& size);

    auto GetTexture() const { return mGuiTexture.get(); }

    auto GetTextureView() const { return mGuiTextureView.get(); }

    void Draw(REDPass* pass, NGIBundleCommandList* cmdList);

    void Draw(REDPass* pass, NGIBundleCommandList* cmdList, REDTextureView* textureView);

    ImGuiContext* GetGuiContext() { return mGuiContext; };
    void OnEndFrame();

protected:
    ImGuiContext* mGuiContext;
    NameID mREDViewTextureName;
    std::string_view mREDPassName;

    std::unique_ptr<NGITexture> mFontTexture;

    std::unique_ptr<NGITextureView> mFontTextureView;

    RendererSystemR* mRendererSys = nullptr;

    REDUniquePtr<REDResidentTexture> mGuiTexture;

    REDUniquePtr<REDResidentTextureView> mGuiTextureView;

    // IMGUI Display
    InputLayoutDesc mIMGUIDisplayInputLayout{};
    resource::Shader* mIMGUIDisplayShader;
};

}
