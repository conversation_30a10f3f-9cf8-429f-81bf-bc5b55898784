#pragma once
#include "RenderEngine/RenderEngineForward.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"

#include "RenderEngine/RenderPipeline/RenderPipelineBaseSetting.h"

namespace cross
{
struct CEMeta(Reflect) RENDER_ENGINE_API BuiltinRenderPipelineSetting : public RenderPipelineBaseSetting
{
    CEMeta(Reflect)
    BuiltinRenderPipelineSetting();

    RenderPipelineSetting* Clone() const override;

    void Initialize() override;

    CE_Virtual_Serialize_Deserialize;

    // Expand the first macro to fix the issue that must has at least one CEMeta for CodeGen scanning.
    CEMeta(Serialize, Editor)
    CECSAttribute(JsonProperty("Bloom"))
    CECSAttribute(PropertyInfo(PropertyType = "Auto", DisplayName = "Bloom", ToolTips = "", Category = "General Settings"))
    bool Bloom = false;
    //RENDER_PIPELINE_VALUE(bool, <PERSON>, false, "Bloom", "", "General Settings");
    RENDER_PIPELINE_VALUE(bool, ColorGrading, false, "Color Grading", "", "General Settings");
    RENDER_PIPELINE_RESOURCE(Material, PostProcessMtl, "EngineResource/Material/PostProcess.nda", "Post Process Material", "", "General Settings");

    RENDER_PIPELINE_VALUE(bool, MSAA, false, "MSAA", "", "Anti-Alising Settings");
    RENDER_PIPELINE_VALUE(UInt16, MSAASampleCount, 8, "MSAA Sample Count", "", "Anti-Alising Settings");
    RENDER_PIPELINE_VALUE(bool, TAA, false, "TAA", "", "Anti-Alising Settings");
    RENDER_PIPELINE_VALUE(float, TAACurrentFrameWeight, 0.01f, "TAA Current Frame Weight", "", "Anti-Alising Settings");

    RENDER_PIPELINE_VALUE(bool, SSR, false, "SSR", "", "Screen Space Reflection Settings");
};

}