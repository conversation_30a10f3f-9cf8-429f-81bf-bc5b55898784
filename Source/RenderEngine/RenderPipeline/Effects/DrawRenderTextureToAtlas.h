#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

namespace cross {
class DrawRenderTextureToAtlasPassSettings : public PassSetting
{
public:
    DrawRenderTextureToAtlasPassSettings()
    {
        enable = false;
    }

    //CE_Virtual_Serialize_Deserialize
    RENDER_PIPELINE_RESOURCE_INVISIBLE(ComputeShader, DrawRenderTextureToAtlasShader, "Shader/Features/Imposter/DrawRenderTextureToAtlas.compute.nda", "DrawRenderTextureToAtlas Shader", "", "");

    virtual void Initialize() override;
};

class RENDER_ENGINE_API DrawRenderTextureToAtlasPass : public PassBase<DrawRenderTextureToAtlasPassSettings, DrawRenderTextureToAtlasPass>
{
public:
    DrawRenderTextureToAtlasPass() {}

    static PassDesc GetPassDesc();

    bool ExecuteImp(const GameContext& gameContext);

private:
    const inline static NameID DrawRenderTextureToAtlasPassID = "DrawRenderTextureToAtlasPass";
};

}   // namespace cross