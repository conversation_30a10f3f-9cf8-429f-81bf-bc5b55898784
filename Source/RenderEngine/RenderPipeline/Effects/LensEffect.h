#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
namespace cross {
class CEMeta(Editor, Puerts, PartOf(PostProcessVolumeSetting)) RENDER_ENGINE_API PostProcessLensFlareSetting : public PassSetting
{
public:
    static constexpr UInt32 LENS_FLARE_COUNT = 8;

    float mTargetWidthRatio = 1.0f;
    float mTargetHeightRatio = 1.0f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "float", ToolTips = "FlareSize", bKeyFrame = true))
    float Size = 1.0f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "float", ToolTips = "FlareIntensity", bKeyFrame = true))
    float Intesnity = 1.0f;
    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "float", ToolTips = "FlareThreshold", bKeyFrame = true))
    float Threshold = 100.0f;

    CEMeta(Serialize, Editor, Reflect, ScriptReadWrite, EditorPropertyInfo(PropertyType = "Float4AsColor", ToolTips = "Tint", bKeyFrame = true))
    Float4 Tint = {1, 1, 1, 1};

    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "List", ToolTips = "Tint", bKeyFrame = true))
    std::array<Float4, LENS_FLARE_COUNT> TintPerFlare;

    CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "StringAsResource", ToolTips = "LensFlareBokehShape", bKeyFrame = true))
    std::string LensFlareBokehShape;

    virtual void Initialize() override {}
    CE_Virtual_Serialize_Deserialize;
};
struct LensEffectInput
{
    REDTextureView* inputcolor = nullptr;
    REDTextureView* inputcolorforlensflare = nullptr;
    void GenerateInputData(const GameContext& gameContext);
};
struct LensEffectOutput
{
    REDTextureView* outputcolor = nullptr;
    void SetOutputData(const GameContext& gameContext);
};
struct RENDER_ENGINE_API LensEffect
{
public:
    static constexpr UInt32 mLensFlareStep = 4;
    static constexpr UInt32 Downsamplecount = 4;
    constexpr std::string_view GetPassName()
    {
        return std::string_view("LensEffect");
    }
    void FillInput(const GameContext& gameContext);
    void Execute(const GameContext& gameContext);
    ComputeShaderR* mDownSampleShader = nullptr;
    MaterialR* mPostMat = nullptr;
    UInt32 gameViewWidth{0};
    UInt32 gameViewHeight{0};
    void GenLensFlareSmallTexture(const GameContext& gameContext, REDTextureView* sceneView, REDTextureView*& outViewForLensFlare);
    std::array<REDTextureView*, Downsamplecount> mDownSampleTargets;
    LensEffectInput mInput;
    LensEffectOutput mOutput;
    PostProcessLensFlareSetting mSetting;
    GPUTexture* mBokehShapeTex = nullptr;
};
}   // namespace cross