#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"

namespace cross
{
    class DepthPyramidPassSettings : public PassSetting
    {
    public:
        DepthPyramidPassSettings()
        {
            enable = false;
        }

        CE_Virtual_Serialize_Deserialize

        RENDER_PIPELINE_RESOURCE(ComputeShader, HiZGeneratorShader, "Shader/Features/GPUDriven/DepthPyramid.compute.nda", "HiZ Generator Shader", "", "");
        RENDER_PIPELINE_RESOURCE(ComputeShader, SPDHiZGeneratorShader, "Shader/Features/GPUDriven/DepthPyramidSPD.compute.nda", "SPD HiZ Generator Shader", "", "");
        RENDER_PIPELINE_VALUE(bool, UseConservativeDepth, false, "Use Conservative Depth", "", "")
        RENDER_PIPELINE_VALUE(bool, UseLargerOffset, true, "Use Larger Offset", "", "")

        virtual void Initialize() override;
    };

    class RENDER_ENGINE_API DepthPyramidPass : public PassBase<DepthPyramidPassSettings, DepthPyramidPass>
    {
    public:
        DepthPyramidPass() {}

        static PassDesc GetPassDesc();

        REDTextureView* inputDepthView = nullptr;
        REDTextureView* outputHiZView = nullptr;

        bool ExecuteImp(const GameContext& gameContext, const RenderCamera* camera, bool reverseZ = true, UInt16 firstArraySlice = 0, bool closestDepth = false);

    private:
    };

    struct SpdGlobalAtomicBuffer
    {
        UInt32 counter[6];
    };

    struct SPDCSParam
    {
        PassBoolParam ExternalImgSrc{NAME_ID("EXT_IMG_SRC"), true};
        PassBoolParam ReverseZ{NAME_ID("REVERSEZ"), true};
        PassBoolParam ClosestDepth{NAME_ID("CLOSEST_DEPTH"), false};
        PassUIntParam Mips{NAME_ID("mips"), 1};
        PassUIntParam NumWorkGroups{NAME_ID("numWorkGroups"), 1};
        PassUInt2Param WorkGroupOffset{NAME_ID("workGroupOffset"), {0, 0}};
        PassUInt2Param SrcImgBorder{NAME_ID("SrcImgBorder"), {0, 0}};
        PassREDBufferParam SpdGlobalAtomic{NAME_ID("spdGlobalAtomic"), nullptr};
        /*
        *   textures
        */

        PassREDTextureParam ImgSrc{NAME_ID("imgSrc"), nullptr};
        PassREDTextureParam ImgDst6{NAME_ID("imgDst6"), nullptr};
        PassREDTextureArrayParam ImgDst{NAME_ID("imgDst"), nullptr, 0};
    };
    }