#pragma once
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include "Resource/Material.h"

namespace cross {
class FFSRenderPipeline;
class FFSWorldRenderPipeline;
struct FFSRenderPipelineSetting;

class CEMeta(Editor) RENDER_ENGINE_API StellarMeshRasterizePipelineSetting : public PassSetting
{
public:
    StellarMeshRasterizePipelineSetting()
    {
        //enable = false;
    }

    CE_Virtual_Serialize_Deserialize;
    RENDER_PIPELINE_RESOURCE_INVISIBLE(Material, BasicRasterizeMaterial, "PipelineResource/FFSRP/Shader/Features/StellarMesh/Rasterize/GenerateVisibilityBuffer.nda", "VisbilityBuffer Rasterize Material", "", "");

    void Initialize() override
    {
        // Resource here is actually fx, not material.
        BasicRasterizeMaterialRes = static_cast<resource::Material*>(gAssetStreamingManager->LoadSynchronously(BasicRasterizeMaterial).get());
        BasicRasterizeMaterialR = dynamic_cast<MaterialR*>(BasicRasterizeMaterialRes->GetRenderMaterial());
    }
};

struct StellarMeshRasterizePipelineInput
{
    REDBuffer* mClusterDrawArgBuffer{};
    REDBufferView* mClusterDrawArgBufferUAV;
    REDBufferView* mClusterDrawArgBufferSRV;

    REDBuffer* mVisibleClusterBuffer;
    REDBufferView* mVisibleClusterBufferUAV;
    REDBufferView* mVisibleClusterBufferSRV;
};

struct StellarMeshRasterizePipelineOutput
{
    REDTextureView* mVisibilityBufferView;
    REDTextureView* mDepthStencilView;
};

class RENDER_ENGINE_API StellarMeshRasterizePipeline
{
public:
    StellarMeshRasterizePipeline();
    ~StellarMeshRasterizePipeline();

    void Execute(const GameContext& gameContext);

    StellarMeshRasterizePipelineInput mInput{};
    StellarMeshRasterizePipelineOutput mOutput{};

private:
    struct Impl;
    std::unique_ptr<Impl> pImpl;
};

}
