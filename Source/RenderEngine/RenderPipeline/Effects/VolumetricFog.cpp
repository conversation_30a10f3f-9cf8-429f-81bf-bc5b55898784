#include "VolumetricFog.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/TransformSystemR.h"

#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RendererSystemR.h"

#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"

#include "NativeGraphicsInterface/NGIManager.h"

#define VFOG_LIGHTINJECTION_THREAD_SIZE 32u
#define VFOG_BLOCK_LIGHT_NUM            32u

namespace cross {
PassDesc VolumetricFog::GetPassDesc()
{
    return PassDesc("VoxelVolumetricFog", "Voxel Based Volumetric Fog");
}

void VolumetricFog::SetAdaptiveVolume(float distanceMin, float distanceMax)
{
    if (mCurrentLightDistanceMin < 0.0f)
    {
        mCurrentLightDistanceMin = distanceMin;
        mCurrentLightDistanceMax = distanceMax;
    }
    else
    {
        float deltaTime = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentGameFrameParam()->GetDeltaTime();
        auto delta = std::max(0.0f, mSetting.VFog.AdaptiveSpeed * deltaTime * 10.0f);
        mCurrentLightDistanceMin = MathUtils::Lerp(mCurrentLightDistanceMin, distanceMin, 1.0f - std::exp(-delta));
        mCurrentLightDistanceMax = MathUtils::Lerp(mCurrentLightDistanceMax, distanceMax, 1.0f - std::exp(-delta));
    }

    distanceMax = std::min(mCurrentLightDistanceMax, mCurrentLightDistanceMin + mSetting.VFog.AdaptiveVolumeMaxLength);
    distanceMax = std::max(distanceMax, mCurrentLightDistanceMin + mSetting.VFog.AdaptiveVolumeMinLength);
    mSetting.VFog.StartDistance = mCurrentLightDistanceMin;
    mSetting.VFog.CutOffDistance = distanceMax;
    mSetting.VFog.FadeOutDistance = distanceMax * 0.8f;

    mSetting.VFog.QualityTrade.ZVoxelScale = 0.5f * mSetting.VFog.QualityTrade.ZSliceNum;
}

void VolumetricFog::UpdateFogApplyContext(RenderContext& context, IRenderPipeline* renderPipeline)
{
    auto fogParam = CalculateFogParams();
    context.SetProperty(NAME_ID("VFogNear"), fogParam.FogNear.mValue);
    context.SetProperty(NAME_ID("VFogFar"), fogParam.FogFar.mValue);
    context.SetProperty(NAME_ID("ZDepthParam"), fogParam.ZDepthParam.mValue);
    context.SetProperty(NAME_ID("FogGridSize"), fogParam.FogGridSize.mValue);
}

// For updating lightVolumetricIntensity, which is yet only used in VolumetricFog feature
void VolumetricFog::UpdateVolumetricLight(const GameContext& gameContext)
{
    auto* lightSystem = TYPE_CAST(const LightSystemR*, mWorld->GetRenderSystem<LightSystemR>());
    auto transSys = mWorld->GetRenderSystem<TransformSystemR>();
    auto& lights = gameContext.mRenderPipeline->GetLightList();
    size_t lightCnt = std::min(1024, std::min(mSetting.VFog.QualityTrade.MaxLight, static_cast<int>(lights.size())));
    std::vector<Float4> lightVolumetricIntensity(lightCnt);

    auto& cameraView = mCamera->GetCameraView();
    Float3 front = {cameraView.mInvertViewMatrix.m20, cameraView.mInvertViewMatrix.m21, cameraView.mInvertViewMatrix.m22};
    float distanceMin = std::numeric_limits<float>::infinity();
    float distanceMax = 0.0;

    // Compute the maximum and minimum light influence range using the position and radius of the light list, and
    // modify the fog volume coverage accordingly
    for (int i = 0; i < lightCnt; i++)
    {
        auto& light = lights.at(i);
        auto [lightComp, transformComp, tilePositionComp] = mWorld->GetComponent<LightComponentR, TransformComponentR, TilePositionComponentR>(light);
        Float3 color = lightSystem->GetLightColor(lightComp.Read());
        // Use Volumetric intensity
        color *= lightSystem->GetLightIntensity(lightComp.Read());
        color *= lightSystem->GetLightVolumetricFactor(lightComp.Read()) * mSetting.VFog.LightVolumetricFactor;

        Float3 tranmittance = lightSystem->GetLightTransmittance(lightComp.Read());
        color *= tranmittance;

        lightVolumetricIntensity[i] = Float4(color.x, color.y, color.z, 1.0f);

        if (lightSystem->GetLightType(lightComp.Read()) == LightType::Directional || color.LengthSquared() <= 1e-4)
            continue;

        // Compute light depth ranges for the adaptive fog volume
        Float3 lightPos = transSys->GetWorldTranslation(transformComp.Read());
#if defined(CE_USE_DOUBLE_TRANSFORM)
        Float3 lightTile = transSys->GetTilePosition(tilePositionComp.Read());
        auto camTile = mCamera->GetTilePosition();
        lightPos += (lightTile - camTile) * LENGTH_PER_TILE;
#endif
        float lightRange = lightSystem->GetLightRange(lightComp.Read());
        auto dir = lightPos - mCamera->GetCameraOrigin();
        auto depth = front.Dot(dir);
        if (depth > 0.0f)
        {
            // The lighting radius is lightRange * 0.5
            // Allow an extra 0.3 * lightRange in the volume for light scattering
            distanceMin = std::min(distanceMin, std::max(0.0f, depth - lightRange * 0.8f));
            distanceMax = std::max(distanceMax, depth + lightRange * 0.5f);
        }
    }

    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    SizeType dataByteSize = sizeof(Float4) * lightCnt;
    auto* scratchBuffer = rendererSys->GetScratchBuffer();
    mLightVolumetricIntensityBuffer = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataByteSize);
    mLightVolumetricIntensityBuffer.MemWrite(0, lightVolumetricIntensity.data(), dataByteSize);

    distanceMin = std::min(distanceMin, distanceMax);

    if (mSetting.VFog.EnableAdaptiveVolume)
        SetAdaptiveVolume(distanceMin, distanceMax);
}

Float3 VolumetricFog::GetWGS84Projection(Float3 world)
{
    world *= 0.01f;
    const float a = 6378136.f;
    const float b = 6356755.f;
    const float c = 6378136.f;
    float x0 = world.x;
    float y0 = world.y;
    float z0 = world.z;

    float ydx = y0 / x0;
    float zdx = z0 / x0;

    float factor = 1.f / (a * a) + (ydx * ydx) / (b * b) + (zdx * zdx) / (c * c);
    float x = 1.f / std::sqrt(factor);
    float y = ydx * x;
    float z = zdx * x;

    Float3 ret = Float3(x, y, z);
    if (world.Dot(ret) < 0)
        ret = -ret;

    return ret * 100.f;
}

UInt32 VolumetricFog::PreCalculateLightInjection(UInt32 lightCnt, const std::vector<ecs::EntityID>& lightList, const VolumetricFogParam& params, std::vector<UInt32>& boundGrid, std::vector<UInt32>& lightUseGroup,
                                                 std::vector<UInt32>& blockLightUseGroup)
{
    QUICK_SCOPED_CPU_TIMING("VFogLightProjection");

    auto* lightSystem = mWorld->GetRenderSystem<LightSystemR>();
    auto* transSystem = mWorld->GetRenderSystem<TransformSystemR>();
    threading::ParallelFor(lightCnt, [&](UInt32 lightIdx) {
        // for (UInt32 lightIdx = 0u; lightIdx < lightCnt; lightIdx++) {

        auto lightH = mWorld->GetComponent<LightComponentR>(lightList[lightIdx]).Read();
        auto transH = mWorld->GetComponent<TransformComponentR>(lightList[lightIdx]).Read();
        auto lightEntity = lightList[lightIdx];
        auto lightType = lightSystem->GetLightType(lightH);

        Float3 lightColor = lightSystem->GetLightColor(lightH) * lightSystem->GetLightVolumetricFactor(lightH) * mSetting.VFog.LightVolumetricFactor;

        Float3 tranmittance = lightSystem->GetLightTransmittance(lightH);
        lightColor *= tranmittance;

        if (lightType == LightType::Directional || lightColor.LengthSquared() < 1e-4)
        {
            for (int i = 0; i < 6; i++)
            {
                boundGrid[lightIdx * 8 + i] = 0u;
            }
            lightUseGroup[lightIdx] = 0u;
            return;
        }

        Float3 lightPos = transSystem->GetWorldTranslation(transH);
#if defined(CE_USE_DOUBLE_TRANSFORM)
        Float3 lightTile = transSystem->GetTilePosition(mWorld->GetComponent<TilePositionComponentR>(lightEntity).Read());
        auto camTile = mCamera->GetTilePosition();
        lightPos += (lightTile - camTile) * LENGTH_PER_TILE;
#endif
        float lightRange = lightSystem->GetLightRange(lightH);
        Float4x4 viewProjMatrix = mCamera->GetViewProjMatrix();
        Float4x4 invProjMatrx = mCamera->GetInvertProjMatrix();

        std::vector<Float4> tempVertList(0);
        std::vector<Float3> convexVertexNdc(0);
        if (lightType == LightType::Spot)
        {
            auto rot = transSystem->GetWorldRotation(transH);
            Float3 boxX = rot.Float3Rotate(Float3(1.f, 0.f, 0.f));
            Float3 boxY = rot.Float3Rotate(Float3(0.f, 1.f, 0.f));
            Float3 boxZ = rot.Float3Rotate(Float3(0.f, 0.f, 1.f));

            float halfCos = lightSystem->GetLightOuterCosAngle(lightH);
            float halfSin = std::sqrt(1 - halfCos * halfCos);

            Float3 extent{lightRange * halfSin, lightRange * halfSin, lightRange * 0.5f};

            Float4 dx = Float4(boxX.x, boxX.y, boxX.z, 0.f) * viewProjMatrix;
            Float4 dy = Float4(boxY.x, boxY.y, boxY.z, 0.f) * viewProjMatrix;
            Float4 dz = Float4(boxZ.x, boxZ.y, boxZ.z, 0.f) * viewProjMatrix;
            Float4 lightPosNdc = Float4(lightPos.x, lightPos.y, lightPos.z, 1.f) * viewProjMatrix;

            tempVertList.emplace_back(lightPosNdc);
            tempVertList.emplace_back(lightPosNdc - extent.x * dx - extent.y * dy + lightRange * dz);
            tempVertList.emplace_back(lightPosNdc - extent.x * dx + extent.y * dy + lightRange * dz);
            tempVertList.emplace_back(lightPosNdc + extent.x * dx - extent.y * dy + lightRange * dz);
            tempVertList.emplace_back(lightPosNdc + extent.x * dx + extent.y * dy + lightRange * dz);
        }
        else if (lightType == LightType::Point)
        {
            Float4 dx = Float4(1.f, 0.f, 0.f, 0.f) * viewProjMatrix;
            Float4 dy = Float4(0.f, 1.f, 0.f, 0.f) * viewProjMatrix;
            Float4 dz = Float4(0.f, 0.f, 1.f, 0.f) * viewProjMatrix;
            Float3 extent{lightRange, lightRange, lightRange};
            Float4 lightPosNdc = Float4(lightPos.x, lightPos.y, lightPos.z, 1.f) * viewProjMatrix;

            tempVertList.emplace_back(lightPosNdc - extent.x * dx - extent.y * dy - extent.z * dz);
            tempVertList.emplace_back(lightPosNdc - extent.x * dx + extent.y * dy - extent.z * dz);
            tempVertList.emplace_back(lightPosNdc + extent.x * dx - extent.y * dy - extent.z * dz);
            tempVertList.emplace_back(lightPosNdc + extent.x * dx + extent.y * dy - extent.z * dz);
            tempVertList.emplace_back(lightPosNdc - extent.x * dx - extent.y * dy + extent.z * dz);
            tempVertList.emplace_back(lightPosNdc - extent.x * dx + extent.y * dy + extent.z * dz);
            tempVertList.emplace_back(lightPosNdc + extent.x * dx - extent.y * dy + extent.z * dz);
            tempVertList.emplace_back(lightPosNdc + extent.x * dx + extent.y * dy + extent.z * dz);
        }

        for (auto& v : tempVertList)
        {
            convexVertexNdc.emplace_back(v.XYZ() / v.w);
        }

        Float3 fogGridSize = params.FogGridSize.mValue;
        Float3 boxMin = fogGridSize - Float3(1, 1, 1);
        Float3 boxMax = Float3(0, 0, 0);
        Float3 ndcMin = Float3(1.f, 1.f, 1.f);
        Float3 ndcMax = Float3(-1.f, -1.f, -1.f);
        auto ComputeGridCoordinateFromNDC = [&](Float3 ndc) {
            Float3 ret;
            float u = (ndc.x + 1.f) * 0.5f;
            float v = (1.f - ndc.y) * 0.5f;
            ret.x = u * fogGridSize.x;
            ret.y = v * fogGridSize.y;
            Float4 posCS = Float4(ndc.x, ndc.y, ndc.z, 1.f) * invProjMatrx;
            float linearDepth = posCS.z / posCS.w;

            ret.z = std::log2(linearDepth * params.ZDepthParam.mValue.x + params.ZDepthParam.mValue.y) * params.ZDepthParam.mValue.z;
            return ret;
        };

        bool partFullScreen = false;
        for (auto& ndc : convexVertexNdc)
        {
            if (ndc.z < 0.f)
            {
                partFullScreen = true;
            }
            ndcMin = Float3::Min(ndcMin, ndc);
            ndcMax = Float3::Max(ndcMax, ndc);
            Float3 gridCoord = ComputeGridCoordinateFromNDC(ndc);
            boxMin = Float3::Min(boxMin, gridCoord);
            boxMax = Float3::Max(boxMax, gridCoord);
        }
        boxMin.x = std::min(fogGridSize.x, std::max(0.f, std::floor(boxMin.x)));
        boxMin.y = std::min(fogGridSize.y, std::max(0.f, std::floor(boxMin.y)));
        boxMin.z = std::min(fogGridSize.z, std::max(0.f, std::floor(boxMin.z)));
        boxMax.x = std::max(0.f, std::min(fogGridSize.x, std::ceil(boxMax.x) + 1.f));
        boxMax.y = std::max(0.f, std::min(fogGridSize.y, std::ceil(boxMax.y) + 1.f));
        boxMax.z = std::max(0.f, std::min(fogGridSize.z, std::ceil(boxMax.z) + 1.f));
        UInt3 iGridMin{
            static_cast<UInt32>(boxMin.x),
            static_cast<UInt32>(boxMin.y),
            static_cast<UInt32>(boxMin.z),
        };
        UInt3 iGridMax{
            static_cast<UInt32>(boxMax.x),
            static_cast<UInt32>(boxMax.y),
            static_cast<UInt32>(boxMax.z),
        };

        if (partFullScreen)
        {
            iGridMin.x = 0;
            iGridMin.y = 0;
            iGridMax.x = static_cast<UInt32>(fogGridSize.x);
            iGridMax.y = static_cast<UInt32>(fogGridSize.y);
        }
        if (iGridMin.x > iGridMax.x || iGridMin.y > iGridMax.y || iGridMin.z > iGridMax.z || ndcMax.x < -1.f || ndcMax.y < -1.f || ndcMax.z < 0.f || ndcMin.x > 1.f || ndcMin.y > 1.f || ndcMin.z > 1.f)
        {
            iGridMax = iGridMin;
        }

        boundGrid[lightIdx * 8 + 0] = iGridMin.x;
        boundGrid[lightIdx * 8 + 1] = iGridMin.y;
        boundGrid[lightIdx * 8 + 2] = iGridMin.z;
        boundGrid[lightIdx * 8 + 3] = 0u;
        boundGrid[lightIdx * 8 + 4] = iGridMax.x;
        boundGrid[lightIdx * 8 + 5] = iGridMax.y;
        boundGrid[lightIdx * 8 + 6] = iGridMax.z;
        boundGrid[lightIdx * 8 + 7] = 0u;

        UInt32 totalCount = (iGridMax.x - iGridMin.x) * (iGridMax.y - iGridMin.y) * (iGridMax.z - iGridMin.z);
        Assert(totalCount <= fogGridSize.x * fogGridSize.y * fogGridSize.z);
        UInt32 blockCount = math::DivideAndRoundUp(totalCount, VFOG_LIGHTINJECTION_THREAD_SIZE);
        lightUseGroup[lightIdx] = blockCount;
    });

    UInt32 dispatchGroupSize = 0u;
    for (UInt32 i = 0; i < lightCnt; i++)
    {
        blockLightUseGroup[i / VFOG_BLOCK_LIGHT_NUM] += lightUseGroup[i];
        dispatchGroupSize += lightUseGroup[i];
    }
    return dispatchGroupSize;
}

bool VolumetricFog::ExecuteImp(const GameContext& gameContext, REDTextureView* depthOnlyView, const ShadowProperties* shadowInfo, REDTextureView*& integratedView, REDTextureView*& outFoggedView)
{
    auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
    auto* atmosSys = mWorld->GetRenderSystem<SkyAtmosphereSystemR>();

    auto& vFogSettings = mSetting.VFog;
    bool bUseTemporal = vFogSettings.QualityTrade.Temporal;
    bool bUseLightInjection = vFogSettings.QualityTrade.LightInjection;

    //Adaptive Volume
    UpdateVolumetricLight(gameContext);
    
    auto fogParam = CalculateFogParams();
    gameContext.mRenderPipeline->GetBuiltInValue<PassSemanticName::VFogVolumeCutOffDistance>() = fogParam.FogFar.mValue;

    UInt16 SizeX = static_cast<UInt16>(fogParam.FogGridSize.mValue.x);
    UInt16 SizeY = static_cast<UInt16>(fogParam.FogGridSize.mValue.y);
    UInt16 SizeZ = static_cast<UInt16>(fogParam.FogGridSize.mValue.z);

    auto& lightList = gameContext.mRenderPipeline->GetLightList();
    UInt32 lightCnt = static_cast<UInt32>(lightList.size());
    lightCnt = std::min(1024, std::min(mSetting.VFog.QualityTrade.MaxLight, static_cast<int>(lightCnt)));

    RED->BeginRegion("VolumetricFog");

    REDTexture* HistoryLightScatterTex = nullptr;
    REDTextureView* HistoryLightScatterView = nullptr;
    // Making sure history vfog scatter view is valid
    if (bUseTemporal)
    {
        NGITextureDesc lightTexDesc{GraphicsFormat::R16G16B16A16_SFloat, NGITextureType::Texture3D, 1, 1, SizeX, SizeY, SizeZ, 1, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess};
        NGITextureViewDesc lightViewDesc{NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess, GraphicsFormat::R16G16B16A16_SFloat, NGITextureType::Texture3D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}};
        if (mHistoryLightScatterTex == nullptr || mHistoryLightScatterTex->GetDesc().Width != SizeX || mHistoryLightScatterTex->GetDesc().Height != SizeY || mHistoryLightScatterTex->GetDesc().Depth != SizeZ)
        {
            mHistoryLightScatterTex = GetNGIDevice().CreateTexture(lightTexDesc, "HistoryLightScatter");
            mHistoryLightScatterTexView = GetNGIDevice().CreateTextureView(mHistoryLightScatterTex, lightViewDesc);
            mHistoryFrame = 0;
        }
        HistoryLightScatterTex = RED->AllocateTexture("HistoryLightScatter", mHistoryLightScatterTex);
        HistoryLightScatterView = RED->AllocateTextureView(HistoryLightScatterTex, lightViewDesc);
        HistoryLightScatterView->SetExternalState(NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        auto transitionPass = RED->AllocatePass("Transition", true);
        transitionPass->AddTextureReference(HistoryLightScatterView, REDResourceState{NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask});
    }

    REDBuffer* LightBoundingGridBuffer = nullptr;
    REDBufferView* LightBoundingGridBufferUAV = nullptr;
    NGIBufferView* NgiLightBoundingGridBufferUAV = nullptr;

    REDBuffer* InjectionDispatchSizeBuffer = nullptr;
    REDBufferView* InjectionDispatchSizeBufferUAV = nullptr;
    NGIBufferView* NgiInjectionDispatchSizeBufferUAV = nullptr;

    REDBuffer* GroupSumBuffer = nullptr;
    REDBufferView* GroupSumBufferUAV = nullptr;
    NGIBufferView* NgiGroupSumBufferUAV = nullptr;

    REDBuffer* DispatchSizeBuffer = nullptr;
    REDBufferView* DispatchSizeBufferUAV = nullptr;

    REDBuffer* LightScatterAccBuffer = nullptr;
    REDBufferView* LightScatterAccBufferUAV = nullptr;

    auto SetLightVolumetricIntensityBuffer = [&gameContext, this](RenderContext& context) {
        auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto lightVolumetricIntensityView = rendererSys->GetTransientResourceManager()->AllocateBufferView(
            NGIBufferViewDesc{
                NGIBufferUsage::StructuredBuffer,
                mLightVolumetricIntensityBuffer.GetNGIOffset(),
                mLightVolumetricIntensityBuffer.GetMapSize(),
                GraphicsFormat::Unknown,
                sizeof(Float4),
            },
            mLightVolumetricIntensityBuffer.GetNGIBuffer());

        auto RED = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        context.SetProperty(NameID("ce_LightVolumetricColors"), lightVolumetricIntensityView);
    };


    // pass 1 : FogMedium --------------------
    REDTextureView* FogMediumView = IRenderPipeline::CreateTextureView3D("Fog Medium Texture", SizeX, SizeY, SizeZ, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
    REDTextureView* FogDustMediumView = nullptr;
    {
        auto* fogMediumPass = RED->AllocatePass("FogVoxel");
        fogParam.UseWGS84.mValue = mSetting.FogCommon.UseWGS84;
        SetPassParameters(fogParam, fogMediumPass);
        fogMediumPass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
        fogMediumPass->SetProperty(NAME_ID("_MediumScatter"), FogMediumView, NGIResourceState::ComputeShaderUnorderedAccess);
        if (vFogSettings.Dust.enable)
        {
            FogDustMediumView = IRenderPipeline::CreateTextureView3D("Fog Medium Texture", SizeX, SizeY, SizeZ, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        }
        else
        {
            FogDustMediumView = IRenderPipeline::CreateTextureView3D("Fog Medium Texture", 1, 1, 1, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        }
        fogMediumPass->SetProperty(NAME_ID("_MediumDustScatter"), FogDustMediumView, NGIResourceState::ComputeShaderUnorderedAccess);

        UInt3 groupSize;
        mVolumetricFogComputeShader->GetThreadGroupSize("MaterialSetupCS", groupSize.x, groupSize.y, groupSize.z);
        fogMediumPass->Dispatch(mVolumetricFogComputeShader, "MaterialSetupCS", SizeX / groupSize.x + 1, SizeY / groupSize.y + 1, SizeZ / groupSize.z + 1);
    }

    // pass 2 : LightScattering -------------
    auto* LightScatterView = IRenderPipeline::CreateTextureView3D("Light Scatter Texture", SizeX, SizeY, SizeZ, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
    {
        // Light Injection
        if (bUseLightInjection && lightCnt > 0)
        {
            bool useCpuProjection = vFogSettings.QualityTrade.CpuLightProjection;
            UInt32 dispatchSize = 0u;

            // Light Projection
            if (useCpuProjection)
            {
                // CPU light bounding projection
                std::vector<UInt32> lightBoundGrid(lightCnt * 8, 0u);
                std::vector<UInt32> lightUseGroup(lightCnt);
                std::vector<UInt32> blockLightUseGroup(math::DivideAndRoundUp(lightCnt, VFOG_BLOCK_LIGHT_NUM));
                dispatchSize = PreCalculateLightInjection(lightCnt, lightList, fogParam, lightBoundGrid, lightUseGroup, blockLightUseGroup);

                auto AllocateGpuBuffer = [&](const std::vector<UInt32>& _Data) {
                    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

                    UInt32 dataByteSize = static_cast<UInt32>(sizeof(UInt32) * _Data.size());
                    UInt32 dataBufferSize = std::max(dataByteSize, 1u);

                    auto* scratchBuffer = rendererSys->GetScratchBuffer();
                    auto dataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataBufferSize);
                    dataBufferWrap.MemWrite(0, _Data.data(), dataByteSize);

                    return rendererSys->GetTransientResourceManager()->AllocateBufferView(
                        NGIBufferViewDesc{
                            NGIBufferUsage::StructuredBuffer,
                            dataBufferWrap.GetNGIOffset(),
                            dataBufferSize,
                            GraphicsFormat::Unknown,
                            sizeof(UInt32) * 4u,
                        },
                        dataBufferWrap.GetNGIBuffer());
                };

                NgiLightBoundingGridBufferUAV = AllocateGpuBuffer(lightBoundGrid);
                NgiInjectionDispatchSizeBufferUAV = AllocateGpuBuffer(lightUseGroup);
                NgiGroupSumBufferUAV = AllocateGpuBuffer(blockLightUseGroup);
            }
            else
            {
                // Allocate buffer
                {
                    LightBoundingGridBuffer = RED->AllocateBuffer("LightBoundingGrid", NGIBufferDesc{8u * sizeof(UInt32) * lightCnt, NGIBufferUsage::RWStructuredBuffer});
                    LightBoundingGridBufferUAV = RED->AllocateBufferView(LightBoundingGridBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 8u * sizeof(UInt32) * lightCnt, GraphicsFormat::Unknown, sizeof(UInt32) * 4u});

                    InjectionDispatchSizeBuffer = RED->AllocateBuffer("LightInjectionSize", NGIBufferDesc{sizeof(UInt32) * lightCnt, NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer});
                    InjectionDispatchSizeBufferUAV = RED->AllocateBufferView(InjectionDispatchSizeBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, sizeof(UInt32) * lightCnt, GraphicsFormat::Unknown, sizeof(UInt32)});

                    GroupSumBuffer = RED->AllocateBuffer("LightGroupSum", NGIBufferDesc{sizeof(UInt32) * math::DivideAndRoundUp(lightCnt, 32u), NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer});
                    GroupSumBufferUAV = RED->AllocateBufferView(GroupSumBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, sizeof(UInt32) * math::DivideAndRoundUp(lightCnt, 32u), GraphicsFormat::Unknown, sizeof(UInt32)});

                    DispatchSizeBuffer = RED->AllocateBuffer("LightInjectionDispatchSize", NGIBufferDesc{3u * sizeof(UInt32), NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer});
                    DispatchSizeBufferUAV = RED->AllocateBufferView(DispatchSizeBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 3u * sizeof(UInt32), GraphicsFormat::Unknown, sizeof(UInt32)});
                }

                // GPU light bounding projection
                {
                    auto* projPass = RED->AllocatePass("Project Light to View Grid", true);
                    SetPassParameters(fogParam, projPass);
                    projPass->SetProperty(NAME_ID("_OutLightBoundingGrid"), LightBoundingGridBufferUAV);
                    projPass->SetProperty(NAME_ID("ce_View"), mCamera->GetViewMatrix());
                    projPass->SetProperty(NAME_ID("ce_Projection"), mCamera->GetProjMatrix());
                    projPass->SetProperty(NAME_ID("_OutLightBoundingGrid"), LightBoundingGridBufferUAV);
                    projPass->SetProperty(NAME_ID("_OutIndirectDispatchArgs"), InjectionDispatchSizeBufferUAV);
                    projPass->SetProperty(NAME_ID("lightCnt"), lightCnt);
                    projPass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
                    UInt3 groupSize;
                    mLightInjectionComputeShader->GetThreadGroupSize("LightProjectionCS", groupSize.x, groupSize.y, groupSize.z);
                    projPass->Dispatch(mLightInjectionComputeShader, "LightProjectionCS", math::DivideAndRoundUp(lightCnt, groupSize.x), 1, 1);
                }

                // Calc Group Task Sum
                {
                    auto* groupSumPass = RED->AllocatePass("Light Group Task Sum", true);
                    groupSumPass->SetProperty(NAME_ID("_IndirectDispatchArgs"), InjectionDispatchSizeBufferUAV);
                    groupSumPass->SetProperty(NAME_ID("_OutSumUpBlock"), GroupSumBufferUAV);
                    groupSumPass->SetProperty(NAME_ID("_LightCnt"), lightCnt);
                    UInt3 groupSize;
                    mDispatchSizeComputeShader->GetThreadGroupSize("AddUpLightGroupCS", groupSize.x, groupSize.y, groupSize.z);
                    groupSumPass->Dispatch(mDispatchSizeComputeShader, "AddUpLightGroupCS", math::DivideAndRoundUp(lightCnt, groupSize.x), 1, 1);
                }

                // Calc Injection Dispatch Size
                {
                    auto* sumPass = RED->AllocatePass("Injection Dispatch Size", true);
                    sumPass->SetProperty(NAME_ID("_SumUpBlock"), GroupSumBufferUAV);
                    sumPass->SetProperty(NAME_ID("_OutDispatchSize"), DispatchSizeBufferUAV);
                    sumPass->SetProperty(NAME_ID("_GroupSize"), math::DivideAndRoundUp(lightCnt, 32u));
                    UInt3 groupSize;
                    mDispatchSizeComputeShader->GetThreadGroupSize("AddUpLightDispatchCS", groupSize.x, groupSize.y, groupSize.z);
                    sumPass->Dispatch(mDispatchSizeComputeShader, "AddUpLightDispatchCS", 1, 1, 1);
                }
            }

            // Clear
            {
                LightScatterAccBuffer = RED->AllocateBuffer("LightScatterAccBuffer", NGIBufferDesc{4u * sizeof(UInt32) * SizeX * SizeY * SizeZ, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
                LightScatterAccBufferUAV = RED->AllocateBufferView(LightScatterAccBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer, 0, 4u * sizeof(UInt32) * SizeX * SizeY * SizeZ, GraphicsFormat::Unknown, sizeof(UInt32)});

                auto* clearPass = RED->AllocatePass("Clear Light Scatter", true);
                clearPass->ClearBuffer(LightScatterAccBufferUAV, 0);
            }

            // Light Injection
            {
                auto* pass = RED->AllocatePass("LightInjection", true);
                SetPassParameters(fogParam, pass);
                SetLightVolumetricIntensityBuffer(pass->GetContext());
                pass->SetProperty(NAME_ID("_OutLightScatterBuffer"), LightScatterAccBufferUAV);

                pass->SetProperty(NAME_ID("_LightCnt"), lightCnt);
                pass->SetProperty(NAME_ID("_GroupSize"), 32u);
                pass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
                gameContext.mRenderPipeline->UpdateShadowContext(pass, shadowInfo);
                if (useCpuProjection)
                {
                    pass->SetProperty(NAME_ID("_LightBoundingGrid"), NgiLightBoundingGridBufferUAV);
                    pass->SetProperty(NAME_ID("_SumUpBlock"), NgiGroupSumBufferUAV);
                    pass->SetProperty(NAME_ID("_IndirectDispatchArgs"), NgiInjectionDispatchSizeBufferUAV);
                    pass->Dispatch(mLightInjectionComputeShader, NameID("PerLightScatteringCS"), dispatchSize, 1, 1);
                }
                else
                {
                    pass->SetProperty(NAME_ID("_LightBoundingGrid"), LightBoundingGridBufferUAV);
                    pass->SetProperty(NAME_ID("_SumUpBlock"), GroupSumBufferUAV);
                    pass->SetProperty(NAME_ID("_IndirectDispatchArgs"), InjectionDispatchSizeBufferUAV);
                    pass->DispatchIndirect(mLightInjectionComputeShader, NameID("PerLightScatteringCS"), DispatchSizeBuffer, 0);
                }
            }
        }

        auto* lightScatterPass = RED->AllocatePass("LightScatter");
        SetPassParameters(fogParam, lightScatterPass);
        SetLightVolumetricIntensityBuffer(lightScatterPass->GetContext());
        lightScatterPass->SetProperty(NAME_ID("lightCnt"), lightCnt);
        // lightScatterPass->SetProperty(NAME_ID("UseLightInjection"), bUseLightInjection);
        lightScatterPass->SetProperty(NAME_ID("DisableDirectionalLight"), vFogSettings.QualityTrade.TurnOffDirectionalLight);
        lightScatterPass->SetProperty(NAME_ID("_MediumScatter"), FogMediumView, NGIResourceState::ComputeShaderUnorderedAccess);
        lightScatterPass->SetProperty(NAME_ID("_LightScatter"), LightScatterView, NGIResourceState::ComputeShaderUnorderedAccess);
        lightScatterPass->SetProperty(NAME_ID("_SceneDepthTex"), depthOnlyView, NGIResourceState::ComputeShaderUnorderedAccess);
        if (bUseTemporal && HistoryLightScatterView != nullptr && ValidHistorySample())
        {
            lightScatterPass->SetProperty(NAME_ID("_SampleHistoryLightScatter"), HistoryLightScatterView, NGIResourceState::ComputeShaderShaderResource);
            lightScatterPass->SetProperty(NAME_ID("UseTemporal"), true);
        }
        else
        {
            lightScatterPass->SetProperty(NAME_ID("UseTemporal"), false);
        }
        if (bUseLightInjection && lightCnt > 0)
        {
            lightScatterPass->SetProperty(NAME_ID("_LightScatterBuffer"), LightScatterAccBufferUAV);
        }

        gameContext.mRenderPipeline->UpdateShadowContext(lightScatterPass, shadowInfo);
        // gameContext.mRenderPipeline->UpdateCloudShadowContext(lightScatterPass);

        std::string scatterCSName = "LightScatteringCS";
        if (bUseLightInjection)
        {
            scatterCSName = vFogSettings.QualityTrade.TurnOffDirectionalLight ? "LightScatterInjectionOnlyCS" : "LightScatterWithInjectionCS";
        }
        UInt3 groupSize;
        mVolumetricFogComputeShader->GetThreadGroupSize(scatterCSName, groupSize.x, groupSize.y, groupSize.z);
        lightScatterPass->Dispatch(mVolumetricFogComputeShader, scatterCSName, SizeX / groupSize.x + 1, SizeY / groupSize.y + 1, SizeZ / groupSize.z + 1);
    }

    /*auto* cpyLightScatteringView = IRenderPipeline::CreateTextureView3D("Copy Light Texture", SizeX, SizeY, SizeZ, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
    {
        auto* lightCpyPass = RED->AllocatePass("CopyLightScattering");
        lightCpyPass->SetProperty(NAME_ID("_LightScatter"), LightScatterView);
        lightCpyPass->SetProperty(NAME_ID("_LightScatterCopy"), cpyLightScatteringView);
        lightCpyPass->SetProperty(NAME_ID("FogGridSize"), fogParam.FogGridSize.mValue);
        UInt3 CopyPass;
        mVolumetricFogComputeShader->GetThreadGroupSize("LightScatteringCopyCS", CopyPass.x, CopyPass.y, CopyPass.z);
        lightCpyPass->Dispatch(mVolumetricFogComputeShader, "LightScatteringCopyCS", SizeX / CopyPass.x + 1, SizeY / CopyPass.y + 1, SizeZ / CopyPass.z + 1);
    }*/

    // blur pass : -----------------------------------
    if (vFogSettings.QualityTrade.UseBlur)
    {
        auto* cpyLightScatteringView = IRenderPipeline::CreateTextureView3D("Copy Light Texture", SizeX, SizeY, SizeZ, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        auto* lightCpyPass = RED->AllocatePass("CopyLightScattering");
        lightCpyPass->SetProperty(NAME_ID("_LightScatter"), LightScatterView);
        lightCpyPass->SetProperty(NAME_ID("_LightScatterCopy"), cpyLightScatteringView);
        lightCpyPass->SetProperty(NAME_ID("FogGridSize"), fogParam.FogGridSize.mValue);
        UInt3 CopyPass;
        mVolumetricFogComputeShader->GetThreadGroupSize("LightScatteringCopyCS", CopyPass.x, CopyPass.y, CopyPass.z);
        lightCpyPass->Dispatch(mVolumetricFogComputeShader, "LightScatteringCopyCS", SizeX / CopyPass.x + 1, SizeY / CopyPass.y + 1, SizeZ / CopyPass.z + 1);

        auto* BlurLightViewH = IRenderPipeline::CreateTextureView3D("Blur Light Texture H", SizeX, SizeY, SizeZ, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        auto* lightBlurPassH = RED->AllocatePass("BlurLightScattering");
        lightBlurPassH->SetProperty(NAME_ID("_LightScatter"), BlurLightViewH);
        lightBlurPassH->SetProperty(NAME_ID("_LightScatterCopy"), cpyLightScatteringView);
        lightBlurPassH->SetProperty(NAME_ID("FogGridSize"), fogParam.FogGridSize.mValue);
        lightBlurPassH->SetProperty(NAME_ID("BlurRadius"), vFogSettings.QualityTrade.BlurSize);
        lightBlurPassH->SetProperty(NAME_ID("BlurStrength"), vFogSettings.QualityTrade.BlurStrength);
        lightBlurPassH->SetProperty(NAME_ID("BlurDirection"), Float2(1.f, 0.f));
        UInt3 BlurPassH;
        mVolumetricFogComputeShader->GetThreadGroupSize("LightScatteringBlurCS", BlurPassH.x, BlurPassH.y, BlurPassH.z);
        lightBlurPassH->Dispatch(mVolumetricFogComputeShader, "LightScatteringBlurCS", SizeX / BlurPassH.x + 1, SizeY / BlurPassH.y + 1, SizeZ / BlurPassH.z + 1);

        auto* lightBlurPassV = RED->AllocatePass("BlurLightScattering");
        lightBlurPassV->SetProperty(NAME_ID("_LightScatter"), LightScatterView);
        lightBlurPassV->SetProperty(NAME_ID("_LightScatterCopy"), BlurLightViewH);
        lightBlurPassV->SetProperty(NAME_ID("FogGridSize"), fogParam.FogGridSize.mValue);
        lightBlurPassV->SetProperty(NAME_ID("BlurRadius"), vFogSettings.QualityTrade.BlurSize);
        lightBlurPassV->SetProperty(NAME_ID("BlurStrength"), vFogSettings.QualityTrade.BlurStrength);
        lightBlurPassV->SetProperty(NAME_ID("BlurDirection"), Float2(0.f, 1.f));
        UInt3 BlurPassV;
        mVolumetricFogComputeShader->GetThreadGroupSize("LightScatteringBlurCS", BlurPassV.x, BlurPassV.y, BlurPassV.z);
        lightBlurPassV->Dispatch(mVolumetricFogComputeShader, "LightScatteringBlurCS", SizeX / BlurPassV.x + 1, SizeY / BlurPassV.y + 1, SizeZ / BlurPassV.z + 1);
    }

    //// debug pass: -----------------
    // auto* lightDbgPass = RED->AllocatePass("DebugPass");
    //{
    //    SetPassParameters(fogParam, lightDbgPass);
    //    lightDbgPass->SetProperty(NAME_ID("LightScatter"), LightScatterView, NGIResourceState::ComputeShaderUnorderedAccess);
    //    lightDbgPass->SetProperty(NAME_ID("LightScatterCopy"), cpyLightScatteringView, NGIResourceState::ComputeShaderUnorderedAccess);
    //    lightDbgPass->SetProperty(NAME_ID("SampleHistoryLightScatter"), HistoryLightScatterView, NGIResourceState::ComputeShaderShaderResource);
    //    lightDbgPass->SetProperty(NAME_ID("FogGridSize"), fogParam.FogGridSize.mValue);
    //    lightDbgPass->SetProperty(NAME_ID("UseTemporal"), true);
    //    UInt3 DbgPass;
    //    mVolumetricFogComputeShader->GetThreadGroupSize("LightScatteringDebugCS", DbgPass.x, DbgPass.y, DbgPass.z);
    //    lightDbgPass->Dispatch(mVolumetricFogComputeShader, "LightScatteringDebugCS", SizeX / DbgPass.x + 1, SizeY / DbgPass.y + 1, SizeZ / DbgPass.z + 1);
    //}

    // copy scattering to history
    if (bUseTemporal)
    {
        auto* lightCpyPass = RED->AllocatePass("CopyToHistory");
        lightCpyPass->SetProperty(NAME_ID("_LightScatter"), LightScatterView);   // debug
        lightCpyPass->SetProperty(NAME_ID("_LightScatterCopy"), HistoryLightScatterView);
        lightCpyPass->SetProperty(NAME_ID("FogGridSize"), fogParam.FogGridSize.mValue);
        UInt3 CopyPass;
        mVolumetricFogComputeShader->GetThreadGroupSize("LightScatteringCopyCS", CopyPass.x, CopyPass.y, CopyPass.z);
        lightCpyPass->Dispatch(mVolumetricFogComputeShader, "LightScatteringCopyCS", SizeX / CopyPass.x + 1, SizeY / CopyPass.y + 1, SizeZ / CopyPass.z + 1);
        mHistoryFrame = mFrameNum;
    }

    // pass 3 : LightIntegrating ---------------
    integratedView = IRenderPipeline::CreateTextureView3D("Light Integrated Texture", SizeX, SizeY, SizeZ, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
    auto* lightIntegratedPass = RED->AllocatePass("LightIntegration");
    {
        SetPassParameters(fogParam, lightIntegratedPass);
        lightIntegratedPass->SetProperty(NAME_ID("CE_USE_DOUBLE_TRANSFORM"), true);
        lightIntegratedPass->SetProperty(NAME_ID("_IntegratedLight"), integratedView, NGIResourceState::ComputeShaderUnorderedAccess);
        lightIntegratedPass->SetProperty(NAME_ID("_SceneDepthTex"), depthOnlyView, NGIResourceState::ComputeShaderUnorderedAccess);
        lightIntegratedPass->SetProperty(NAME_ID("_LightScatter"), LightScatterView, NGIResourceState::ComputeShaderUnorderedAccess);
        lightIntegratedPass->SetProperty(NAME_ID("_MediumScatter"), FogMediumView, NGIResourceState::ComputeShaderUnorderedAccess);
        lightIntegratedPass->SetProperty(NAME_ID("_MediumDustScatter"), FogDustMediumView, NGIResourceState::ComputeShaderUnorderedAccess);

        UInt3 groupSize;
        mVolumetricFogComputeShader->GetThreadGroupSize("FinalIntegrationCS", groupSize.x, groupSize.y, groupSize.z);
        lightIntegratedPass->Dispatch(mVolumetricFogComputeShader, "FinalIntegrationCS", SizeX / groupSize.x + 1, SizeY / groupSize.y + 1, 1);
    }

    // pass FogCombine --------------------
    outFoggedView = IRenderPipeline::CreateTextureView2D("FogTarget", mWidth, mHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::RenderTarget | NGITextureUsage::SubpassInput | NGITextureUsage::ShaderResource);
    {
        auto fogParam2 = CalculateFogParams();
        gameContext.mRenderPipeline->PostProcess([&](auto pass) {
            pass->SetProperty(NAME_ID("sceneDepth"), depthOnlyView, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("fog_texture"), FogMediumView);
            pass->SetProperty(NAME_ID("light_texture"), LightScatterView);
            pass->SetProperty(NAME_ID("integrated_texture"), integratedView, NGIResourceState::PixelShaderShaderResource);
            pass->SetProperty(NAME_ID("VFogNear"), fogParam2.FogNear.mValue);
            pass->SetProperty(NAME_ID("VFogFar"), fogParam2.FogFar.mValue);
            pass->SetProperty(NAME_ID("ZDepthParam"), fogParam2.ZDepthParam.mValue);
            pass->SetProperty(NAME_ID("FogGridSize"), fogParam2.FogGridSize.mValue);
            },
            mPostMat,
            "fog_combine",
            true,
            outFoggedView);
    }

    RED->EndRegion();
    return true;
}

VolumetricFogParam VolumetricFog::CalculateFogParams()
{
    VolumetricFogParam params;
    auto& commonSettings = mSetting.FogCommon;
    auto& vfogSettings = mSetting.VFog;
    const auto& ffsSettings = mSetting.ffsTempTest;

    float xNum = static_cast<float>(std::max(1, static_cast<int>(mWidth / vfogSettings.QualityTrade.ResolutionRatio)));
    float yNum = static_cast<float>(std::max(1, static_cast<int>(mHeight / vfogSettings.QualityTrade.ResolutionRatio)));
    float zNum = static_cast<float>(std::max(1, static_cast<int>(vfogSettings.QualityTrade.ZSliceNum)));

    params.FogGridSize.mValue = Float3(xNum, yNum, zNum);
    params.FogDensity.mValue = std::max(commonSettings.Density / 1000.f, 0.f);
    params.FogDensitySecond.mValue = std::max(commonSettings.DensitySecond / 1000.f, 0.f);
    params.FogHeightFallOff.mValue = std::max(commonSettings.HeightFallOff / 1000.f, 0.f);
    params.FogHeightFallOffSecond.mValue = std::max(commonSettings.HeightFallOffSecond / 1000.f, 0.f);
    params.FogHeight.mValue = commonSettings.HeightOffset;
    params.FogHeightSecond.mValue = commonSettings.HeightOffsetSecond;

    vfogSettings.StartDistance = std::max(vfogSettings.StartDistance, commonSettings.StartDistance);
    vfogSettings.CutOffDistance = std::min(vfogSettings.CutOffDistance, commonSettings.CutOffDistance);
    params.FogNear.mValue = vfogSettings.StartDistance;
    params.FogFar.mValue = vfogSettings.CutOffDistance;
    params.LightVolumetricFactor.mValue = vfogSettings.LightVolumetricFactor;
    params.FrameNum.mValue = static_cast<float>(mFrameNum);

    params.UnitFactor.mValue = 1.f;

    /*float expPara = -fallOff * (camHeight - mSetting.FogCommon.Height * unitFactor);
    expPara = std::max(-126.f + 1.f, std::min(127.f - 1.f, expPara));
    params.FogExpParam.mValue = mSetting.FogCommon.Density * std::powf(2.0f, expPara);
    float expParaSecond = -fallOffSecond * (camHeight - mSetting.FogCommon.HeightSecond * unitFactor);
    expParaSecond = std::max(-126.f + 1.f, std::min(127.f - 1.f, expParaSecond));
    params.FogExpParamSecond.mValue = mSetting.FogCommon.DensitySecond * std::powf(2.0f, expParaSecond);*/

    float S = vfogSettings.QualityTrade.ZVoxelScale;
    float NearOffset = 9.6f;
    float N = vfogSettings.StartDistance + NearOffset;
    float F = vfogSettings.CutOffDistance;
    float O = (F - N * static_cast<float>(pow(2, (params.FogGridSize.mValue.z - 1) / S))) / (F - N);
    float B = (1 - O) / N;
    params.ZDepthParam.mValue = Float3(B, O, S);

    params.Phase1.mValue = std::min(std::max(-1.f, vfogSettings.MiePhase1), 1.f);
    params.Phase2.mValue = std::min(std::max(-1.f, vfogSettings.MiePhase2), 1.f);
    params.FogAlbedo.mValue = vfogSettings.Albedo;
    params.FogAtmosphereContribution.mValue = vfogSettings.AtomsphereFactor;
    params.FogExtinctionScale.mValue = vfogSettings.ExtinctionScale;
    if (vfogSettings.BoundingFadeOut == false)
        params.FogFadeOutDistance.mValue = vfogSettings.CutOffDistance;
    else
        params.FogFadeOutDistance.mValue = std::max(N, std::min(vfogSettings.FadeOutDistance, F));

    params.MultiSampleJitter.mValue = vfogSettings.QualityTrade.MultiSampleJitter;
    params.MultiSampleNum.mValue = std::max(1.f, vfogSettings.QualityTrade.MultiSampleNum);

    params.CloudShadow.mValue = vfogSettings.CloudShadow;
    params.UseWGS84.mValue = mSetting.FogCommon.UseWGS84;
    params.HistoryWeight.mValue = vfogSettings.QualityTrade.HistoryWeight;

    params.UseDust.mValue = vfogSettings.Dust.enable;
    params.DustDensity.mValue = vfogSettings.Dust.DustDensity;
    params.DustScale.mValue = vfogSettings.Dust.DustScale;
    params.Spiral.mValue = vfogSettings.Dust.Spiral;
    params.DustHeight.mValue = vfogSettings.Dust.Height + ((ffsSettings.enable) ? (ffsSettings.FogBaseHeight / 100.f) : 0.f);
    params.DustLightAbsorb.mValue = vfogSettings.Dust.LightAbsorb;
    params.WindDir.mValue = vfogSettings.Dust.Wind;
    params.DustAlbedo.mValue = vfogSettings.Dust.DustAlbedo;

    params.Cloudy.mValue = commonSettings.CloudyAtomsphere;

    return params;
}
}   // namespace cross