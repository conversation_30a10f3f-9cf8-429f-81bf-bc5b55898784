#include "EnginePrefix.h"
#include "PrefabProxyRenderPipeline.h"

namespace cross
{
    void PrefabProxyRenderPipeline::Assemble()
    {
        auto renderCamera = mWorkingRenderPipeline->GetRenderCamera();
        if (renderCamera->GetIsRenderToTargetCamera())
        {
            mRED->BeginRegion("PrefabProxyCapture");
            mWorkingRenderPipeline->PrepareAssembleRenderContext();
            mWorkingRenderPipeline->Assemble(mWorkingRenderPipeline->GetTargetView());
            mRED->EndRegion();

            //const_cast<RenderCamera*>(renderCamera)->mRenderToTarget = false;
        }
    }
    RENDER_ENGINE_API void PrefabProxyRenderPipeline::PreAssemble()
    {
        mWorkingRenderPipeline->PreAssemble();
    }
    }
