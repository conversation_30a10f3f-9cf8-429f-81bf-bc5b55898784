#pragma once
#include "../RenderPipelineR.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/StellarMesh/StellarMeshScene.h"
#include "RenderEngine/RayTracing/RayTracingScene.h"

namespace cross {

class RENDER_ENGINE_API WorldRenderPipeline
{
public:
    WorldRenderPipeline();

    WorldRenderPipeline(const WorldRenderPipeline&) = delete;

    const WorldRenderPipeline& operator=(const WorldRenderPipeline&) = delete;

    virtual ~WorldRenderPipeline();

    virtual void Initialize(RenderWorld* world, RenderingExecutionDescriptor* RED)
    {
        mRenderWorld = world;
        mRED = RED;
    }

    void UpdateFrameIndexContext()
    {
        mRED->SetProperty(BuiltInProperty::ce_FrameID, mCurFrameParam->GetFrameCount());
        mRED->SetProperty(BuiltInProperty::ce_FrameIDMod1024, mCurFrameParam->GetFrameCount() % 1024);
        mRED->SetProperty(BuiltInProperty::ce_FrameIDMod8, mCurFrameParam->GetFrameCount() % 8);
    }

    auto GetCurrentFrameParam() const { return mCurFrameParam; }

    virtual void Assemble(FrameParam* frameParam) = 0;

    virtual void UpdateSetting(const RenderPipelineSetting* setting);

    void AddRenderPipeline(ViewType type, IRenderPipeline* renderPipeline);

    auto& GetAllRenderPipelines() const
    {
        return mRenderPipelines;
    }

    void ExtractRenderPipeline(IRenderPipeline* pipeline);

    void DestroyRenderPipeline(IRenderPipeline* pipeline);

    virtual GPUScene* GetGPUScene()
    {
        return nullptr;
    }

    virtual StellarMesh::StellarMeshScene* GetStellarMeshScene()
    {
        return nullptr;
    }
    
    virtual RayTracingScene* GetRayTracingScene()
    {
        return nullptr;
    }

protected:
    RenderWorld* mRenderWorld;
    RenderingExecutionDescriptor* mRED;
    const RenderPipelineSetting* mSetting;
    std::multimap<ViewType, std::unique_ptr<IRenderPipeline>> mRenderPipelines;

    FrameParam* mCurFrameParam = nullptr;
};
}   // namespace cross
