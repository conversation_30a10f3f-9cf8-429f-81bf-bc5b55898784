#pragma once

#include "CECommon/Utilities/NDACommon.h"
#include "Resource/Resource.h"
#include "CrossBase/Containers/HashMap/HashMap.hpp"

namespace cross {

namespace filesystem{
    class FileSystem;
}

class Resource_API ResourceConvertManager
{
public:
    using FileGuidMap = CEHashMap<std::string, std::string>;
    using FileDependMap = std::unordered_map<std::string, std::unordered_set<std::string>>;
    using FileMetaHeaderMap = std::unordered_map<std::string, ResourceMetaHeader>;

public:
    static ResourceConvertManager& Instance() {
        static ResourceConvertManager gInstance;
        return gInstance;
    }

public:
    ResourceConvertManager();
    ~ResourceConvertManager();

    void Initialize();
    void Update();
    void ReConvert();
    void Convert();
    void UpdateDir(const std::string& rootDir);

    void Add(const std::string& path, const ResourceMetaHeader& metaHeader);
    void Change(const std::string& oldPath, const std::string& newPath);
    void Remove(const std::string& path);
    const std::string& GetGuid(const std::string& path, const std::string& defaultPath = "", bool tmp = false);
    const std::string& GetPath(const std::string& guid, const std::string& defaultPath = "", bool tmp = false);
    bool HasGuid(const std::string& guid) { return mGuidFiles.find(guid) != mGuidFiles.end();};

    void AddNdaFile(const std::string& filename, bool newGuid = true, bool force = true);
    void AddNdaFile(const std::string& path, const ResourceMetaHeader& metaHeader);
    void Clear();

    bool Load();
    void Dump(bool ansyc = false);

    bool Load(const std::string& rootDir);
    void Dump(const std::string& rootDir, bool check = false);
    const auto* GetFileHeader(const std::string& filepath){ auto it = mFileHeaders.find(filepath); return it != mFileHeaders.end() ? &(it->second) : nullptr; };

    void TraverseFileDepend(const std::string path, std::function<bool(const std::string&, const ResourceMetaHeader&)> handle, bool recursion = false);
    void TraverseFileReDepend(const std::string path, std::function<bool(const std::string&, const ResourceMetaHeader&)> handle, bool recursion = false);
    
    std::string ConvertFileGuid(const std::string& path, const std::string& newGuid);

private:
    void UpdateOneDir(const std::string& rootDir);
    void ConvertFileHeaderOneDir(const std::string& rootDir);
    void ConvertFilePathOneDir(const std::string& rootDir);
    void ConvertFileGuidOneDir(const std::string& rootDir);
    ResourceMetaHeader ConvertFileHeader(const std::string& path, bool force = false);
    void ConvertFilePath(const std::string& path);
    void ConvertFileGuid(const std::string& path);
    void GenerateDepend();

    bool UpdateMetaHeader(const std::string& path, ResourceMetaHeader& inMetaHeader, bool force = false);
    bool ConvertGuidToPath(SerializeNode& node);
    bool ConvertPathToGuid(SerializeNode& node);
    std::string SerializeMetaHeader(ResourceMetaHeader& inMetaHeader, int convertTp = 0);
    void AddFileDepend(const std::string& path, const ResourceMetaHeader& metaHeader);


    FileGuidMap             mGuidFiles;
    FileGuidMap             mTmpGuidFiles;
    FileGuidMap             mTmpFileGuids;
    FileMetaHeaderMap       mFileHeaders;
    FileDependMap           mFileReDepends;
    bool                    mIsEditor{false};
    filesystem::FileSystem* mFileSys{nullptr};
};

#define gResourceConvertMgr cross::ResourceConvertManager::Instance()
}
