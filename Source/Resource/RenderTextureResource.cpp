#include "EnginePrefix.h"
#include "Resource/RenderTextureResource.h"
#include "Resource/ResourceManager.h"

namespace cross::resource {
RenderTextureResource::RenderTextureResource(const RenderTextureInfo& info)
    : mInfo{info}
{
    mGPUTex = gResourceMgr.mCreateRenderObjectMgr->GetGpuRenderTex(mInfo);

    // @cx init other base class param
    mWide = mInfo.Width;
    mHigh = mInfo.Height;
    mDepth = mInfo.Depth;
}

bool RenderTextureResource::Serialize(const std::string& path)
{
    if (!HasAsset())
    {
        CreateAsset(path);
    }
    ClearReference();

    SerializeNode node;
    SerializeContext context;
    node["RenderTexture"] = std::move(mInfo.Serialize(context));

    return Resource::Serialize(std::move(node), path);
}
cross::resource::RenderTextureResource* RenderTextureResource::RenderTexture_CreateRenderTexture(const char* path)
{
    auto mtlPtr = gResourceMgr.CreateResourceAs<cross::resource::RenderTextureResource>();
    mtlPtr->Serialize(path);
    //mtlPtr->IncreaseRefCount();
    return mtlPtr.get();
}

bool RenderTextureResource::Deserialize(DeserializeNode const& s)
{
    mInfo = {};
    if (s.HasMember("RenderTexture"))
    {
        SerializeContext context;
        mInfo.Deserialize(s["RenderTexture"], context);
    }
    // @cx init other base class param
    mWide = mInfo.Width;
    mHigh = mInfo.Height;
    mDepth = mInfo.Depth;
    return true;
}
Resource* RenderTextureResource::CreateRenderTextureFromScratch(const char* name, int dimension, int format, int width, int height, int depth, int mipcount, int layercount, int samplecount)
{
    RenderTextureInfo info;
    info.Name = name;
    info.Dimension = static_cast<TextureDimension>(dimension);
    info.Format = static_cast<RenderTextureFormat>(format);
    info.Width = width;
    info.Height = height;
    info.Depth = depth;
    info.MipCount = mipcount;
    info.LayerCount = layercount;
    info.SampleCount = samplecount;
    auto renderTexture = gResourceMgr.CreateResourceAs<cross::resource::RenderTextureResource>(info);
    //renderTexture->IncreaseRefCount();
    return renderTexture.get();
}
bool RenderTextureResource::PostDeserialize()
{
    mGPUTex = gResourceMgr.mCreateRenderObjectMgr->GetGpuRenderTex(mInfo);
    return true;
}

}   // namespace cross::resource