#include "EnginePrefix.h"
#include "VulkanDevice.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "NativeGraphicsInterface/Statistics.h"
#include "NativeGraphicsInterface/Vulkan/VulkanObject.h"
#include "NativeGraphicsInterface/Vulkan/VulkanResource.h"
#include "NativeGraphicsInterface/SLWrapper.h"
#include "NativeGraphicsInterface/Vulkan/VulkanSwapChain.h"
#include "vulkan.hpp"
#include "VulkanTimeStamp.h"


#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/CmdSettings.h"
#include "CECommon/Common/SettingsManager.h"
#include "base/core/imodules/ilog_module.h"

#ifdef max
#    undef max
#endif   //
#ifdef min
#    undef min
#endif   //

using CrossSchema::ShaderResourceType;

namespace cross {

static const std::vector<const char*> gMandatoryLayers{
};

static const std::vector<const char*> gMandatoryInstanceExtensions
{
    VK_KHR_SURFACE_EXTENSION_NAME,

#if CROSSENGINE_WIN
        VK_KHR_WIN32_SURFACE_EXTENSION_NAME,
#elif CROSSENGINE_ANDROID
        VK_KHR_ANDROID_SURFACE_EXTENSION_NAME,
#elif CROSSENGINE_OSX
        VK_MVK_MACOS_SURFACE_EXTENSION_NAME
#elif CROSSENGINE_IOS
        VK_MVK_IOS_SURFACE_EXTENSION_NAME
#elif CROSSENGINE_LINUX
#    error "To be implemented"
#else
#    error "Unsuppported platform"
#endif   //
};


static const std::vector<const char*> gOptionalInstanceExtensions
{
    // agina for device memory budget;
    VK_KHR_GET_PHYSICAL_DEVICE_PROPERTIES_2_EXTENSION_NAME,
    VK_KHR_GET_SURFACE_CAPABILITIES_2_EXTENSION_NAME,
#if CROSSENGINE_WIN
    VK_KHR_EXTERNAL_MEMORY_CAPABILITIES_EXTENSION_NAME,
    VK_KHR_EXTERNAL_SEMAPHORE_CAPABILITIES_EXTENSION_NAME
#endif
};

static const std::vector<const char*> gMandatoryDeviceExtensions{
    VK_KHR_SWAPCHAIN_EXTENSION_NAME,
    VK_KHR_CREATE_RENDERPASS_2_EXTENSION_NAME,
};

static const std::vector<const char*> gOptionalDeviceExtensions{
    // TODO Check impl
    //VK_KHR_MAINTENANCE_4_EXTENSION_NAME,
    VK_KHR_SYNCHRONIZATION_2_EXTENSION_NAME,
    VK_KHR_DEPTH_STENCIL_RESOLVE_EXTENSION_NAME,
    // for #pragma enable debug_symbol
    VK_KHR_SHADER_NON_SEMANTIC_INFO_EXTENSION_NAME,
    VK_EXT_DESCRIPTOR_INDEXING_EXTENSION_NAME,
    VK_EXT_CONSERVATIVE_RASTERIZATION_EXTENSION_NAME,
    // extension required for device memory budget,
    // https://gpuopen-librariesandsdks.github.io/VulkanMemoryAllocator/html/staying_within_budget.html
    VK_EXT_MEMORY_BUDGET_EXTENSION_NAME,
    VK_EXT_MEMORY_PRIORITY_EXTENSION_NAME,
    VK_KHR_CALIBRATED_TIMESTAMPS_EXTENSION_NAME,

#if CROSSENGINE_WIN
#if NGI_ENABLE_GPU_DUMP
    VK_NV_DEVICE_DIAGNOSTICS_CONFIG_EXTENSION_NAME,
#endif
    VK_EXT_FULL_SCREEN_EXCLUSIVE_EXTENSION_NAME,
    VK_KHR_EXTERNAL_MEMORY_EXTENSION_NAME,
    VK_KHR_EXTERNAL_SEMAPHORE_EXTENSION_NAME,
    VK_KHR_EXTERNAL_MEMORY_WIN32_EXTENSION_NAME,
    VK_KHR_EXTERNAL_SEMAPHORE_WIN32_EXTENSION_NAME,
#endif
    
    VK_KHR_ACCELERATION_STRUCTURE_EXTENSION_NAME,
    VK_KHR_RAY_TRACING_PIPELINE_EXTENSION_NAME,
    VK_KHR_RAY_QUERY_EXTENSION_NAME,
    VK_KHR_DEFERRED_HOST_OPERATIONS_EXTENSION_NAME,
    VK_KHR_BUFFER_DEVICE_ADDRESS_EXTENSION_NAME,
    VK_KHR_MAINTENANCE_4_EXTENSION_NAME
};

VkBool32 VKAPI_PTR DebugUtilsMessengerCallback(
    VkDebugUtilsMessageSeverityFlagBitsEXT      messageSeverity,
    VkDebugUtilsMessageTypeFlagsEXT             messageTypes,
    const VkDebugUtilsMessengerCallbackDataEXT* pCallbackData, void* pUserData)
{
    if (messageSeverity != VK_DEBUG_UTILS_MESSAGE_SEVERITY_ERROR_BIT_EXT) {
        switch (pCallbackData->messageIdNumber)
        {
        // loader message
        case 0:
            return VK_FALSE;
        default:
            break;
        }
    }

    std::string objectInfos;
    for (auto i = 0u; i < pCallbackData->objectCount; i++)
    {
        auto& object = pCallbackData->pObjects[i];
        if (object.pObjectName)
        {
            objectInfos.append(fmt::format("object: {}\n", object.pObjectName));
        }
    }


    Logger::LogLevel logLevel = Logger::LogLevel::LOG_LEVEL_INFO;
    LOG_LEVEL ll = LOG_LEVEL::LL_INFO;
    switch (messageSeverity)
    {
    case VkDebugUtilsMessageSeverityFlagBitsEXT::VK_DEBUG_UTILS_MESSAGE_SEVERITY_VERBOSE_BIT_EXT:
        logLevel = Logger::LogLevel::LOG_LEVEL_TRACE;
        ll = LOG_LEVEL::LL_TRACE;
        break;
    case VkDebugUtilsMessageSeverityFlagBitsEXT::VK_DEBUG_UTILS_MESSAGE_SEVERITY_INFO_BIT_EXT:
        logLevel = Logger::LogLevel::LOG_LEVEL_INFO;
        ll = LOG_LEVEL::LL_INFO;
        break;
    case VkDebugUtilsMessageSeverityFlagBitsEXT::VK_DEBUG_UTILS_MESSAGE_SEVERITY_WARNING_BIT_EXT:
        logLevel = Logger::LogLevel::LOG_LEVEL_WARN;
        ll = LOG_LEVEL::LL_WARN;
        break;
    case VkDebugUtilsMessageSeverityFlagBitsEXT::VK_DEBUG_UTILS_MESSAGE_SEVERITY_ERROR_BIT_EXT:
        logLevel = Logger::LogLevel::LOG_LEVEL_ERROR;
        ll = LOG_LEVEL::LL_ERROR;
        break;
    default:
        logLevel = Logger::LogLevel::LOG_LEVEL_DEBUG;
        ll = LOG_LEVEL::LL_DEBUG;
        break;
    }


    static bool gIgnoredMessageInited = false;
    static std::set<int32_t> gIgnoredMessages;

    if (gIgnoredMessageInited == false && EngineGlobal::GetSettingMgr()->HasKey("VulkanDebugLayerIgnoreMessage"))
    {
        std::vector<double> ignore_setting;
        EngineGlobal::GetSettingMgr()->GetValue("VulkanDebugLayerIgnoreMessage", ignore_setting);
        for (auto& itr : ignore_setting)
        {
            gIgnoredMessages.insert(static_cast<int>(itr));
        }
        gIgnoredMessageInited = true;
    }

    if (!gIgnoredMessages.count(pCallbackData->messageIdNumber))
    {

        LogModule::Instance().Log(LogModule::LogMode::eGame,
                                  logLevel,
                                  CROSS_SRC_LOC,
                                  "\ntype: {}\nmessage id: {}-{}\nmessage:{}\nobjects: {}\n\n",
                                  vk::to_string(static_cast<vk::DebugUtilsMessageTypeFlagsEXT>(messageTypes)),
                                  pCallbackData->messageIdNumber,
                                  pCallbackData->pMessageIdName,
                                  pCallbackData->pMessage,
                                  objectInfos);
    }

#if NGI_BREAK_ON_ERROR
    static const std::set<int32_t> gIgnoredMessages
    {
        // fragment shader has less outputs than the attachments
        602160055,
        // fragment shader has more outputs than the attachments
        101294395,
        // OpImageWrite to any Image whose Image Format is not Unknown must have the Texel operand contain at least as many components as the corresponding VkFormat as given in the SPIR-V Image Format compatibility table
        // https://vulkan.lunarg.com/doc/view/1.3.236.0/windows/1.3-extensions/vkspec.html#VUID-RuntimeSpirv-OpImageWrite-07112
        2144011273,
    };
    if (!gIgnoredMessages.count(pCallbackData->messageIdNumber))
    {
        DEBUG_BREAK;
    }
#endif
    return VK_FALSE;
};

static bool CheckRenderDoc()
{
    VK_CHECK(volkInitialize());

    // create blank instance
    VkApplicationInfo appInfo{
        VK_STRUCTURE_TYPE_APPLICATION_INFO,
        nullptr,
        "EmptyVKInstance",
        0,
        "EmptyVKInstance",
        0,
        // note the appInfo should be VK_API_VERSION_1_1,
        // if validation is closed, we can still use newer version of vulkan function
        cross::VulkanRequiredVersion,
    };

    VkInstanceCreateInfo instanceCreateInfo{VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO, nullptr, 0, &appInfo, 0, nullptr, 0, nullptr};

    VkInstance instance;
    vkCreateInstance(&instanceCreateInfo, nullptr, &instance);
#if !CROSSENGINE_IOS
    NGI_LOG_DEBUG("Vulkan load instance extensions");
    volkLoadInstanceOnly(instance);
#endif
    // phy device
    NGI_LOG_DEBUG("Vulkan choose physical device");

    uint32_t phyDevCount{};
    vkEnumeratePhysicalDevices(instance, &phyDevCount, nullptr);
    std::vector<VkPhysicalDevice> phyDevs{phyDevCount};
    vkEnumeratePhysicalDevices(instance, &phyDevCount, phyDevs.data());

    std::vector<std::unique_ptr<cross::PhysicalDeviceInfo>> phyDevInfos;
    for (auto phyDev : phyDevs)
    {
        phyDevInfos.emplace_back(std::make_unique<cross::PhysicalDeviceInfo>(phyDev, instance));
    }
    std::sort(phyDevInfos.begin(), phyDevInfos.end(), [](auto& a, auto& b) {
        UInt8 aIsDiscrete = a->mProps.properties.deviceType == VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU ? 0xff : 0;
        UInt8 bIsDiscrete = b->mProps.properties.deviceType == VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU ? 0xff : 0;
        if (aIsDiscrete ^ bIsDiscrete)
        {
            if (aIsDiscrete)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return a->mDedicatedMemorySize > b->mDedicatedMemorySize;
        }
    });

    auto phyDevice = phyDevInfos.front()->mPhyDev;
    // check renderdoc
    uint32_t toolCount = 0;

    vkGetPhysicalDeviceToolProperties(phyDevice, &toolCount, nullptr);

    std::vector<VkPhysicalDeviceToolProperties> toolProps(toolCount);

    for (auto& prop : toolProps)
    {
        prop.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_TOOL_PROPERTIES;
    }
    bool hasRenderDoc = false;
    vkGetPhysicalDeviceToolProperties(phyDevice, &toolCount, toolProps.data());
    for (auto& prop : toolProps)
    {
        auto str = std::string(prop.name);
        if (str == "RenderDoc")
        {
            LOG_INFO("RenderDoc detected");
            hasRenderDoc = true;
        }
    }
    // free
    vkDestroyInstance(instance, nullptr);
    instance = nullptr;
    volkFinalize();

    return hasRenderDoc;
}


}   // namespace cross

cross::PhysicalDeviceInfo::PhysicalDeviceInfo(VkPhysicalDevice phyDev, VkInstance inInstance)
    : mPhyDev{phyDev}
{
    vkGetPhysicalDeviceProperties2(phyDev, &mProps);

    bool value = false;
    if (CmdSettings::Inst().GetConfigValueByName<bool>("gUseEngineStats", value))
    {
        if (value)
        {
            LinkVulkanFeatures(mFeatures, mPresentIdFeature);
        }
    }

    LinkVulkanFeatures(mFeatures
        , mVulkan12Features
        , mSynchronization2Feature
    );
    
    auto renderDocEnabled = EngineGlobal::GetSettingMgr()->GetRenderdocEnable();
    if (!renderDocEnabled)
    {
        LinkVulkanFeatures(mSynchronization2Feature
            , mRayTracingPipelineFeatures
            , mRayQueryFeatures
            , mAccelerationStructureFeatures);
    }

    vkGetPhysicalDeviceFeatures2(phyDev, &mFeatures);

    vkGetPhysicalDeviceMemoryProperties(phyDev, &mMemProp);
    for (auto i = 0u; i < mMemProp.memoryHeapCount; i++)
    {
        auto& heap = mMemProp.memoryHeaps[i];
        if (heap.flags & VK_MEMORY_HEAP_DEVICE_LOCAL_BIT)
        {
            mDedicatedMemorySize += heap.size;
        }
    }

    std::uint32_t queFamilyCount{};

    vkGetPhysicalDeviceQueueFamilyProperties2(phyDev, &queFamilyCount, nullptr);
    mQueFamilyProps.resize(queFamilyCount, {VK_STRUCTURE_TYPE_QUEUE_FAMILY_PROPERTIES_2});
    vkGetPhysicalDeviceQueueFamilyProperties2(phyDev, &queFamilyCount, mQueFamilyProps.data());

    mGraphicsQueueInfo =
        GetQueueInfo(VK_QUEUE_GRAPHICS_BIT, VK_QUEUE_COMPUTE_BIT | VK_QUEUE_TRANSFER_BIT, 0);
    mComputeQueueInfo =
        GetQueueInfo(VK_QUEUE_COMPUTE_BIT, VK_QUEUE_TRANSFER_BIT, VK_QUEUE_GRAPHICS_BIT);
    mTransferQueueInfo =
        GetQueueInfo(VK_QUEUE_TRANSFER_BIT, 0, VK_QUEUE_GRAPHICS_BIT | VK_QUEUE_COMPUTE_BIT);


    // Output Device Info
    LOG_INFO("VulkanDevice {}", mProps.properties.deviceName);
    LOG_INFO("DriverVersion {}", mProps.properties.driverVersion);
    LOG_INFO("ApiVersion {}.{}.{}", VK_API_VERSION_MAJOR(mProps.properties.apiVersion), VK_API_VERSION_MINOR(mProps.properties.apiVersion), VK_API_VERSION_PATCH(mProps.properties.apiVersion));
}

cross::PhysicalDeviceInfo::QueueInfo cross::PhysicalDeviceInfo::GetQueueInfo(
    VkQueueFlags mandatoryFlags, VkQueueFlags optionalFlags, VkQueueFlags forbiddenBits)
{
    auto ret =
        std::max_element(mQueFamilyProps.cbegin(), mQueFamilyProps.cend(), [&](auto& a, auto& b) {
            auto& aProp = a.queueFamilyProperties;
            auto& bProp = b.queueFamilyProperties;
            auto  aCond = (aProp.queueFlags & mandatoryFlags) == mandatoryFlags &&
                         (aProp.queueFlags & forbiddenBits) == 0;
            auto bCond = (bProp.queueFlags & mandatoryFlags) == mandatoryFlags &&
                         (bProp.queueFlags & forbiddenBits) == 0;
            if (aCond && !bCond)
            {
                return false;
            }
            else if (!aCond && bCond)
            {
                return true;
            }
            else
            {
                std::bitset<sizeof(VkQueueFlags) * 8> aOptBits{aProp.queueFlags & optionalFlags};
                std::bitset<sizeof(VkQueueFlags) * 8> bOptBits{bProp.queueFlags & optionalFlags};
                return aOptBits.count() < bOptBits.count();
            }
        });

    if (ret != mQueFamilyProps.cend())
    {
        std::set<uint32_t> freeQueues;

        for (auto j = 0u; j < ret->queueFamilyProperties.queueCount; j++)
        {
            freeQueues.emplace(j);
        }
        return {static_cast<UInt32>(std::distance(mQueFamilyProps.cbegin(), ret)),
                std::move(freeQueues)};
    }
    else
    {
        return {};
    }
}

bool cross::VulkanDevice::IsSupportMSAA(NGIResolveType depthResolveType , NGIResolveType stencilResolveType)
{
    auto vkDepthResolveMode = MapResolveMode(depthResolveType);
    auto vkStencilResolveMode = MapResolveMode(stencilResolveType);
    auto& vulkan12Props = mPhysicalDeviceInfo->mVulkan12Props;

    if (vkDepthResolveMode != vkStencilResolveMode)
    {
        if (vkDepthResolveMode != VK_RESOLVE_MODE_NONE && vkStencilResolveMode != VK_RESOLVE_MODE_NONE)
        {
            if (vulkan12Props.independentResolve == VK_FALSE)
            {
                return false;
            }
        }
        else
        {
            if (vulkan12Props.independentResolveNone == VK_FALSE)
            {
                return false;
            }
        }
    }

    if (vkDepthResolveMode != VK_RESOLVE_MODE_NONE && !(vulkan12Props.supportedDepthResolveModes & vkDepthResolveMode))
    {
        return false;
    }

    if (vkStencilResolveMode != VK_RESOLVE_MODE_NONE && !(vulkan12Props.supportedStencilResolveModes & vkStencilResolveMode))
    {
        return false;
    }

    return true;
}

bool cross::VulkanDevice::IsSupportDrawIndirectCount()
{
    return vkCmdDrawIndexedIndirectCount != nullptr;
}

cross::VulkanDevice::VulkanDevice()
{
#ifdef WIN32
    mIsRenderDocEnabled = CheckRenderDoc();
#endif
#if NGI_ENABLE_GPU_DUMP
    mGpuDump = CmdSettings::Inst().gGpuDump;
    if (mGpuDump)
    {
        mGPUCrashTracker.Initialize();
    }
#endif

    NGI_LOG_DEBUG("Vulkan initialize loader");

#if !CROSSENGINE_IOS
    VK_CHECK(volkInitialize());
    // hook create instance
#ifdef WIN32
    if (mIsRenderDocEnabled == false &&EngineGlobal::GetSettingMgr()->GetEnableDLSS())
    {
        //EngineGlobal::GetSettingMgr()->SetFFXFrameInterpolation(false);
        SLWrapper::Get().ManualHookCreateInstance();
    }
#endif
    mDeviceSupportedVulkanVersion = volkGetInstanceVersion();
#else
    uint32_t vkVer = 0;
    VkResult checkResult = vkEnumerateInstanceVersion(&vkVer);
    Assert(checkResult == VK_SUCCESS);
#endif
    if (mDeviceSupportedVulkanVersion < VulkanRequiredVersion)
    {
        LOG_FATAL("Vulkan version less than Leaset supported version {}, fail to initialize!", VulkanRequiredVersion);
        return;
    }

    if (CmdSettings::Inst().gValidation)
    {
        //https ://registry.khronos.org/vulkan/specs/1.3-extensions/man/html/VkApplicationInfo.html
         // note the appInfo should be VK_API_VERSION_1_1,
            // if validation is closed, we can still use newer version of vulkan function
        mVulkanVersion = VulkanRequiredVersion;
    }
    else
    {
        // use the maximum supported;
        mVulkanVersion = mDeviceSupportedVulkanVersion;
    }
}

cross::VulkanDevice::~VulkanDevice()
{
    NGI_LOG_DEBUG("Vulkan destroy device");
    mDefaultVertexBuffer.reset();
    VK_CHECK(vkDeviceWaitIdle(mDevice));
    vkDestroyDescriptorSetLayout(mDevice, mEmptyDescriptorSetLayout, gVkAllocCallback);
    vkDestroyDescriptorPool(mDevice, mDescriptorPool, gVkAllocCallback);
    if (mExportablePool)
    {
        delete mexportMemory;
        vmaDestroyPool(mAllocator, mExportablePool);
    }

    vmaDestroyAllocator(mAllocator);
    vkDestroyDevice(mDevice, gVkAllocCallback);
    if (VulkanCapability::Inst().DebugUtil)
    {
        vkDestroyDebugUtilsMessengerEXT(mInstance, mDebugMessenger, gVkAllocCallback);
    }
    vkDestroyInstance(mInstance, gVkAllocCallback);
}

void cross::VulkanDevice::Initialize()
{
    NGI_LOG_DEBUG("Vulkan check layers");

    uint32_t layerCount = 0;
    VK_CHECK(vkEnumerateInstanceLayerProperties(&layerCount, nullptr));
    std::vector<VkLayerProperties> layerProps{layerCount};
    VK_CHECK(vkEnumerateInstanceLayerProperties(&layerCount, layerProps.data()));
    std::vector<const char*> foundedLayers;
    for (auto requiredLayer : gMandatoryLayers)
    {
        if (std::any_of(layerProps.cbegin(), layerProps.cend(), [=](auto& layerProp) {
                return strncmp(layerProp.layerName, requiredLayer, VK_MAX_EXTENSION_NAME_SIZE) == 0;
            }))
        {
            foundedLayers.emplace_back(requiredLayer);
        }
        else
        {
            LOG_WARN("Can't find required vulkan layer: {}", requiredLayer);
        }
    }

    if (CmdSettings::Inst().gValidation)
    {
        const char* validation_layer = "VK_LAYER_KHRONOS_validation";
        if (std::any_of(layerProps.cbegin(), layerProps.cend(), [=](auto& layerProp) {
            return strncmp(layerProp.layerName, validation_layer, VK_MAX_EXTENSION_NAME_SIZE) == 0;
            }))
        {
            foundedLayers.emplace_back(validation_layer);
        }
    }

    NGI_LOG_DEBUG("Vulkan check instance extensions");

    uint32_t instExtcount = 0;
    VK_CHECK(vkEnumerateInstanceExtensionProperties(nullptr, &instExtcount, nullptr));
    std::vector<VkExtensionProperties> insExtProps{instExtcount};
    VK_CHECK(vkEnumerateInstanceExtensionProperties(nullptr, &instExtcount, insExtProps.data()));
    auto& instExts = mInstExtension;
    instExts = gMandatoryInstanceExtensions;

#if NCNN_ASSEMBLE
    NcnnVkRequirements ncnnRequire;
    std::vector<const char*> ncnnInstExts = ncnnRequire.GetNcnnInstanceExtensions(instExtcount, insExtProps);
    instExts.insert(instExts.end(), ncnnInstExts.begin(), ncnnInstExts.end());
#endif

    for (auto instExt : instExts)
    {
        if (std::none_of(insExtProps.cbegin(), insExtProps.cend(), [=](auto& instExtprop) {
                return std::strncmp(
                           instExtprop.extensionName, instExt, VK_MAX_EXTENSION_NAME_SIZE) == 0;
            }))
        {
            LOG_FATAL("Can't find required vulkan instance extension: {}", instExt);
            Assert(false);
            return;
        }
    }

    if (std::any_of(insExtProps.cbegin(), insExtProps.cend(), [=](auto& instExtprop) {
            return std::strncmp(instExtprop.extensionName,
                                VK_EXT_DEBUG_UTILS_EXTENSION_NAME,
                                VK_MAX_EXTENSION_NAME_SIZE) == 0;
        }))
    {
        VulkanCapability::Inst().DebugUtil = true;
        instExts.emplace_back(VK_EXT_DEBUG_UTILS_EXTENSION_NAME);
    }
    else
    {
        LOG_INFO("Can't find extension: {}", VK_EXT_DEBUG_UTILS_EXTENSION_NAME);
        VulkanCapability::Inst().DebugUtil = false;
    }

    for (auto& opt_inst_ext : gOptionalInstanceExtensions)
    {
        if (std::any_of(insExtProps.cbegin(), insExtProps.cend(), [=](auto& instExtprop) {
            return std::strncmp(opt_inst_ext, instExtprop.extensionName, VK_MAX_EXTENSION_NAME_SIZE) == 0;
            }))
        {
            instExts.emplace_back(opt_inst_ext);
        }
    }

    NGI_LOG_DEBUG("Vulkan create instance");

    VkApplicationInfo appInfo{
        VK_STRUCTURE_TYPE_APPLICATION_INFO,
        nullptr,
        "CrossEditor",
        0,
        "CrossEngine",
        0,
        // note the appInfo should be VK_API_VERSION_1_1,
        // if validation is closed, we can still use newer version of vulkan function
        VulkanRequiredVersion,
    };

    VkDebugUtilsMessengerCreateInfoEXT debugUtilMessengerCreateInfo{
        VK_STRUCTURE_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT,
        nullptr,
        0,
        VK_DEBUG_UTILS_MESSAGE_SEVERITY_WARNING_BIT_EXT |
            VK_DEBUG_UTILS_MESSAGE_SEVERITY_ERROR_BIT_EXT,
        VK_DEBUG_UTILS_MESSAGE_TYPE_GENERAL_BIT_EXT |
            VK_DEBUG_UTILS_MESSAGE_TYPE_PERFORMANCE_BIT_EXT |
            VK_DEBUG_UTILS_MESSAGE_TYPE_VALIDATION_BIT_EXT,
        DebugUtilsMessengerCallback,
        nullptr,
    };
    VkInstanceCreateInfo instanceCreateInfo{
        VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO,
        VulkanCapability::Inst().DebugUtil ? &debugUtilMessengerCreateInfo : nullptr,
        0,
        &appInfo,
        static_cast<uint32_t>(foundedLayers.size()),
        foundedLayers.empty() ? nullptr : foundedLayers.data(),
        static_cast<uint32_t>(instExts.size()),
        instExts.empty() ? nullptr : instExts.data(),
    };

    mInstance = CreateInstance(instanceCreateInfo);

#if !CROSSENGINE_IOS
    NGI_LOG_DEBUG("Vulkan load instance extensions");
    volkLoadInstanceOnly(mInstance);
    // hook create device
#ifdef WIN32
    if (SLWrapper::Get().m_sl_initialised)
    {
        SLWrapper::Get().ManualHookCreateDevice(mInstance);
    }
#endif
#endif

    if (VulkanCapability::Inst().DebugUtil)
    {
        NGI_LOG_DEBUG("Vulkan create debug messenger");
        VK_CHECK(vkCreateDebugUtilsMessengerEXT(
            mInstance, &debugUtilMessengerCreateInfo, gVkAllocCallback, &mDebugMessenger));
    }

    mPhysicalDeviceInfo = GetPhysicalDevice();
    mPhysicalDevice     = mPhysicalDeviceInfo->mPhyDev;

    if (mPhysicalDeviceInfo->mFeatures.features.geometryShader)
    {
        VulkanCapability::Inst().GeometryShader = true;
    }

    if (mPhysicalDeviceInfo->mFeatures.features.tessellationShader)
    {
        VulkanCapability::Inst().TessellationShader = true;
    }
    
    if (auto& feature = mPhysicalDeviceInfo->mVulkan12Features;
        feature.descriptorIndexing &&
        feature.runtimeDescriptorArray &&
        feature.descriptorBindingVariableDescriptorCount &&
        feature.descriptorBindingPartiallyBound &&
        feature.descriptorBindingSampledImageUpdateAfterBind &&
        feature.shaderSampledImageArrayNonUniformIndexing &&
        feature.shaderStorageBufferArrayNonUniformIndexing &&
        feature.descriptorBindingUniformBufferUpdateAfterBind &&
        feature.shaderUniformBufferArrayNonUniformIndexing &&
        feature.descriptorBindingStorageBufferUpdateAfterBind &&
        feature.descriptorBindingUniformTexelBufferUpdateAfterBind &&
        feature.shaderUniformTexelBufferArrayDynamicIndexing &&
        feature.shaderUniformTexelBufferArrayNonUniformIndexing)
    {
        VulkanCapability::Inst().Bindless = true;
    }

    if (mPhysicalDeviceInfo->mSynchronization2Feature.synchronization2)
    {
        VulkanCapability::Inst().Synchronization2 = true;
    }

    if (mPhysicalDeviceInfo->mRayTracingPipelineFeatures.rayTracingPipeline &&
        mPhysicalDeviceInfo->mAccelerationStructureFeatures.accelerationStructure &&
        mPhysicalDeviceInfo->mRayQueryFeatures.rayQuery &&
        mPhysicalDeviceInfo->mVulkan12Features.bufferDeviceAddress &&
        VulkanCapability::Inst().Bindless)
    {
        VulkanCapability::Inst().RayTracing = true;
    }

    NGI_LOG_DEBUG("Vulkan create device, queue create info");

//    auto queFamilyCount       = static_cast<uint32_t>(mPhysicalDeviceInfo->mQueFamilyProps.size());
//    auto queCreateFamilyInfos = std::make_unique<VkDeviceQueueCreateInfo[]>(queFamilyCount);
//
//    auto queCount = 0u;
//    for (auto queFamilyProp : mPhysicalDeviceInfo->mQueFamilyProps)
//        queCount += queFamilyProp.queueFamilyProperties.queueCount;
//    //maybe need #ifdef DLSSG
//#ifdef STREAMLINE_FEATURE_DLSS_FG
//        queCount -= 5;
//#endif
//    auto quePriorities = std::make_unique<float[]>(queCount);
//
//    auto quePrioritiesPerFamily = quePriorities.get();
//    for (auto i = 0u; i < queFamilyCount; i++)
//    {
//        auto queFamilyProp = mPhysicalDeviceInfo->mQueFamilyProps[i].queueFamilyProperties;
//        // maybe need #ifdef DLSSG
//#ifdef STREAMLINE_FEATURE_DLSS_FG
//         if (i == 0)
//         {
//             queFamilyProp.queueCount -= 2;
//         }
//         else if (i == 2)
//         {
//             queFamilyProp.queueCount -= 2;
//         }
//         else if (i == 5)
//         {
//             queFamilyProp.queueCount -= 1;
//         }
//#endif
//
//        
//        std::fill_n(quePrioritiesPerFamily, queFamilyProp.queueCount, 1.f);
//        queCreateFamilyInfos[i] = {
//            VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO,
//            nullptr,
//            0,
//            i,
//            queFamilyProp.queueCount,
//            quePrioritiesPerFamily,
//        };
//        quePrioritiesPerFamily += queFamilyProp.queueCount;
//    }


    std::vector<VkDeviceQueueCreateInfo> queCreateFamilyInfos;
    float queuePriority = 1.0f;
    if (EngineGlobal::GetSettingMgr()->GetFFXFrameInterpolation())
    {
        VkDeviceQueueCreateInfo graphicsQueueInfo = {VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO, nullptr, 0, mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex, 2, &queuePriority};
        queCreateFamilyInfos.push_back(graphicsQueueInfo);
    }
    else
    {
        VkDeviceQueueCreateInfo graphicsQueueInfo = {VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO, nullptr, 0, mPhysicalDeviceInfo->mGraphicsQueueInfo.familyIndex, 1, &queuePriority};
        queCreateFamilyInfos.push_back(graphicsQueueInfo);
    }


    VkDeviceQueueCreateInfo computeQueueInfo = {VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO,
                                                nullptr,
                                                0, mPhysicalDeviceInfo->mComputeQueueInfo.familyIndex,   
                                                1,                          
                                                &queuePriority};
    queCreateFamilyInfos.push_back(computeQueueInfo);

    VkDeviceQueueCreateInfo transferQueueInfo = {VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO,
                                                 nullptr,
                                                 0, mPhysicalDeviceInfo->mTransferQueueInfo.familyIndex,   
                                                 1,                          
                                                 &queuePriority};
    queCreateFamilyInfos.push_back(transferQueueInfo);

    NGI_LOG_DEBUG("Vulkan create device, device extension info");

    std::uint32_t devExtCount{};
    VK_CHECK(vkEnumerateDeviceExtensionProperties(mPhysicalDevice, nullptr, &devExtCount, nullptr));

    std::vector<VkExtensionProperties> devExtProps{devExtCount};
    VK_CHECK(vkEnumerateDeviceExtensionProperties(mPhysicalDevice, nullptr, &devExtCount, devExtProps.data()));

    std::vector<const char*>& devExts = mDeviceExtension;
    for (auto devExt : gMandatoryDeviceExtensions)
    {
        if (std::any_of(devExtProps.cbegin(), devExtProps.cend(), [=](auto& devExtProp) {
                return strncmp(devExtProp.extensionName, devExt, VK_MAX_EXTENSION_NAME_SIZE) == 0;
            }))
        {
            devExts.emplace_back(devExt);
            LOG_INFO("Enable mandatory device ext {}", devExt);
        }
        else
        {
            LOG_FATAL("Fail to find mandatory device extension: {}", devExt);
            Assert(false);
            return;
        }
    }
    for (auto devExt : gOptionalDeviceExtensions)
    {
        if (std::any_of(devExtProps.cbegin(), devExtProps.cend(), [=](auto& devExtProp) {
                return strncmp(devExtProp.extensionName, devExt, VK_MAX_EXTENSION_NAME_SIZE) == 0;
            }))
        {
            devExts.emplace_back(devExt);
            LOG_INFO("Enable optional device ext {}", devExt);
        }
        else
        {
            LOG_WARN("Fail to find optional device extension: {}", devExt);
        }
    }

    if (CmdSettings::Inst().gVsync)
    {
        const char* devExt1 = "VK_KHR_present_wait";
        if (std::any_of(devExtProps.cbegin(), devExtProps.cend(), [=](auto& devExtProp) { return strncmp(devExtProp.extensionName, devExt1, VK_MAX_EXTENSION_NAME_SIZE) == 0; }))
        {
            devExts.emplace_back(devExt1);
        }

        const char* devExt2 = "VK_KHR_present_id";
        if (std::any_of(devExtProps.cbegin(), devExtProps.cend(), [=](auto& devExtProp) { return strncmp(devExtProp.extensionName, devExt2, VK_MAX_EXTENSION_NAME_SIZE) == 0; }))
        {
            devExts.emplace_back(devExt2);
        }
    }
#if CROSSENGINE_OSX   // for fix the validation issue on macos
    devExts.emplace_back("VK_KHR_portability_subset");
#endif

    NGI_LOG_DEBUG("Vulkan create device, finally");

    void* pNext = &mPhysicalDeviceInfo->mFeatures;
#if NGI_ENABLE_GPU_DUMP

    VkDeviceDiagnosticsConfigFlagsNV flags = VK_DEVICE_DIAGNOSTICS_CONFIG_ENABLE_RESOURCE_TRACKING_BIT_NV; // Enable tracking of resources.

#if CROSSENGINE_DEBUG | CROSSENGINE_RELWITHDEBINFO
    flags |= VK_DEVICE_DIAGNOSTICS_CONFIG_ENABLE_AUTOMATIC_CHECKPOINTS_BIT_NV; // Capture call stacks for all draw calls, compute dispatches, and resource copies.
    flags |= VK_DEVICE_DIAGNOSTICS_CONFIG_ENABLE_SHADER_DEBUG_INFO_BIT_NV; // Generate debug information for shaders.
#endif
    // Set up device creation info for Aftermath feature flag configuration.
    VkDeviceDiagnosticsConfigCreateInfoNV aftermathInfo
    {
        VK_STRUCTURE_TYPE_DEVICE_DIAGNOSTICS_CONFIG_CREATE_INFO_NV,
        &mPhysicalDeviceInfo->mFeatures,
        flags
    };
    if (mGpuDump)
    {
        pNext = &aftermathInfo;
    }
#endif

#if NCNN_ASSEMBLE
    ncnnRequire.InitNcnnGpuInfo(mPhysicalDevice, devExtCount, devExtProps);
    // request ncnn vulkan device extensions
    std::vector<const char*> ncnnDevExtensions = ncnnRequire.GetNcnnDeviceExtensions();
    for (auto ncnnDevExt : ncnnDevExtensions)
    {
        if (std::any_of(devExtProps.cbegin(), devExtProps.cend(), [=](auto& devExtProp) { return strncmp(devExtProp.extensionName, ncnnDevExt, VK_MAX_EXTENSION_NAME_SIZE) == 0; }))
        {
            devExts.emplace_back(ncnnDevExt);
        }
        else
        {
            LOG_WARN("Fail to find optional device extension: {}", ncnnDevExt);
        }
    }
    // request ncnn vulkan device feature chains
    ncnnRequire.SetNcnnDeviceExtensionsFeaturesChain(mPhysicalDeviceInfo->mVulkan11Features, mPhysicalDeviceInfo->mVulkan12Features, mPhysicalDeviceInfo->mCooperativeMatrixFeature);
#endif

    VkDeviceCreateInfo deviceCreateInfo
    {
        VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO,
        pNext,
        0,
        3,
        queCreateFamilyInfos.data(),
        0,
        nullptr,                                // device layer was deprecated
        static_cast<uint32_t>(devExts.size()),
        devExts.data(),
        nullptr
    };
    mDevice = CreateDevice(deviceCreateInfo);

    NGI_LOG_DEBUG("Vulkan load device extensions");
#if !CROSSENGINE_IOS
    volkLoadDevice(mDevice);
    // hook device related
#ifdef WIN32
    if (SLWrapper::Get().m_sl_initialised)
    {
        SLWrapper::Get().ManualHookAPIs(mDevice);
    }
#endif
#endif

    mBudgetMemoryEnable = IsExtensionEnabled(VK_KHR_GET_PHYSICAL_DEVICE_PROPERTIES_2_EXTENSION_NAME) && IsExtensionEnabled(VK_EXT_MEMORY_BUDGET_EXTENSION_NAME);
    bool memoryPriorityEnable = IsExtensionEnabled(VK_EXT_MEMORY_PRIORITY_EXTENSION_NAME);

    NGI_LOG_DEBUG("Create memory allocator");

    VmaVulkanFunctions funcs{
        vkGetInstanceProcAddr,
        vkGetDeviceProcAddr,
        vkGetPhysicalDeviceProperties,
        vkGetPhysicalDeviceMemoryProperties,
        vkAllocateMemory,
        vkFreeMemory,
        vkMapMemory,
        vkUnmapMemory,
        vkFlushMappedMemoryRanges,
        vkInvalidateMappedMemoryRanges,
        vkBindBufferMemory,
        vkBindImageMemory,
        vkGetBufferMemoryRequirements,
        vkGetImageMemoryRequirements,
        vkCreateBuffer,
        vkDestroyBuffer,
        vkCreateImage,
        vkDestroyImage,
        vkCmdCopyBuffer,
        vkGetBufferMemoryRequirements2,
        vkGetImageMemoryRequirements2,
        vkBindBufferMemory2,
        vkBindImageMemory2,
        vkGetPhysicalDeviceMemoryProperties2,
        vkGetDeviceBufferMemoryRequirementsKHR,
        vkGetDeviceImageMemoryRequirementsKHR,
    };

    UInt32 flag = 0u;
    if (IsMemoryBudgetEnabled())
        flag |= VMA_ALLOCATOR_CREATE_EXT_MEMORY_BUDGET_BIT;
    if (memoryPriorityEnable)
        flag |= VMA_ALLOCATOR_CREATE_EXT_MEMORY_PRIORITY_BIT;

    flag |= VMA_ALLOCATOR_CREATE_BUFFER_DEVICE_ADDRESS_BIT;

    VmaAllocatorCreateInfo allocatorCreateInfo{
        flag,
        mPhysicalDevice,
        mDevice,
        0,
        nullptr,
        nullptr,
        nullptr,
        &funcs,
        mInstance,
        mVulkanVersion,
    };
    VK_CHECK(vmaCreateAllocator(&allocatorCreateInfo, &mAllocator));
    vkGetDeviceProcAddrProxy = funcs.vkGetDeviceProcAddr;
    //vkGetBufferMemoryRequirements2Proxy = funcs.vkGetBufferMemoryRequirements2KHR;
    NGI_LOG_DEBUG("Create descriptor pool");

    std::vector<VkDescriptorPoolSize> poolSizes{
        {
            VK_DESCRIPTOR_TYPE_SAMPLER,
            1024,
        },
        {
            VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE,
            4096,
        },
        {
            VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
            1024,
        },
        {
            VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER,
            1024,
        },
        {
            VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER,
            1024,
        },
        {
            VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER,
            4096,
        },
        {
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            1024,
        },
        {
            VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC,
            4096,
        },
        {
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC,
            1024,
        },
        {
            VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT,
            1024,
        },
        {
            VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR,
            10
        }
    };
    VkDescriptorPoolCreateInfo descriptorPoolInfo{
        VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO,
        nullptr,
        VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT,
        4096,
        static_cast<uint32_t>(poolSizes.size()),
        poolSizes.data(),
    };
    VK_CHECK(vkCreateDescriptorPool(mDevice, &descriptorPoolInfo, gVkAllocCallback, &mDescriptorPool));

    CreateEmptyDescriptorSetLayout();

    CreateDefaultVertexBuffer();
    mVulkanTimeStamp = std::make_unique<VulkanTimeStamp>();
}

VkInstance cross::VulkanDevice::CreateInstance(VkInstanceCreateInfo& info)
{
    VkInstance instance;
    VK_CHECK(vkCreateInstance(&info, gVkAllocCallback, &instance));
    return instance;
}

std::unique_ptr<cross::PhysicalDeviceInfo> cross::VulkanDevice::GetPhysicalDevice()
{
    NGI_LOG_DEBUG("Vulkan choose physical device");

    uint32_t phyDevCount{};
    VK_CHECK(vkEnumeratePhysicalDevices(mInstance, &phyDevCount, nullptr));
    std::vector<VkPhysicalDevice> phyDevs{ phyDevCount };
    VK_CHECK(vkEnumeratePhysicalDevices(mInstance, &phyDevCount, phyDevs.data()));

    std::vector<std::unique_ptr<PhysicalDeviceInfo>> phyDevInfos;
    for (auto phyDev : phyDevs)
    {
        phyDevInfos.emplace_back(std::make_unique<PhysicalDeviceInfo>(phyDev, mInstance));
    }
    std::sort(
        phyDevInfos.begin(), phyDevInfos.end(), [](auto& a, auto& b)
        {
            UInt8 aIsDiscrete =
                a->mProps.properties.deviceType == VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU ? 0xff : 0;
            UInt8 bIsDiscrete =
                b->mProps.properties.deviceType == VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU ? 0xff : 0;
            if (aIsDiscrete ^ bIsDiscrete)
            {
                if (aIsDiscrete)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return a->mDedicatedMemorySize > b->mDedicatedMemorySize;
            }
        });

    return std::move(phyDevInfos.front());
}

VkDevice cross::VulkanDevice::CreateDevice(VkDeviceCreateInfo& info)
{
    VkDevice device;
    VK_CHECK(vkCreateDevice(mPhysicalDevice, &info, gVkAllocCallback, &device));
    return device;
}

void cross::VulkanDevice::SetDebugName(VkObjectType objectType, void* objectHandle, const char* pObjectName)
{
    if (VulkanCapability::Inst().DebugUtil)
    {
        VkDebugUtilsObjectNameInfoEXT nameInfo
        {
            VK_STRUCTURE_TYPE_DEBUG_UTILS_OBJECT_NAME_INFO_EXT,
            nullptr,
            objectType,
            reinterpret_cast<uint64_t>(objectHandle),
            pObjectName,
        };
        VK_CHECK(vkSetDebugUtilsObjectNameEXT(mDevice, &nameInfo));
    }
}

void cross::VulkanDevice::CreateEmptyDescriptorSetLayout()
{
    VkDescriptorSetLayoutCreateInfo setLayoutDesc{
        VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO,
        nullptr,
        0,
        0,
        nullptr,
    };
    VK_CHECK(vkCreateDescriptorSetLayout(
        mDevice, &setLayoutDesc, gVkAllocCallback, &mEmptyDescriptorSetLayout));
}

void cross::VulkanDevice::CreateDefaultVertexBuffer()
{
    NGIBufferDesc desc
    {
        sizeof(DefaultVertex),
        NGIBufferUsage::VertexBuffer,
    };
    mDefaultVertexBuffer.reset(static_cast<VulkanStagingBuffer*>(CreateStagingBuffer(desc)));
    mDefaultVertexBuffer->SetDebugName("DefaultVertexBuffer");
    auto* data = mDefaultVertexBuffer->MapRange(NGIBufferUsage::VertexBuffer, 0, desc.Size);
    DefaultVertex defaultVertex;
    memcpy(data, &defaultVertex, sizeof(DefaultVertex));
    mDefaultVertexBuffer->UnmapRange(0, desc.Size);
}

void cross::VulkanDevice::WaitIdle()
{
    VK_CHECK(vkDeviceWaitIdle(mDevice));
}

cross::NGIFormatCapability cross::VulkanDevice::GetFormatCapability(GraphicsFormat format)
{
    // load capability
    {
        std::shared_lock read_ocker(mFormatCapabilityLocker);
        auto it = mFormatCapability.find(format);
        if (it != mFormatCapability.end())
            return it->second;
    }

    VkFormatProperties2 property{
        VK_STRUCTURE_TYPE_FORMAT_PROPERTIES_2,
        nullptr,
    };
    vkGetPhysicalDeviceFormatProperties2(mPhysicalDevice, MapGraphicsFormat(format), &property);
    NGIFormatCapability capability{};

    constexpr static std::tuple<VkFormatFeatureFlags, NGIBufferUsage> gBufferFeatureMapping[]{
        {
            VK_FORMAT_FEATURE_VERTEX_BUFFER_BIT,
            NGIBufferUsage::VertexBuffer,
        },
        {
            VK_FORMAT_FEATURE_UNIFORM_TEXEL_BUFFER_BIT,
            NGIBufferUsage::TexelBuffer,
        },
        {
            VK_FORMAT_FEATURE_STORAGE_TEXEL_BUFFER_BIT,
            NGIBufferUsage::RWTexelBuffer,
        },
    };

    for (auto [feature, usage] : gBufferFeatureMapping)
    {
        if ((property.formatProperties.bufferFeatures & feature) == feature)
        {
            capability.BufferCapability |= usage;
        }
    }

    constexpr static std::tuple<VkFormatFeatureFlags, NGITextureUsage> gTextureFeatureMapping[]{
        {
            VK_FORMAT_FEATURE_TRANSFER_SRC_BIT,
            NGITextureUsage::CopySrc,
        },
        {
            VK_FORMAT_FEATURE_TRANSFER_DST_BIT,
            NGITextureUsage::CopyDst,
        },
        {
            VK_FORMAT_FEATURE_COLOR_ATTACHMENT_BIT |
            VK_FORMAT_FEATURE_COLOR_ATTACHMENT_BLEND_BIT,
            NGITextureUsage::RenderTarget,
        },
        {
            VK_FORMAT_FEATURE_DEPTH_STENCIL_ATTACHMENT_BIT,
            NGITextureUsage::DepthStencil,
        },
        {
            VK_FORMAT_FEATURE_STORAGE_IMAGE_BIT,
            NGITextureUsage::UnorderedAccess,
        },
        {
            VK_FORMAT_FEATURE_SAMPLED_IMAGE_BIT |
            VK_FORMAT_FEATURE_SAMPLED_IMAGE_FILTER_LINEAR_BIT,
            NGITextureUsage::ShaderResource,
        },
        {
            VK_FORMAT_FEATURE_COLOR_ATTACHMENT_BIT |
            VK_FORMAT_FEATURE_SAMPLED_IMAGE_BIT |
            VK_FORMAT_FEATURE_SAMPLED_IMAGE_FILTER_LINEAR_BIT,
            NGITextureUsage::SubpassInput,
        },
    };

    for (auto [feature, usage] : gTextureFeatureMapping)
    {
        if ((property.formatProperties.optimalTilingFeatures & feature) == feature)
        {
            capability.TextureCapability |= usage;
        }
    }

    if (EnumHasAnyFlags(capability.TextureCapability, NGITextureUsage::RenderTarget | NGITextureUsage::DepthStencil))
    {
        // get MultiSampleCounts
        VkImageFormatProperties2 formatProperties{VK_STRUCTURE_TYPE_IMAGE_FORMAT_PROPERTIES_2, nullptr};
        VkImageUsageFlags usage = EnumHasAnyFlags(capability.TextureCapability, NGITextureUsage::DepthStencil) ? VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT : VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
        VkPhysicalDeviceImageFormatInfo2 imageFormatInfo{
            VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_IMAGE_FORMAT_INFO_2,
            nullptr,
            MapGraphicsFormat(format),
            VK_IMAGE_TYPE_2D,
            VK_IMAGE_TILING_OPTIMAL,
            usage,
            0,
        };

        VK_CHECK(vkGetPhysicalDeviceImageFormatProperties2(mPhysicalDevice, &imageFormatInfo, &formatProperties));
        capability.MultiSampleCounts = formatProperties.imageFormatProperties.sampleCounts;
    }
    else
    {
        capability.MultiSampleCounts = 1;
    }

    // save capability
    {
        std::unique_lock write_ocker(mFormatCapabilityLocker);
        mFormatCapability.emplace(format, capability);
    }

    return capability;
}

std::shared_ptr<cross::GPUResourceStatistics> cross::VulkanDevice::GetGpuResourceStatistics()
{
    uint32_t num = mPhysicalDeviceInfo->mMemProp.memoryHeapCount;

    VmaBudget * budgets = new VmaBudget[num];

    vmaGetHeapBudgets(mAllocator, budgets);

    for (UInt32 i = 0; i < num; i++)
    {
        if (mPhysicalDeviceInfo->mMemProp.memoryHeaps[i].flags == VK_MEMORY_HEAP_DEVICE_LOCAL_BIT)
        {
            mGpuResourcesStatistic->mDeviceResources.usage = static_cast<float>(budgets[i].usage) / 1024.f / 1024.f;
            mGpuResourcesStatistic->mDeviceResources.available = static_cast<float>(budgets[i].budget) / 1024.f / 1024.f;
            mGpuResourcesStatistic->mDeviceResources.allocated = static_cast<float>(budgets[i].statistics.blockBytes) / 1024.f / 1024.f;
            mGpuResourcesStatistic->mDeviceResources.used = static_cast<float>(budgets[i].statistics.allocationBytes) / 1024.f / 1024.f;
            break;
        }
    }

    delete[] budgets;

    return mGpuResourcesStatistic;
}

NGI_API void cross::VulkanDevice::DumpVideoGraphicsMemory(const std::string name)
{
    char* pstr = nullptr;
    vmaBuildStatsString(mAllocator, &pstr, true);
    std::string file_name = PathHelper::GetSavedPath() +std::string("/Statistics/GPUMem_") + name + "_" + cross::time::TimeStamp() + ".json";
    std::string content = pstr;
    EngineGlobal::GetFileSystem()->Save(file_name, content.c_str(), content.size());
    vmaFreeStatsString(mAllocator, pstr);

    auto engine_statistics = GetGpuResourceStatistics()->ToJson();

    file_name = PathHelper::GetSavedPath() + std::string("/Statistics/InEngine_GPUMem_") + name + "_" + cross::time::TimeStamp() + ".json";
    std::string engine_result = engine_statistics.FormatToJson();
    EngineGlobal::GetFileSystem()->Save(file_name, engine_result.c_str(), engine_result.size());
}

cross::NGIQueryHeap* cross::VulkanDevice::CreateQueryHeap(const NGIQueryHeapDesc& desc)
{
    return new VulkanQueryHeap{desc, this};
}


/*
 * bufferOffset must be multible of 4 when used by copy queue, generialize to 4
 * situations where offset was not multiple of 4 may be copy multi planner or depth stencil format
 * in non-copy queue, offset may be multiple of 1 or 2
 */
SizeType cross::VulkanDevice::GetTextureDataPlacementAlignment(GraphicsFormat format)
{
    return std::max(GetFormatTexelBlockProperty(format).Size, 4u);
}

SizeType cross::VulkanDevice::GetTextureDataPitchAlignment(GraphicsFormat format)
{
    return std::max(GetFormatTexelBlockProperty(format).Size, 4u);
}

bool cross::VulkanDevice::IsExtensionEnabled(const char* ext) const
{
    if (std::any_of(mInstExtension.cbegin(), mInstExtension.cend(), [=](auto& inst_ext) {
        return strncmp(inst_ext, ext, VK_MAX_EXTENSION_NAME_SIZE) == 0;
        }))
    {
        return true;
    }

    return std::any_of(mDeviceExtension.cbegin(), mDeviceExtension.cend(), [=](auto& dev_ext) {
            return strncmp(dev_ext, ext, VK_MAX_EXTENSION_NAME_SIZE) == 0;
            });
}

bool cross::VulkanDevice::IsMemoryBudgetEnabled() const
{
    return mBudgetMemoryEnable;
}

void cross::VulkanDevice::SetFrameIndex(UInt32 frameIndex)
{
    vmaSetCurrentFrameIndex(mAllocator, frameIndex);
}

std::vector<std::tuple<cross::GPUProfiling::GPUProfilingContextInfo, std::vector<cross::GPUProfiling::GPUProfilingItem>>> cross::VulkanDevice::PopGpuProfileItems()
{
    return mVulkanTimeStamp->GetGpuProfilingItems();
}

cross::VulkanCapability& cross::VulkanCapability::Inst()
{
    static VulkanCapability gCap{};
    return gCap;
}

std::vector<const char*> cross::NcnnVkRequirements::GetNcnnInstanceExtensions(uint32_t instanceExtensionPropertyCount, std::vector<VkExtensionProperties>& instanceExtensionProperties)
{
    if (instanceExtensionPropertyCount == 0 || instanceExtensionProperties.empty())
    {
        LOG_FATAL("GetNcnnInstanceExtensions failed !!!");
    }

    std::vector<const char*> required = {};

    support_VK_KHR_get_physical_device_properties2 = 0;
    support_VK_KHR_get_surface_capabilities2 = 0;
    support_VK_KHR_portability_enumeration = 0;
    support_VK_KHR_surface = 0;
    support_VK_EXT_debug_utils = 0;

    for (uint32_t j = 0; j < instanceExtensionPropertyCount; j++)
    {
        const VkExtensionProperties& exp = instanceExtensionProperties[j];
        //NCNN_LOGE("instance extension %s = %u", exp.extensionName, exp.specVersion);

        if (strcmp(exp.extensionName, "VK_KHR_external_memory_capabilities") == 0)
            support_VK_KHR_external_memory_capabilities = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_get_physical_device_properties2") == 0)
            support_VK_KHR_get_physical_device_properties2 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_get_surface_capabilities2") == 0)
            support_VK_KHR_get_surface_capabilities2 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_portability_enumeration") == 0)
            support_VK_KHR_portability_enumeration = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_surface") == 0)
            support_VK_KHR_surface = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_EXT_debug_utils") == 0)
            support_VK_EXT_debug_utils = exp.specVersion;
    }

    if (support_VK_KHR_external_memory_capabilities)
        required.push_back("VK_KHR_external_memory_capabilities");
    if (support_VK_KHR_get_physical_device_properties2)
        required.push_back("VK_KHR_get_physical_device_properties2");
    if (support_VK_KHR_get_surface_capabilities2)
        required.push_back("VK_KHR_get_surface_capabilities2");
    if (support_VK_KHR_portability_enumeration)
        required.push_back("VK_KHR_portability_enumeration");
    if (support_VK_KHR_surface)
        required.push_back("VK_KHR_surface");
#if ENABLE_VALIDATION_LAYER
    if (support_VK_EXT_debug_utils)
        required.push_back("VK_EXT_debug_utils");
#endif   // ENABLE_VALIDATION_LAYER
    return required;
}

void cross::NcnnVkRequirements::InitNcnnGpuInfo(VkPhysicalDevice& physicalDevice, uint32_t deviceExtensionPropertyCount, std::vector<VkExtensionProperties>& deviceExtensionProperties)
{
    if (deviceExtensionPropertyCount == 0 || deviceExtensionProperties.empty())
    {
        LOG_FATAL("InitNcnnGpuInfo failed !!!");
        return;
    }
    GpuInfo& gpu_info = gpuInfo;

    // extension capability
    gpu_info.bug_implicit_fp16_arithmetic = false;

    gpu_info.support_VK_KHR_8bit_storage = 0;
    gpu_info.support_VK_KHR_16bit_storage = 0;
    gpu_info.support_VK_KHR_bind_memory2 = 0;
    gpu_info.support_VK_KHR_create_renderpass2 = 0;
    gpu_info.support_VK_KHR_dedicated_allocation = 0;
    gpu_info.support_VK_KHR_descriptor_update_template = 0;
    gpu_info.support_VK_KHR_external_memory = 0;
    gpu_info.support_VK_KHR_get_memory_requirements2 = 0;
    gpu_info.support_VK_KHR_maintenance1 = 0;
    gpu_info.support_VK_KHR_maintenance2 = 0;
    gpu_info.support_VK_KHR_maintenance3 = 0;
    gpu_info.support_VK_KHR_multiview = 0;
    gpu_info.support_VK_KHR_portability_subset = 0;
    gpu_info.support_VK_KHR_push_descriptor = 0;
    gpu_info.support_VK_KHR_sampler_ycbcr_conversion = 0;
    gpu_info.support_VK_KHR_shader_float16_int8 = 0;
    gpu_info.support_VK_KHR_shader_float_controls = 0;
    gpu_info.support_VK_KHR_storage_buffer_storage_class = 0;
    gpu_info.support_VK_KHR_swapchain = 0;
    gpu_info.support_VK_EXT_descriptor_indexing = 0;
    gpu_info.support_VK_EXT_memory_budget = 0;
    gpu_info.support_VK_EXT_queue_family_foreign = 0;
    gpu_info.support_VK_NV_cooperative_matrix = 0;

    // device type
    VkPhysicalDeviceProperties physicalDeviceProperties;
    vkGetPhysicalDeviceProperties(physicalDevice, &physicalDeviceProperties);

    if (physicalDeviceProperties.vendorID == 0x13b5 &&
        (physicalDeviceProperties.deviceID == 0x7500001 || physicalDeviceProperties.deviceID == 0x7501000 || physicalDeviceProperties.deviceID == 0x8602000 || physicalDeviceProperties.deviceID == 0x8800020 ||
         physicalDeviceProperties.deviceID == 0x70930000 || physicalDeviceProperties.deviceID == 0x70901010 || physicalDeviceProperties.deviceID == 0x72120000 || physicalDeviceProperties.deviceID == 0x74021000 ||
         physicalDeviceProperties.deviceID == 0x60a00002 || physicalDeviceProperties.deviceID == 0x62210001))
    {
        // NOTE rk3288/rk3399/t880/g31/g51/g52/g71/g72
        // however, g76/g77 has explicit fp16 arithmetic
        // arm mali driver accept spirv with fp16 arithmetic
        gpu_info.bug_implicit_fp16_arithmetic = true;
    }

    if (physicalDeviceProperties.vendorID == 0x5143 && (physicalDeviceProperties.deviceID == 0x6030001 || physicalDeviceProperties.deviceID == 0x6040001 || physicalDeviceProperties.deviceID == 0x6050002))
    {
        // NOTE to enable devices other than qcom845/qcom855/qcom855plus/qcom865
        // qcom adreno driver accept spirv with fp16 arithmetic
        gpu_info.bug_implicit_fp16_arithmetic = true;
    }

    for (uint32_t j = 0; j < deviceExtensionPropertyCount; j++)
    {
        const VkExtensionProperties& exp = deviceExtensionProperties[j];

        if (strcmp(exp.extensionName, "VK_KHR_8bit_storage") == 0)
            gpu_info.support_VK_KHR_8bit_storage = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_16bit_storage") == 0)
            gpu_info.support_VK_KHR_16bit_storage = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_bind_memory2") == 0)
            gpu_info.support_VK_KHR_bind_memory2 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_create_renderpass2") == 0)
            gpu_info.support_VK_KHR_create_renderpass2 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_dedicated_allocation") == 0)
            gpu_info.support_VK_KHR_dedicated_allocation = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_descriptor_update_template") == 0)
            gpu_info.support_VK_KHR_descriptor_update_template = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_external_memory") == 0)
            gpu_info.support_VK_KHR_external_memory = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_get_memory_requirements2") == 0)
            gpu_info.support_VK_KHR_get_memory_requirements2 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_maintenance1") == 0)
            gpu_info.support_VK_KHR_maintenance1 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_maintenance2") == 0)
            gpu_info.support_VK_KHR_maintenance2 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_maintenance3") == 0)
            gpu_info.support_VK_KHR_maintenance3 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_multiview") == 0)
            gpu_info.support_VK_KHR_multiview = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_portability_subset") == 0)
            gpu_info.support_VK_KHR_portability_subset = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_push_descriptor") == 0)
            gpu_info.support_VK_KHR_push_descriptor = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_sampler_ycbcr_conversion") == 0)
            gpu_info.support_VK_KHR_sampler_ycbcr_conversion = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_shader_float16_int8") == 0)
            gpu_info.support_VK_KHR_shader_float16_int8 = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_shader_float_controls") == 0)
            gpu_info.support_VK_KHR_shader_float_controls = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_storage_buffer_storage_class") == 0)
            gpu_info.support_VK_KHR_storage_buffer_storage_class = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_KHR_swapchain") == 0)
            gpu_info.support_VK_KHR_swapchain = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_EXT_descriptor_indexing") == 0)
            gpu_info.support_VK_EXT_descriptor_indexing = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_EXT_memory_budget") == 0)
            gpu_info.support_VK_EXT_memory_budget = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_EXT_queue_family_foreign") == 0)
            gpu_info.support_VK_EXT_queue_family_foreign = exp.specVersion;
        else if (strcmp(exp.extensionName, "VK_NV_cooperative_matrix") == 0)
            gpu_info.support_VK_NV_cooperative_matrix = exp.specVersion;
    }

    // check features
    gpu_info.support_fp16_packed = true;
    gpu_info.support_fp16_storage = false;
    gpu_info.support_fp16_arithmetic = false;
    gpu_info.support_int8_packed = true;
    gpu_info.support_int8_storage = false;
    gpu_info.support_int8_arithmetic = false;
    gpu_info.support_ycbcr_conversion = false;
    gpu_info.support_cooperative_matrix = false;
    gpu_info.support_cooperative_matrix_16_8_8 = false;
    if (support_VK_KHR_get_physical_device_properties2)
    {
        void* queryExtensionFeatures = 0;

        // query int8 storage
        VkPhysicalDevice8BitStorageFeaturesKHR query8BitStorageFeatures{};
        query8BitStorageFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_8BIT_STORAGE_FEATURES_KHR;
        query8BitStorageFeatures.pNext = 0;
        if (gpu_info.support_VK_KHR_8bit_storage)
        {
            query8BitStorageFeatures.pNext = queryExtensionFeatures;
            queryExtensionFeatures = &query8BitStorageFeatures;
        }

        // query fp16/int16 storage
        VkPhysicalDevice16BitStorageFeaturesKHR query16BitStorageFeatures{};
        query16BitStorageFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_16BIT_STORAGE_FEATURES_KHR;
        query16BitStorageFeatures.pNext = 0;
        if (gpu_info.support_VK_KHR_16bit_storage)
        {
            query16BitStorageFeatures.pNext = queryExtensionFeatures;
            queryExtensionFeatures = &query16BitStorageFeatures;
        }

        // query fp16/int8 arithmetic
        VkPhysicalDeviceFloat16Int8FeaturesKHR queryFloat16Int8Features{};
        queryFloat16Int8Features.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FLOAT16_INT8_FEATURES_KHR;
        queryFloat16Int8Features.pNext = 0;
        if (gpu_info.support_VK_KHR_shader_float16_int8)
        {
            queryFloat16Int8Features.pNext = queryExtensionFeatures;
            queryExtensionFeatures = &queryFloat16Int8Features;
        }

        // query ycbcr_conversion
        VkPhysicalDeviceSamplerYcbcrConversionFeaturesKHR querySamplerYcbcrConversionFeatures{};
        querySamplerYcbcrConversionFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_YCBCR_CONVERSION_FEATURES_KHR;
        querySamplerYcbcrConversionFeatures.pNext = 0;
        if (gpu_info.support_VK_KHR_sampler_ycbcr_conversion)
        {
            querySamplerYcbcrConversionFeatures.pNext = queryExtensionFeatures;
            queryExtensionFeatures = &querySamplerYcbcrConversionFeatures;
        }

        // query cooperative_matrix
        VkPhysicalDeviceCooperativeMatrixFeaturesNV queryCooperativeMatrixFeatures{};
        queryCooperativeMatrixFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_COOPERATIVE_MATRIX_FEATURES_NV;
        queryCooperativeMatrixFeatures.pNext = 0;
        if (gpu_info.support_VK_NV_cooperative_matrix)
        {
            queryCooperativeMatrixFeatures.pNext = queryExtensionFeatures;
            queryExtensionFeatures = &queryCooperativeMatrixFeatures;
        }

        VkPhysicalDeviceFeatures2KHR queryFeatures{};
        queryFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2_KHR;
        queryFeatures.pNext = queryExtensionFeatures;

        vkGetPhysicalDeviceFeatures2KHR(physicalDevice, &queryFeatures);

        if (gpu_info.support_VK_KHR_8bit_storage)
        {
            gpu_info.support_int8_storage = query8BitStorageFeatures.storageBuffer8BitAccess;
        }
        if (gpu_info.support_VK_KHR_16bit_storage && queryFeatures.features.shaderStorageImageExtendedFormats)
        {
            // shaderStorageImageExtendedFormats enables r16f format in storage image
            gpu_info.support_fp16_storage = query16BitStorageFeatures.storageBuffer16BitAccess;
        }
        if (gpu_info.support_VK_KHR_shader_float16_int8)
        {
            gpu_info.support_fp16_arithmetic = queryFloat16Int8Features.shaderFloat16;
            gpu_info.support_int8_arithmetic = queryFloat16Int8Features.shaderInt8;
        }
        if (gpu_info.support_VK_KHR_sampler_ycbcr_conversion)
        {
            gpu_info.support_ycbcr_conversion = querySamplerYcbcrConversionFeatures.samplerYcbcrConversion;
        }
        if (gpu_info.support_VK_NV_cooperative_matrix)
        {
            gpu_info.support_cooperative_matrix = queryCooperativeMatrixFeatures.cooperativeMatrix;
        }
    }
    else
    {
        //             // TODO
        //             VkPhysicalDeviceFeatures features;
        //             vkGetPhysicalDeviceFeatures(physicalDevice, &features);
    }

    if (physicalDeviceProperties.vendorID == 0x13b5 && physicalDeviceProperties.apiVersion < VK_MAKE_VERSION(1, 0, 82))
    {
        // the 16bit_storage implementation of arm mali driver is buggy :[
        gpu_info.support_fp16_storage = false;
    }

    if (physicalDeviceProperties.vendorID == 0x10002 && physicalDeviceProperties.deviceID == 0x70006214 && physicalDeviceProperties.apiVersion == VK_MAKE_VERSION(1, 1, 82))
    {
        // the 16bit_storage implementation of vivante gc1700 driver is buggy :[
        gpu_info.support_fp16_storage = false;
    }

    if (gpu_info.bug_implicit_fp16_arithmetic)
    {
        // force capability on as long as the driver accept spirv with fp16 arithmetic :D
        gpu_info.support_fp16_arithmetic = true;
    }

    if (physicalDeviceProperties.vendorID == 0x5143 && !gpu_info.support_fp16_storage)
    {
        // fp16 arithmetic yields wrong result on old adreno drivers :(
        gpu_info.support_fp16_arithmetic = false;
    }

    if (gpu_info.support_cooperative_matrix)
    {
        // query supported cooperative matrix types and operations
        uint32_t propertyCount = 0;
        VkResult ret = vkGetPhysicalDeviceCooperativeMatrixPropertiesNV(physicalDevice, &propertyCount, 0);
        if (ret != VK_SUCCESS)
        {
            LOG_INFO("vkGetPhysicalDeviceCooperativeMatrixPropertiesNV failed {}", ret);
        }

        std::vector<VkCooperativeMatrixPropertiesNV> properties(propertyCount, {VK_STRUCTURE_TYPE_COOPERATIVE_MATRIX_PROPERTIES_NV});
        ret = vkGetPhysicalDeviceCooperativeMatrixPropertiesNV(physicalDevice, &propertyCount, properties.data());
        if (ret != VK_SUCCESS)
        {
            LOG_INFO("vkGetPhysicalDeviceCooperativeMatrixPropertiesNV failed {}", ret);
        }

        for (uint32_t j = 0; j < properties.size(); j++)
        {
            const VkCooperativeMatrixPropertiesNV& cmp = properties[j];
            // NCNN_LOGE("cpm %2d %2d %2d  %d %d %d %d  %d", cmp.MSize, cmp.NSize, cmp.KSize, cmp.AType, cmp.BType, cmp.CType, cmp.DType, cmp.scope);

            if (cmp.MSize == 16 && cmp.NSize == 8 && cmp.KSize == 8 && cmp.AType == VK_COMPONENT_TYPE_FLOAT16_NV && cmp.BType == VK_COMPONENT_TYPE_FLOAT16_NV && cmp.CType == VK_COMPONENT_TYPE_FLOAT32_NV &&
                cmp.DType == VK_COMPONENT_TYPE_FLOAT32_NV && cmp.scope == VK_SCOPE_SUBGROUP_NV)
            {
                gpu_info.support_cooperative_matrix_16_8_8 = true;
            }
        }
    }

    gpu_info.initialized = true;
}


std::vector<const char*> cross::NcnnVkRequirements::GetNcnnDeviceExtensions()
{
    GpuInfo& info = gpuInfo;
    if (info.initialized == false)
    {
        LOG_FATAL("NcnnVkRequirements::GpuInfo not initialized !!!");
        return {};
    }


    std::vector<const char*> enabledExtensions = {};
    if (info.support_VK_KHR_8bit_storage)
        enabledExtensions.push_back("VK_KHR_8bit_storage");
    if (info.support_VK_KHR_16bit_storage)
        enabledExtensions.push_back("VK_KHR_16bit_storage");
    if (info.support_VK_KHR_bind_memory2)
        enabledExtensions.push_back("VK_KHR_bind_memory2");
    if (info.support_VK_KHR_create_renderpass2)
        enabledExtensions.push_back("VK_KHR_create_renderpass2");
    if (info.support_VK_KHR_dedicated_allocation)
        enabledExtensions.push_back("VK_KHR_dedicated_allocation");
    if (info.support_VK_KHR_descriptor_update_template)
        enabledExtensions.push_back("VK_KHR_descriptor_update_template");
    if (info.support_VK_KHR_external_memory)
        enabledExtensions.push_back("VK_KHR_external_memory");
    if (info.support_VK_KHR_get_memory_requirements2)
        enabledExtensions.push_back("VK_KHR_get_memory_requirements2");
    if (info.support_VK_KHR_maintenance1)
        enabledExtensions.push_back("VK_KHR_maintenance1");
    if (info.support_VK_KHR_maintenance2)
        enabledExtensions.push_back("VK_KHR_maintenance2");
    if (info.support_VK_KHR_maintenance3)
        enabledExtensions.push_back("VK_KHR_maintenance3");
    if (info.support_VK_KHR_multiview)
        enabledExtensions.push_back("VK_KHR_multiview");
    if (info.support_VK_KHR_portability_subset)
        enabledExtensions.push_back("VK_KHR_portability_subset");
    if (info.support_VK_KHR_push_descriptor)
        enabledExtensions.push_back("VK_KHR_push_descriptor");
    if (info.support_VK_KHR_sampler_ycbcr_conversion)
        enabledExtensions.push_back("VK_KHR_sampler_ycbcr_conversion");
    if (info.support_VK_KHR_shader_float16_int8)
        enabledExtensions.push_back("VK_KHR_shader_float16_int8");
    if (info.support_VK_KHR_shader_float_controls)
        enabledExtensions.push_back("VK_KHR_shader_float_controls");
    if (info.support_VK_KHR_storage_buffer_storage_class)
        enabledExtensions.push_back("VK_KHR_storage_buffer_storage_class");
    if (info.support_VK_KHR_swapchain)
        enabledExtensions.push_back("VK_KHR_swapchain");
    if (info.support_VK_EXT_descriptor_indexing)
        enabledExtensions.push_back("VK_EXT_descriptor_indexing");
    if (info.support_VK_EXT_memory_budget)
        enabledExtensions.push_back("VK_EXT_memory_budget");
    if (info.support_VK_EXT_queue_family_foreign)
        enabledExtensions.push_back("VK_EXT_queue_family_foreign");
    if (info.support_VK_NV_cooperative_matrix)
        enabledExtensions.push_back("VK_NV_cooperative_matrix");

    return enabledExtensions;
}

void cross::NcnnVkRequirements::SetNcnnDeviceExtensionsFeaturesChain(VkPhysicalDeviceVulkan11Features& vulkan11Features, VkPhysicalDeviceVulkan12Features& vulkan12Features, VkPhysicalDeviceCooperativeMatrixFeaturesNV& coopMatFeature)
{
    GpuInfo& info = gpuInfo;
    if (info.initialized == false)
    {
        LOG_FATAL("NcnnVkRequirements::GpuInfo not initialized !!!");
        return;
    }

    if (support_VK_KHR_get_physical_device_properties2 && info.support_VK_KHR_8bit_storage)
    {
        vulkan12Features.storageBuffer8BitAccess = info.support_int8_storage;
    }
    // enable fp16/int16 storage
    if (support_VK_KHR_get_physical_device_properties2 && info.support_VK_KHR_16bit_storage)
    {
        vulkan11Features.storageBuffer16BitAccess = info.support_fp16_storage;
        vulkan11Features.uniformAndStorageBuffer16BitAccess = VK_TRUE;
    }
    // enable fp16/int8 arithmetic
    if (support_VK_KHR_get_physical_device_properties2 && info.support_VK_KHR_shader_float16_int8)
    {
        vulkan12Features.shaderFloat16 = info.support_fp16_arithmetic;
        vulkan12Features.shaderInt8 = info.support_int8_arithmetic;
    }
    // enable ycbcr conversion
    if (support_VK_KHR_get_physical_device_properties2 && info.support_ycbcr_conversion)
    {
        vulkan11Features.samplerYcbcrConversion = info.support_ycbcr_conversion;
    }
    // enable cooperative matrix
    if (support_VK_KHR_get_physical_device_properties2 && info.support_cooperative_matrix)
    {
        vulkan12Features.pNext = &coopMatFeature;
    }

    // enable to remove validation error
    if (true)
    {
        vulkan12Features.vulkanMemoryModel = true;
        vulkan12Features.vulkanMemoryModelDeviceScope = true;
    }
}
