#pragma once
#include "EnginePrefix.h"
#include <mutex>
#include "CrossBaseForward.h"
#include "PlatformDefs.h"
#include "CrossBase/NSharedPtr.h"
#include "CrossBase/ReferenceCountObject.h"
#include "CrossBase/String/StringHash.h"

namespace cross
{
class UniqueStringPool;

class UniqueStringEntry : public ReferenceCountObject
{
public:
	char* mString{ nullptr };
	UInt32 mLength{ 0 };

	UniqueStringEntry(const char* str, UInt32 len);
    ~UniqueStringEntry();
};

class CEMeta(Cli, Puerts) UniqueString 
{
    using UniqueStringEntryPtr = NSharedPtr<UniqueStringEntry>;
    
public:
    CEMeta(Editor)
	CROSS_BASE_API UniqueString(const char* str);
    CEMeta(Editor)
    CROSS_BASE_API UniqueString(const char* str, UInt32 length);

    inline std::string_view GetStringView() const{ return mEntry? std::string_view(mEntry->mString, mEntry->mLength) : ""; }
    CEMeta(Editor, Cli, ScriptCallable)
	inline const char* GetCString() const { return mEntry? mEntry->mString : ""; }
    CEMeta(Editor)
    inline cross::StringHash64 GetHash64()const { return mHash; }
    CEMeta(Editor)
    inline cross::StringHash32 GetHash32() const { return mHash.ConvertTo32(); }

	inline bool operator==(const UniqueString& other) const { return mHash == other.mHash; }
	inline bool operator!=(const UniqueString& other) const { return mHash != other.mHash; }
	inline bool operator<(const UniqueString& other) const { return mHash < other.mHash; }
	inline bool operator>(const UniqueString& other) const { return mHash > other.mHash; }
	inline bool operator()(const UniqueString& lhs, const UniqueString& rhs) const{ return lhs.mHash < rhs.mHash;}

    CEMeta(Editor)
    UniqueString() = default;
    CEMeta(Editor)
	UniqueString(const UniqueString& src) = default;
	UniqueString(UniqueString && str) = default;
	UniqueString& operator=(const UniqueString & src) = default;
    UniqueString& operator=(UniqueString && src) = default;
    ~UniqueString() = default;

    CROSS_BASE_API const UniqueStringPool& GetPool();

    struct hash{ cross::StringHash64 operator()(const UniqueString& x) const{ return x.mHash.GetValue(); } };
    struct hash32 { cross::StringHash32 operator()(const UniqueString& x) const { return x.mHash.ConvertTo32(); } };

private:
	UniqueStringEntryPtr mEntry;
	StringHash64 mHash;

	friend class UniqueStringPool;
};

inline bool operator==(const UniqueString& s0, const char* s1)
{
    return std::strcmp(s0.GetCString(), s1) == 0;
}

inline bool operator==(const char* s0, const UniqueString& s1)
{
    return std::strcmp(s0, s1.GetCString()) == 0;
}

class UniqueStringPool
{
    using UniqueStringEntryPtr = NSharedPtr<UniqueStringEntry>;
    
public:
	UniqueString GetUniqueString_threadsafe(const char* str, UInt32 length);
    CROSS_BASE_API UInt32 GetUniqueStringCount() const { return (UInt32)mTable.size(); }
    CROSS_BASE_API UInt32 GetUniqueStringMemorySize() const { return mConservedMemoryUsedInByte; }

private:
	std::unordered_map<StringHash64, UniqueStringEntryPtr, StringHash64::hash> mTable;
	std::mutex mTableMtx;

	UInt32 mConservedMemoryUsedInByte{ 0 };
	static const UInt32 sStringInstanceFixedSizeInByte = UInt32(sizeof(UniqueStringEntry) + sizeof(UniqueStringEntryPtr) + sizeof(StringHash64));
};
}

namespace std {
template<>
struct std::hash<cross::UniqueString>
{
    size_t operator()(cross::UniqueString us) const noexcept
    {
        // only use high and low (uint64_t)
        return us.GetHash32();
    }
};
}   // namespace std
