#pragma once

namespace cross
{
    // TODO ... error handling for system error

    class PlatformMutex final
    {
    public:
        PlatformMutex()
        {
            pthread_mutex_init(&mMutex, nullptr);
        }
        
        ~PlatformMutex()
        {
            pthread_mutex_destroy(&mMutex);
        }
        
        PlatformMutex(PlatformMutex const&) = delete;
        PlatformMutex& operator=(PlatformMutex const&) = delete;
        
    public:
        void Lock()
        {
            pthread_mutex_lock(&mMutex);
        }
        
        void Unlock()
        {
            pthread_mutex_unlock(&mMutex);
        }
        
        bool TryLock()
        {
            return pthread_mutex_trylock(&mMutex) == 0;
        }

        pthread_mutex_t* GetNativeHandle() noexcept
        {
            return &mMutex;
        }

    protected:
        pthread_mutex_t mMutex;
    };

    class PlatformRWMutex final
    {
    public:
        PlatformRWMutex()
        {
            pthread_rwlock_init(&mMutex, nullptr);
        }
        
        ~PlatformRWMutex()
        {
            pthread_rwlock_destroy(&mMutex);
        }
        
        PlatformRWMutex(PlatformRWMutex const&) = delete;
        PlatformRWMutex& operator=(PlatformRWMutex const&) = delete;
        
    public:
        void Lock()
        {
            pthread_rwlock_wrlock(&mMutex);
        }
        
        void Unlock()
        {
            pthread_rwlock_unlock(&mMutex);
        }
        
        bool TryLock()
        {
            return pthread_rwlock_trywrlock(&mMutex) == 0;
        }
        
        void LockRead()
        {
            pthread_rwlock_rdlock(&mMutex);
        }
        
        void UnlockRead()
        {
            pthread_rwlock_unlock(&mMutex);
        }
        
        bool TryLockRead()
        {
            return pthread_rwlock_tryrdlock(&mMutex) == 0;
        }

        void* GetNativeHandle() noexcept
        {
            return &mMutex;
        }

    protected:
        pthread_rwlock_t    mMutex;
    };
}
