#pragma once
// condition variable

#include "Concurrency/Synchronization/Mutex.h"
#include "TimeDef.h"

namespace cross
{
    class PlatformCondVar;

    class CROSS_BASE_API CondVar final
    {
    public:
        CondVar();
        ~CondVar();
        CondVar(CondVar const&) = delete;
        CondVar& operator=(CondVar const&) = delete;

    public:
        void Wait(TUniqueLock<Mutex>& lock);

        template <typename Pred>
        void Wait(TUniqueLock<Mutex>& lock, Pred pred)
        {
            while (!pred()) Wait(lock);
        }

        template <typename Dura>
        auto WaitFor(TUniqueLock<Mutex>& lock, Dura const& duration) -> std::enable_if_t<TIsDurationV<Dura>, bool>
        {
            if constexpr (std::is_same_v<Dura, MillionSecondCount>)
            {
                return WaitForImpl(lock.GetMutex(), duration);
            }
            else
            {
                return WaitForImpl(lock.GetMutex(), time::Cast<MillionSecondCount>(duration));
            }
        }

        template <typename Dura, typename Pred>
        auto WaitFor(TUniqueLock<Mutex>& lock, Dura const& duration, Pred const& pred) -> std::enable_if_t<TIsDurationV<Dura>, bool>
        {
            while(!pred())
            {
                if(WaitFor(lock, duration))
                {
                    return pred();
                }
            }
            return true;
        }

        template <typename TM>
        auto WaitUntil(TUniqueLock<Mutex>& lock, TM const& timePoint) -> std::enable_if_t<TIsTimePointV<TM>, bool>
        {
            if constexpr (std::is_same_v<std::chrono::system_clock::time_point, TM>)
            {
                return WaitUntilImpl(lock.GetMutex(), timePoint);
            }
            else
            {
                return WaitUntilImpl(lock.GetMutex(), time::Cast<std::chrono::system_clock::time_point>(timePoint));
            }
        }
        
        template <typename TM, typename Pred>
        auto WaitUntil(TUniqueLock<Mutex>& lock, TM const& timePoint, Pred const& pred) -> std::enable_if_t<TIsTimePointV<TM>, bool>
        {
            while(!pred())
            {
                if(WaitUntil(lock, timePoint))
                {
                    return pred();
                }
            }
            return true;
        }

        void NotifyOne();
        void NotifyAll();

    private:
        bool WaitForImpl(Mutex* lock, MillionSecondCount const& millionSecondCoun);
        bool WaitUntilImpl(Mutex* lock, std::chrono::system_clock::time_point const& timePoint);

    private:
        PlatformCondVar*     mPImpl;
    };
}
