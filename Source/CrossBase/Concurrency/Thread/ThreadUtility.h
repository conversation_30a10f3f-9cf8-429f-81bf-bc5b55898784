#pragma once

#if CROSSENGINE_OSX
#ifndef __ppc__
#include <libkern/OSAtomic.h>
#endif
#endif

#if CROSSENGINE_IOS
#include <libkern/OSAtomic.h>
#endif

// Memory barrier.
//
// Necessary to call this when using volatile to order writes/reads in multiple threads.
inline void ThreadMemoryBarrier()
#if CROSSENGINE_WIN || CROSSENGINE_OSX || CROSSENGINE_IOS || CROSSENGINE_ANDROID || CROSSENGINE_LINUX || CROSSENGINE_WASM
{
	#if CROSSENGINE_WIN 

	#ifdef MemoryBarrier
	MemoryBarrier();
	#else
	long temp;
	__asm xchg temp,eax;
	#endif
	
	#elif CROSSENGINE_OSX || CROSSENGINE_IOS
 
	//OSMemoryBarrier();
    std::atomic_thread_fence(std::memory_order_seq_cst);
	
	#elif CROSSENGINE_ANDROID

		__sync_synchronize();

	#elif CROSSENGINE_LINUX || CROSSENGINE_WASM
		#ifdef __arm__
			__asm__ __volatile__ ("mcr p15, 0, %0, c7, c10, 5" : : "r" (0) : "memory");
		#else
			__sync_synchronize();
		#endif
	#else

	#endif
}
#else
	;
#endif // UNKNOWN PLATFORM
