#pragma once
#include "CrossMath.h"
#include "Template/TypeTraits.hpp"
// Methods
// Transform the sphere
namespace cross {

#ifndef _MANAGED
inline SIMDVector4 MATH_CALL MathSIMD::VectorReciprocalEstAccurate(SIMDVec4Param1 v)
{
    // Newton method
    /*
        Calculate the Reciprocal of a.
        Define f(x) = 1 / x - a, we have f(x)' = -1 * x^2.
        We apply Newton method to get the root of f(x).

        Start from x0 = MathSIMD::VectorReciprocal(a).
        According from Newton method, x1 = x0 - f(x0) / f'(x0), x1 = 2 * x0 - a * x0 ^ 2,
        and x2 = 2 * x1 - a * x1 ^ 2.
    */
    // Initial estimate
    const SIMDVector4 x0 = MathSIMD::VectorReciprocalEst(v);

    // First iteration

    const SIMDVector4 x0x2 = MathSIMD::VectorAdd(x0, x0);
    const SIMDVector4 x0Squared = MathSIMD::VectorMultiply(x0, x0);
    const SIMDVector4 x0SquaredxV = MathSIMD::VectorMultiply(v, x0Squared);

    const SIMDVector4 x1 = MathSIMD::VectorSubtract(x0x2, x0SquaredxV);

    // Second iteration
    const SIMDVector4 x1x2 = MathSIMD::VectorAdd(x1, x1);
    const SIMDVector4 x1Squared = MathSIMD::VectorMultiply(x1, x1);
    const SIMDVector4 x1SquaredxV = MathSIMD::VectorMultiply(v, x1Squared);

    const SIMDVector4 x2 = MathSIMD::VectorSubtract(x1x2, x1SquaredxV);

    return x2;
}

inline SIMDVector4 MATH_CALL MathSIMD::VectorReciprocalSafe(SIMDVec4Param1 InScale)
{
    /*
        result = scale == 0.f ? 0.f : 1.f / scale;
    */
    const SIMDVector4 ScaleRcp = MathSIMD::VectorReciprocalEstAccurate(InScale);
    const SIMDVector4 Scale0Mask = MathSIMD::VectorGreaterOrEqual(cross::sVectorEpsilon, MathSIMD::VectorAbs(InScale));

    const SIMDVector4 SafeScaleRcp = MathSIMD::VectorSelect(ScaleRcp, cross::sVectorZero, Scale0Mask);
    return SafeScaleRcp;
}

inline void BoundingSphere::Transform(BoundingSphere& Out, Float4x4& M) const noexcept
{
    DirectX::XMMATRIX xM = DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(&M));

    mSphere.Transform(Out.mSphere, xM);
}
inline void BoundingSphere::Transform(BoundingSphere& Out, float Scale, Quaternion& Rotation, Float3& Translation) const noexcept
{
    DirectX::XMVECTOR xR = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Rotation));
    DirectX::XMVECTOR xT = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&Translation));

    mSphere.Transform(Out.mSphere, Scale, xR, xT);
}

inline void BoundingSphere::Transform(BoundingSphere& Out, const Float3& Translation) const noexcept
{
    DirectX::XMVECTOR xT = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&Translation));
    DirectX::XMVECTOR xCenter = DirectX::XMLoadFloat3(&mSphere.Center);

    xCenter = DirectX::XMVectorAdd(xT, xCenter);
    DirectX::XMStoreFloat3(&Out.mSphere.Center, xCenter);
    Out.mSphere.Radius = mSphere.Radius;
}

inline ContainmentType BoundingSphere::Contains(Float3 Point) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&Point));

    return (ContainmentType)(mSphere.Contains(xP));
}

// Contains with other shapes
inline ContainmentType BoundingSphere::Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return (ContainmentType)mSphere.Contains(xV0, xV1, xV2);
};

inline ContainmentType BoundingSphere::Contains(const BoundingSphere& sh) const noexcept
{
    return (ContainmentType)mSphere.Contains(sh.mSphere);
}
inline ContainmentType BoundingSphere::Contains(const BoundingBox& box) const noexcept
{
    return (ContainmentType)mSphere.Contains(box.mBox);
}
inline ContainmentType BoundingSphere::Contains(const BoundingOrientedBox& box) const noexcept
{
    return (ContainmentType)mSphere.Contains(box.mOBox);
}

inline ContainmentType BoundingSphere::Contains(const BoundingFrustum& fr) const noexcept
{
    return (ContainmentType)mSphere.Contains(fr.mFrustum);
}

// Intersects with other shapes
inline bool BoundingSphere::Intersects(const BoundingSphere& sh) const noexcept
{
    return mSphere.Intersects(sh.mSphere);
}
inline bool BoundingSphere::Intersects(const BoundingBox& box) const noexcept
{
    return mSphere.Intersects(box.mBox);
}
inline bool BoundingSphere::Intersects(const BoundingOrientedBox& box) const noexcept
{
    return mSphere.Intersects(box.mOBox);
}
inline bool BoundingSphere::Intersects(const BoundingFrustum& fr) const noexcept
{
    return mSphere.Intersects(fr.mFrustum);
}

// Triangle sphere test
inline bool BoundingSphere::Intersects(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return mSphere.Intersects(xV0, xV1, xV2);
}

// Plane-sphere test
inline PlaneIntersectionType BoundingSphere::Intersects(Float4 Plane) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Plane));

    return (PlaneIntersectionType)mSphere.Intersects(xP);
}

// Ray-sphere test
inline bool BoundingSphere::Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept
{
    DirectX::XMVECTOR xO = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Origin));
    DirectX::XMVECTOR xD = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Direction));

    return mSphere.Intersects(xO, xD, Dist);
}

// Test sphere against six planes (see BoundingFrustum::GetPlanes)
inline ContainmentType BoundingSphere::ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept
{
    DirectX::XMVECTOR xP0 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane0));
    DirectX::XMVECTOR xP1 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane1));
    DirectX::XMVECTOR xP2 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane2));
    DirectX::XMVECTOR xP3 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane3));
    DirectX::XMVECTOR xP4 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane4));
    DirectX::XMVECTOR xP5 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane5));

    return (ContainmentType)mSphere.ContainedBy(xP0, xP1, xP2, xP3, xP4, xP5);
}

// static methods
inline void BoundingSphere::CreateMerged(BoundingSphere& Out, const BoundingSphere& S1, const BoundingSphere& S2) noexcept
{
    DirectX::BoundingSphere::CreateMerged(Out.mSphere, S1.mSphere, S2.mSphere);
}

inline void BoundingSphere::CreateFromBoundingBox(BoundingSphere& Out, const BoundingBox& box) noexcept
{
    DirectX::BoundingSphere::CreateFromBoundingBox(Out.mSphere, box.mBox);
}
inline void BoundingSphere::CreateFromBoundingBox(BoundingSphere& Out, const BoundingOrientedBox& box) noexcept
{
    DirectX::BoundingSphere::CreateFromBoundingBox(Out.mSphere, box.mOBox);
}

inline void BoundingSphere::CreateFromPoints(BoundingSphere& Out, size_t Count, _In_reads_bytes_(sizeof(Float3) + Stride * (Count - 1)) const Float3* pPoints, size_t Stride) noexcept
{
    DirectX::BoundingSphere::CreateFromPoints(Out.mSphere, Count, reinterpret_cast<const DirectX::XMFLOAT3*>(pPoints), Stride);
}

inline void BoundingSphere::CreateFromFrustum(BoundingSphere& Out, const BoundingFrustum& fr) noexcept
{
    DirectX::BoundingSphere::CreateFromFrustum(Out.mSphere, fr.mFrustum);
}

// Methods// Transform the sphere
inline void BoundingBox::Transform(BoundingBox& Out, const Float4x4& M) const noexcept
{
    DirectX::XMMATRIX xM = DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(&M));

    mBox.Transform(Out.mBox, xM);
}
inline void BoundingBox::Transform(BoundingBox& Out, float Scale, const Quaternion& Rotation, const Float3& Translation) const noexcept
{
    DirectX::XMVECTOR xR = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&Rotation));
    DirectX::XMVECTOR xT = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&Translation));

    mBox.Transform(Out.mBox, Scale, xR, xT);
}

inline void BoundingBox::Transform(BoundingBox& Out, const Float3& Translation) const noexcept
{
    DirectX::XMVECTOR xT = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&Translation));
    DirectX::XMVECTOR xCenter = DirectX::XMLoadFloat3(&mBox.Center);

    xCenter = DirectX::XMVectorAdd(xT, xCenter);
    DirectX::XMStoreFloat3(&Out.mBox.Center, xCenter);
    Out.mBox.Extents = mBox.Extents;
}

inline void BoundingBox::GetCorners(_Out_writes_(8) Float3* Corners) const noexcept
{
    mBox.GetCorners(reinterpret_cast<DirectX::XMFLOAT3*>(Corners));
}

inline void BoundingBox::GetCenter(Float3* Out) const noexcept
{
    *Out = *(reinterpret_cast<const Float3*>(&mBox.Center));
}

inline const Float3& BoundingBox::GetCenter() const noexcept
{
    return *(reinterpret_cast<const Float3*>(&mBox.Center));
}

inline void BoundingBox::GetMinMax(Float3* OutMin, Float3* OutMax) const noexcept
{
    DirectX::XMVECTOR boxCenter = DirectX::XMLoadFloat3(&mBox.Center);
    DirectX::XMVECTOR boxExtents = DirectX::XMLoadFloat3(&mBox.Extents);
    DirectX::XMVECTOR boxMin = DirectX::XMVectorSubtract(boxCenter, boxExtents);
    DirectX::XMVECTOR boxMax = DirectX::XMVectorAdd(boxCenter, boxExtents);
    XMStoreFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(OutMin), boxMin);
    XMStoreFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(OutMax), boxMax);
}

inline void BoundingBox::Encapsulate(const Float3& point) noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&point));
    if (mBox.Contains(xP) == DirectX::ContainmentType::CONTAINS)
    {
        return;
    }
    DirectX::XMVECTOR boxCenter = DirectX::XMLoadFloat3(&mBox.Center);
    DirectX::XMVECTOR boxExtents = DirectX::XMLoadFloat3(&mBox.Extents);
    DirectX::XMVECTOR boxMin = DirectX::XMVectorSubtract(boxCenter, boxExtents);
    DirectX::XMVECTOR boxMax = DirectX::XMVectorAdd(boxCenter, boxExtents);
    DirectX::XMVECTOR lessThanMin = DirectX::XMVectorLess(xP, boxMin);
    DirectX::XMVECTOR greaterThanMax = DirectX::XMVectorGreater(xP, boxMax);
    DirectX::XMVECTOR newMin = DirectX::XMVectorSelect(boxMin, xP, lessThanMin);
    DirectX::XMVECTOR newMax = DirectX::XMVectorSelect(boxMax, xP, greaterThanMax);
    DirectX::BoundingBox::CreateFromPoints(mBox, newMin, newMax);
}

inline void BoundingBox::GetExtent(Float3* Out) const noexcept
{
    *Out = *(reinterpret_cast<const Float3*>(&mBox.Extents));
}

inline const Float3& BoundingBox::GetExtent() const noexcept
{
    return *(reinterpret_cast<const Float3*>(&mBox.Extents));
}

inline ContainmentType BoundingBox::Contains(Float3 Point) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&Point));

    return (ContainmentType)(mBox.Contains(xP));
}

// Contains with other shapes
inline ContainmentType BoundingBox::Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return (ContainmentType)mBox.Contains(xV0, xV1, xV2);
};

inline ContainmentType BoundingBox::Contains(const BoundingSphere& sh) const noexcept
{
    return (ContainmentType)mBox.Contains(sh.mSphere);
}
inline ContainmentType BoundingBox::Contains(const BoundingBox& box) const noexcept
{
    return (ContainmentType)mBox.Contains(box.mBox);
}
inline ContainmentType BoundingBox::Contains(const BoundingOrientedBox& box) const noexcept
{
    return (ContainmentType)mBox.Contains(box.mOBox);
}

inline ContainmentType BoundingBox::Contains(const BoundingFrustum& fr) const noexcept
{
    return (ContainmentType)mBox.Contains(fr.mFrustum);
}

// Intersects with other shapes
inline bool BoundingBox::Intersects(const BoundingSphere& sh) const noexcept
{
    return mBox.Intersects(sh.mSphere);
}

inline float BoundingBox::Distance(const BoundingSphere& crossSphere) const noexcept
{
    using namespace DirectX;
    auto sh = crossSphere.mSphere;
    XMVECTOR SphereCenter = XMLoadFloat3(&sh.Center);
    XMVECTOR SphereRadius = XMVectorReplicatePtr(&sh.Radius);

    XMVECTOR BoxCenter = XMLoadFloat3(&mBox.Center);
    XMVECTOR BoxExtents = XMLoadFloat3(&mBox.Extents);

    XMVECTOR BoxMin = XMVectorSubtract(BoxCenter, BoxExtents);
    XMVECTOR BoxMax = XMVectorAdd(BoxCenter, BoxExtents);

    // Find the distance to the nearest point on the box.
    // for each i in (x, y, z)
    // if (SphereCenter(i) < BoxMin(i)) d2 += (SphereCenter(i) - BoxMin(i)) ^ 2
    // else if (SphereCenter(i) > BoxMax(i)) d2 += (SphereCenter(i) - BoxMax(i)) ^ 2

    XMVECTOR d = XMVectorZero();

    // Compute d for each dimension.
    XMVECTOR LessThanMin = XMVectorLess(SphereCenter, BoxMin);
    XMVECTOR GreaterThanMax = XMVectorGreater(SphereCenter, BoxMax);

    XMVECTOR MinDelta = XMVectorSubtract(SphereCenter, BoxMin);
    XMVECTOR MaxDelta = XMVectorSubtract(SphereCenter, BoxMax);

    // Choose value for each dimension based on the comparison.
    d = XMVectorSelect(d, MinDelta, LessThanMin);
    d = XMVectorSelect(d, MaxDelta, GreaterThanMax);

    // Use a dot-product to square them and sum them together.
    XMVECTOR d2 = XMVector3Dot(d, d);

    d = XMVectorSubtract(XMVectorSqrt(d2), SphereRadius);
    float distance;
    XMStoreFloat(&distance, d);
    return distance;
}

inline BoundingBox& BoundingBox::operator+=(const BoundingBox& box) noexcept
{
    Float3 boxMin, boxMax;
    GetMinMax(&boxMin, &boxMax);

    Float3 otherBoxMin, otherBoxMax;
    box.GetMinMax(&otherBoxMin, &otherBoxMax);

    boxMin.x = std::min(boxMin.x, otherBoxMin.x);
    boxMin.y = std::min(boxMin.y, otherBoxMin.y);
    boxMin.z = std::min(boxMin.z, otherBoxMin.z);

    boxMax.x = std::max(boxMax.x, otherBoxMax.x);
    boxMax.y = std::max(boxMax.y, otherBoxMax.y);
    boxMax.z = std::max(boxMax.z, otherBoxMax.z);

    mBox.Center.x = (boxMin.x + boxMax.x) * 0.5f;
    mBox.Center.y = (boxMin.y + boxMax.y) * 0.5f;
    mBox.Center.z = (boxMin.z + boxMax.z) * 0.5f;

    mBox.Extents.x = (boxMax.x - boxMin.x) * 0.5F;
    mBox.Extents.y = (boxMax.y - boxMin.y) * 0.5F;
    mBox.Extents.z = (boxMax.z - boxMin.z) * 0.5F;
    return *this;
}

inline BoundingBox& BoundingBox::operator+=(const Float3& point) noexcept
{
    Float3 boxMin, boxMax;
    GetMinMax(&boxMin, &boxMax);
    boxMin.x = std::min(boxMin.x, point.x);
    boxMin.y = std::min(boxMin.y, point.y);
    boxMin.z = std::min(boxMin.z, point.z);

    boxMax.x = std::max(boxMax.x, point.x);
    boxMax.y = std::max(boxMax.y, point.y);
    boxMax.z = std::max(boxMax.z, point.z);

    mBox.Center.x = (boxMin.x + boxMax.x) * 0.5f;
    mBox.Center.y = (boxMin.y + boxMax.y) * 0.5f;
    mBox.Center.z = (boxMin.z + boxMax.z) * 0.5f;

    mBox.Extents.x = (boxMax.x - boxMin.x) * 0.5F;
    mBox.Extents.y = (boxMax.y - boxMin.y) * 0.5F;
    mBox.Extents.z = (boxMax.z - boxMin.z) * 0.5F;
    return *this;
}

inline bool BoundingBox::Intersects(const BoundingBox& box) const noexcept
{
    return mBox.Intersects(box.mBox);
}
inline bool BoundingBox::Intersects(const BoundingOrientedBox& box) const noexcept
{
    return mBox.Intersects(box.mOBox);
}
inline bool BoundingBox::Intersects(const BoundingFrustum& fr) const noexcept
{
    return mBox.Intersects(fr.mFrustum);
}

// Triangle AABB test
inline bool BoundingBox::Intersects(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return mBox.Intersects(xV0, xV1, xV2);
}

// Plane-AABB test
inline PlaneIntersectionType BoundingBox::Intersects(Float4 Plane) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Plane));

    return (PlaneIntersectionType)mBox.Intersects(xP);
}

// Ray-AABB test
inline bool BoundingBox::Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept
{
    DirectX::XMVECTOR xO = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Origin));
    DirectX::XMVECTOR xD = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Direction));

    return mBox.Intersects(xO, xD, Dist);
}

// Test sphere against six planes (see BoundingFrustum::GetPlanes)
inline ContainmentType BoundingBox::ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept
{
    DirectX::XMVECTOR xP0 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane0));
    DirectX::XMVECTOR xP1 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane1));
    DirectX::XMVECTOR xP2 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane2));
    DirectX::XMVECTOR xP3 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane3));
    DirectX::XMVECTOR xP4 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane4));
    DirectX::XMVECTOR xP5 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane5));

    return (ContainmentType)mBox.ContainedBy(xP0, xP1, xP2, xP3, xP4, xP5);
}

// Static methods
inline void BoundingBox::CreateMerged(BoundingBox& Out, const BoundingBox& b1, const BoundingBox& b2) noexcept
{
    DirectX::BoundingBox::CreateMerged(Out.mBox, b1.mBox, b2.mBox);
}

inline void BoundingBox::CreateFromSphere(BoundingBox& Out, const BoundingSphere& sh) noexcept
{
    DirectX::BoundingBox::CreateFromSphere(Out.mBox, sh.mSphere);
}

inline void BoundingBox::CreateFromPoints(BoundingBox& Out, Float3 pt1, Float3 pt2) noexcept
{
    DirectX::XMVECTOR xP0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&pt1));
    DirectX::XMVECTOR xP1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&pt2));

    DirectX::BoundingBox::CreateFromPoints(Out.mBox, xP0, xP1);
}

inline void BoundingBox::CreateFromPoints(BoundingBox& Out, size_t Count, _In_reads_bytes_(sizeof(Float3) + Stride * (Count - 1)) const Float3* pPoints, size_t Stride) noexcept
{
    DirectX::BoundingBox::CreateFromPoints(Out.mBox, Count, reinterpret_cast<const DirectX::XMFLOAT3*>(pPoints), Stride);
}

inline void BoundingBox::CreateIntersection(BoundingBox& Out, const BoundingBox& b1, const BoundingBox& b2) noexcept
{
    DirectX::XMVECTOR b1Center = DirectX::XMLoadFloat3(&b1.mBox.Center);
    DirectX::XMVECTOR b1Extents = DirectX::XMLoadFloat3(&b1.mBox.Extents);
    DirectX::XMVECTOR b1Min = DirectX::XMVectorSubtract(b1Center, b1Extents);
    DirectX::XMVECTOR b1Max = DirectX::XMVectorAdd(b1Center, b1Extents);

    DirectX::XMVECTOR b2Center = DirectX::XMLoadFloat3(&b2.mBox.Center);
    DirectX::XMVECTOR b2Extents = DirectX::XMLoadFloat3(&b2.mBox.Extents);
    DirectX::XMVECTOR b2Min = DirectX::XMVectorSubtract(b2Center, b2Extents);
    DirectX::XMVECTOR b2Max = DirectX::XMVectorAdd(b2Center, b2Extents);

    DirectX::XMVECTOR b1MinGreaterThanb2Min = DirectX::XMVectorGreater(b1Min, b2Min);
    DirectX::XMVECTOR b1MaxLessThanb2Max = DirectX::XMVectorLess(b1Max, b2Max);

    DirectX::XMVECTOR newMin = DirectX::XMVectorSelect(b2Min, b1Min, b1MinGreaterThanb2Min);
    DirectX::XMVECTOR newMax = DirectX::XMVectorSelect(b2Max, b1Max, b1MaxLessThanb2Max);
    DirectX::XMVECTOR minGreaterThanMax = DirectX::XMVectorGreater(newMin, newMax);
    DirectX::BoundingBox::CreateFromPoints(Out.mBox, newMin, reinterpret_cast<Float3*>(&minGreaterThanMax)->IsNearlyZero() ? newMax : newMin);
}

// Methods// Transform the sphere
inline void BoundingOrientedBox::Transform(BoundingOrientedBox& Out, const Float4x4& M) const noexcept
{
    DirectX::XMMATRIX xM = DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(&M));

    mOBox.Transform(Out.mOBox, xM);
}
inline void BoundingOrientedBox::Transform(BoundingOrientedBox& Out, float Scale, const Quaternion& Rotation, const Float3& Translation) const noexcept
{
    DirectX::XMVECTOR xR = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&Rotation));
    DirectX::XMVECTOR xT = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&Translation));

    mOBox.Transform(Out.mOBox, Scale, xR, xT);
}

inline void BoundingOrientedBox::Transform(BoundingOrientedBox& Out, const Float3& Translation) const noexcept
{
    DirectX::XMVECTOR xT = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&Translation));
    DirectX::XMVECTOR xCenter = DirectX::XMLoadFloat3(&mOBox.Center);

    xCenter = DirectX::XMVectorAdd(xT, xCenter);
    DirectX::XMStoreFloat3(&Out.mOBox.Center, xCenter);
    Out.mOBox.Extents = mOBox.Extents;
    Out.mOBox.Orientation = mOBox.Orientation;
}

inline void BoundingOrientedBox::GetCorners(_Out_writes_(8) Float3* Corners) const noexcept
{
    mOBox.GetCorners(reinterpret_cast<DirectX::XMFLOAT3*>(Corners));
}

inline void BoundingOrientedBox::GetCenter(Float3* Out) const noexcept
{
    *Out = *(reinterpret_cast<const Float3*>(&mOBox.Center));
}

inline const Float3& BoundingOrientedBox::GetCenter() const noexcept
{
    return *(reinterpret_cast<const Float3*>(&mOBox.Center));
}

inline void BoundingOrientedBox::GetExtent(Float3* Out) const noexcept
{
    *Out = *(reinterpret_cast<const Float3*>(&mOBox.Extents));
}

inline const Float3& BoundingOrientedBox::GetExtent() const noexcept
{
    return *(reinterpret_cast<const Float3*>(&mOBox.Extents));
}

inline Float3& BoundingOrientedBox::GetExtent() noexcept
{
    return *(reinterpret_cast<Float3*>(&mOBox.Extents));
}

inline void BoundingOrientedBox::GetOrientation(Quaternion* Out) const noexcept
{
    *Out = *(reinterpret_cast<const Quaternion*>(&mOBox.Orientation));
}

inline const Quaternion& BoundingOrientedBox::GetOrientation() const noexcept
{
    return *(reinterpret_cast<const Quaternion*>(&mOBox.Orientation));
}

inline void BoundingOrientedBox::SetOrientation(Quaternion in) noexcept
{
    *(reinterpret_cast<Quaternion*>(&mOBox.Orientation)) = in;
}

inline ContainmentType BoundingOrientedBox::Contains(Float3 Point) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&Point));

    return (ContainmentType)(mOBox.Contains(xP));
}

// Contains with other shapes
inline ContainmentType BoundingOrientedBox::Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return (ContainmentType)mOBox.Contains(xV0, xV1, xV2);
};

inline ContainmentType BoundingOrientedBox::Contains(const BoundingSphere& sh) const noexcept
{
    return (ContainmentType)mOBox.Contains(sh.mSphere);
}
inline ContainmentType BoundingOrientedBox::Contains(const BoundingBox& box) const noexcept
{
    return (ContainmentType)mOBox.Contains(box.mBox);
}
inline ContainmentType BoundingOrientedBox::Contains(const BoundingOrientedBox& box) const noexcept
{
    return (ContainmentType)mOBox.Contains(box.mOBox);
}

inline ContainmentType BoundingOrientedBox::Contains(const BoundingFrustum& fr) const noexcept
{
    return (ContainmentType)mOBox.Contains(fr.mFrustum);
}

// Intersects with other shapes
inline bool BoundingOrientedBox::Intersects(const BoundingSphere& sh) const noexcept
{
    return mOBox.Intersects(sh.mSphere);
}
inline bool BoundingOrientedBox::Intersects(const BoundingBox& box) const noexcept
{
    return mOBox.Intersects(box.mBox);
}
inline bool BoundingOrientedBox::Intersects(const BoundingOrientedBox& box) const noexcept
{
    return mOBox.Intersects(box.mOBox);
}
inline bool BoundingOrientedBox::Intersects(const BoundingFrustum& fr) const noexcept
{
    return mOBox.Intersects(fr.mFrustum);
}

// Triangle OBB test
inline bool BoundingOrientedBox::Intersects(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return mOBox.Intersects(xV0, xV1, xV2);
}

// Plane-OBB test
inline PlaneIntersectionType BoundingOrientedBox::Intersects(Float4 Plane) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Plane));

    return (PlaneIntersectionType)mOBox.Intersects(xP);
}

// Ray-OBB test
inline bool BoundingOrientedBox::Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept
{
    DirectX::XMVECTOR xO = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Origin));
    DirectX::XMVECTOR xD = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Direction));

    return mOBox.Intersects(xO, xD, Dist);
}

// Test sphere against six planes (see BoundingFrustum::GetPlanes)
inline ContainmentType BoundingOrientedBox::ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept
{
    DirectX::XMVECTOR xP0 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane0));
    DirectX::XMVECTOR xP1 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane1));
    DirectX::XMVECTOR xP2 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane2));
    DirectX::XMVECTOR xP3 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane3));
    DirectX::XMVECTOR xP4 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane4));
    DirectX::XMVECTOR xP5 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane5));

    return (ContainmentType)mOBox.ContainedBy(xP0, xP1, xP2, xP3, xP4, xP5);
}

inline void BoundingOrientedBox::CreateFromBoundingBox(BoundingOrientedBox& Out, const BoundingBox& box) noexcept
{
    DirectX::BoundingOrientedBox::CreateFromBoundingBox(Out.mOBox, box.mBox);
}

inline void BoundingOrientedBox::CreateFromPoints(BoundingOrientedBox& Out, size_t Count, _In_reads_bytes_(sizeof(Float3) + Stride * (Count - 1)) const Float3* pPoints, size_t Stride) noexcept
{
    DirectX::BoundingOrientedBox::CreateFromPoints(Out.mOBox, Count, reinterpret_cast<const DirectX::XMFLOAT3*>(pPoints), Stride);
}

// Methods// Transform the frustum
inline void BoundingFrustum::Transform(BoundingFrustum& Out, const Float4x4& M) const noexcept
{
    DirectX::FXMMATRIX xM = DirectX::XMLoadFloat4x4(reinterpret_cast<const DirectX::XMFLOAT4X4*>(&M));

    mFrustum.Transform(Out.mFrustum, xM);
}
inline void BoundingFrustum::Transform(BoundingFrustum& Out, float Scale, const Quaternion& Rotation, const Float3& Translation) const noexcept
{
    DirectX::XMVECTOR xR = DirectX::XMLoadFloat4(reinterpret_cast<const DirectX::XMFLOAT4*>(&Rotation));
    DirectX::XMVECTOR xT = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&Translation));

    mFrustum.Transform(Out.mFrustum, Scale, xR, xT);
}

inline void BoundingFrustum::GetCorners(_Out_writes_(8) Float3* Corners) const noexcept
{
    mFrustum.GetCorners(reinterpret_cast<DirectX::XMFLOAT3*>(Corners));
}

inline ContainmentType BoundingFrustum::Contains(Float3 Point) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&Point));

    return (ContainmentType)(mFrustum.Contains(xP));
}

// Contains with other shapes
inline ContainmentType BoundingFrustum::Contains(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return (ContainmentType)mFrustum.Contains(xV0, xV1, xV2);
};

inline ContainmentType BoundingFrustum::Contains(const BoundingSphere& sh) const noexcept
{
    return (ContainmentType)mFrustum.Contains(sh.mSphere);
}
inline ContainmentType BoundingFrustum::Contains(const BoundingBox& box) const noexcept
{
    return (ContainmentType)mFrustum.Contains(box.mBox);
}
inline ContainmentType BoundingFrustum::Contains(const BoundingOrientedBox& box) const noexcept
{
    return (ContainmentType)mFrustum.Contains(box.mOBox);
}

inline ContainmentType BoundingFrustum::Contains(const BoundingFrustum& fr) const noexcept
{
    return (ContainmentType)mFrustum.Contains(fr.mFrustum);
}

// Intersects with other shapes
inline bool BoundingFrustum::Intersects(const BoundingSphere& sh) const noexcept
{
    return mFrustum.Intersects(sh.mSphere);
}
inline bool BoundingFrustum::Intersects(const BoundingBox& box) const noexcept
{
    return mFrustum.Intersects(box.mBox);
}
inline bool BoundingFrustum::Intersects(const BoundingOrientedBox& box) const noexcept
{
    return mFrustum.Intersects(box.mOBox);
}
inline bool BoundingFrustum::Intersects(const BoundingFrustum& fr) const noexcept
{
    return mFrustum.Intersects(fr.mFrustum);
}

// Triangle Frustum test
inline bool BoundingFrustum::Intersects(Float3 V0, Float3 V1, Float3 V2) const noexcept
{
    DirectX::XMVECTOR xV0 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V0));
    DirectX::XMVECTOR xV1 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V1));
    DirectX::XMVECTOR xV2 = DirectX::XMLoadFloat3(reinterpret_cast<DirectX::XMFLOAT3*>(&V2));

    return mFrustum.Intersects(xV0, xV1, xV2);
}

// Plane-Frustum test
inline PlaneIntersectionType BoundingFrustum::Intersects(Float4 Plane) const noexcept
{
    DirectX::XMVECTOR xP = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Plane));

    return (PlaneIntersectionType)mFrustum.Intersects(xP);
}

// Ray-Frustum test
inline bool BoundingFrustum::Intersects(Float4 Origin, Float4 Direction, float& Dist) const noexcept
{
    DirectX::XMVECTOR xO = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Origin));
    DirectX::XMVECTOR xD = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&Direction));

    return mFrustum.Intersects(xO, xD, Dist);
}

// Test sphere against six planes (see BoundingFrustum::GetPlanes)
inline ContainmentType BoundingFrustum::ContainedBy(Float4 plane0, Float4 plane1, Float4 plane2, Float4 plane3, Float4 plane4, Float4 plane5) const noexcept
{
    DirectX::XMVECTOR xP0 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane0));
    DirectX::XMVECTOR xP1 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane1));
    DirectX::XMVECTOR xP2 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane2));
    DirectX::XMVECTOR xP3 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane3));
    DirectX::XMVECTOR xP4 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane4));
    DirectX::XMVECTOR xP5 = DirectX::XMLoadFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(&plane5));

    return (ContainmentType)mFrustum.ContainedBy(xP0, xP1, xP2, xP3, xP4, xP5);
}

// Test frustum against six planes (see BoundingFrustum::GetPlanes)

inline void BoundingFrustum::GetPlanes(Float4* NearPlane, Float4* FarPlane, Float4* RightPlane, Float4* LeftPlane, Float4* TopPlane, Float4* BottomPlane) const noexcept
{
    DirectX::XMVECTOR vNearPlane, vFarPlane, vRightPlane, vLeftPlane, vTopPlane, vBottomPlane;

    mFrustum.GetPlanes(&vNearPlane, &vFarPlane, &vRightPlane, &vLeftPlane, &vTopPlane, &vBottomPlane);

    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(NearPlane), vNearPlane);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(FarPlane), vFarPlane);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(RightPlane), vRightPlane);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(LeftPlane), vLeftPlane);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(TopPlane), vTopPlane);
    DirectX::XMStoreFloat4(reinterpret_cast<DirectX::XMFLOAT4*>(BottomPlane), vBottomPlane);
}

// CTG Begin: Double Dong, create frustum without projection matrix
// static methods
inline void BoundingFrustum::CreateFromCameraInfo(BoundingFrustum& Out, const float AspectRatioOrWidth, const float FovOrHeight, const float NearPlane, const float FarPlane, bool bPerspectiveCamera) noexcept
{
    if (bPerspectiveCamera)
    {
        auto const halfFov = FovOrHeight * 0.5f;
        auto const ySlope = std::tan(halfFov);
        auto const xSlope = ySlope * AspectRatioOrWidth;

        auto& innerFrustum = Out.mFrustum;

        innerFrustum.Near = NearPlane;
        innerFrustum.Far = FarPlane;

        innerFrustum.Origin = DirectX::XMFLOAT3(0.0f, 0.0f, 0.0f);
        innerFrustum.Orientation = DirectX::XMFLOAT4(0.0f, 0.0f, 0.0f, 1.0f);

        innerFrustum.RightSlope = xSlope;
        innerFrustum.LeftSlope = -xSlope;

        innerFrustum.TopSlope = ySlope;
        innerFrustum.BottomSlope = -ySlope;
    }
    else
    {
        // TODO:  DirectX math cannot build orthogonal frustum, this module should be rewritten totally.
        auto const xSlope = AspectRatioOrWidth * 0.5f / FarPlane;
        auto const ySlope = FovOrHeight * 0.5f / FarPlane;

        auto& innerFrustum = Out.mFrustum;

        innerFrustum.Near = NearPlane;
        innerFrustum.Far = FarPlane;

        innerFrustum.Origin = DirectX::XMFLOAT3(0.0f, 0.0f, 0.0f);
        innerFrustum.Orientation = DirectX::XMFLOAT4(0.0f, 0.0f, 0.0f, 1.0f);

        innerFrustum.RightSlope = xSlope;
        innerFrustum.LeftSlope = -xSlope;

        innerFrustum.TopSlope = ySlope;
        innerFrustum.BottomSlope = -ySlope;
    }
}
// CTG End
inline Float4 operator*(const Float4x4& M, const Float4& V)
{
    float fX = (M.m00 * V.x) + (M.m01 * V.y) + (M.m02 * V.z) + (M.m03 * V.w);
    float fY = (M.m10 * V.x) + (M.m11 * V.y) + (M.m12 * V.z) + (M.m13 * V.w);
    float fZ = (M.m20 * V.x) + (M.m21 * V.y) + (M.m22 * V.z) + (M.m23 * V.w);
    float fW = (M.m30 * V.x) + (M.m31 * V.y) + (M.m32 * V.z) + (M.m33 * V.w);
    return Float4{fX, fY, fZ, fW};
}

inline Float4 operator*(const Float4& v, const Float4x4& m)
{
    float fX = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z) + (m.m30 * v.w);
    float fY = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z) + (m.m31 * v.w);
    float fZ = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z) + (m.m32 * v.w);
    float fW = (m.m03 * v.x) + (m.m13 * v.y) + (m.m23 * v.z) + (m.m33 * v.w);
    return Float4{fX, fY, fZ, fW};
}


inline Float3 Quaternion::Float3Rotate(const Float3& v) const
{
    Quaternion vq(v.x, v.y, v.z, 0.0f);
    Quaternion Result = Conjugate() * vq * *this;
    return Float3(Result.x, Result.y, Result.z);
}

inline Float4 Quaternion::Float4Rotate(const Float4& v) const
{
    Quaternion vq(v.x, v.y, v.z, 0.0f);
    Quaternion Result = Conjugate() * vq * *this;
    return Float4(Result.x, Result.y, Result.z, 1.0f);
}

inline Double3 Quaternion64::Double3Rotate(const Double3& v) const
{
    Quaternion64 vq(v.x, v.y, v.z, 0.0);
    Quaternion64 Result = Conjugate() * vq * *this;
    return Double3(Result.x, Result.y, Result.z);
}

inline Double4 Quaternion64::Double4Rotate(const Double4& v) const
{
    Quaternion64 vq(v.x, v.y, v.z, 0.0f);
    Quaternion64 Result = Conjugate() * vq * *this;
    return Double4(Result.x, Result.y, Result.z, 1.0);
}

inline Double4 operator*(const Double4x4& M, const Double4& V)
{
    double dX = (M.m00 * V.x) + (M.m01 * V.y) + (M.m02 * V.z) + (M.m03 * V.w);
    double dY = (M.m10 * V.x) + (M.m11 * V.y) + (M.m12 * V.z) + (M.m13 * V.w);
    double dZ = (M.m20 * V.x) + (M.m21 * V.y) + (M.m22 * V.z) + (M.m23 * V.w);
    double dW = (M.m30 * V.x) + (M.m31 * V.y) + (M.m32 * V.z) + (M.m33 * V.w);
    return Double4{dX, dY, dZ, dW};
}

inline Double4 operator*(const Double4& v, const Double4x4& m)
{
    double dX = (m.m00 * v.x) + (m.m10 * v.y) + (m.m20 * v.z) + (m.m30 * v.w);
    double dY = (m.m01 * v.x) + (m.m11 * v.y) + (m.m21 * v.z) + (m.m31 * v.w);
    double dZ = (m.m02 * v.x) + (m.m12 * v.y) + (m.m22 * v.z) + (m.m32 * v.w);
    double dW = (m.m03 * v.x) + (m.m13 * v.y) + (m.m23 * v.z) + (m.m33 * v.w);
    return Double4{dX, dY, dZ, dW};
}

inline Double4x4::operator Float4x4() const noexcept
{
    using namespace DirectX;
    XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m00));
    XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m10));
    XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m20));
    XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m30));
    XMVECTOR y1 = XMConvertVectorDoubleToFloat(x1);
    XMVECTOR y2 = XMConvertVectorDoubleToFloat(x2);
    XMVECTOR y3 = XMConvertVectorDoubleToFloat(x3);
    XMVECTOR y4 = XMConvertVectorDoubleToFloat(x4);
    Float4 z1, z2, z3, z4;
    SIMD::StoreFloat4(&z1, y1);
    SIMD::StoreFloat4(&z2, y2);
    SIMD::StoreFloat4(&z3, y3);
    SIMD::StoreFloat4(&z4, y4);
    return Float4x4(z1, z2, z3, z4);
}

inline Double4x4::operator Float4x4A() const noexcept
{
    using namespace DirectX;
    XMVECTOR256D x1 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m00));
    XMVECTOR256D x2 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m10));
    XMVECTOR256D x3 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m20));
    XMVECTOR256D x4 = XMLoadDouble4(reinterpret_cast<const XMDOUBLE4*>(&m30));
    XMVECTOR y1 = XMConvertVectorDoubleToFloat(x1);
    XMVECTOR y2 = XMConvertVectorDoubleToFloat(x2);
    XMVECTOR y3 = XMConvertVectorDoubleToFloat(x3);
    XMVECTOR y4 = XMConvertVectorDoubleToFloat(x4);
    Float4A z1, z2, z3, z4;
    SIMD::StoreFloat4A(&z1, y1);
    SIMD::StoreFloat4A(&z2, y2);
    SIMD::StoreFloat4A(&z3, y3);
    SIMD::StoreFloat4A(&z4, y4);
    return Float4x4A{z1, z2, z3, z4};
}

inline bool SHEvalDirectionalLight(size_t order, const Float3& dir, const Float3& color, float* resultR, float* resultG, float* resultB)
{
    DirectX::FXMVECTOR xDir = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&dir));
    DirectX::FXMVECTOR xColor = DirectX::XMLoadFloat3(reinterpret_cast<const DirectX::XMFLOAT3*>(&color));
    return DirectX::XMSHEvalDirectionalLight(order, xDir, xColor, resultR, resultG, resultB);
}
#endif
}   // namespace cross