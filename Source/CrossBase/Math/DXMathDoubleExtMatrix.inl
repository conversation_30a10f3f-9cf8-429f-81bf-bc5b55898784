#pragma once

/****************************************************************************
 *
 * Matrix
 *
 ****************************************************************************/

inline bool XM_CALLCONV XMMatrix64IsNaN(FXMMATRIX64 M) noexcept
{
    assert(false);
    return false;
}

inline bool XM_CALLCONV XMMatrix64IsInfinite(FXMMATRIX64 M) noexcept
{
    assert(false);
    return false;
}

inline bool XM_CALLCONV XMMatrix64IsIdentity(FXMMATRIX64 M) noexcept
{
    assert(false);
    return false;
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Multiply(FXMMATRIX64 M1, CXMMATRIX64 M2) noexcept
{
#if defined(_XM_SSE_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_)

    XMMATRIX64 mResult;
    XMVECTOR256D vX = XMVector256SplatX(M1.r[0]);
    XMVECTOR256D vY = XMVector256SplatY(M1.r[0]);
    XMVECTOR256D vZ = XMVector256SplatZ(M1.r[0]);
    XMVECTOR256D vW = XMVector256SplatW(M1.r[0]);

    // Perform the operation on the first row
    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    // Perform a binary add to reduce cumulative errors
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);

    mResult.r[0] = vX;

    vX = XMVector256SplatX(M1.r[1]);
    vY = XMVector256SplatY(M1.r[1]);
    vZ = XMVector256SplatZ(M1.r[1]);
    vW = XMVector256SplatW(M1.r[1]);

    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);

    mResult.r[1] = vX;

    vX = XMVector256SplatX(M1.r[2]);
    vY = XMVector256SplatY(M1.r[2]);
    vZ = XMVector256SplatZ(M1.r[2]);
    vW = XMVector256SplatW(M1.r[2]);

    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);

    mResult.r[2] = vX;

    vX = XMVector256SplatX(M1.r[3]);
    vY = XMVector256SplatY(M1.r[3]);
    vZ = XMVector256SplatZ(M1.r[3]);
    vW = XMVector256SplatW(M1.r[3]);

    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);
    mResult.r[3] = vX;
    return mResult;
#else
    XMMATRIX64 mResult;
    // Cache the invariants in registers
    double x = M1.m[0][0];
    double y = M1.m[0][1];
    double z = M1.m[0][2];
    double w = M1.m[0][3];
    // Perform the operation on the first row
    mResult.m[0][0] = (M2.m[0][0] * x) + (M2.m[1][0] * y) + (M2.m[2][0] * z) + (M2.m[3][0] * w);
    mResult.m[0][1] = (M2.m[0][1] * x) + (M2.m[1][1] * y) + (M2.m[2][1] * z) + (M2.m[3][1] * w);
    mResult.m[0][2] = (M2.m[0][2] * x) + (M2.m[1][2] * y) + (M2.m[2][2] * z) + (M2.m[3][2] * w);
    mResult.m[0][3] = (M2.m[0][3] * x) + (M2.m[1][3] * y) + (M2.m[2][3] * z) + (M2.m[3][3] * w);
    // Repeat for all the other rows
    x = M1.m[1][0];
    y = M1.m[1][1];
    z = M1.m[1][2];
    w = M1.m[1][3];
    mResult.m[1][0] = (M2.m[0][0] * x) + (M2.m[1][0] * y) + (M2.m[2][0] * z) + (M2.m[3][0] * w);
    mResult.m[1][1] = (M2.m[0][1] * x) + (M2.m[1][1] * y) + (M2.m[2][1] * z) + (M2.m[3][1] * w);
    mResult.m[1][2] = (M2.m[0][2] * x) + (M2.m[1][2] * y) + (M2.m[2][2] * z) + (M2.m[3][2] * w);
    mResult.m[1][3] = (M2.m[0][3] * x) + (M2.m[1][3] * y) + (M2.m[2][3] * z) + (M2.m[3][3] * w);
    x = M1.m[2][0];
    y = M1.m[2][1];
    z = M1.m[2][2];
    w = M1.m[2][3];
    mResult.m[2][0] = (M2.m[0][0] * x) + (M2.m[1][0] * y) + (M2.m[2][0] * z) + (M2.m[3][0] * w);
    mResult.m[2][1] = (M2.m[0][1] * x) + (M2.m[1][1] * y) + (M2.m[2][1] * z) + (M2.m[3][1] * w);
    mResult.m[2][2] = (M2.m[0][2] * x) + (M2.m[1][2] * y) + (M2.m[2][2] * z) + (M2.m[3][2] * w);
    mResult.m[2][3] = (M2.m[0][3] * x) + (M2.m[1][3] * y) + (M2.m[2][3] * z) + (M2.m[3][3] * w);
    x = M1.m[3][0];
    y = M1.m[3][1];
    z = M1.m[3][2];
    w = M1.m[3][3];
    mResult.m[3][0] = (M2.m[0][0] * x) + (M2.m[1][0] * y) + (M2.m[2][0] * z) + (M2.m[3][0] * w);
    mResult.m[3][1] = (M2.m[0][1] * x) + (M2.m[1][1] * y) + (M2.m[2][1] * z) + (M2.m[3][1] * w);
    mResult.m[3][2] = (M2.m[0][2] * x) + (M2.m[1][2] * y) + (M2.m[2][2] * z) + (M2.m[3][2] * w);
    mResult.m[3][3] = (M2.m[0][3] * x) + (M2.m[1][3] * y) + (M2.m[2][3] * z) + (M2.m[3][3] * w);
    return mResult;
#endif
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64MultiplyTranspose(FXMMATRIX64 M1, CXMMATRIX64 M2) noexcept
{
#if defined(_XM_SSE_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_) 
    XMVECTOR256D vX = XMVector256SplatX(M1.r[0]);
    XMVECTOR256D vY = XMVector256SplatY(M1.r[0]);
    XMVECTOR256D vZ = XMVector256SplatZ(M1.r[0]);
    XMVECTOR256D vW = XMVector256SplatW(M1.r[0]);

    // Perform the operation on the first row
    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    // Perform a binary add to reduce cumulative errors
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);

    XMVECTOR256D r0 = vX;

    vX = XMVector256SplatX(M1.r[1]);
    vY = XMVector256SplatY(M1.r[1]);
    vZ = XMVector256SplatZ(M1.r[1]);
    vW = XMVector256SplatW(M1.r[1]);

    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);

    XMVECTOR256D r1 = vX;

    vX = XMVector256SplatX(M1.r[2]);
    vY = XMVector256SplatY(M1.r[2]);
    vZ = XMVector256SplatZ(M1.r[2]);
    vW = XMVector256SplatW(M1.r[2]);

    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);

    XMVECTOR256D r2 = vX;

    vX = XMVector256SplatX(M1.r[3]);
    vY = XMVector256SplatY(M1.r[3]);
    vZ = XMVector256SplatZ(M1.r[3]);
    vW = XMVector256SplatW(M1.r[3]);

    vX = XMVector256Multiply(vX, M2.r[0]);
    vY = XMVector256Multiply(vY, M2.r[1]);
    vZ = XMVector256Multiply(vZ, M2.r[2]);
    vW = XMVector256Multiply(vW, M2.r[3]);
    vX = XMVector256Add(vX, vZ);
    vY = XMVector256Add(vY, vW);
    vX = XMVector256Add(vX, vY);
    XMVECTOR256D r3 = vX;

    XMMATRIX64 mResult;
    mResult.r[0] = XMVector256Set128D(
        // hi r2[0], r3[0]
        _mm_shuffle_pd(XMVector256XY(r2), XMVector256XY(r3), 0b00),
        // low r0[0], r1[0]
        _mm_shuffle_pd(XMVector256XY(r0), XMVector256XY(r1), 0b00));

    mResult.r[1] = XMVector256Set128D(
        // hi r2[1], r3[1]
        _mm_shuffle_pd(XMVector256XY(r2), XMVector256XY(r3), 0b11),
        // low r0[1], r1[1]
        _mm_shuffle_pd(XMVector256XY(r0), XMVector256XY(r1), 0b11));

    mResult.r[2] = XMVector256Set128D(
        // hi r2[2], r3[2]
        _mm_shuffle_pd(XMVector256ZW(r2), XMVector256ZW(r3), 0b00),
        // low r0[2], r1[2]
        _mm_shuffle_pd(XMVector256ZW(r0), XMVector256ZW(r1), 0b00));

    mResult.r[3] = XMVector256Set128D(
        // hi r2[3], r3[3]
        _mm_shuffle_pd(XMVector256ZW(r2), XMVector256ZW(r3), 0b11),
        // low r0[3], r1[3]
        _mm_shuffle_pd(XMVector256ZW(r0), XMVector256ZW(r1), 0b11));
    return mResult;
#else
    XMMATRIX64 mResult;
    // Cache the invariants in registers
    double x = M2.m[0][0];
    double y = M2.m[1][0];
    double z = M2.m[2][0];
    double w = M2.m[3][0];
    // Perform the operation on the first row
    mResult.m[0][0] = (M1.m[0][0] * x) + (M1.m[0][1] * y) + (M1.m[0][2] * z) + (M1.m[0][3] * w);
    mResult.m[0][1] = (M1.m[1][0] * x) + (M1.m[1][1] * y) + (M1.m[1][2] * z) + (M1.m[1][3] * w);
    mResult.m[0][2] = (M1.m[2][0] * x) + (M1.m[2][1] * y) + (M1.m[2][2] * z) + (M1.m[2][3] * w);
    mResult.m[0][3] = (M1.m[3][0] * x) + (M1.m[3][1] * y) + (M1.m[3][2] * z) + (M1.m[3][3] * w);
    // Repeat for all the other rows
    x = M2.m[0][1];
    y = M2.m[1][1];
    z = M2.m[2][1];
    w = M2.m[3][1];
    mResult.m[1][0] = (M1.m[0][0] * x) + (M1.m[0][1] * y) + (M1.m[0][2] * z) + (M1.m[0][3] * w);
    mResult.m[1][1] = (M1.m[1][0] * x) + (M1.m[1][1] * y) + (M1.m[1][2] * z) + (M1.m[1][3] * w);
    mResult.m[1][2] = (M1.m[2][0] * x) + (M1.m[2][1] * y) + (M1.m[2][2] * z) + (M1.m[2][3] * w);
    mResult.m[1][3] = (M1.m[3][0] * x) + (M1.m[3][1] * y) + (M1.m[3][2] * z) + (M1.m[3][3] * w);
    x = M2.m[0][2];
    y = M2.m[1][2];
    z = M2.m[2][2];
    w = M2.m[3][2];
    mResult.m[2][0] = (M1.m[0][0] * x) + (M1.m[0][1] * y) + (M1.m[0][2] * z) + (M1.m[0][3] * w);
    mResult.m[2][1] = (M1.m[1][0] * x) + (M1.m[1][1] * y) + (M1.m[1][2] * z) + (M1.m[1][3] * w);
    mResult.m[2][2] = (M1.m[2][0] * x) + (M1.m[2][1] * y) + (M1.m[2][2] * z) + (M1.m[2][3] * w);
    mResult.m[2][3] = (M1.m[3][0] * x) + (M1.m[3][1] * y) + (M1.m[3][2] * z) + (M1.m[3][3] * w);
    x = M2.m[0][3];
    y = M2.m[1][3];
    z = M2.m[2][3];
    w = M2.m[3][3];
    mResult.m[3][0] = (M1.m[0][0] * x) + (M1.m[0][1] * y) + (M1.m[0][2] * z) + (M1.m[0][3] * w);
    mResult.m[3][1] = (M1.m[1][0] * x) + (M1.m[1][1] * y) + (M1.m[1][2] * z) + (M1.m[1][3] * w);
    mResult.m[3][2] = (M1.m[2][0] * x) + (M1.m[2][1] * y) + (M1.m[2][2] * z) + (M1.m[2][3] * w);
    mResult.m[3][3] = (M1.m[3][0] * x) + (M1.m[3][1] * y) + (M1.m[3][2] * z) + (M1.m[3][3] * w);
    return mResult;
#endif
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Transpose(FXMMATRIX64 M) noexcept
{
#if defined(_XM_SSE_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_) 
    XMMATRIX64 mResult;
    mResult.r[0] = XMVector256Set128D(
        // hi r2[0], r3[0]
        _mm_shuffle_pd(XMVector256XY(M.r[2]), XMVector256XY(M.r[3]), 0b00),
        // low r0[0], r1[0]
        _mm_shuffle_pd(XMVector256XY(M.r[0]), XMVector256XY(M.r[1]), 0b00));

    mResult.r[1] = XMVector256Set128D(
        // hi r2[1], r3[1]
        _mm_shuffle_pd(XMVector256XY(M.r[2]), XMVector256XY(M.r[3]), 0b11),
        // low r0[1], r1[1]
        _mm_shuffle_pd(XMVector256XY(M.r[0]), XMVector256XY(M.r[1]), 0b11));

    mResult.r[2] = XMVector256Set128D(
        // hi r2[2], r3[2]
        _mm_shuffle_pd(XMVector256ZW(M.r[2]), XMVector256ZW(M.r[3]), 0b00),
        // low r0[2], r1[2]
        _mm_shuffle_pd(XMVector256ZW(M.r[0]), XMVector256ZW(M.r[1]), 0b00));

    mResult.r[3] = XMVector256Set128D(
        // hi r2[3], r3[3]
        _mm_shuffle_pd(XMVector256ZW(M.r[2]), XMVector256ZW(M.r[3]), 0b11),
        // low r0[3], r1[3]
        _mm_shuffle_pd(XMVector256ZW(M.r[0]), XMVector256ZW(M.r[1]), 0b11));
    return mResult;
#else 
    return {
        M.r[0].vector4_d64[0], M.r[1].vector4_d64[0], M.r[2].vector4_d64[0], M.r[3].vector4_d64[0], 
        M.r[0].vector4_d64[1], M.r[1].vector4_d64[1], M.r[2].vector4_d64[1], M.r[3].vector4_d64[1], 
        M.r[0].vector4_d64[2], M.r[1].vector4_d64[2], M.r[2].vector4_d64[2], M.r[3].vector4_d64[2], 
        M.r[0].vector4_d64[3], M.r[1].vector4_d64[3], M.r[2].vector4_d64[3], M.r[3].vector4_d64[3]
    };
#endif
}

_Use_decl_annotations_ inline XMMATRIX64 XM_CALLCONV XMMatrix64Inverse(XMVECTOR256D* pDeterminant, FXMMATRIX64 M) noexcept
{
    //Todo: The swizzle and permute operation is not optimized for all platform, we should do a performance test.

   XMMATRIX64 MT = XMMatrix64Transpose(M);
   
   XMVECTOR256D V0[4], V1[4];
   V0[0] = XMVector256Swizzle<XM_SWIZZLE_X_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_Y_D>(MT.r[2]);
   V1[0] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D>(MT.r[3]);
   V0[1] = XMVector256Swizzle<XM_SWIZZLE_X_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_Y_D>(MT.r[0]);
   V1[1] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D>(MT.r[1]);
   V0[2] = XMVector256Permute<XM_PERMUTE_0X_D, XM_PERMUTE_0Z_D, XM_PERMUTE_1X_D, XM_PERMUTE_1Z_D>(MT.r[2], MT.r[0]);
   V1[2] = XMVector256Permute<XM_PERMUTE_0Y_D, XM_PERMUTE_0W_D, XM_PERMUTE_1Y_D, XM_PERMUTE_1W_D>(MT.r[3], MT.r[1]);

   XMVECTOR256D D0 = XMVector256Multiply(V0[0], V1[0]);
   XMVECTOR256D D1 = XMVector256Multiply(V0[1], V1[1]);
   XMVECTOR256D D2 = XMVector256Multiply(V0[2], V1[2]);
   
   V0[0] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D>(MT.r[2]);
   V1[0] = XMVector256Swizzle<XM_SWIZZLE_X_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_Y_D>(MT.r[3]);
   V0[1] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D>(MT.r[0]);
   V1[1] = XMVector256Swizzle<XM_SWIZZLE_X_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_Y_D>(MT.r[1]);
   V0[2] = XMVector256Permute<XM_PERMUTE_0Y_D, XM_PERMUTE_0W_D, XM_PERMUTE_1Y_D, XM_PERMUTE_1W_D>(MT.r[2], MT.r[0]);
   V1[2] = XMVector256Permute<XM_PERMUTE_0X_D, XM_PERMUTE_0Z_D, XM_PERMUTE_1X_D, XM_PERMUTE_1Z_D>(MT.r[3], MT.r[1]);
   
   D0 = XMVector256NegativeMultiplySubtract(V0[0], V1[0], D0);
   D1 = XMVector256NegativeMultiplySubtract(V0[1], V1[1], D1);
   D2 = XMVector256NegativeMultiplySubtract(V0[2], V1[2], D2);
   
   V0[0] = XMVector256Swizzle<XM_SWIZZLE_Y_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D>(MT.r[1]);
   V1[0] = XMVector256Permute<XM_PERMUTE_1Y_D, XM_PERMUTE_0Y_D, XM_PERMUTE_0W_D, XM_PERMUTE_0X_D>(D0, D2);
   V0[1] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_X_D>(MT.r[0]);
   V1[1] = XMVector256Permute<XM_PERMUTE_0W_D, XM_PERMUTE_1Y_D, XM_PERMUTE_0Y_D, XM_PERMUTE_0Z_D>(D0, D2);
   V0[2] = XMVector256Swizzle<XM_SWIZZLE_Y_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D>(MT.r[3]);
   V1[2] = XMVector256Permute<XM_PERMUTE_1W_D, XM_PERMUTE_0Y_D, XM_PERMUTE_0W_D, XM_PERMUTE_0X_D>(D1, D2);
   V0[3] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_X_D>(MT.r[2]);
   V1[3] = XMVector256Permute<XM_PERMUTE_0W_D, XM_PERMUTE_1W_D, XM_PERMUTE_0Y_D, XM_PERMUTE_0Z_D>(D1, D2);
   
   XMVECTOR256D C0 = XMVector256Multiply(V0[0], V1[0]);
   XMVECTOR256D C2 = XMVector256Multiply(V0[1], V1[1]);
   XMVECTOR256D C4 = XMVector256Multiply(V0[2], V1[2]);
   XMVECTOR256D C6 = XMVector256Multiply(V0[3], V1[3]);
   
   V0[0] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_Z_D>(MT.r[1]);
   V1[0] = XMVector256Permute<XM_PERMUTE_0W_D, XM_PERMUTE_0X_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1X_D>(D0, D2);
   V0[1] = XMVector256Swizzle<XM_SWIZZLE_W_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Y_D>(MT.r[0]);
   V1[1] = XMVector256Permute<XM_PERMUTE_0Z_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1X_D, XM_PERMUTE_0X_D>(D0, D2);
   V0[2] = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_Z_D>(MT.r[3]);
   V1[2] = XMVector256Permute<XM_PERMUTE_0W_D, XM_PERMUTE_0X_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1Z_D>(D1, D2);
   V0[3] = XMVector256Swizzle<XM_SWIZZLE_W_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D, XM_SWIZZLE_Y_D>(MT.r[2]);
   V1[3] = XMVector256Permute<XM_PERMUTE_0Z_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1Z_D, XM_PERMUTE_0X_D>(D1, D2);
   
   C0 = XMVector256NegativeMultiplySubtract(V0[0], V1[0], C0);
   C2 = XMVector256NegativeMultiplySubtract(V0[1], V1[1], C2);
   C4 = XMVector256NegativeMultiplySubtract(V0[2], V1[2], C4);
   C6 = XMVector256NegativeMultiplySubtract(V0[3], V1[3], C6);
   
   V0[0] = XMVector256Swizzle<XM_SWIZZLE_W_D, XM_SWIZZLE_X_D, XM_SWIZZLE_W_D, XM_SWIZZLE_X_D>(MT.r[1]);
   V1[0] = XMVector256Permute<XM_PERMUTE_0Z_D, XM_PERMUTE_1Y_D, XM_PERMUTE_1X_D, XM_PERMUTE_0Z_D>(D0, D2);
   V0[1] = XMVector256Swizzle<XM_SWIZZLE_Y_D, XM_SWIZZLE_W_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Z_D>(MT.r[0]);
   V1[1] = XMVector256Permute<XM_PERMUTE_1Y_D, XM_PERMUTE_0X_D, XM_PERMUTE_0W_D, XM_PERMUTE_1X_D>(D0, D2);
   V0[2] = XMVector256Swizzle<XM_SWIZZLE_W_D, XM_SWIZZLE_X_D, XM_SWIZZLE_W_D, XM_SWIZZLE_X_D>(MT.r[3]);
   V1[2] = XMVector256Permute<XM_PERMUTE_0Z_D, XM_PERMUTE_1W_D, XM_PERMUTE_1Z_D, XM_PERMUTE_0Z_D>(D1, D2);
   V0[3] = XMVector256Swizzle<XM_SWIZZLE_Y_D, XM_SWIZZLE_W_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Z_D>(MT.r[2]);
   V1[3] = XMVector256Permute<XM_PERMUTE_1W_D, XM_PERMUTE_0X_D, XM_PERMUTE_0W_D, XM_PERMUTE_1Z_D>(D1, D2);
   
   XMVECTOR256D C1 = XMVector256NegativeMultiplySubtract(V0[0], V1[0], C0);
   C0 = XMVector256MultiplyAdd(V0[0], V1[0], C0);
   XMVECTOR256D C3 = XMVector256MultiplyAdd(V0[1], V1[1], C2);
   C2 = XMVector256NegativeMultiplySubtract(V0[1], V1[1], C2);
   XMVECTOR256D C5 = XMVector256NegativeMultiplySubtract(V0[2], V1[2], C4);
   C4 = XMVector256MultiplyAdd(V0[2], V1[2], C4);
   XMVECTOR256D C7 = XMVector256MultiplyAdd(V0[3], V1[3], C6);
   C6 = XMVector256NegativeMultiplySubtract(V0[3], V1[3], C6);
   
   XMMATRIX64 R;
   R.r[0] = XMVector256Select(C0, C1, g_XM64Select0101.v);
   R.r[1] = XMVector256Select(C2, C3, g_XM64Select0101.v);
   R.r[2] = XMVector256Select(C4, C5, g_XM64Select0101.v);
   R.r[3] = XMVector256Select(C6, C7, g_XM64Select0101.v);
   
   XMVECTOR256D Determinant = XMVector64x4Dot(R.r[0], MT.r[0]);
   
   if (pDeterminant != nullptr)
       *pDeterminant = Determinant;
   
   XMVECTOR256D Reciprocal = XMVector256Reciprocal(Determinant);
   
   XMMATRIX64 Result;
   Result.r[0] = XMVector256Multiply(R.r[0], Reciprocal);
   Result.r[1] = XMVector256Multiply(R.r[1], Reciprocal);
   Result.r[2] = XMVector256Multiply(R.r[2], Reciprocal);
   Result.r[3] = XMVector256Multiply(R.r[3], Reciprocal);
   return Result;
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64VectorTensorProduct(FXMVECTOR256D V1, FXMVECTOR256D V2) noexcept
{
    assert(false);
    return {};
}

inline XMVECTOR256D XM_CALLCONV XMMatrix64Determinant(FXMMATRIX64 M) noexcept
{
    static const XMVECTORD64 Sign = {{{1.0, -1.0, 1.0, -1.0}}};

#if defined(_XM_SSE_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_)
    XMVECTOR256D V0 = XMVector256Set128D(XMVector256XX(M.r[2]), XMVector256YX(M.r[2]));
    XMVECTOR256D V1 = XMVector256Set128D(XMVector256YY(M.r[3]), XMVector256ZZ(M.r[3]));
    XMVECTOR256D V2 = XMVector256Set128D(XMVector256XX(M.r[2]), XMVector256YX(M.r[2]));
    XMVECTOR256D V3 = XMVector256Set128D(XMVector256WZ(M.r[3]), XMVector256WW(M.r[3]));
    XMVECTOR256D V4 = XMVector256Set128D(XMVector256YY(M.r[2]), XMVector256ZZ(M.r[2]));
    XMVECTOR256D V5 = XMVector256Set128D(XMVector256WZ(M.r[3]), XMVector256WW(M.r[3]));
#else 
    XMVECTOR256D V0 = {M.r[2].vector4_d64[1], M.r[2].vector4_d64[0], M.r[2].vector4_d64[0], M.r[2].vector4_d64[0]};
    XMVECTOR256D V1 = {M.r[3].vector4_d64[2], M.r[3].vector4_d64[2], M.r[3].vector4_d64[1], M.r[3].vector4_d64[1]};
    XMVECTOR256D V2 = {M.r[2].vector4_d64[1], M.r[2].vector4_d64[0], M.r[2].vector4_d64[0], M.r[2].vector4_d64[0]};
    XMVECTOR256D V3 = {M.r[3].vector4_d64[3], M.r[3].vector4_d64[3], M.r[3].vector4_d64[3], M.r[3].vector4_d64[2]};
    XMVECTOR256D V4 = {M.r[2].vector4_d64[2], M.r[2].vector4_d64[2], M.r[2].vector4_d64[1], M.r[2].vector4_d64[1]};
    XMVECTOR256D V5 = {M.r[3].vector4_d64[3], M.r[3].vector4_d64[3], M.r[3].vector4_d64[3], M.r[3].vector4_d64[2]};
#endif
    XMVECTOR256D P0 = XMVector256Multiply(V0, V1);
    XMVECTOR256D P1 = XMVector256Multiply(V2, V3);
    XMVECTOR256D P2 = XMVector256Multiply(V4, V5);

#if defined(_XM_SSE_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_)
    V0 = XMVector256Set128D(XMVector256YY(M.r[2]), XMVector256ZZ(M.r[2]));
    V1 = XMVector256Set128D(XMVector256XX(M.r[3]), XMVector256YX(M.r[3]));
    V2 = XMVector256Set128D(XMVector256WZ(M.r[2]), XMVector256WW(M.r[2]));
    V3 = XMVector256Set128D(XMVector256XX(M.r[3]), XMVector256YX(M.r[3]));
    V4 = XMVector256Set128D(XMVector256WZ(M.r[2]), XMVector256WW(M.r[2]));
    V5 = XMVector256Set128D(XMVector256YY(M.r[3]), XMVector256ZZ(M.r[3]));
#else
    V0 = {M.r[2].vector4_d64[2], M.r[2].vector4_d64[2], M.r[2].vector4_d64[1], M.r[2].vector4_d64[1]};
    V1 = {M.r[3].vector4_d64[1], M.r[3].vector4_d64[0], M.r[3].vector4_d64[0], M.r[3].vector4_d64[0]};
    V2 = {M.r[2].vector4_d64[3], M.r[2].vector4_d64[3], M.r[2].vector4_d64[3], M.r[2].vector4_d64[2]};
    V3 = {M.r[3].vector4_d64[1], M.r[3].vector4_d64[0], M.r[3].vector4_d64[0], M.r[3].vector4_d64[0]};
    V4 = {M.r[2].vector4_d64[3], M.r[2].vector4_d64[3], M.r[2].vector4_d64[3], M.r[2].vector4_d64[2]};
    V5 = {M.r[3].vector4_d64[2], M.r[3].vector4_d64[2], M.r[3].vector4_d64[1], M.r[3].vector4_d64[1]};
#endif
    P0 = XMVector256NegativeMultiplySubtract(V0, V1, P0);
    P1 = XMVector256NegativeMultiplySubtract(V2, V3, P1);
    P2 = XMVector256NegativeMultiplySubtract(V4, V5, P2);
#if defined(_XM_SSE_INTRINSICS_) && !defined(_XM_NO_INTRINSICS_)
    V0 = XMVector256Set128D(XMVector256WZ(M.r[1]), XMVector256WW(M.r[1]));
    V1 = XMVector256Set128D(XMVector256YY(M.r[1]), XMVector256ZZ(M.r[1]));
    V2 = XMVector256Set128D(XMVector256XX(M.r[1]), XMVector256YX(M.r[1]));
#else
    V0 = {M.r[1].vector4_d64[3], M.r[1].vector4_d64[3], M.r[1].vector4_d64[3], M.r[1].vector4_d64[2]};
    V1 = {M.r[1].vector4_d64[2], M.r[1].vector4_d64[2], M.r[1].vector4_d64[1], M.r[1].vector4_d64[1]};
    V2 = {M.r[1].vector4_d64[1], M.r[1].vector4_d64[0], M.r[1].vector4_d64[0], M.r[1].vector4_d64[0]};
#endif
    XMVECTOR256D S = XMVector256Multiply(M.r[0], Sign.v);
    XMVECTOR256D R = XMVector256Multiply(V0, P0);
    R = XMVector256NegativeMultiplySubtract(V1, P1, R);
    R = XMVector256MultiplyAdd(V2, P2, R);
    return XMVector64x4Dot(S, R);
}

#define XM3RANKDECOMPOSE(a, b, c, x, y, z)      \
    if ((x) < (y))                  \
    {                               \
        if ((y) < (z))              \
        {                           \
            (a) = 2;                \
            (b) = 1;                \
            (c) = 0;                \
        }                           \
        else                        \
        {                           \
            (a) = 1;                \
                                    \
            if ((x) < (z))          \
            {                       \
                (b) = 2;            \
                (c) = 0;            \
            }                       \
            else                    \
            {                       \
                (b) = 0;            \
                (c) = 2;            \
            }                       \
        }                           \
    }                               \
    else                            \
    {                               \
        if ((x) < (z))              \
        {                           \
            (a) = 2;                \
            (b) = 0;                \
            (c) = 1;                \
        }                           \
        else                        \
        {                           \
            (a) = 0;                \
                                    \
            if ((y) < (z))          \
            {                       \
                (b) = 2;            \
                (c) = 1;            \
            }                       \
            else                    \
            {                       \
                (b) = 1;            \
                (c) = 2;            \
            }                       \
        }                           \
    }

#define XM3_DECOMP_EPSILON 0.0001

_Use_decl_annotations_ inline bool XM_CALLCONV XMMatrix64Decompose(XMVECTOR256D* outScale, XMVECTOR256D* outRotQuat, XMVECTOR256D* outTrans, FXMMATRIX64 M) noexcept
{
    static const XMVECTOR256D* pvCanonicalBasis[3] = {&g_XM64IdentityR0.v, &g_XM64IdentityR1.v, &g_XM64IdentityR2.v};

    assert(outScale != nullptr);
    assert(outRotQuat != nullptr);
    assert(outTrans != nullptr);

    // Get the translation
    outTrans[0] = M.r[3];

    XMVECTOR256D* ppvBasis[3];
    XMMATRIX64 matTemp;
    ppvBasis[0] = &matTemp.r[0];
    ppvBasis[1] = &matTemp.r[1];
    ppvBasis[2] = &matTemp.r[2];

    matTemp.r[0] = M.r[0];
    matTemp.r[1] = M.r[1];
    matTemp.r[2] = M.r[2];
    matTemp.r[3] = g_XM64IdentityR3;

    auto pdScales = reinterpret_cast<double*>(outScale);

    size_t a, b, c;
    XMVector256GetXPtr(&pdScales[0], XMVector64x3Length(ppvBasis[0][0]));
    XMVector256GetXPtr(&pdScales[1], XMVector64x3Length(ppvBasis[1][0]));
    XMVector256GetXPtr(&pdScales[2], XMVector64x3Length(ppvBasis[2][0]));
    pdScales[3] = 0.;

    XM3RANKDECOMPOSE(a, b, c, pdScales[0], pdScales[1], pdScales[2])

    if (pdScales[a] < XM3_DECOMP_EPSILON)
    {
        ppvBasis[a][0] = pvCanonicalBasis[a][0];
    }
    ppvBasis[a][0] = XMVector64x3Normalize(ppvBasis[a][0]);

    if (pdScales[b] < XM3_DECOMP_EPSILON)
    {
        size_t aa, bb, cc;
        double dAbsX, dAbsY, dAbsZ;

        dAbsX = fabs(XMVector256GetX(ppvBasis[a][0]));
        dAbsY = fabs(XMVector256GetY(ppvBasis[a][0]));
        dAbsZ = fabs(XMVector256GetZ(ppvBasis[a][0]));

        XM3RANKDECOMPOSE(aa, bb, cc, dAbsX, dAbsY, dAbsZ)

        ppvBasis[b][0] = XMVector64x3Cross(ppvBasis[a][0], pvCanonicalBasis[cc][0]);
    }

    ppvBasis[b][0] = XMVector64x3Normalize(ppvBasis[b][0]);

    if (pdScales[c] < XM3_DECOMP_EPSILON)
    {
        ppvBasis[c][0] = XMVector64x3Cross(ppvBasis[a][0], ppvBasis[b][0]);
    }

    ppvBasis[c][0] = XMVector64x3Normalize(ppvBasis[c][0]);

    double dDet = XMVector256GetX(XMMatrix64Determinant(matTemp));

    // use Kramer's rule to check for handedness of coordinate system
    if (dDet < 0.0)
    {
        // switch coordinate system by negating the scale and inverting the basis vector on the x-axis
        pdScales[a] = -pdScales[a];
        ppvBasis[a][0] = XMVector256Negate(ppvBasis[a][0]);

        dDet = -dDet;
    }

    dDet -= 1.0;
    dDet *= dDet;

    if (XM3_DECOMP_EPSILON < dDet)
    {
        // Non-SRT matrix encountered
        return false;
    }

    // generate the quaternion from the matrix
    outRotQuat[0] = XMQuaternion64RotationMatrix(matTemp);
    return true;
}

#undef XM3_DECOMP_EPSILON
#undef XM3RANKDECOMPOSE

inline XMMATRIX64 XM_CALLCONV XMMatrix64Identity() noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Set(double m00, double m01, double m02, double m03, double m10, double m11, double m12, double m13, double m20, double m21, double m22, double m23, double m30, double m31, double m32,
                                            double m33) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Translation(double OffsetX, double OffsetY, double OffsetZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64TranslationFromVector(FXMVECTOR256D Offset) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Scaling(double ScaleX, double ScaleY, double ScaleZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64ScalingFromVector(FXMVECTOR256D Scale) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationX(double Angle) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationY(double Angle) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationZ(double Angle) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationRollPitchYaw(double Pitch, double Yaw, double Roll) noexcept
{
    XMVECTOR256D Angles = XMVector256Set(Pitch, Yaw, Roll, 0.0);
    return XMMatrix64RotationRollPitchYawFromVector(Angles);
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationRollPitchYawFromVector(FXMVECTOR256D Angles) noexcept
{
    XMVECTOR256D Q = XMQuaternion64RotationRollPitchYawFromVector(Angles);
    return XMMatrix64RotationQuaternion(Q);
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationNormal(FXMVECTOR256D NormalAxis, double Angle) noexcept
{
    double fSinAngle;
    double fCosAngle;
    XM64ScalarSinCos(&fSinAngle, &fCosAngle, Angle);

    XMVECTOR256D A = XMVector256Set(fSinAngle, fCosAngle, 1.0 - fCosAngle, 0.0);

    XMVECTOR256D C2 = XMVector256SplatZ(A);
    XMVECTOR256D C1 = XMVector256SplatY(A);
    XMVECTOR256D C0 = XMVector256SplatX(A);

    XMVECTOR256D N0 = XMVector256Swizzle<XM_SWIZZLE_Y_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_X_D, XM_SWIZZLE_W_D>(NormalAxis);
    XMVECTOR256D N1 = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_W_D>(NormalAxis);

    XMVECTOR256D V0 = XMVector256Multiply(C2, N0);
    V0 = XMVector256Multiply(V0, N1);

    XMVECTOR256D R0 = XMVector256Multiply(C2, NormalAxis);
    R0 = XMVector256MultiplyAdd(R0, NormalAxis, C1);

    XMVECTOR256D R1 = XMVector256MultiplyAdd(C0, NormalAxis, V0);
    XMVECTOR256D R2 = XMVector256NegativeMultiplySubtract(C0, NormalAxis, V0);

    V0 = XMVector256Select(A, R0, g_XM64Select1110.v);
    XMVECTOR256D V1 = XMVector256Permute<XM_PERMUTE_0Z_D, XM_PERMUTE_1Y_D, XM_PERMUTE_1Z_D, XM_PERMUTE_0X_D>(R1, R2);
    XMVECTOR256D V2 = XMVector256Permute<XM_PERMUTE_0Y_D, XM_PERMUTE_1X_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1X_D>(R1, R2);

    XMMATRIX64 M;
    M.r[0] = XMVector256Permute<XM_PERMUTE_0X_D, XM_PERMUTE_1X_D, XM_PERMUTE_1Y_D, XM_PERMUTE_0W_D>(V0, V1);
    M.r[1] = XMVector256Permute<XM_PERMUTE_1Z_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1W_D, XM_PERMUTE_0W_D>(V0, V1);
    M.r[2] = XMVector256Permute<XM_PERMUTE_1X_D, XM_PERMUTE_1Y_D, XM_PERMUTE_0Z_D, XM_PERMUTE_0W_D>(V0, V2);
    M.r[3] = g_XM64IdentityR3.v;
    return M;
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationAxis(FXMVECTOR256D Axis, double Angle) noexcept
{
    assert(!XMVector64x3Equal(Axis, XMVector256Zero()));
    assert(!XMVector64x3IsInfinite(Axis));

    XMVECTOR256D Normal = XMVector64x3Normalize(Axis);
    return XMMatrix64RotationNormal(Normal, Angle);
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64RotationQuaternion(FXMVECTOR256D Quaternion) noexcept
{
    static const XMVECTORD64 Constant1110 = {{{1.0, 1.0, 1.0, 0.0}}};

    XMVECTOR256D Q0 = XMVector256Add(Quaternion, Quaternion);
    XMVECTOR256D Q1 = XMVector256Multiply(Quaternion, Q0);

    XMVECTOR256D V0 = XMVector256Permute<XM_PERMUTE_0Y_D, XM_PERMUTE_0X_D, XM_PERMUTE_0X_D, XM_PERMUTE_1W_D>(Q1, Constant1110.v);
    XMVECTOR256D V1 = XMVector256Permute<XM_PERMUTE_0Z_D, XM_PERMUTE_0Z_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1W_D>(Q1, Constant1110.v);
    XMVECTOR256D R0 = XMVector256Subtract(Constant1110, V0);
    R0 = XMVector256Subtract(R0, V1);

    V0 = XMVector256Swizzle<XM_SWIZZLE_X_D, XM_SWIZZLE_X_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_W_D>(Quaternion);
    V1 = XMVector256Swizzle<XM_SWIZZLE_Z_D, XM_SWIZZLE_Y_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_W_D>(Q0);
    V0 = XMVector256Multiply(V0, V1);

    V1 = XMVector256SplatW(Quaternion);
    XMVECTOR256D V2 = XMVector256Swizzle<XM_SWIZZLE_Y_D, XM_SWIZZLE_Z_D, XM_SWIZZLE_X_D, XM_SWIZZLE_W_D>(Q0);
    V1 = XMVector256Multiply(V1, V2);

    XMVECTOR256D R1 = XMVector256Add(V0, V1);
    XMVECTOR256D R2 = XMVector256Subtract(V0, V1);

    V0 = XMVector256Permute<XM_PERMUTE_0Y_D, XM_PERMUTE_1X_D, XM_PERMUTE_1Y_D, XM_PERMUTE_0Z_D>(R1, R2);
    V1 = XMVector256Permute<XM_PERMUTE_0X_D, XM_PERMUTE_1Z_D, XM_PERMUTE_0X_D, XM_PERMUTE_1Z_D>(R1, R2);

    XMMATRIX64 M;
    M.r[0] = XMVector256Permute<XM_PERMUTE_0X_D, XM_PERMUTE_1X_D, XM_PERMUTE_1Y_D, XM_PERMUTE_0W_D>(R0, V0);
    M.r[1] = XMVector256Permute<XM_PERMUTE_1Z_D, XM_PERMUTE_0Y_D, XM_PERMUTE_1W_D, XM_PERMUTE_0W_D>(R0, V0);
    M.r[2] = XMVector256Permute<XM_PERMUTE_1X_D, XM_PERMUTE_1Y_D, XM_PERMUTE_0Z_D, XM_PERMUTE_0W_D>(R0, V1);
    M.r[3] = g_XM64IdentityR3.v;
    return M;
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Transformation2D(FXMVECTOR256D ScalingOrigin, double ScalingOrientation, FXMVECTOR256D Scaling, FXMVECTOR256D RotationOrigin, double Rotation, GXMVECTOR256D Translation) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Transformation(FXMVECTOR256D ScalingOrigin, FXMVECTOR256D ScalingOrientationQuat, FXMVECTOR256D Scaling, GXMVECTOR256D RotationOrigin, HXMVECTOR256D RotationQuat, HXMVECTOR256D Translation) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64AffineTransformation2D(FXMVECTOR256D Scaling, FXMVECTOR256D RotationOrigin, double Rotation, FXMVECTOR256D Translation) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64AffineTransformation(FXMVECTOR256D Scaling, FXMVECTOR256D RotationOrigin, FXMVECTOR256D RotationQuaternion, GXMVECTOR256D Translation) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Reflect(FXMVECTOR256D ReflectionPlane) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64Shadow(FXMVECTOR256D ShadowPlane, FXMVECTOR256D LightPosition) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64LookAtLH(FXMVECTOR256D EyePosition, FXMVECTOR256D FocusPosition, FXMVECTOR256D UpDirection) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64LookAtRH(FXMVECTOR256D EyePosition, FXMVECTOR256D FocusPosition, FXMVECTOR256D UpDirection) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64LookToLH(FXMVECTOR256D EyePosition, FXMVECTOR256D EyeDirection, FXMVECTOR256D UpDirection) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64LookToRH(FXMVECTOR256D EyePosition, FXMVECTOR256D EyeDirection, FXMVECTOR256D UpDirection) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveLH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveRH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveFovLH(double FovAngleY, double AspectRatio, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveFovRH(double FovAngleY, double AspectRatio, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveOffCenterLH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64PerspectiveOffCenterRH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicLH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicRH(double ViewWidth, double ViewHeight, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicOffCenterLH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}

inline XMMATRIX64 XM_CALLCONV XMMatrix64OrthographicOffCenterRH(double ViewLeft, double ViewRight, double ViewBottom, double ViewTop, double NearZ, double FarZ) noexcept
{
    assert(false);
    return {};
}