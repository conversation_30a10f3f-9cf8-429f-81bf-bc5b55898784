#pragma once

#include <string>
#include "CrossBaseForward.h"
#include <fstream>
#include <queue>
#if CROSSENGINE_LINUX
#    include <unistd.h>
#    include <dirent.h>
#endif
#if CROSSENGINE_WIN
#    include <io.h>
#    include <corecrt_io.h>
#    include <filesystem>
#endif
namespace cross
{
    class CROSS_BASE_API FileHelper
    {
    public:
        // Load a text file to an std::string.
        static bool LoadFileToString(std::string& strResult, const char* pcszFilename, std::ios_base::openmode openMode = std::ios::in);

        // Write the std::string to a file.
        static bool SaveStringToFile(std::string strSource, const char* pcszFilename, std::ios_base::openmode openMode = std::ios::out);

        static bool GetFiles(std::string dir, std::vector<std::string>& dirs, std::vector<std::string>& files);

        static bool CopyFile(const std::string& scrPath, const std::string& desPath);

        static bool CopyDir(const std::string& scrPath, const std::string& desPath);

        static bool ParallelCopyDir(const std::string& scrPath, const std::string& desPath);

        static std::string GetFolderPath(const std::string& inFilePath);

        static UInt64 GetFileSize(const std::string& inPath);

#if CROSSENGINE_WIN
        static bool IsExistingFile(const std::string& inFilePath);

        static bool IsExistingFolder(const std::string& inFilePath);

        static bool GuarenteeDirectoryExisted(const std::string& inPath);

        static bool IsExistingOrCreate(const std::string& name);

        static bool IsExistingOrDelete(const std::string& inPath);
#endif
    };
}
