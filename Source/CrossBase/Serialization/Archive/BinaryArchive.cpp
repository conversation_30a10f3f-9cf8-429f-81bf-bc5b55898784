#include "BinaryArchive.h"

#include <memory>

namespace cross {
BinaryArchive::BinaryArchive()
    : mBuffer(nullptr)
    , mSize(0)
    , mAnchor(0)
{}

BinaryArchive::BinaryArchive(UInt64 size)
    : mBuffer(nullptr)
    , mSize(size)
    , mAnchor(0)
{
    if (mSize > 0)
    {
        mBuffer = new UInt8[mSize];
    }
}

BinaryArchive::BinaryArchive(UInt8* data, UInt64 size)
    : mBuffer(data)
    , mSize(size)
    , mAnchor(0)
{}

BinaryArchive::~BinaryArchive()
{
    if (mBuffer)
        delete[] mBuffer;
    mBuffer = nullptr;
}

bool BinaryArchive::Write(void const* data, UInt64 size)
{
    if (mAnchor + size > mSize)
    {
        UInt8* newBuff = new UInt8[mAnchor + size];
        memcpy(newBuff, mBuffer, mAnchor);
        if (mBuffer)
            delete mBuffer;
        mBuffer = newBuff;
        mSize = mAnchor + size;
    }
    memcpy(mBuffer + mAnchor, data, size);
    mAnchor += size;
    return true;
}

UInt8 const* BinaryArchive::Read(UInt64 size) const
{
    if (mAnchor + size > mSize)
        return nullptr;
    UInt8* ret = new UInt8[size];
    memcpy(ret, mBuffer + mAnchor, size);
    mAnchor += size;
    return ret;
}

UInt64 BinaryArchive::Read(void* data, UInt64 size) const
{
    if (mAnchor + size > mSize)
        return 0;
    memcpy(data, mBuffer + mAnchor, size);
    mAnchor += size;
    return size;
}

void BinaryArchive::Resize(UInt64 newSize) {
    if (newSize == mSize)
        return;

    if (mSize < newSize)
    {
        UInt8* newBuff = new UInt8[newSize];
        if (mBuffer)
        {
            memcpy(newBuff, mBuffer, mSize);
            delete[] mBuffer;
        }
        mBuffer = newBuff;
    }
    mSize = newSize;
    mAnchor = mAnchor < newSize ? mAnchor : newSize;
}

void BinaryArchive::Delete(BinaryArchive* ptr, bool reserveBufer)
{
    if (!ptr)
        return;
    if (reserveBufer)
        ptr->Hold(nullptr, 0);
    delete ptr;
}
}   // namespace cross
