#include "EnginePrefix.h"
#include "IGameWorld.h"
#include "CECommon/Common/FrameTickManager.h"
#include "CECommon/Common/GameSystemBase.h"

namespace cross {
IGameWorld::IGameWorld()
    : mFrameTickManager(std::make_unique<FrameTickManager>())
{}

void IGameWorld::AddSystemDependency(GameSystemBase* predecessor, GameSystemBase* successor)
{
    mFrameTickManager->AddSystemDependency(predecessor, successor);
}

void IGameWorld::AddSystemDependency(const FrameTickManager::DependencyGroup& predecessors, const FrameTickManager::DependencyGroup& successors)
{
    mFrameTickManager->AddSystemDependency(predecessors, successors);
}

void IGameWorld::AddSystemDependency(GameSystemBase* predecessor, const FrameTickManager::DependencyGroup& successors)
{
    mFrameTickManager->AddSystemDependency(predecessor, successors);
}

void IGameWorld::AddSystemDependency(const FrameTickManager::DependencyGroup& predecessors, GameSystemBase* successor)
{
    mFrameTickManager->AddSystemDependency(predecessors, successor);
}
}   // namespace cross
