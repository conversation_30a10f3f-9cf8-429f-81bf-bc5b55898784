#pragma once

#include <stdint.h>

namespace cross
{
//Do *NOT* change following enum order and value unless you change modify frame allocator too!
enum FrameStage : uint8_t
{
    FRAME_STAGE_INVALID = 0,
    FRAME_STAGE_GAME = 0x01,
    FRAME_STAGE_RENDER = 0x02,
    FRAME_STAGE_GAME_RENDER = FRAME_STAGE_GAME | FRAME_STAGE_RENDER,   
    //private value
    FRAME_STAGE_FULL = FRAME_STAGE_GAME | FRAME_STAGE_RENDER,
};

enum FrameParamConst
{
    MAX_FRAME_PARAM_COUNT = 8,
    MAX_FRAME_STAGE_MEM_BUCKET_COUNT = 6,
    FRAME_GAME_RENDER_STAGE_ALLOCATOR_COUNT = 3, // Game\Render\Game_Render
};
#define MAX_LIFE_TIME 2
}