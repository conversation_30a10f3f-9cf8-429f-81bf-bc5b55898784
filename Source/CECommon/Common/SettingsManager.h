#pragma once
#include "CECommon/Common/CECommonForward.h"
#include "CECommon/Common/SystemEvent.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "CECommon/Utilities/ValueContainer.h"
#include "CrossBase/String/HashString.h"
#include "CrossBase/Math/CrossMath.h"
#include "Runtime/Reflection/TypeGet.h"
#include "CECommon/Common/InGameTerminalManager.h"
namespace cross
{

    enum AppStartUpType : UInt8
    {
        AppStartUpTypeStandAlone = 0,
        AppStartUpTypeCrossEditor = 1,
        AppStartUpTypeThumbnailProcessor = 2,
        AppStartUpTypeHeadless = 3,
    };

    enum PlatformType : uint16_t
    {
        PLATFORMTYPE_INVALID = 0,
        PLATFORMTYPE_ANDROID = 0x01,
        PLATFORMTYPE_MACOS = 0x02,
        PLATFORMTYPE_IOS = 0x04,
        PLATFORMTYPE_WIN = 0x08,
        PLATFORMTYPE_CONSOLE = 0x10,
        PLATFORMTYPE_PC = PLATFORMTYPE_MACOS | PLATFORMTYPE_WIN,
        PLATFORMTYPE_MOBILE = PLATFORMTYPE_IOS | PLATFORMTYPE_ANDROID
    };

    enum FeatureLevel : uint8_t
    {
        FEATURE_LEVEL0 = 0,
        FEATURE_LEVEL1 = 1,
        FEATURE_LEVEL2 = 2,
        FEATURE_LEVEL3 = 3,
    };

    enum FullScreenMode : UInt8
    {
        Windowed = 0,
        Exclusive = 1,
        BoardlessWindowed = 2,
    };

    enum ScalabilityLevel : SInt8
    {
        SCALABILITY_LEVEL_INVALID = -1,
        SCALABILITY_LEVEL0 = 0,
        SCALABILITY_LEVEL1 = 1,
        SCALABILITY_LEVEL2 = 2,
    };

    class IFileSystemInfrastructure;


    using RenderPipelineSettingEvent = SystemEvent<const RenderPipelineSetting*>;

    struct DecalSetting
    {
        bool Enable = true;
    };
    struct MonitorSetting
    {
        std::vector<std::pair<UInt32, UInt32>> ResolutionList;
        SInt32 CurrentResolutionIndex = -1;
        FullScreenMode DisplayMode = FullScreenMode::Windowed;
        SInt32 MonitorIndex = -1;
    };
    struct LayerInfo
    {
        CEProperty(Editor) UInt32 LayerIndex;
        CEProperty(Editor) std::string LayerName;
        CEProperty(Editor) bool IsBuiltInLayer = false;
    };

    class LayerSetting
    {
    public:
        static constexpr UInt32 LayerCountMax = 32u;

        CEMeta(Editor) static constexpr bool IsLayerIndexValid(UInt32 layerIndex)
        {
            return layerIndex < LayerCountMax;
        }
        
        LayerSetting(SettingsManager* settingManager);

        CEMeta(Editor) CECOMMON_API bool SetLayer(UInt32 index, const std::string& name);

        CEMeta(Editor) CECOMMON_API const LayerInfo& GetLayerInfo(UInt32 index) const;

        CEMeta(Editor) CECOMMON_API bool IsLayerAllocated(UInt32 index) const;

        CEMeta(Editor) CECOMMON_API SInt32 GetLayerIndex(const std::string& name) const;

        CEMeta(Editor) CECOMMON_API const std::array<LayerInfo, LayerCountMax>& GetAllLayerInfos() const;

        CEMeta(Editor) CECOMMON_API std::vector<LayerInfo> GetAllocatedLayerInfos() const;

        CEMeta(Editor) CECOMMON_API void SerializeConfigToFile();

    private:
        LayerSetting()
            : mEmptyLayerInfo()
        {}

    private:
        const LayerInfo mEmptyLayerInfo;
        SettingsManager* mSettingManager;
        std::array<LayerInfo, LayerCountMax> mLayers;
    };

    class CEMeta(Puerts) SettingsManager : public SystemEventManager<RenderPipelineSettingEvent>
    {
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    // new setting manager
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    public:
        using SettingKey = HashString;
        using CustomSettingValue = std::variant<bool, int, float, std::string, std::vector<double>>;
        using SettingValue = CustomSettingValue;
//        using CustomeSettingFunc = std::function<void(std::unordered_map<SettingKey, CustomeSettingValue>&)>;

        template<typename T, typename VARIANT_T>
        struct is_one_of;

        template<typename T, typename... ALL_T>
        struct is_one_of<T, std::variant<ALL_T...>> : public std::disjunction<std::is_same<T, ALL_T>...>
        {};

        struct MPCDI
        {
            bool enabled = false;
            double yaw = 0;
            double pitch = 0;
            double roll = 0;
            double left = 0;
            double right = 0;
            double top = 0;
            double bottom = 0;
            Int3 color_table_size = Int3(3, 3, 3);
            Float2 color_table_pixel_size = Float2(1.0f / 4.0f / 4.0f / 3.0f, 1.0f / 4.0f / 2.0f);
            Int2 grid_size = Int2(3, 2);
            float effectOpacity = -1.0f;
        };

        //void SetCustomeSettingFunc(CustomeSettingFunc  customeSettingsFunc) { mCustomeSettingsFunc = customeSettingsFunc; }
        template<typename T>
        CECOMMON_API bool HasKey(const SettingKey& key)
        {
            if constexpr
                (std::is_same_v<Float2, T> || std::is_same_v<Float3, T> || std::is_same_v<Float4, T>)
            {
                    return mContext.HasKey<std::vector<double>>(key);
            }

            return mContext.HasKey<T>(key);
        }

        CECOMMON_API bool HasKey(const SettingKey& key) {
            return mContext.HasValidKey(key);
        }

        // note: need use HasKey<T> to check the key and value type match
        template<typename T>
        CECOMMON_API T GetKeyValue(const SettingKey& key)
        {
            if constexpr
                (std::is_same_v<Float2, T>)
                {
                auto ret = std::get<std::vector<double>>(mContext[key]);
                    return Float2(ret[0], ret[1]);
                }
            else if constexpr
                (std::is_same_v<Float3, T>) {
                auto ret = std::get<std::vector<double>>(mContext[key]);
                    return Float3(ret[0], ret[1], ret[2]);
            }
            else if constexpr
                (std::is_same_v<Float4, T>) {
                auto ret = std::get<std::vector<double>>(mContext[key]);
                    return Float4(ret[0], ret[1], ret[2], ret[3]);
            }

            return std::get<T>(mContext[key]);
        }

        CECOMMON_API void SetKeyValue(const SettingKey& key, SettingValue value)
        {
            mContext.SetKeyValue(key, value);
        }

        template<typename T>
        CECOMMON_API bool GetValue(const SettingKey& key, T& OutValue) const
        {
            std::vector<double> ret;
            if constexpr (std::is_same_v<Float2, T>)
            {
                mContext.GetValue(key, ret);
                OutValue = Float2(ret[0], ret[1]);
                return true;
            }
            else if constexpr (std::is_same_v<Float3, T>)
            {
                mContext.GetValue(key, ret);
                OutValue= Float3(ret[0], ret[1], ret[2]);
                return true;
            }
            else if constexpr (std::is_same_v<Float4, T>)
            {
                mContext.GetValue(key, ret);
                OutValue = Float4(ret[0], ret[1], ret[2], ret[3]);
                return true;
            }
            return mContext.GetValue(key, OutValue);
        }

        auto& GetContext()
        {
            return mContext;
        }
    private:
        void ExtractValue(std::string PrevKey, const DeserializeNode& node);

        void ExtractLayers(const DeserializeNode& node);

        void OverrideProjectConfig(ScalabilityLevel level, const DeserializeNode& node);

        ValueContainer<SettingValue> mContext;
        /////////////////////////////////////////////////////////////////////////////////////////////////////
        // old setting managet
        /////////////////////////////////////////////////////////////////////////////////////////////////////
    public:
        CECOMMON_API ~SettingsManager();

        CEMeta(Editor) void SetProjectName(const char* projectName)
        {
            mProjectName = projectName;
        }

        CEMeta(Editor) const cross::HashString& GetProjectName() const
        {
            return mProjectName;
        }

        CECOMMON_API bool GetIsAsyncLoading() const;

        CECOMMON_API void SetUseSeparateRenderingThread(bool enable);

        CECOMMON_API void SetMessageKeepOnInit(bool enable);


        CEMeta(Editor) bool GetUseSeparateRenderingThread() const
        {
            return mUseSeparateRenderingThread;
        }

        CEMeta(Editor) bool GetUseSeparatePresentThread() const
        {
            return mUseSeperatePresentThread && GetAppStartUpType() == AppStartUpTypeStandAlone;
        }

        CEMeta(Editor) CECOMMON_API void SetNumTaskThreads(SInt32 numTaskThreads);

        CEMeta(Editor) SInt32 GetNumTaskThreads() const
        {
            return mNumTaskThreads;
        }

        CEMeta(Editor) CECOMMON_API void SetLaunchScene(std::string const& scenePath);

        CEMeta(Editor) std::string const& GetLaunchScene() const noexcept
        {
            return mLaunchScene;
        }

        CEMeta(Editor) void SetAppStartUpType(AppStartUpType startUpType)
        {
            mAppStartUpType = startUpType;
            if (mFFXFrameInterpolation && mAppStartUpType != AppStartUpType::AppStartUpTypeStandAlone)
            {
                mFFXFrameInterpolation = false;
            }
        }

        CEMeta(Editor) cross::AppStartUpType GetAppStartUpType() const
        {
            return mAppStartUpType;
        }

        CEMeta(Editor) bool ValidSetting() const noexcept
        {
            return mSettingSuccessfullyLoaded;
        }

        CEMeta(Editor) bool GetRenderdocEnable() const
        {
            return mRenderdoc;
        }

        CEMeta(Editor) bool GetEnableDLSS() const
        {
            return mEnableDLSS;
        }

        CEMeta(Editor) void SetEnableDLSS(bool enable)
        {
            mEnableDLSS=enable;
        }

        CEMeta(Editor) bool GetFFXFrameInterpolation() const
        {
            return mFFXFrameInterpolation;
        }

        CEMeta(Editor) void SetFFXFrameInterpolation(bool enable)
        {
            mFFXFrameInterpolation = enable;
        }

        CEMeta(Editor) bool GetEnableDLSSG() const
        {
            return mEnableDLSSG;
        }

        CEMeta(Editor)
        std::string GetClientName() const
        {
            return mClientName;
        }

        CEMeta(Editor)
        void SetClientName(std::string clientName)
        {
            mClientName = clientName;
        }

        CEMeta(Editor)
        bool GetShowDLSSDebugInfo() const
        {
            return mShowDLSSDebugInfo;
        }

        CEMeta(Editor) void SetRenderdocEnable(bool enable)
        {
            mRenderdoc = enable;
        }
        CEMeta(Editor) bool GetGPUSkinEnable() const
        {
            return mUseGPUSkin;
        }

        CEMeta(Editor) void SetGPUSkinEnable(bool enable)
        {
            mUseGPUSkin = enable;
        }

        CEMeta(Editor) bool GetUseAsyncLoading() const
        {
            return mUseAsyncLoading;
        }

        CEMeta(Editor) void SetUseAsyncLoading(bool useAsyncLoading)
        {
            mUseAsyncLoading = useAsyncLoading;
        }

        CEMeta(Editor) bool GetUseMultithreadedRendering() const
        {
            return mUseMultithreadedRendering;
        }

        CEMeta(Editor) void SetUseMultithreadedRendering(bool useMultithreadedRendering)
        {
            mUseMultithreadedRendering = useMultithreadedRendering;
        }

        CEMeta(Editor) bool GetCullingEnable() const
        {
            return mEnableCulling;
        }

        bool GetMessageKeepOnInit() const 
        {
            return mMessageKeep;
        }

        CEMeta(Editor) void SetCullingEnable(bool enableCulling)
        {
            mEnableCulling = enableCulling;
        }

        CEMeta(Editor) bool GetEnableFoliagePicking() const
        {
            return mEnableFoliagePicking;
        }

        CEMeta(Editor) void SetEnableFoliagePicking(bool enableFoliagePicking)
        {
            mEnableFoliagePicking = enableFoliagePicking;
        }

        CEMeta(Editor) bool GetCullingVisualizationEnable() const
        {
            return mEnableCullingVisualization;
        }

        CEMeta(Editor) void SetCullingVisualizationEnable(bool enableCullingVisualization)
        {
            mEnableCullingVisualization = enableCullingVisualization;
        }

        CEMeta(Editor) CECOMMON_API void SetRGPThreadingMultiplexingFactor(UInt32 factor)
        {
            mRGPThreadingMultiplexingFactor = std::max(1U, factor);
        }

        CEMeta(Editor) UInt32 GetRGPThreadingMultiplexingFactor() const
        {
            return mRGPThreadingMultiplexingFactor;
        }

        void SetIsThumbnailProcess(bool isThumbnailProcess)
        {
            mIsThumbnailProcess = isThumbnailProcess;
        }
        bool GetIsThumbnailProcess()
        {
            return mIsThumbnailProcess;
        }
        CEMeta(Editor) CECOMMON_API bool LoadFromFile();

        CEMeta(Editor) CECOMMON_API void SerializeEngineConfigToFile();

        CEMeta(Editor) CECOMMON_API void SerializeProjectConfigToFile();

        CEMeta(Editor) CECOMMON_API void SerializeLayerConfigToFile();

        CEMeta(Editor) PlatformType GetPlatform() const
        {
            return mPlatform;
        }

        CEMeta(Editor) const float GetMaxShadowDistance() const
        {
            return mRenderPipelineSetting->MaxShadowDistance;
        }

        CEMeta(Editor) CECOMMON_API cross::NGIPlatform GetRenderMode() const
        {
            return mRenderMode;
        }

        CEMeta(Editor) CECOMMON_API cross::RenderPipelineSetting& GetRenderPipelineSettingForEditor()
        {
            return *mRenderPipelineSetting;
        }

        CEMeta(Editor) CECOMMON_API void NotifyRenderPipelineSettingChanged();

        CECOMMON_API void ReloadRenderPipelineSetting();

        cross::RenderPipelineSetting* GetRenderPipelineSetting()
        {
            return mRenderPipelineSetting;
        }

        CEMeta(Editor) CECOMMON_API SInt32 GetMaxTickRates() const
        {
            return mMaxTickRates;
        }
        template<typename RPSettingtype>
        static void RegisterRPSettingVar(RPSettingtype* setting);

        CEMeta(Editor) CECOMMON_API void SetMaxTickRates(SInt32 maxTickRates);

        CEMeta(Editor) CECOMMON_API const std::vector<std::string>& GetEnginePreloadResources() const
        {
            return mEnginePreloadResources;
        }

        CEMeta(Editor) CECOMMON_API UInt32 GetClientScreenWidth() const
        {
            return mClientScreenWidth;
        }

        CEMeta(Editor) CECOMMON_API void SetClientScreenWidth(UInt32 client_screen_width)
        {
             mClientScreenWidth = client_screen_width;
        }

        CEMeta(Editor) CECOMMON_API UInt32 GetClientScreenHeight() const
        {
            return mClientScreenHeight;
        }
        CECOMMON_API void InitDisplayModeList();

        CECOMMON_API CEFunction(ScriptCallable)
        std::string GetDisplaySettings() const;

        CECOMMON_API CEFunction(ScriptCallable)
        void SetDisplaySettings(unsigned resolutionIndex, unsigned mode);

        CEMeta(Editor) CECOMMON_API void SetClientScreenHeight(UInt32 client_screen_height)
        {
            mClientScreenHeight = client_screen_height;
        }

        CEMeta(Editor) CECOMMON_API float GetScreenScale() const
        {
            return mScreenScale;
        }

        CEMeta(Editor) CECOMMON_API void SetScreenScale(float client_screen_scale)
        {
            mScreenScale = client_screen_scale;
        }

        CEMeta(Editor) CECOMMON_API auto GetFullScreen()
        {
            return mMonitorSetting.DisplayMode;
        }

        CEMeta(Editor) CECOMMON_API auto GetMonitorIndex() const
        {
            return mMonitorSetting.MonitorIndex;
        }

        CEMeta(Editor) CECOMMON_API void SetDpiAware(bool dpiAware)
        {
            mDpiAware = dpiAware;
        }

        CEMeta(Editor) CECOMMON_API bool GetDpiAware() const
        {
            return mDpiAware;
        }

        CEMeta(Editor) void SetFoliageLoDBias(SInt32 foliageLoDBias)
        {
            mFoliageLoDBias = std::min<SInt32>(foliageLoDBias, std::numeric_limits<UInt8>::max());
        }

        CEMeta(Editor) SInt32 GetFoliageLoDBias() const
        {
            return mFoliageLoDBias;
        }

        CEMeta(Editor) void SetFoliageInstanceLoadFactor(float loadFactor)
        {
            mFoliageInstanceLoadFactor = std::max(0.f, std::min(1.f, loadFactor));
        }

        CEMeta(Editor) float GetFoliageInstanceLoadFactor() const
        {
            return mFoliageInstanceLoadFactor;
        }

        CEMeta(Editor) void SetTerrainLoDPixelError(float pixelError)
        {
            mTerrainLoDPixelError = std::max(0.f, pixelError);
        }

        CEMeta(Editor) float GetTerrainLoDPixelError() const
        {
            return mTerrainLoDPixelError;
        }

        CEMeta(Editor) void SetTerrainStreamingDistance(float streamingDistance)
        {
            mTerrainStreamingDistance = std::max(0.f, streamingDistance);
        }

        CEMeta(Editor) float GetTerrainStreamingDistance() const
        {
            return mTerrainStreamingDistance;
        }

        CEMeta(Editor) void SetDisableTerrainCulling(bool value)
        {
            mDisableTerrainCulling = value;
        }

        CEMeta(Editor) bool GetDisableTerrainCulling() const
        {
            return mDisableTerrainCulling;
        }

        CEMeta(Editor) CECOMMON_API bool GetEnableTangents() const
        {
            return mEnableQTangents;
        }

        CEMeta(Editor) CECOMMON_API void SetEnableTangents(bool enable)
        {
            mEnableQTangents = enable;
        }

        CEMeta(Editor) CECOMMON_API UInt16 GetFrustumCullingBlockSize()
        {
            return mFrustumCullingBlockSize;
        }

        CEMeta(Editor) CECOMMON_API float GetEditorIconScale()
        {
            return mEditorIconScale;
        }

        CEMeta(Editor) CECOMMON_API std::string GetQTangentsMarco() const
        {
            return QTANGENTMACRO;
        }

        UInt32 GetClientScreenTop() { return mClientSceenTop; }
        
        UInt32 GetClientScreenLeft() { return mClientScreenLeft; }
        
        void SetClientScreenTop(UInt32 inTop) { mClientSceenTop = inTop; }
        
        void SetClientScreenLeft(UInt32 inLeft) { mClientScreenLeft = inLeft; }

        CECOMMON_API std::optional<SerializeNode> LoadConfigFile(const std::string& fileName);

        CECOMMON_API void SaveConfigFile(SerializeNode& node, const std::string& fileName);

        CECOMMON_API std::optional<SerializeNode> GetSettingOption(const std::string& inName);

        CECOMMON_API MPCDI GetMPCDI() const { return mMPCDI; }
        CECOMMON_API void EnableMPCDI(bool enabled) { mMPCDI.enabled = enabled; }
        void SetMPCDIScreenEffectOpacity(float _opacity)
        {
            mMPCDI.effectOpacity = _opacity;
        }

        std::string GetQualificationResourcePath() const { return mQualificationResourcePath; }

        bool GetBlackScreenSetting() const { return mBlackScreenSetting; }

        void SetBlackScreenSetting(bool enabled) { mBlackScreenSetting = enabled; }

        const DecalSetting& GetDecalSetting() const
        {
            return mDecalSetting;
        }

        CEMeta(Editor) CECOMMON_API LayerSetting& GetLayerSetting()
        {
            return mLayerSetting;
        }

        CEMeta(Editor) CECOMMON_API bool GetShowRegenerateFxMsgBoxBeforeOpenProject() const
        {
            return mShowRegenerateFxMsgBoxBeforeOpenProject;
        }
        CECOMMON_API SettingsManager(IFileSystemInfrastructure * fs);
    private:
        void InitPlatFormType();

        void LoadRenderPipelineSetting();

        void LoadDecalSetting();

        void LoadAndApplyScalabilityConfig();

    private:


        IFileSystemInfrastructure* mFileSystem{ nullptr };
        const std::string mEngineConfigFileName;
        const std::string mProjectConfigFileName;
        const std::string mLayerConfigFileName;
        HashString mProjectName{ "CrossEngine" };
        std::string mClientName{""};
        // QualitySettings - Rendering
        bool mIsAsyncLoading = false;
        bool mAsyncLoadReCreateDependceFile = false;
        std::string mLaunchScene;
        AppStartUpType mAppStartUpType{ AppStartUpTypeStandAlone };
        bool mUseSeparateRenderingThread{ true };
        bool mMessageKeep {false};
        // for FFS
        bool mUseSeperatePresentThread = false;
        bool mSettingSuccessfullyLoaded{ false };
        bool mRenderdoc{ false };
        bool mEnableDLSS{ false };
        bool mEnableDLSSG{false};
        bool mFFXFrameInterpolation{false};
        bool mShowDLSSDebugInfo{false};
        SInt32 mMaxTickRates{ -1 };
        SInt32 mNumTaskThreads{ -1 };
        bool mUseMultithreadedRendering{ true };
        bool mUseAsyncLoading{ true };

        UInt32 mRGPThreadingMultiplexingFactor{ 2U };

        bool mUseLogFile{ false };
        std::string mLogFilePath = "";

        bool mEnableFoliagePicking{ false };

        SInt32 mFoliageLoDBias{ 0 };
        // will not store normal and tangents into assets when import new assets.
        // for old assets, will QTangents on the fly
        // will not upload normal and tangents into gpu
        // require all material used to support QTangents.
        bool mEnableQTangents{ false };
        const std::string QTANGENTMACRO = "QTANGENT";

        float mFoliageInstanceLoadFactor{ 1.f };

        float mTerrainLoDPixelError{ 1.f };

        float mTerrainStreamingDistance{ std::numeric_limits<float>::infinity() };

        bool mDisableTerrainCulling{ false };

#if CROSSENGINE_WIN
        bool mUseGPUSkin{ false };
#else
        bool mUseGPUSkin{ false };
#endif

        // default device type
        NGIPlatform mRenderMode{};
        std::vector<std::string> mEnginePreloadResources;
        bool mEnableCulling{ true };
        bool mEnableCullingVisualization{ false };
        std::optional<cross::SerializeNode> HasSettingOption(const std::string& inName);
        std::optional<SerializeNode> mEngineConfig;
        std::optional<SerializeNode> mProjectConfig;
        std::optional<SerializeNode> mLayerConfig;

        gbf::reflection::UserObject mRenderPipelineSettingHolder;
        RenderPipelineSetting* mRenderPipelineSetting;

        UInt32 mClientScreenWidth  = 1280;
        UInt32 mClientScreenHeight = 720;
        UInt32 mClientSceenTop     = 0;
        UInt32 mClientScreenLeft   = 0;
        float  mScreenScale = 1.0f;
        MonitorSetting mMonitorSetting;
        bool mDpiAware = false;
        DecalSetting mDecalSetting;
        LayerSetting mLayerSetting;
        UInt16 mFrustumCullingBlockSize;
        float mEditorIconScale = 1.0f;

        void InitFileSystem();

        PlatformType mPlatform;

        bool mIsThumbnailProcess = false;

        MPCDI mMPCDI;
        std::string mQualificationResourcePath = "";

        bool mBlackScreenSetting = false;

        bool mShowRegenerateFxMsgBoxBeforeOpenProject = false;

        friend class CrossEngine;
    };

    template<typename RPSettingtype>
    void cross::SettingsManager::RegisterRPSettingVar(RPSettingtype* setting)
    {
        auto metaclass = gbf::reflection::query_meta_class<RPSettingtype>();
        if (metaclass)
        {
            gbf::reflection::UserObject uo = gbf::reflection::make_user_object(setting, gbf::reflection::remote_storage_policy{});

            for (auto& prop : uo.GetClass().GetProperties())
            {
                auto clasname = uo.GetClass().name().substr(7);
                if (prop->kind() == gbf::reflection::ValueKind::kBoolean)
                {
                    InGameTerminalManager::DeclareTerminalVar(clasname.append(".").append(prop->name()).c_str(), [propname = prop->name(), setting = setting](TerminalVar var) {
                        gbf::reflection::UserObject uo = gbf::reflection::make_user_object(setting, gbf::reflection::remote_storage_policy{});
                        auto v = std::get<float>(var);
                        uo.SetField(propname, static_cast<bool>(v));
                        EngineGlobal::GetSettingMgr()->NotifyRenderPipelineSettingChanged();
                    });
                }
                else if (prop->kind() == gbf::reflection::ValueKind::kInteger)
                {
                    InGameTerminalManager::DeclareTerminalVar(clasname.append(".").append(prop->name()).c_str(), [propname = prop->name(), setting = setting](TerminalVar var) {
                        gbf::reflection::UserObject uo = gbf::reflection::make_user_object(setting, gbf::reflection::remote_storage_policy{});
                        auto v = std::get<float>(var);
                        uo.SetField(propname, static_cast<UInt32>(v));
                        EngineGlobal::GetSettingMgr()->NotifyRenderPipelineSettingChanged();
                    });
                }
                else if (prop->kind() == gbf::reflection::ValueKind::kReal)
                {
                    InGameTerminalManager::DeclareTerminalVar(clasname.append(".").append(prop->name()).c_str(), [propname = prop->name(), setting = setting](TerminalVar var) {
                        gbf::reflection::UserObject uo = gbf::reflection::make_user_object(setting, gbf::reflection::remote_storage_policy{});
                        auto v = std::get<float>(var);
                        uo.SetField(propname, v);
                        EngineGlobal::GetSettingMgr()->NotifyRenderPipelineSettingChanged();
                    });
                }
            }
        }
    }
}   // namespace cross
