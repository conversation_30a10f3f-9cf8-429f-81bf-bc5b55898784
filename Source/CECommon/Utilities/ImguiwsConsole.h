#pragma once
#include "imgui-ws/imgui-ws.h"
#include "IMGUINodeEditor/TextEditor.h"
#include "IMGUINodeEditor/imgui.h"

#include "CECommon/Common/GlobalSystemBase.h"
#include "CECommon/Utilities/ImguiwsCommon.h"
#include "CECommon/PostProcess/PostProcessToneMappingSettingProxy.h"
#include "RenderEngine/RenderPipeline/Effects/CombinedLUTTonemapper.h"
#include "memory/allocator/range_allocator.hpp"
namespace cross {

struct PopUpState
{
    bool mIsPopUpOpen;
    int mActiveIdx;
    int mClickedIdx;
    bool mSelectionChanged;
    std::vector<std::string> mEntries;
};
class CECOMMON_API ImguiModule
{
public:
    ImguiModule(const char* name);
    virtual ~ImguiModule()
    {
    }
    HashString& GetModuleName();
    virtual void UpdateImgui(){};
    void SetImguiContext(ImGuiContext* context)
    {
        mGuiContext = context;
    }

protected:
    ImGuiContext* mGuiContext;

private:
    HashString mName;
};
class CECOMMON_API ImguiwsConsole : public GlobalGameSystemBase
{
public:
    static constexpr int sConsoleGap = 10;
    CEFunction(Reflect)
    static const GlobalSystemDesc& GetDesc();
    CEFunction(Reflect)
    static ImguiwsConsole* CreateInstance();

    virtual void Release() override { delete this; };

    virtual const GlobalSystemDesc& GetSystemDesc() const override { return GetDesc(); }

    virtual void NotifyShutdownEngine() override;

    virtual void OnUpdate(FrameParam* frameParam) override;

    virtual void OnPostUpdate(FrameParam* fp) override;

    virtual void OnBeginFrame(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    virtual void NotifyAddRenderSystemToRenderEngine() override { mIsRenderObjectOwner = false; }

    void AddModuleToConsole(ImguiModule* consolemodule);
    void RemoveModuleFromConsole(const HashString& moduleName);
    auto& GetImguiwsInst() { return mImguiws; }
    auto& GetCurrentScript() { return mCurrentScript; }
    
    void AddLog(const std::string& inLog);

    FORCE_INLINE void ClearLog()
    {
        mLogContents.clear();
        mLogCursor.store(0, std::memory_order_relaxed);
    }

    void SetToneMappingSetting(const PostProcessToneMappingSettingProxy& inSetting)
    {
        mTonemapSetting = inSetting;
        mToneMappingSettingLoaded = !mToneMappingSettingLoaded;
    }

    const PostProcessToneMappingSettingProxy& GetToneMappingSetting() const
    {
        return mTonemapSetting;
    }

    void SetQueryToneMappingSetting(bool value)
    {
        mQueryToneMappingSetting = value;
    }

    bool GetQueryToneMappingSetting() const
    {
        return mQueryToneMappingSetting;
    }

    bool GetToneMappingSettingModified() const
    {
        return mToneMappingSettingModified;
    }

    static void SetInputFromActiveIndex(ImGuiInputTextCallbackData* data, int entryIndex, const std::vector<std::string>& entries);

    bool ParseCommandAndChangeCVar(const char* inputbuffer); 

    void RenderContent(ImGuiContext* context);

protected:
    virtual GlobalRenderSystemBase* GetRenderSystem() override {return nullptr;}

    void RenderCmdConsoleEditor();
    void RenderLogConsoleEditor();
    void RenderScriptConsoleEditor();
    void RenderPostProcessToneMappingEditor();

private:
    bool LoadToneMappingDataFromEngine();

private:
    ImguiwsConsole();
    ~ImguiwsConsole();
    ImguiwsConsole(ImguiwsConsole const&) = delete;
    ImguiwsConsole& operator=(ImguiwsConsole const&) = delete;
    void DrawPopUpMenu(PopUpState& state, Float2 popuppos, Float2 popupsize, bool& isfocused);
    ImGuiWS mImguiws;
    State mState;
    ImGuiContext* mGuiContext;
    PopUpState mPopState;
    bool mIspopupfocused = false;
    
    TextEditor mLogConsoleEditor;
    TextEditor mTextEditor;

    std::string mCurrentScript = "";
    gbf::allocator::RangeAllocatorResource mResource;
    std::pmr::vector<std::string> mLogContents;
    std::atomic_int mLogCursor;
    std::unordered_map<HashString, ImguiModule*> mRegistModules;
    VSync mVSync;
    bool mIsRenderObjectOwner{false};
    float mFpslimit = 30.0f;

    bool mToneMappingSettingLoaded = false;
    bool mQueryToneMappingSetting = false;
    bool mToneMappingSettingModified = false;

    PostProcessToneMappingSettingProxy mTonemapSetting;
};
}   // namespace cross