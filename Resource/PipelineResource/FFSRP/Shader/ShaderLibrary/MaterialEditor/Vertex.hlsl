#ifndef VERTEX_HLSL
#define VERTEX_HLSL

StructuredBuffer<PrimitiveSceneData> ce_PerPrimitive : register(space2);
StructuredBuffer<ObjectSceneData> ce_PerObject : register(space2);

#if !defined(ENABLE_VSM) && !defined(USED_WITH_LOCAL_SPACE_PARTICLE) && !defined(USED_WITH_GLOBAL_SPACE_PARTICLE) && !defined(USED_WITH_NPARTICLE_SPRITE) && !defined(USED_WITH_NPARTICLE_MESH) && !defined(USED_WITH_TERRAIN) && WRITES_VELOCITY_TO_GBUFFER
#include "VertexMotionVector.hlsl"
#endif

#if defined(USED_WITH_LOCAL_SPACE_PARTICLE) || defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_NPARTICLE_SPRITE) || defined(USED_WITH_NPARTICLE_MESH)
#include "../../Features/VFX/ParticleSystemFactory_MaterialEditor.hlsl"
#define INSTANCING 1
#endif

#include "../../Features/VFX/ParticleDataGetters_MaterialEditor.hlsl"

cbuffer InstancedStaticMeshVSParams : register(space2)
{
	float4 instancingFadeOutParams;
};

#ifdef ENABLE_VSM
float4 GetClipDistance(VirtualShadowMapViewData viewData, float4 positionNDC, PageInfo pageInfo)
{
	// for orthographic shadow camera
	float2 uv = positionNDC.xy * float2(0.5, -0.5) + 0.5;
	float2 pixels = uv * (uint(VSM_VIRTUAL_MAX_RESOLUTION_XY) >> viewData.levelIndex);
	
	uint4 pageRect = _PageRectBounds[viewData.virtualShadowMapId * VSM_MAX_MIP_LEVELS + viewData.levelIndex];

	float2 minClip = pixels - (pageRect.xy + 0) * VSM_PAGE_SIZE;
	float2 maxClip = -pixels + (pageRect.zw + 1) * VSM_PAGE_SIZE;

	return float4(minClip, maxClip);
}
#endif

float3 NormalizeSafe(float3 v)
{
	float len2 = max(M_FloatMin, dot(v, v));
	return v * rsqrt(len2);
}

#ifdef USED_WITH_TERRAIN
#include "VertexTerrain.hlsl"
VSOutput VSInputToVSOutput(VSInput input)
{
	VSOutput output = (VSOutput)0;
	output = TerrainVSInputToVSOutput(input);
	return output;
}
#else
VSOutput VSInputToVSOutput(VSInput input)
{
	VSOutput output = (VSOutput)0;

	// Get ObjectSceneData + PrimitiveSceneData
	uint instanceIndex = input.instanceIDOffset + input.instanceID;
	uint objectIndex = _ObjectIndexBuffer[instanceIndex];
	ObjectSceneData objectData = ce_PerObject[objectIndex];
	PrimitiveSceneData primitiveData = ce_PerPrimitive[objectData.ce_PrimitiveIndex];

	// Get VirtualShadowMapViewData (in VSM pass)
#ifdef ENABLE_VSM
	uint packedPageInfo = _PageInfoBuffer[instanceIndex];
	PageInfo pageInfo = UnpackPageInfo(packedPageInfo);
	VirtualShadowMapViewData viewData = _VirtualShadowMapViewDatas[pageInfo.viewId];
#endif

	// Set InstanceID
	output.instanceID = objectIndex;

	// Set OnePassLocalLightShadowViewData (in shadow_all pass)
#ifdef USE_MULTI_VIEWPORT
	uint lightViewIndex = _LightViewIndexBuffer[instanceIndex];
	LocalLightViewData viewData = _LocalLightViewDatas[lightViewIndex];
	output.ViewportArrayIndex = viewData.viewportIndex;
    output.RenderTargetArrayIndex = viewData.arrayIndex;
	output.lightViewIndex = lightViewIndex;
#endif

#if defined(USED_WITH_LOCAL_SPACE_PARTICLE) || defined(USED_WITH_GLOBAL_SPACE_PARTICLE)

#ifdef ENABLE_SPRITE_PARTICLE
	ProcessSpriteParticle(objectData, primitiveData, input, objectIndex);
#endif

#ifdef ENABLE_MESH_PARTICLE
	ProcessMeshParticle(objectData, primitiveData, input, objectIndex);
#endif

#endif

#if defined(USED_WITH_NPARTICLE_SPRITE)
	ProcessSpriteParticle(objectData, primitiveData, input, objectIndex);
#elif defined(USED_WITH_NPARTICLE_MESH)
	ProcessMeshParticle(objectData, primitiveData, input, objectIndex);
#endif
	// Set PositionWS
#if defined(USED_WITH_LOCAL_SPACE_PARTICLE) || defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_NPARTICLE_SPRITE) || defined(USED_WITH_NPARTICLE_MESH)
	float4 positionWS = float4(input.position.xyz, 1.0);
#else
    float4 positionWS = mul(objectData.ce_World, float4(input.position.xyz, 1.0));
#endif
	float3 tilePosition;
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE)
	tilePosition = objectData.ce_TilePosition;
#else
	tilePosition = primitiveData.ce_TilePosition;
#endif

#ifdef ENABLE_VSM
    positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, tilePosition, viewData.tilePosition);
#elif defined(USE_MULTI_VIEWPORT)
	positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, tilePosition, viewData.lightTileAndRange.xyz);
#else
    positionWS.xyz = GetLargeCoordinateReltvPosition(positionWS.xyz, tilePosition, ce_CameraTilePosition);
#endif

	// Set Normal
#ifdef VERTEX_NEED_NORMAL
	#if defined(USED_WITH_LOCAL_SPACE_PARTICLE) || defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_NPARTICLE_SPRITE) || defined(USED_WITH_NPARTICLE_MESH)
		output.normalWS = float4(NormalizeSafe(input.normal), 0);
	#else
		output.normalWS = NormalizeSafe(mul(float4(NormalizeSafe(input.normal), 0), objectData.ce_InvWorld).xyz);
	#endif
#endif

	// Set Tangent
#ifdef VERTEX_NEED_TANGENT
	#if defined(USED_WITH_LOCAL_SPACE_PARTICLE) || defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_NPARTICLE_SPRITE) || defined(USED_WITH_NPARTICLE_MESH)
		output.tangentWS.xyz = NormalizeSafe(input.tangent.xyz);
		output.tangentWS.w = 1;
	#else
		output.tangentWS.xyz = NormalizeSafe(mul(objectData.ce_World, float4(NormalizeSafe(input.tangent.xyz), 0)).xyz);
		output.tangentWS.w = input.tangent.w;
	#endif
#endif

	// Set VertexColor
#ifdef VERTEX_NEED_VERTEX_COLOR
	output.vertexColor = input.vertexColor;
#endif

	// Set UV
#if NUM_MATERIAL_TEXCOORDS > 0
	output.uvs[0].xy = input.uv;
#endif

#if NUM_MATERIAL_TEXCOORDS > 1
	output.uvs[0].zw = input.uv1;

#endif

#if NUM_MATERIAL_TEXCOORDS > 2
	output.uvs[1].xy = input.uv2;
#endif



	// Generate CustomInterpolators
	GenerateCustomInterpolators(primitiveData, objectData, positionWS, output, input);

	// Do WorldPositionOffset
	float3 reservedPositionWS = positionWS;
	positionWS = float4(GetWorldPositionOffset(primitiveData, objectData, reservedPositionWS, output, input), 1.0);
#ifdef ENABLE_VSM
	float4 reservedPositionNDC = mul(viewData.worldToShadowMatrix, float4(reservedPositionWS, 1));
#elif defined(USE_MULTI_VIEWPORT)
	float4 reservedPositionNDC = mul(viewData.lightViewProjMatrix, float4(reservedPositionWS, 1));
#else
	float4 reservedPositionNDC = mul(ce_Projection, mul(ce_View, float4(reservedPositionWS, 1)));
#endif
	// ComputeMotionVector
	#if !defined(ENABLE_VSM) && !defined(USED_WITH_LOCAL_SPACE_PARTICLE) && !defined(USED_WITH_GLOBAL_SPACE_PARTICLE) && !defined(USED_WITH_NPARTICLE_SPRITE) && !defined(USED_WITH_NPARTICLE_MESH) && WRITES_VELOCITY_TO_GBUFFER
		ComputeMotionVector(primitiveData, objectData, reservedPositionNDC, ce_PrevCameraTilePosition, positionWS, input, output);
	#endif

	// Set PositionNDC
#ifdef ENABLE_VSM
	output.positionNDC = mul(viewData.worldToShadowMatrix, positionWS);
	output.positionWS = positionWS;
#elif defined(USE_MULTI_VIEWPORT)
	output.positionNDC = mul(viewData.lightViewProjMatrix, positionWS);
	output.positionWS = positionWS;
#else
	output.positionNDC = mul(ce_Projection, mul(ce_View, positionWS));
#endif

	// Set ClipDistance (only used in VSM pass)
#ifdef ENABLE_VSM
	output.packedPageInfo = packedPageInfo;
	output.clipDistance = GetClipDistance(viewData, output.positionNDC, pageInfo);
#endif

#if defined(VERTEX_NEED_PER_INSTANCE_PARAMS)
	output.perInstanceParams.x = 1.0 - saturate((length(positionWS.xyz - ce_CameraPos) - instancingFadeOutParams.x) * instancingFadeOutParams.y);
#endif

	return output;
}
#endif //if terrain else

PSInput VSOutputToPSInput(VSOutput input)
{
	PSInput output = (PSInput)0;
#if defined(ENABLE_VSM) || defined(USE_MULTI_VIEWPORT)
	output.positionWS = input.positionWS;
#else
	float2 screenUV = input.positionNDC.xy * ce_ScreenParams.zw;
    screenUV = float2(screenUV.x, 1 - screenUV.y) * 2 - 1.0;
	float4 posNDC = float4(float3(screenUV, input.positionNDC.z), 1.0);
	float4 posWS = mul(ce_InvViewProjMatrix, posNDC);
	output.positionWS = posWS.xyz / posWS.w;
#endif

	output.positionNDC = input.positionNDC;
#if WRITES_VELOCITY_TO_GBUFFER
	output.prePositionNDC = input.prePositionNDC;
    output.nowPositionNDC = input.nowPositionNDC;
#endif

	output.instanceID = input.instanceID;

#ifdef VERTEX_NEED_NORMAL
	output.normalWS = input.normalWS;
#endif

#ifdef VERTEX_NEED_TANGENT
	output.tangentWS = input.tangentWS.xyz;
#endif

#if defined(VERTEX_NEED_NORMAL) && defined(VERTEX_NEED_TANGENT)
	#ifdef USED_WITH_TERRAIN
		output.binormalWS = input.binormalWS;
	#else
		output.binormalWS = input.tangentWS.w * cross(output.normalWS, output.tangentWS);
	#endif
#endif

#ifdef VERTEX_NEED_VERTEX_COLOR
	output.vertexColor = input.vertexColor;
#endif

#if NUM_MATERIAL_TEXCOORDS > 0
	output.uv = input.uvs[0].xy;
	output.uvs = input.uvs;
#endif

#if NUM_MATERIAL_TEXCOORDS > 1
	output.uv1 = input.uvs[0].zw;
#endif

#if NUM_MATERIAL_TEXCOORDS > 2
	output.uv2 = input.uvs[1].xy;
#endif

	output.screenUV = output.positionNDC.xy * ce_ScreenParams.zw;

#ifdef ENABLE_VSM
	output.packedPageInfo = input.packedPageInfo;
#endif

#if defined(VERTEX_NEED_PER_INSTANCE_PARAMS)
	output.perInstanceParams = input.perInstanceParams;
#endif

	SET_CUSTOM_PS_INPUT
	
	return output;
}

PSInput VSInputToPSInput(VSInput input)
{
	return VSOutputToPSInput(VSInputToVSOutput(input));
}

#endif