#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword QTANGENT
#pragma keyword CE_INSTANCING
#pragma keyword TEXTURE_ARRAY_ENABLE
#pragma keyword PUNCTUAL_LIGHT
#pragma keyword USE_MULTI_VIEWPORT

#define CE_USE_DOUBLE_TRANSFORM
#define SHADOW_PASS
#define ENABLE_VIEW_MODE_VISUALIZE
#define NUM_MATERIAL_TEXCOORDS 2

#include "Common/SceneData.hlsl"
#include "ShaderLibrary/CommonStruct.hlsl"
#include "ShaderLibrary/GlobalModelVariables.hlsl"
#include "Material/Lit/LitUEVariables.hlsl"
#ifdef LOCAL_SHADOW
StructuredBuffer<FoliageEntityData> _FoliageEntityBuffer;
StructuredBuffer<FoliageCompactSceneData> _FoliageObjectSceneDatas;
#endif
StructuredBuffer<ObjectSceneData> ce_PerObject : register(space2);

#ifdef CE_INSTANCING
    #include "ShaderLibrary/Vertex_Instancing.hlsl"
#else
    #include "ShaderLibrary/Vertex.hlsl"
#endif

#include "Material/Material.hlsl"
#include "Lighting/Lighting.hlsl"

#include "Material/Lit/LitDataUE.hlsl"
#include "Material/Lit/Lit.hlsl"
#include "RenderPass/ShaderPassDepthForShadow.hlsl"