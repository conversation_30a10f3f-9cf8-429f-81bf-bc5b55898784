#pragma vertex VSMain
#pragma pixel PSMain

#pragma keyword CE_INSTANCING
#pragma keyword FOLIAGE_VSM

#define SHADOW_PASS
#define ENABLE_VIEW_MODE_VISUALIZE
#define ENABLE_VSM
#define NUM_MATERIAL_TEXCOORDS 2


#include "Common/SceneDataShadow.hlsl"
#include "ShaderLibrary/CommonStruct.hlsl"
#include "LitCommonStruct.hlsl"

StructuredBuffer<uint> _PageTable;
StructuredBuffer<uint4> _PageRectBounds;
RWTexture2D<uint> _PhysicalPagePool;
StructuredBuffer<FoliageEntityData> _FoliageEntityBuffer;
StructuredBuffer<FoliageCompactSceneData> _FoliageObjectSceneDatas;
StructuredBuffer<ObjectSceneData> ce_PerObject : register(space2);

#include "Lighting/Shadow/VirtualShadowMap/VirtualShadowMapViewCommon.hlsl"
#include "Lighting/Shadow/VirtualShadowMap/VirtualShadowMapPageAccessCommon.hlsl"

#include "ShaderLibrary/GlobalModelVariables.hlsl"
#include "Material/Lit/LitUEVariables.hlsl"

#include "ShaderLibrary/Vertex_Instancing.hlsl"

#include "Material/Material.hlsl"
#include "Lighting/Lighting.hlsl"

#include "Material/Lit/LitDataUE.hlsl"
#include "Material/Lit/Lit.hlsl"
#include "RenderPass/ShaderPassDepthForShadow.hlsl"