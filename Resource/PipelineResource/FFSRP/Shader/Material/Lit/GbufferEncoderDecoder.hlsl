#ifndef GBUFFER_ENCODER_DECODER_HLSL
#define GBUFFER_ENCODER_DECODER_HLSL

#include "./LitCommonStruct.hlsl"
#include "../NormalBuffer.hlsl"
#include "../../ShaderLibrary/MotionVector.hlsl"
#include "../../Debug/ViewModeVisualizeDef.hlsl"


//-----------------------------------------------------------------------------
// conversion function for deferred
//-----------------------------------------------------------------------------

// GBuffer layout.

// GBuffer0      RGBA8   UNorm
// GBuffer1      RGBA16  UNorm
// GBuffer2      RGBA8	 UNorm
// GBuffer3      RGBA16  UNorm
// GBuffer4      RGBA16	 SFloat
// GBuffer5		 RG8	 UNorm
// GBuffer6		 RGBA16	 UNorm

// FeatureName   Standard
// GBuffer0      BaseColor.rgb, metallic
// GBuffer1      normal.xy (1212),  roughness, specular
// GBuffer2      -- -- -- typeID
// GBuffer3      motionVector.rg, b:opacity a:ambient occlusion
// GBuffer4      indirectLighting.rgb, 1.0f
// GBuffer5		 temporalReactive.xy (when only temporal reactive) or objectCullingGUID.xy (when only smart surfel)
// GBuffer6		 objectCullingGUID.xy (when both temporal reactive and smart surfel are enabled)

// FeatureName   TwosidedFoliage
// GBuffer0      BaseColor.rgb,  metallic
// GBuffer1      normal.xy (1212),  roughness, specular
// GBuffer2      subsurfaceColor.rgb, typeID
// GBuffer3      motionVector.rg, b:opacity a:ambient occlusion
// GBuffer4      indirectLighting.rgb, 
// GBuffer5		 temporalReactive.xy (when only temporal reactive) or objectCullingGUID.xy (when only smart surfel)
// GBuffer6		 objectCullingGUID.xy (when both temporal reactive and smart surfel are enabled)

#ifndef DEFERRED_SHADING
void EncodeIntoGBufferDecal(SurfaceData surfaceData, BuiltinData builtinData, out float4 outGBuffer0, out float4 outGBuffer1, out float4 outGBuffer2, out float4 outGBuffer3, out float4 outGBuffer4, out float4 outGBuffer5, out float4 outGBuffer6)
{
    if(VIEW_MODE == ViewMode_LightingOnly)
    {
        surfaceData.baseColor = float3(LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR);
        surfaceData.metallic = 0;
        surfaceData.specular = 0;
        surfaceData.roughness = 0.5f;
		surfaceData.emissiveColor = 0.xxx;
		surfaceData.subsurfaceColor = 0.xxx;
        surfaceData.normalWS = surfaceData.geomNormalWS;
    }
     if(VIEW_MODE == ViewMode_DetailLighting)
    {
        surfaceData.baseColor = float3(LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR);
        surfaceData.metallic = 0;
        surfaceData.specular = 0;
        surfaceData.roughness = 0.5f;
		surfaceData.emissiveColor = 0.xxx;
		surfaceData.subsurfaceColor = 0.xxx;
    }
	// GBuffer1 : Normal + Roughness
    float3 normalData;
    normalData.xyz = surfaceData.normalWS;
    EncodeIntoNormalBuffer(normalData, surfaceData.roughness, surfaceData.specular, outGBuffer1);

    // GBuffer0 + GBuffer2 + GBuffer3
    if (MATERIAL_TYPE == MaterialType_Standard)
    {
        outGBuffer0 = float4(surfaceData.baseColor, 1);
        outGBuffer2 = float4(surfaceData.roughness, surfaceData.metallic, surfaceData.specular, PackByte(surfaceData.materialType));
        outGBuffer3 = float4(0, 0, 0, builtinData.bakeShadow);
    }
    else if (MATERIAL_TYPE == MaterialType_Unlit)
    {
        outGBuffer2 = float4(0, 0, 0, PackByte(MaterialType_Unlit));
    }
    else if (MATERIAL_TYPE == MaterialType_TwosidedFoliage
		  || MATERIAL_TYPE == MaterialType_ImposterFoliage
		  || MATERIAL_TYPE == MaterialType_Subsurface)
    {
        outGBuffer0 = float4(surfaceData.baseColor, 1);
        outGBuffer2 = float4(surfaceData.roughness, surfaceData.metallic, surfaceData.specular, PackByte(surfaceData.materialType));
        outGBuffer3 = float4(surfaceData.subsurfaceColor, builtinData.bakeShadow);
    }

	// GBuffer4
    outGBuffer4 = float4(builtinData.motionVector, surfaceData.opacity, surfaceData.ambientOcclusion);

	// GBuffer5
    outGBuffer5 = float4(builtinData.indirectLighting + surfaceData.emissiveColor, builtinData.coverage);

	// GBuffer6
    outGBuffer6.xy = surfaceData.temporalReactive;
}

void EncodeIntoGBuffer(SurfaceData surfaceData, BuiltinData builtinData, out float4 outGBuffer0, out float4 outGBuffer1, out float4 outGBuffer2, out float4 outGBuffer3, out float4 outGBuffer4, out float4 outGBuffer5)
{
    if (VIEW_MODE == ViewMode_LightingOnly)
    {
        surfaceData.baseColor = float3(LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR);
        surfaceData.metallic = 0;
        surfaceData.specular = 0;
        surfaceData.roughness = 0.5f;
		surfaceData.emissiveColor = 0.xxx;
		surfaceData.subsurfaceColor = 0.xxx;
        surfaceData.normalWS = surfaceData.geomNormalWS;
    }
    if (VIEW_MODE == ViewMode_DetailLighting)
    {
        surfaceData.baseColor = float3(LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR);
        surfaceData.metallic = 0;
        surfaceData.specular = 0;
        surfaceData.roughness = 0.5f;
		surfaceData.emissiveColor = 0.xxx;
		surfaceData.subsurfaceColor = 0.xxx;
    }
	if (VIEW_MODE == ViewMode_DebugColor)
	{
	    surfaceData.baseColor = surfaceData.debugColor;
	}
	// GBuffer1 : Normal + Roughness
	float3 normalData;
	normalData.xyz = surfaceData.normalWS;

    EncodeIntoNormalBuffer(normalData, surfaceData.roughness, surfaceData.specular, outGBuffer1);

	// GBuffer0 + GBuffer2 + GBuffer3
	if (MATERIAL_TYPE == MaterialType_Standard)
	{
        outGBuffer0 = float4(surfaceData.baseColor, surfaceData.metallic);
        outGBuffer2.xyz = float3(0, 0, 0);
    }
	else if (MATERIAL_TYPE == MaterialType_Unlit)
	{
        outGBuffer0 = float4(0, 0, 0, surfaceData.metallic);
    }
	else if (MATERIAL_TYPE == MaterialType_TwosidedFoliage 
		  || MATERIAL_TYPE == MaterialType_ImposterFoliage
		  || MATERIAL_TYPE == MaterialType_Subsurface)
	{
        outGBuffer0 = float4(surfaceData.baseColor, surfaceData.metallic);
        outGBuffer2.xyz = float3(surfaceData.subsurfaceColor);
    }

    outGBuffer2.w = PackByte(surfaceData.materialType);

	// GBuffer3
	outGBuffer3 = float4(builtinData.motionVector, surfaceData.opacity, surfaceData.ambientOcclusion);

	// GBuffer4
    outGBuffer4 = float4(builtinData.indirectLighting + surfaceData.emissiveColor, 1.0f);

	// GBuffer5 - temporalReactive (controlled by C++ side allocation)
	outGBuffer5.xy = surfaceData.temporalReactive;
}

// New overload for 6-parameter version (when both temporal reactive and smart surfel are enabled)
void EncodeIntoGBuffer(SurfaceData surfaceData, BuiltinData builtinData, out float4 outGBuffer0, out float4 outGBuffer1, out float4 outGBuffer2, out float4 outGBuffer3, out float4 outGBuffer4, out float4 outGBuffer5, out float4 outGBuffer6)
{
    if (VIEW_MODE == ViewMode_LightingOnly)
    {
        surfaceData.baseColor = float3(LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR);
        surfaceData.metallic = 0;
        surfaceData.specular = 0;
        surfaceData.roughness = 0.5f;
		surfaceData.emissiveColor = 0.xxx;
		surfaceData.subsurfaceColor = 0.xxx;
        surfaceData.normalWS = surfaceData.geomNormalWS;
    }
    if (VIEW_MODE == ViewMode_DetailLighting)
    {
        surfaceData.baseColor = float3(LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR, LIGHTING_ONLY_DIFFUSE_COLOR);
        surfaceData.metallic = 0;
        surfaceData.specular = 0;
        surfaceData.roughness = 0.5f;
		surfaceData.emissiveColor = 0.xxx;
		surfaceData.subsurfaceColor = 0.xxx;
    }
	if (VIEW_MODE == ViewMode_DebugColor)
	{
	    surfaceData.baseColor = surfaceData.debugColor;
	}
	// GBuffer1 : Normal + Roughness
	float3 normalData;
	normalData.xyz = surfaceData.normalWS;

    EncodeIntoNormalBuffer(normalData, surfaceData.roughness, surfaceData.specular, outGBuffer1);

	// GBuffer0 + GBuffer2 + GBuffer3
	if (MATERIAL_TYPE == MaterialType_Standard)
	{
        outGBuffer0 = float4(surfaceData.baseColor, surfaceData.metallic);
        outGBuffer2.xyz = float3(0, 0, 0);
    }
	else if (MATERIAL_TYPE == MaterialType_Unlit)
	{
        outGBuffer0 = float4(0, 0, 0, surfaceData.metallic);
    }
	else if (MATERIAL_TYPE == MaterialType_TwosidedFoliage 
		  || MATERIAL_TYPE == MaterialType_ImposterFoliage
		  || MATERIAL_TYPE == MaterialType_Subsurface)
	{
        outGBuffer0 = float4(surfaceData.baseColor, surfaceData.metallic);
        outGBuffer2.xyz = float3(surfaceData.subsurfaceColor);
    }

    outGBuffer2.w = PackByte(surfaceData.materialType);

	// GBuffer3
	outGBuffer3 = float4(builtinData.motionVector, surfaceData.opacity, surfaceData.ambientOcclusion);

	// GBuffer4
    outGBuffer4 = float4(builtinData.indirectLighting + surfaceData.emissiveColor, 1.0f);

	// GBuffer5 - temporalReactive (always written when using 6-parameter version)
	outGBuffer5.xy = surfaceData.temporalReactive;

	// GBuffer6 - objCullingGUID (controlled by C++ side allocation)
	outGBuffer6.xy = PackObjCullingGUIDToFloat2(surfaceData.objCullingGUID);
}
#endif

#ifdef DEFERRED_SHADING
void DecodeFromGBuffer(float2 uv, out BSDFData bsdfData, out BuiltinData builtinData
#ifdef USE_IN_COMPUTE_SHADER
	, float2 ScreenSize
#endif	
	)
{
	bsdfData = (BSDFData)0;
	builtinData = (BuiltinData)0;
	SurfaceData surfaceData = (SurfaceData)0;

#ifdef USE_IN_COMPUTE_SHADER
	float2 positionSS = uv * ScreenSize.xy;
	float4 gBuffer0 = _GBuffer0[positionSS];
	float4 gBuffer1 = _GBuffer1[positionSS];
	float4 gBuffer2 = _GBuffer2[positionSS];
	float4 gBuffer3 = _GBuffer3[positionSS];
	float4 gBuffer4 = _GBuffer4[positionSS];
#else
	float4 gBuffer0 = _GBuffer0.Sample(ce_Sampler_Point, uv);
	float4 gBuffer1 = _GBuffer1.Sample(ce_Sampler_Point, uv);
	float4 gBuffer2 = _GBuffer2.Sample(ce_Sampler_Point, uv);
	float4 gBuffer3 = _GBuffer3.Sample(ce_Sampler_Point, uv);
	float4 gBuffer4 = _GBuffer4.Sample(ce_Sampler_Point, uv);
#endif

	surfaceData.baseColor = gBuffer0.xyz;
	surfaceData.roughness = gBuffer1.z;
	surfaceData.metallic = gBuffer0.w;
	surfaceData.specular = gBuffer1.w;

	// GBuffer1 : Normal + Roughness
	if (any(gBuffer1))
	{
		float3 normalData;
		DecodeFromNormalBuffer(gBuffer1, normalData);
		bsdfData.normalWS = normalData;
	}
	else
	{
		bsdfData.normalWS = 0.0.xxx;
	}
	bsdfData.geomNormalWS = bsdfData.normalWS; // No geometric normal in deferred, use normal map
	
	bsdfData.roughness = surfaceData.roughness;
    bsdfData.metallic = surfaceData.metallic;
	
	// GBuffer0 + GBuffer2 + GBuffer3
	uint materialType = UnpackByte(gBuffer2.w);
	bsdfData.materialType = materialType;
    builtinData.bakeShadow = 1.0f;

	float3 diffuseColor = ComputeDiffuseColor(surfaceData.baseColor, surfaceData.metallic);
	float3 fresnel0 =  ComputeFresnel0(surfaceData.baseColor, surfaceData.metallic, surfaceData.specular);

	if (materialType == MaterialType_Standard)
	{
		bsdfData.diffuseColor = diffuseColor;
		bsdfData.fresnel0 = fresnel0;
	}
	else if (materialType == MaterialType_TwosidedFoliage 
		  || materialType == MaterialType_ImposterFoliage
		  || materialType == MaterialType_Subsurface)
	{
		bsdfData.diffuseColor = diffuseColor;
		bsdfData.fresnel0 = fresnel0;
		bsdfData.subsurfaceColor = gBuffer2.xyz;
	}

	// Save the file below to refresh white model mode:
	//  Resource/PipelineResource/FFSRP/Shader/Lighting/StencilDeferredLightingBackPass.shader
    if (VIEW_MODE == ViewMode_Wireframe)
    {
    	bsdfData.diffuseColor = 1.0;
    	bsdfData.subsurfaceColor = 0.0;
    }

	// GBuffer3
	bsdfData.opacity = gBuffer3.z;
	bsdfData.ambientOcclusion = gBuffer3.w;
	if (gBuffer3.x > 0)
	{
		builtinData.motionVector = DecodeVelocityFromTexture(gBuffer3.xy);
	}
	else
	{
		builtinData.motionVector = 0;
	}

    // decode object culling guid from GBuffer6 (if available)
    // Note: This assumes GBuffer6 is available when smart surfel is enabled
    // The C++ side controls whether GBuffer6 is actually allocated
}

void DecodeFromSecondaryGBuffer(float2 uv, out BSDFData bsdfData, out BuiltinData builtinData
#ifdef USE_IN_COMPUTE_SHADER
	, float2 ScreenSize
#endif	
	)
{
	bsdfData = (BSDFData)0;
	builtinData = (BuiltinData)0;
	SurfaceData surfaceData = (SurfaceData)0;

#ifdef USE_IN_COMPUTE_SHADER
	float2 positionSS = uv * ScreenSize.xy;
	float4 gBuffer0 = _SecondaryGBuffer0[positionSS];
	float4 gBuffer1 = _SecondaryGBuffer1[positionSS];
	float4 gBuffer2 = _SecondaryGBuffer2[positionSS];
#else
	float4 gBuffer0 = _SecondaryGBuffer0.Sample(ce_Sampler_Point, uv);
	float4 gBuffer1 = _SecondaryGBuffer1.Sample(ce_Sampler_Point, uv);
	float4 gBuffer2 = _SecondaryGBuffer2.Sample(ce_Sampler_Point, uv);
#endif

	surfaceData.baseColor = gBuffer0.xyz;
	surfaceData.roughness = gBuffer1.z;
	surfaceData.metallic = gBuffer0.w;
	//surfaceData.specular = gBuffer1.w;

	// GBuffer1 : Normal + Roughness
	if (any(gBuffer1))
	{
		float3 normalData;
		DecodeFromNormalBuffer(gBuffer1, normalData);
		bsdfData.normalWS = normalData;
	}
	else
	{
		bsdfData.normalWS = 0.0.xxx;
	}
	bsdfData.geomNormalWS = bsdfData.normalWS; // No geometric normal in deferred, use normal map
	
	bsdfData.roughness = surfaceData.roughness;
    bsdfData.metallic = surfaceData.metallic;
	
	// GBuffer0 + GBuffer2 + GBuffer3
	uint materialType = UnpackByte(gBuffer2.w);
	bsdfData.materialType = materialType;
	builtinData.bakeShadow = 1.0;

	float3 diffuseColor = ComputeDiffuseColor(surfaceData.baseColor, surfaceData.metallic);
	float3 fresnel0 =  ComputeFresnel0(surfaceData.baseColor, surfaceData.metallic, surfaceData.specular);

	if (materialType == MaterialType_Standard)
	{
		bsdfData.diffuseColor = diffuseColor;
		bsdfData.fresnel0 = fresnel0;
	}
}
#endif

#endif