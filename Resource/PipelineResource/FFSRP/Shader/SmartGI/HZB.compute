#pragma compute DownsampleDepth

Texture2D<float> _DepthSrc;
RWTexture2D<float> _DepthDst;
SamplerState ce_Sampler_Clamp;
SamplerState ce_Sampler_Point;

cbuffer _cbCommon
{
    matrix ce_Projection;
    matrix ce_View;

    float4 _DispatchThreadIdToBufferUV;
    float2 _InvSrcSize;
    float2 _InputViewportMaxBound;
}

//Dimension = target Size
[numthreads(8, 8, 1)]
void DownsampleDepth(uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID)
{
    float2 bufferUV = (DTid.xy + 0.5f) * _DispatchThreadIdToBufferUV.xy + _DispatchThreadIdToBufferUV.zw;
    float2 uv = min(bufferUV + float2(-0.25f, -0.25f) * _InvSrcSize, _InputViewportMaxBound - _InvSrcSize);
    float4 minDepth = _DepthSrc.Gather(ce_Sampler_Clamp, uv);

    _DepthDst[DTid.xy] = min(min(minDepth.x, minDepth.y), min(minDepth.z, minDepth.w));
}