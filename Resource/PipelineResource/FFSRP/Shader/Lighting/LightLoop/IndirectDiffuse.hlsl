#ifndef INDIRECT_DIFFUSE_HLSL
#define INDIRECT_DIFFUSE_HLSL

// NOTE(scolu): if ENABLE_SKY_LIGHT_REALTIME_CAPTURE, should transform sample dir to ue's coordinates, since captured cubemap and sky sh is calculated in ue's coordinates
SHADER_CONST(bool, ENABLE_SKY_LIGHT_REALTIME_CAPTURE, false);

struct FSkyLightVisibilityData
{
    float SkyDiffuseLookUpMul;
    float3 SkyDiffuseLookUpAdd;
    float3 SkyDiffuseLookUpNormal;
};

float3 EnvBRDF(Texture2D EnvBRDFLutMap, SamplerState EnvBRDFLutMapSampler, float3  SpecularColor, float  Roughness, float  NoV)
{
	float2 AB = EnvBRDFLutMap.SampleLevel(EnvBRDFLutMapSampler, float2(NoV, 1.0 - Roughness), 0).rg;
	float3 GF = SpecularColor * AB.x + AB.y;
	return GF;
}


FSkyLightVisibilityData GetSkyLightVisibilityData(float3 SkyLightingNormal, const float3 WorldNormal, const float GBufferAO, float ScreenAO, const float3 BentNormal)
{
    float SkyVisibility = 1;
    float DotProductFactor = 1;

    // apply sky shadowing
    SkyVisibility = length(BentNormal);
    float3 NormalizedBentNormal = BentNormal / (max(SkyVisibility, .00001f));

    // Use more bent normal in corners
    float BentNormalWeightFactor = SkyVisibility;

    SkyLightingNormal = lerp(NormalizedBentNormal, WorldNormal, BentNormalWeightFactor);
    DotProductFactor = lerp(dot(NormalizedBentNormal, WorldNormal), 1, BentNormalWeightFactor);

    // saturate curve, not supported yet
    // float ContrastCurve = 1 / (1 + exp(-ContrastAndNormalizeMulAdd.x * (SkyVisibility * 10 - 5)));
    // SkyVisibility = saturate(ContrastCurve * ContrastAndNormalizeMulAdd.y + ContrastAndNormalizeMulAdd.z);

    // disance Ao is not enabled in CE
    //// Apply DFAO controls
    // SkyVisibility = pow(SkyVisibility, OcclusionExponent);
    // SkyVisibility = lerp(SkyVisibility, 1, OcclusionTintAndMinOcclusion.w);

    // only use this mode, since we don't have DFAO
    // Combine with mul, which continues to add SSAO depth even indoors.  SSAO will need to be tweaked to be less strong.
    SkyVisibility = SkyVisibility * min(GBufferAO, ScreenAO);

    // Apply AO to the sky diffuse and account for darkening due to the geometry term
    // apply the Diffuse color to the lighting (including OcclusionTintAndMinOcclusion as it's considered another light, that fixes SubsurfaceProfile being too dark)
    FSkyLightVisibilityData SkyVisData;
    SkyVisData.SkyDiffuseLookUpMul = SkyVisibility * DotProductFactor;
    // SkyVisData.SkyDiffuseLookUpAdd = (1 - SkyVisibility) * OcclusionTintAndMinOcclusion.xyz;
    SkyVisData.SkyDiffuseLookUpNormal = SkyLightingNormal;
    return SkyVisData;
}

half3 IndirectDiffuse_GetSkySHOnly(BSDFData bsdfData, float3 V, float3 Normal, StructuredBuffer<float4> UE4AmbientProbeSH, float3 skyColor,
    float skyIntensity, float screenSpaceAO, float3 BentNormal, float3 diffuseColor, float3 specularColor, bool IsSkyLightRealTimeCapture)
{
    half3 skyNormal = Normal;
    // if (UE4AmbientProbeSH[6].w == 0)
    if (IsSkyLightRealTimeCapture)
    {
        skyNormal = Float3CE2UE(Normal);
    }

    // could be redundant with function ReflectionEnviroment, but still add here for clarity
    float3 N = Normal;
    float3 R = 2 * dot(V, N) * N - V;
    float NoV = max(0.0001, saturate(dot(N, V)));

    float materialAmbientOcclusion = 1.0;
    // note, a lot of them (Skynormal, worldNormal, bentNormal) are not presented/implemented in CE 20220829, so use the same value temporally
    FSkyLightVisibilityData SkyVisData = GetSkyLightVisibilityData(skyNormal, skyNormal, materialAmbientOcclusion, screenSpaceAO, BentNormal);

    const FBxDFEnergyTerms EnergyTerms = ComputeGGXSpecEnergyTerms(bsdfData.roughness, NoV, specularColor);
    float3 DiffuseWeight = ComputeEnergyPreservation(EnergyTerms);

    half3 Lighting = 0;
    // still a lot of material type to add
    if (bsdfData.materialType == MaterialType_TwosidedFoliage)
    {
        float3 SubsurfaceLookup = GetSkySHDiffuse(-skyNormal, UE4AmbientProbeSH) * skyColor * skyIntensity;
        float3 SubsurfaceColor = bsdfData.subsurfaceColor;
        Lighting += SkyVisData.SkyDiffuseLookUpMul * SubsurfaceLookup * SubsurfaceColor;
    }

    float3 DiffuseLookup = GetSkySHDiffuse(skyNormal, UE4AmbientProbeSH) * skyIntensity * skyColor;
    Lighting += (SkyVisData.SkyDiffuseLookUpMul * DiffuseLookup) * diffuseColor * DiffuseWeight;

    return Lighting;
}

float3 GetSkySHLighting_Simple(BSDFData bsdfData, float3 V, float3 L, float3 skyColor, StructuredBuffer<float4> ambientProbeSH)
{
    float NdotL = dot(bsdfData.normalWS, L);
    float3 skyNormal = L;
    if (ambientProbeSH[6].w == 0)
    {
        skyNormal = Float3CE2UE(L);
    }
    return GetSkySHDiffuse(skyNormal, ambientProbeSH) * skyColor * NdotL * bsdfData.diffuseColor;
}

float3 GetSkySHLightColor(float3 L, float3 skyColor, StructuredBuffer<float4> ambientProbeSH)
{
    float3 skyNormal = L;
    if (ambientProbeSH[6].w == 0)
    {
        skyNormal = Float3CE2UE(L);
    }
    return GetSkySHDiffuse(skyNormal, ambientProbeSH) * skyColor;
}

float IrradianceBounce0Rescale(float AO)
{
    return AO * (1 + (1 - AO) / (2 * sqrt(sqrt(1 - AO))));
}

float IrradianceBounce1Rescale(float AO)
{
    float A = 27.576937094210385f;
    float B = 3.3364392003423804f;
    return A * AO * pow(1 - AO, 1.5f) * exp(-B * sqrt(sqrt(AO)));
}

// From "2018 Patapom - Improved Ambient Occlusion"
float3 DistantIlluminationRescale(float3 DiffuseColor, float AO)
{
    float F0 = IrradianceBounce0Rescale(min(AO, .999f));
    float F1 = IrradianceBounce1Rescale(min(AO, .999f));
    float Tau = 1 - F1 / max(1 - F0, 0.00001f);

    return F0 + DiffuseColor / max(1 - DiffuseColor * Tau, 0.00001f) * F1;
}

float ApproximateConeConeIntersection(float ArcLength0, float ArcLength1, float AngleBetweenCones)
{
	float AngleDifference = abs(ArcLength0 - ArcLength1);

	float Intersection = smoothstep(
		0,
		1.0,
		1.0 - saturate((AngleBetweenCones - AngleDifference) / (ArcLength0 + ArcLength1 - AngleDifference)));

	return Intersection;
}

float CalculateSpecularOcclusion(float Roughness, float3 WorldNormal, float AO, float3 V, float3 BentNormalOcclusion)
{
	float ReflectionConeAngle = max(Roughness, .1f) * M_PI;
	float UnoccludedAngle = AO * M_PI;
	float3 ReflectionVector = reflect(-V, WorldNormal);
	float AngleBetween = acosFast(dot(BentNormalOcclusion, ReflectionVector) / max(AO, .001f));
	float SpecularOcclusion = ApproximateConeConeIntersection(ReflectionConeAngle, UnoccludedAngle, AngleBetween);

	// Can't rely on the direction of the bent normal when close to fully occluded, lerp to shadowed
	SpecularOcclusion = lerp(0, SpecularOcclusion, saturate((UnoccludedAngle - .1f) / .2f));
	return SpecularOcclusion;
}

float RemapScreenSpaceAO(float screenSpaceAo)
{
    //screenSpaceAo = saturate((screenSpaceAo - 0.3) * (1/0.7));
    //screenSpaceAo = saturate((screenSpaceAo - 0.372) * (1/0.618));
    //screenSpaceAo = saturate((screenSpaceAo - 0.5) * 2);
    //screenSpaceAo = Pow10(screenSpaceAo);
    return screenSpaceAo;
}

// if (roughness >= MaxRoughnessToTrace): then FadeAlpha = 0, -> RoughReflections;
// else if (roughness < MaxRoughnessToTrace - RoughnessFadeLength): then FadeAlpha = 1, -> RayTracedReflections;
// else: lerp
float3 CombineRoughSpecular(float Roughness, float3 RayTracedReflections, float3 RoughReflections, float3 EnvBrdf, float MaxRoughnessToTrace, float InvRoughnessFadeLength)
{
    //to be uniform param
    //float MaxRoughnessToTrace = 0.5f;
    //float InvRoughnessFadeLength = 2.f;

    float3 Lighting;

    // if (GBuffer.ShadingModelID == SHADINGMODELID_CLEAR_COAT)
    // {
    // 	Lighting = ClearCoatLayerCombine(GBuffer, NoV, RayTracedReflections, RoughReflections, SpecularColor);
    // }
    // else
    {
        float FadeAlpha = saturate((MaxRoughnessToTrace - Roughness) * InvRoughnessFadeLength);

        Lighting = RoughReflections * (1 - FadeAlpha);

        // Must branch as RayTracedReflections can be uninitialized where not needed and contain NaN
        if (FadeAlpha > 0.0f)
        {
            Lighting += RayTracedReflections * FadeAlpha;
        }

        Lighting *= EnvBrdf;
    }
    return Lighting;
}

half3 GetSkySHOnly(SurfaceData surfaceData, float3 V, float3 Normal, StructuredBuffer<float4> UE4AmbientProbeSH, float3 skyColor, float skyIntensity, float screenSpaceAO, float3 BentNormal,
    float3 diffuseColor, float3 specularColor, bool IsSkyLightRealTimeCapture)
{
    half3 skyNormal = Normal;
    // if (UE4AmbientProbeSH[6].w == 0)
    if (IsSkyLightRealTimeCapture)
    {
        skyNormal = Float3CE2UE(Normal);
    }

    // could be redundant with function ReflectionEnviroment, but still add here for clarity
    float3 N = Normal;
    float3 R = 2 * dot(V, N) * N - V;
    float NoV = max(0.0001, saturate(dot(N, V)));

    // note, a lot of them (Skynormal, worldNormal, bentNorma) are not presented/implemented in CE 20220829, so use the same value temporally
    FSkyLightVisibilityData SkyVisData = GetSkyLightVisibilityData(skyNormal, skyNormal, surfaceData.ambientOcclusion, screenSpaceAO, BentNormal);

    const FBxDFEnergyTerms EnergyTerms = ComputeGGXSpecEnergyTerms(surfaceData.roughness, NoV, specularColor);
    float3 DiffuseWeight = ComputeEnergyPreservation(EnergyTerms);

    half3 Lighting = 0;

    // still a lot of material type to add
    if (surfaceData.materialType == MaterialType_TwosidedFoliage)
    {
        float3 SubsurfaceLookup = GetSkySHDiffuse(-skyNormal, UE4AmbientProbeSH) * skyColor * skyIntensity;
        float3 SubsurfaceColor = surfaceData.subsurfaceColor;
        Lighting += SkyVisData.SkyDiffuseLookUpMul * SubsurfaceLookup * SubsurfaceColor;
    }

    float3 DiffuseLookup = skyIntensity * GetSkySHDiffuse(skyNormal, UE4AmbientProbeSH) * skyColor;

    Lighting += (SkyVisData.SkyDiffuseLookUpMul * DiffuseLookup /*+ SkyDiffuseLookUpAdd */) * diffuseColor * DiffuseWeight;

    return Lighting;
}
#endif