#pragma keyword CE_USE_DOUBLE_TRANSFORM
#pragma keyword debug_symbol;

#define DEFERRED_SHADING

#include "../Material/NormalBuffer.hlsl"
#include "../ShaderLibrary/Common.hlsl"
#include "../Material/MaterialCommon.hlsl"
#include "../Material/BSDF.hlsl"
#include "../Material/Lit/LitCommonStruct.hlsl"
#include "../ShaderLibrary/GlobalModelVariables.hlsl"
#include "IndirectLightingCommon.hlsl"
#include "LightLoop/IndirectDiffuse.hlsl"
#include "../Material/Lit/GbufferEncoderDecoder.hlsl"

#define DISABLE_INDIRECT_LIGHTING_3S 0
#define INDIRECT_LIGHTING_3S_USE_GI 1
#define INDIRECT_LIGHTING_3S_USE_SKYLIGHT 2

SHADER_CONST(bool, ENABLE_REFLECTION_INDIRECT, false);
SHADER_CONST(bool, ENABLE_AO, false);
SHADER_CONST(bool, <PERSON><PERSON><PERSON>E_SMARTGI, false);
SHADER_CONST(bool, LONG_DISTANCE_USE_SKY_LIGHT, false);
SHADER_CONST(bool, IsolatePixelFlickingSuppression, false);
SHADER_CONST(bool, DEBUG_SHOW_ISOLATED_PIXEL, false);
SHADER_CONST(uint, IsolatePixelThreshold, 1u);
SHADER_CONST(bool, ENABLE_AO_RESCALE, false);

// IndirectLightingComposite params, mainly SmartGI's params
cbuffer IndirectLightingCompositeParams
{
    float IndirectLightIntensity;
    float IndirectSpecularLightingIntensity;
    float IndirectDiffuseLightingIntensity;
    float DiffuseBoost;
    float FoliageSkyLightLerpDistance;
    float LongDistanceUseSkyLightDistStart;
    float LongDistanceUseSkyLightDistLerp;
    float MaxRoughnessToTrace;
    float InvRoughnessFadeLength;

    int FOLIAGE_TWOSIDED_LIGHTING_MODE;
}

// IndirectLightingComposite textures
Texture2D<float4>  ReflectionIndirectTex : register(space0);
Texture2D<float4>  SmartGIDiffuseTex     : register(space0);
Texture2D<float4>  SmartGISpecularTex    : register(space0);
// AOTex : xyz = bent normal, w = [0,1] ao value
Texture2D<float4>  AOTex                 : register(space0);
Texture2D<float4>  EnvBRDFLutMap         : register(space0);

Texture2D<float4>  SceneColorTex : register(space0);
float4 _View_SizeAndInvSize;


struct VS2PS
{
    float2 UV  :    TEXCOORD0;
    float4 Pos :    SV_POSITION;
};

void DrawRectangle(
    float4 InPosition,
    float2 InTexCoord,
    out float4 OutPosition,
    out float2 OutTexCoord)
{
    OutPosition = InPosition;
    OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
    OutPosition.xy *= float2(1, -1);
    OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
    VS2PS ret;
    float4 Outpos;
    float2 Outuv;
    DrawRectangle(
        Pos,
        uv,
        Outpos,
        Outuv);
    ret.Pos = Outpos;
    ret.UV = Outuv;
    return ret;
}

struct PSOutput
{
    float4 compositeColor : SV_Target0;
    float4 giColor        : SV_Target1;
};

/**
 * Detects isolated pixels by counting zero-depth neighbors in a 3x3 grid, 
 *   Returns true if number of zero-depth neighbors >= IsolatePixelThreshold
 */
bool IsPixelIsolated(float2 uv, float centerDepth)
{
    if (centerDepth == 0.0f)
        return false;

    float2 texelSize = _View_SizeAndInvSize.zw;
    
    float4 adjacentDepth = float4(
        SampleCameraDepth(uv + float2(-texelSize.x, 0)),
        SampleCameraDepth(uv + float2(texelSize.x, 0)),
        SampleCameraDepth(uv + float2(0, -texelSize.y)),
        SampleCameraDepth(uv + float2(0, texelSize.y))
    );
    
    uint numZeroDepth = dot(uint4(adjacentDepth == 0.f), 1.f);
    if (numZeroDepth >= IsolatePixelThreshold)
    {
        return true;
    }
    
    float4 diagonalDepth = float4(
        SampleCameraDepth(uv + float2(-texelSize.x, -texelSize.y)),
        SampleCameraDepth(uv + float2(texelSize.x, -texelSize.y)),
        SampleCameraDepth(uv + float2(-texelSize.x, texelSize.y)),
        SampleCameraDepth(uv + float2(texelSize.x, texelSize.y))
    );
    numZeroDepth += dot(uint4(diagonalDepth == 0.f), 1.f);
    
    return numZeroDepth >= IsolatePixelThreshold;
}


PSOutput PSMain(VS2PS input)
{

    PSOutput psOutput;

    float4 sceneColor = SceneColorTex.Sample(ce_Sampler_Point, input.UV);

    float2 screenUV = input.UV;
    float depth = SampleCameraDepth(screenUV);

    PositionInputs posInput = GetPositionInput(screenUV, depth, ce_InvViewProjMatrix, ce_View);

    float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

    BSDFData bsdfData;
    BuiltinData builtinData;
    DecodeFromGBuffer(posInput.uv, bsdfData, builtinData);

    // Use GBuffer's normal and baked AO as default value
    float3 screenSpaceBentNormal = bsdfData.normalWS;
    float  screenSpaceAo = bsdfData.ambientOcclusion; // original value = 1.0
    float3 diffuseAO = screenSpaceAo.xxx;
    float  specularOcclusion = 1.f;

    if (ENABLE_AO)
    {
        screenSpaceAo = min(screenSpaceAo, AOTex.Sample(ce_Sampler_Clamp, posInput.uv).w);
        // Use mix baked ao and gtao, should use min here
        diffuseAO = ENABLE_AO_RESCALE ? DistantIlluminationRescale(bsdfData.diffuseColor, screenSpaceAo) : screenSpaceAo;
        float3 bentNormal = AOTex.Sample(ce_Sampler_Clamp, posInput.uv).xyz;
        float bentNormalLen = length(bentNormal);
        if (bentNormalLen > 0)  // means bent normal enabled
        {
            screenSpaceBentNormal = bentNormal;
            screenSpaceBentNormal = Convert_0to1_To_minus1to1(screenSpaceBentNormal);
        }

        // (chopperlin) 20250421 : use specularOcclusion will cause too much noise, if you improve GTAO/other AO you can reopen this
        // specularOcclusion = CalculateSpecularOcclusion(bsdfData.roughness, bsdfData.normalWS, screenSpaceAo, V, screenSpaceBentNormal);
    }

	// Combine specular indirect lighting here, from ReflectionIndirect and SmartGI's specularTex
    float NoV = max(0.0001, saturate(dot(bsdfData.normalWS, V)));
    float3 specularIndirect = 0.xxx;
    float3 reflectionIndirectSpecular = 0.xxx;
    if (ENABLE_REFLECTION_INDIRECT)
    {
        reflectionIndirectSpecular = ReflectionIndirectTex.Sample(ce_Sampler_Clamp, posInput.uv).xyz;
    }

    float3 envBrdf = EnvBRDF(EnvBRDFLutMap, ce_Sampler_Clamp, 0.4, bsdfData.roughness, NoV);
    
    if (ENABLE_SMARTGI)
    {
	    float3 specularSmartGI = SmartGISpecularTex.Sample(ce_Sampler_Clamp, posInput.uv).xyz;
        specularSmartGI *= IndirectSpecularLightingIntensity * IndirectLightIntensity; // smart gi param, should not affect reflection indirect
        if (ENABLE_REFLECTION_INDIRECT)
        {
            specularIndirect = CombineRoughSpecular(bsdfData.roughness, reflectionIndirectSpecular, specularSmartGI, envBrdf, MaxRoughnessToTrace, InvRoughnessFadeLength);
        }
        else
        {
            specularIndirect = specularSmartGI * envBrdf;
        }
	}
	else
	{
		specularIndirect = reflectionIndirectSpecular * envBrdf;
	}

    specularIndirect *= specularOcclusion;

	// Combine diffuse indirect lighting here
    float3 diffuseIndirect = 0.xxx;
    
    bool isIsolatedPixel = false;
    if (IsolatePixelFlickingSuppression)
    {
        isIsolatedPixel = IsPixelIsolated(screenUV, depth);
    }

    if (ENABLE_SMARTGI && !isIsolatedPixel /* Skip computation when for isolated pixels*/)
    {
        // Diffuse boost for SmartGI
        float3 boostDiffuseColor = BoostDiffuseColor(bsdfData.diffuseColor, DiffuseBoost);
        float3 diffuseSmartGILighting = SmartGIDiffuseTex.Sample(ce_Sampler_Clamp, input.UV).xyz;
        float3 diffuseSmartGI = diffuseSmartGILighting * boostDiffuseColor;
        
        if (LONG_DISTANCE_USE_SKY_LIGHT)
        {
            float3 skyNormal = bsdfData.normalWS;
            if(ce_UE4AmbientProbeSH[6].w == 0)
            {
                skyNormal = Float3CE2UE(bsdfData.normalWS);
            }

            float3 fresnel0 = bsdfData.fresnel0;
            float materialAmbientOcclusion = 1.0;
            // note, a lot of them (Skynormal, worldNormal, bentNorma) are not presented/implemented in CE 20220829, so use the same value temporally
            FSkyLightVisibilityData SkyVisData = GetSkyLightVisibilityData(skyNormal, skyNormal, materialAmbientOcclusion, screenSpaceAo, screenSpaceBentNormal);

            const FBxDFEnergyTerms EnergyTerms = ComputeGGXSpecEnergyTerms(bsdfData.roughness, NoV, fresnel0);
            float3 DiffuseWeight = ComputeEnergyPreservation(EnergyTerms);

            float3 DiffuseLookup = GetSkySHDiffuse(skyNormal, ce_UE4AmbientProbeSH) * ce_SkyLightColor * ce_SkyLightIntensity;

            float3 skyLighting = (SkyVisData.SkyDiffuseLookUpMul * DiffuseLookup) * boostDiffuseColor * DiffuseWeight;

            float viewDistance = length(ce_CameraPos.xyz - posInput.positionWS.xyz);
            float lerpWeight = saturate((viewDistance - LongDistanceUseSkyLightDistStart) / LongDistanceUseSkyLightDistLerp);
            diffuseSmartGI = lerp(diffuseSmartGILighting, skyLighting, lerpWeight) * boostDiffuseColor;
        }

        diffuseSmartGI *= diffuseAO;
        
        // Foliage diffuse GI TODO(chopperlin) need to improve
        if (bsdfData.materialType == MaterialType_TwosidedFoliage || bsdfData.materialType == MaterialType_ImposterFoliage)
        {
            if (FOLIAGE_TWOSIDED_LIGHTING_MODE == INDIRECT_LIGHTING_3S_USE_SKYLIGHT)
            {
                float3 N = screenSpaceBentNormal;
                float3 skyNormal = bsdfData.normalWS;
                if (ce_UE4AmbientProbeSH[6].w == 0)
                {
                    skyNormal = Float3CE2UE(bsdfData.normalWS);
                }

                // could be redundant with function ReflectionEnviroment, but still add here for clarity
                float3 R = 2 * dot(V, N) * N - V;

                float3 specularColor = bsdfData.fresnel0;
                float materialAmbientOcclusion = 1.0;
                // note, a lot of them (Skynormal, worldNormal, bentNorma) are not presented/implemented in CE 20220829, so use the same value temporally
                FSkyLightVisibilityData SkyVisData = GetSkyLightVisibilityData(skyNormal, skyNormal, materialAmbientOcclusion, screenSpaceAo, screenSpaceBentNormal);

                const FBxDFEnergyTerms EnergyTerms = ComputeGGXSpecEnergyTerms(bsdfData.roughness, NoV, specularColor);
                float3 DiffuseWeight = ComputeEnergyPreservation(EnergyTerms);

                diffuseSmartGI *= DiffuseWeight;

                // still a lot of material type to add
                //if (bsdfData.materialType == MaterialType_TwosidedFoliage)
                {
                    float3 SubsurfaceLookup = max(GetSkySHDiffuse(-skyNormal, ce_UE4AmbientProbeSH) * ce_SkyLightColor * ce_SkyLightIntensity, 0.0001f);
                    float3 SubsurfaceLookupNormal = max(GetSkySHDiffuse(skyNormal, ce_UE4AmbientProbeSH) * ce_SkyLightColor * ce_SkyLightIntensity, 0.0001f);

                    float3 SubsurfaceColor = bsdfData.subsurfaceColor;
                    float3 FoliageSSS = SkyVisData.SkyDiffuseLookUpMul * SubsurfaceLookup * SubsurfaceColor * diffuseSmartGILighting / SubsurfaceLookupNormal;

                    float viewDistance = length(ce_CameraPos.xyz - posInput.positionWS.xyz);
                    float lerpWeight = saturate(viewDistance/FoliageSkyLightLerpDistance);
                    float3 FoliageGISSS = diffuseSmartGILighting * bsdfData.subsurfaceColor * diffuseAO * SubsurfaceLookup / SubsurfaceLookupNormal;
                    diffuseSmartGI += lerp(FoliageGISSS, FoliageSSS, lerpWeight);
                }
            }
            else if (FOLIAGE_TWOSIDED_LIGHTING_MODE == INDIRECT_LIGHTING_3S_USE_GI)
            {
                float3 skyNormal = bsdfData.normalWS;
                if(ce_UE4AmbientProbeSH[6].w == 0)
                {
                    skyNormal = Float3CE2UE(bsdfData.normalWS);
                }
                float3 SubsurfaceLookup =max(GetSkySHDiffuse(-skyNormal, ce_UE4AmbientProbeSH) * ce_SkyLightColor * ce_SkyLightIntensity, 0.0001f);
                float3 SubsurfaceLookupNormal = max(GetSkySHDiffuse(skyNormal, ce_UE4AmbientProbeSH) * ce_SkyLightColor * ce_SkyLightIntensity, 0.0001f);
                float3 avgSubsurface = (SubsurfaceLookup + SubsurfaceLookupNormal) / 2;

                float3 SubsurfaceColor = bsdfData.subsurfaceColor;
                diffuseSmartGI += diffuseSmartGILighting * SubsurfaceColor * diffuseAO * (SubsurfaceLookup + avgSubsurface * 0.5) / (SubsurfaceLookupNormal + avgSubsurface * 0.5);
            }
        }
        diffuseIndirect = diffuseSmartGI * IndirectDiffuseLightingIntensity * IndirectLightIntensity;
    }
    else
    {
        diffuseIndirect = IndirectDiffuse_GetSkySHOnly(bsdfData, V, bsdfData.normalWS, ce_UE4AmbientProbeSH,
                    ce_SkyLightColor * ce_SkyLightIntensity, 1.0, screenSpaceAo, screenSpaceBentNormal, bsdfData.diffuseColor, bsdfData.fresnel0, ENABLE_SKY_LIGHT_REALTIME_CAPTURE);
    }

	float3 indirectColor = 0.xxx;
    if (bsdfData.materialType != MaterialType_Unlit && depth != 0)
    {
        indirectColor = (diffuseIndirect + specularIndirect);
        
        if (isIsolatedPixel)
        {
            indirectColor = IndirectDiffuse_GetSkySHOnly(bsdfData, V, bsdfData.normalWS, ce_UE4AmbientProbeSH,
                    ce_SkyLightColor * ce_SkyLightIntensity, 1.0, screenSpaceAo, screenSpaceBentNormal, bsdfData.diffuseColor, bsdfData.fresnel0, ENABLE_SKY_LIGHT_REALTIME_CAPTURE) + specularIndirect;
            if (DEBUG_SHOW_ISOLATED_PIXEL)
            {
                indirectColor = float3(1.f, 0.f, 0.f);
            }
        }
    }

    psOutput.compositeColor = float4(sceneColor.xyz + indirectColor, 0.f);

    // For view mode visualization
    if (VIEW_MODE == ViewMode_AmbientOcclusion)
    {
        psOutput.giColor = float4(diffuseAO, 0.f);
    }
    if (VIEW_MODE == ViewMode_BentNormal)
    {
        psOutput.giColor = float4(screenSpaceBentNormal, 0.f);
    }
    if (VIEW_MODE == ViewMode_Reflections)
    {
        psOutput.giColor = float4(specularIndirect, 0.f);
    }
    if (VIEW_MODE == ViewMode_GlobalIllumination)
    {
        psOutput.giColor = float4(diffuseIndirect, 0.f);
    }
    if (VIEW_MODE == ViewMode_GILighting)
    {
        float3 giLighting = 0.xxx;
        
        [flatten]
        if (ENABLE_SMARTGI && !isIsolatedPixel)
        {
            float3 diffuseGILighting = SmartGIDiffuseTex.Sample(ce_Sampler_Clamp, input.UV).xyz;
            float3 specularGILighting = SmartGISpecularTex.Sample(ce_Sampler_Clamp, input.UV).xyz * envBrdf;
            giLighting = (diffuseGILighting * IndirectDiffuseLightingIntensity * diffuseAO + specularGILighting * IndirectSpecularLightingIntensity) * IndirectLightIntensity;
        }
        else  // Use Stable Lighting on Isolated Edge
        {
            giLighting = IndirectDiffuse_GetSkySHOnly(bsdfData, V, bsdfData.normalWS, ce_UE4AmbientProbeSH,
                ce_SkyLightColor * ce_SkyLightIntensity, 1.0, screenSpaceAo, screenSpaceBentNormal, bsdfData.diffuseColor, bsdfData.fresnel0, ENABLE_SKY_LIGHT_REALTIME_CAPTURE) + specularIndirect;
            if (isIsolatedPixel && DEBUG_SHOW_ISOLATED_PIXEL)
            {
                giLighting = float3(1.f, 0.f, 0.f);
            }
        }
        if (depth == 0.f)
            giLighting = 0.xxx;

        psOutput.giColor = float4(giLighting, 0.f);
    }

    return psOutput;
}
