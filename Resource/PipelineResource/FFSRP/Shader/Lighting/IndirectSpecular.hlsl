#ifndef INDIRECT_LIGHTING_HLSL
#define INDIRECT_LIGHTING_HLSL

/*
float3 Float3CE2UE(float3 v)
{
    return float3(v.x, -v.z, v.y);
}
*/

float ApproximateConeConeIntersection(float ArcLength0, float ArcLength1, float AngleBetweenCones)
{
	float AngleDifference = abs(ArcLength0 - ArcLength1);

	float Intersection = smoothstep(
		0,
		1.0,
		1.0 - saturate((AngleBetweenCones - AngleDifference) / (ArcLength0 + ArcLength1 - AngleDifference)));

	return Intersection;
}

float CalculateSpecularOcclusion(float Roughness, float3 WorldNormal, float AO, float3 V, float3 BentNormalOcclusion)
{
	float ReflectionConeAngle = max(Roughness, .1f) * PI;
	float UnoccludedAngle = AO * PI;
	float3 ReflectionVector = reflect(-V, WorldNormal);
	float AngleBetween = acosFast(dot(BentNormalOcclusion, ReflectionVector) / max(AO, .001f));
	float SpecularOcclusion = ApproximateConeConeIntersection(ReflectionConeAngle, UnoccludedAngle, AngleBetween);

	// Can't rely on the direction of the bent normal when close to fully occluded, lerp to shadowed
	SpecularOcclusion = lerp(0, SpecularOcclusion, saturate((UnoccludedAngle - .1f) / .2f));
	return SpecularOcclusion;
}

float CalculateSpecularOcclusionGTAO(float Roughness, float3 WorldNormal, float AO, float3 V, float3 BentNormal)
{
	float ReflectionConeAngle = max(Roughness, .1f) * PI;
	float UnoccludedAngle = acosFast(sqrt(1.0f - AO));
	float3 ReflectionVector = reflect(-V, WorldNormal);
	float AngleBetween = acosFast(dot(BentNormal, ReflectionVector));
	float SpecularOcclusion = ApproximateConeConeIntersection(ReflectionConeAngle, UnoccludedAngle, AngleBetween);

	// Can't rely on the direction of the bent normal when close to fully occluded, lerp to shadowed
	SpecularOcclusion = lerp(0, SpecularOcclusion, saturate((UnoccludedAngle - .1f) / .2f));
	return SpecularOcclusion;
}

#endif