#ifndef LIGHT_DEFINITION_HLSL
#define LIGHT_DEFINITION_HLSL

struct DirectionalLightData
{
	float3 tilePosition;
	float3 forward;
	float3 color;
};

struct PunctualLightData
{
	float3 positionWS;
	float3 tilePosition;
	float3 forward;
	float3 color;
    /*
     * For point or spot Light: z for 1 / radius^2, w for radius^2
     * spot light x for cos outer angle, y for 1.f (cos inner angle - cos outer angle)
     * For rect light, xy for extent, z for cos barn angle, w for barn length
     */
	float4 attenuation;
};

#define LIGHT_INSTANCE_FLAG_CAST_SHADOW 0x00000001

struct InstanceLightType
{
    matrix world;
    float4 lightDirPos;
    float4 lightTilePos;
    float4 lightAttenuation;
    float4 lightColor;
    float4 lightSpotDirection;
    int shadowDataIndex;
    int lightIndex;
    uint instanceID;
    uint flags;
};

#endif