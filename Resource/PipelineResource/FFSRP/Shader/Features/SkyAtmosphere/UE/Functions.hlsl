#include "Common.hlsl"

#ifndef PER_PIXEL_NOISE
#define PER_PIXEL_NOISE 0
#endif

#define SHADOWMAP_ENABLED SKY_RENDER_PASS
#ifdef ATMOSPHERE_USE_VSM
//#include "Lighting\Shadow\ShadowInclude.hlsl"
#include "Lighting\Shadow\Shadow.hlsl"

cbuffer cbVSMData : register(space1)
{
	int VirtualShadowMapId0;
	int VirtualShadowMapId1;
}
#endif

#ifdef SAMPLE_CLOUD_SHADOW
#include "Lighting\Shadow\CloudShadows.hlsl"
#endif
struct SingleScatteringResult
{
	float3 L;						// Scattered light (luminance)
	float3 OpticalDepth;			// Optical depth (1/m)
	float3 Transmittance;			// Transmittance in [0,1] (unitless)
	float3 MultiScatAs1;

	float3 NewMultiScatStep0Out;
	float3 NewMultiScatStep1Out;
};

struct SamplingSetup
{
	bool VariableSampleCount;
	float SampleCountIni;			// Used when VariableSampleCount is false
	float MinSampleCount;
	float MaxSampleCount;
	float DistanceToSampleCountMaxInv;
};

struct AtmosphereLightParam 
{
    float3 Direction;
    float3 Illuminance;
	bool IsSun;
	bool Reversed;
	uint ReversedLightRadius;
};

float3 UvToClipSpace(in float2 uv)
{
    return float3(uv.x * 2 - 1, (1 - uv.y) * 2 - 1, 1.0);
}

#define DEFAULT_PIXEL_NOISE 0.3f
float GetSkyAtmospherePixelNoise(float2 uv)
{
#if PER_PIXEL_NOISE
	return InterleavedGradientNoise(uv * ce_ScreenParams.xy, float(ce_FrameIDMod8));
#else
	return DEFAULT_PIXEL_NOISE;
#endif
}

float3 GetAtmosphereTransmittance(
	float3 WorldPos, float3 WorldDir, in AtmosphereParametersUE Atmosphere,
	Texture2D<float4> TransmittanceLutTexture, SamplerState TransmittanceLutTextureSampler)
{
	// For each view height entry, transmittance is only stored from zenith to horizon. Earth shadow is not accounted for.
	// It does not contain earth shadow in order to avoid texel linear interpolation artefact when LUT is low resolution.
	// As such, at the most shadowed point of the LUT when close to horizon, pure black with earth shadow is never hit.
	// That is why we analytically compute the virtual planet shadow here.
	const float2 Sol = rayIntersectSphere(WorldPos, WorldDir, float4(float3(0.0f, 0.0f, 0.0f), Atmosphere.BottomRadius));
	if (Sol.x > 0.0f || Sol.y > 0.0f)
	{
		return 0.0f;
	}

	const float PHeight = length(WorldPos);
	const float3 UpVector = WorldPos / PHeight;
	const float LightZenithCosAngle = dot(WorldDir, UpVector);
	float2 TransmittanceLutUv;
	LutTransmittanceParamsToUv(Atmosphere, PHeight, LightZenithCosAngle, TransmittanceLutUv);
	const float3 TransmittanceToLight = TransmittanceLutTexture.SampleLevel(TransmittanceLutTextureSampler, TransmittanceLutUv, 0.0f).rgb;
	return TransmittanceToLight;
}

float3 GetLightDiskLuminance(
	float3 WorldPos, float3 WorldDir, in AtmosphereParametersUE Atmosphere,
	Texture2D<float4> TransmittanceLutTexture, SamplerState TransmittanceLutTextureSampler,
	float3 AtmosphereLightDirection, float AtmosphereLightDiscCosHalfApexAngle, float3 AtmosphereLightDiscLuminance)
{
	const float ViewDotLight = dot(WorldDir, AtmosphereLightDirection);
	const float CosHalfApex = AtmosphereLightDiscCosHalfApexAngle;
	if (ViewDotLight > CosHalfApex)
	{
		const float3 TransmittanceToLight = GetAtmosphereTransmittance(
			WorldPos, WorldDir, Atmosphere, TransmittanceLutTexture, TransmittanceLutTextureSampler);

		// Soften out the sun disk to avoid bloom flickering at edge. The soften is applied on the outer part of the disk.
		const float SoftEdge = saturate(2.0f * (ViewDotLight - CosHalfApex) / (1.0f - CosHalfApex));

		return TransmittanceToLight * AtmosphereLightDiscLuminance * SoftEdge;
	}
	return 0.0f;
}

float3 GetLightDiskLuminance_MoonPhase(
	float3 WorldPos, float3 WorldDir, in AtmosphereParametersUE Atmosphere,
	Texture2D<float4> TransmittanceLutTexture, SamplerState TransmittanceLutTextureSampler,
	float3 AtmosphereSunDirection, float3 AtmosphereMoonDirection, float AtmosphereLightDiscCosHalfApexAngle, float3 AtmosphereMoonDiscLuminance)
{
	const float ViewDotLight = dot(WorldDir, AtmosphereMoonDirection);
	const float CosHalfApex = AtmosphereLightDiscCosHalfApexAngle;
	float CosGamma = ViewDotLight;
	float CosXita = CosHalfApex;
	if (ViewDotLight > CosHalfApex)
	{
		float LengthOfView = CosGamma - sqrt(CosGamma * CosGamma - CosXita * CosXita);
		float3 ViewVec = LengthOfView * WorldDir;
		float3 MoonCenterToIntersection = ViewVec - AtmosphereMoonDirection;
		float NoL = dot(MoonCenterToIntersection, AtmosphereSunDirection);
		if(NoL > 0.0)
		{

			const float3 TransmittanceToLight = GetAtmosphereTransmittance(
				WorldPos, WorldDir, Atmosphere, TransmittanceLutTexture, TransmittanceLutTextureSampler);

			// Soften out the sun disk to avoid bloom flickering at edge. The soften is applied on the outer part of the disk.
			const float SoftEdge = saturate(2.0f * (ViewDotLight - CosHalfApex) / (1.0f - CosHalfApex));

			return TransmittanceToLight * AtmosphereMoonDiscLuminance * SoftEdge * NoL;
		}
	}
	return 0.0f;
}

float2 ComputeMoonTexUV(float3 WorldPos, float3 MoonDir, float3 ViewDir, float CosHalfApex)
{
	// Z-Up
	float3 Up = float3(0, 0, 1);//normalize(WorldPos);
	if(dot(Up, MoonDir) > 0.99999) // moon at north pole
	{
		Up = float3(1, 0, 0);
	}

	float3 Side = normalize(cross(Up, MoonDir));
	Up = normalize(cross(MoonDir, Side));
	float SinHalfApex = sqrt(1 - CosHalfApex * CosHalfApex);

	float ClipEdge = 0.85;
	float2 uv;
	// -1 ~ 1
	uv.x = ClipEdge * dot(ViewDir, Side) / SinHalfApex;
	uv.y = -ClipEdge * dot(ViewDir, Up) / SinHalfApex;

	// 0 ~ 1
	uv = (uv + 1) * 0.5;
	return uv;
}

float3 GetLightDiskLuminance_MoonPhase_Texture(
	float3 WorldPos, float3 WorldDir, in AtmosphereParametersUE Atmosphere,
	Texture2D<float4> MoonTexture, Texture2D<float4> TransmittanceLutTexture, SamplerState TransmittanceLutTextureSampler,
	float3 AtmosphereSunDirection, float3 AtmosphereMoonDirection, float AtmosphereLightDiscCosHalfApexAngle, float3 AtmosphereMoonDiscLuminance)
{
	const float ViewDotLight = dot(WorldDir, AtmosphereMoonDirection);
	const float CosHalfApex = AtmosphereLightDiscCosHalfApexAngle;
	float CosGamma = ViewDotLight;
	float CosXita = CosHalfApex;
	if (ViewDotLight > CosHalfApex)
	{
		float LengthOfView = CosGamma - sqrt(CosGamma * CosGamma - CosXita * CosXita);
		float3 ViewVec = LengthOfView * WorldDir;
		float3 MoonCenterToIntersection = ViewVec - AtmosphereMoonDirection;
		float NoL = dot(MoonCenterToIntersection, AtmosphereSunDirection);
		if(NoL > 0.0)
		{
			float2 MoonUV = ComputeMoonTexUV(WorldPos, AtmosphereMoonDirection, WorldDir, CosHalfApex);
			float3 MoonAlbedo = MoonTexture.Sample(TransmittanceLutTextureSampler, MoonUV);

			const float3 TransmittanceToLight = GetAtmosphereTransmittance(
				WorldPos, WorldDir, Atmosphere, TransmittanceLutTexture, TransmittanceLutTextureSampler);

			// Soften out the sun disk to avoid bloom flickering at edge. The soften is applied on the outer part of the disk.
			const float SoftEdge = 1.0f;//saturate(10.0f * (ViewDotLight - CosHalfApex) / (1.0f - CosHalfApex));

			return TransmittanceToLight * AtmosphereMoonDiscLuminance * SoftEdge * NoL * MoonAlbedo;
		}
	}
	return 0.0f;
}

float3 GetLightDiskLuminance(float3 WorldPos, float3 WorldDir, float LightDiscCosHalhApexAngle, in float3 SunDir,in AtmosphereParametersUE Atmosphere, in float3 SunLuminance)
{
	float t = raySphereIntersectNearest(WorldPos, WorldDir, float3(0.0f, 0.0f, 0.0f), Atmosphere.BottomRadius);
	if (t < 0.0f)
	{

		float3 LightDiskLuminance = GetLightDiskLuminance(
			WorldPos, WorldDir, Atmosphere,
			_TransmittanceLutTextureReadOnly, ce_Sampler_Clamp,
			SunDir, LightDiscCosHalhApexAngle, SunLuminance);

		const float3 MaxLightLuminance = 64000.0f;
		return min(LightDiskLuminance, MaxLightLuminance);;
	}
	return 0.0f;
}

float3 GetLightDiskLuminance_MoonPhase(float3 WorldPos, float3 WorldDir, float LightDiscCosHalhApexAngle, in float3 SunDir,in float3 MoonDir,in AtmosphereParametersUE Atmosphere, in float3 MoonLuminance)
{
	float t = raySphereIntersectNearest(WorldPos, WorldDir, float3(0.0f, 0.0f, 0.0f), Atmosphere.BottomRadius);
	if (t < 0.0f)
	{
		float3 LightDiskLuminance = GetLightDiskLuminance_MoonPhase(
			WorldPos, WorldDir, Atmosphere,
			_TransmittanceLutTextureReadOnly, ce_Sampler_Clamp,
			SunDir, MoonDir, LightDiscCosHalhApexAngle, MoonLuminance);

		const float3 MaxLightLuminance = 64000.0f;
		return min(LightDiskLuminance, MaxLightLuminance);;
	}
	return 0.0f;
}

float3 GetLightDiskLuminance_MoonPhase(float3 WorldPos, float3 WorldDir, float LightDiscCosHalhApexAngle, in float3 SunDir,in float3 MoonDir,in AtmosphereParametersUE Atmosphere, in float3 MoonLuminance, in Texture2D<float4> MoonTexture)
{
	float t = raySphereIntersectNearest(WorldPos, WorldDir, float3(0.0f, 0.0f, 0.0f), Atmosphere.BottomRadius);
	if (t < 0.0f)
	{
		float3 LightDiskLuminance = GetLightDiskLuminance_MoonPhase_Texture(
			WorldPos, WorldDir, Atmosphere,
			MoonTexture, _TransmittanceLutTextureReadOnly, ce_Sampler_Clamp,
			SunDir, MoonDir, LightDiscCosHalhApexAngle, MoonLuminance);

		const float3 MaxLightLuminance = 64000.0f;
		return min(LightDiskLuminance, MaxLightLuminance);;
	}
	return 0.0f;
}

SingleScatteringResult IntegrateScatteredLuminance(
	in float2 uv, in float3 WorldPos, in float3 WorldDir, in AtmosphereLightParam LightParams[3], in uint LightCount, in AtmosphereParametersUE Atmosphere,
	in bool ground, in SamplingSetup Sampling, in float DepthBufferValue, 
	in bool MieRayPhase, in float tMaxMax = 9000000.0f)
{
	SingleScatteringResult result = (SingleScatteringResult)0;

    if (dot(WorldPos, WorldPos) <= Atmosphere.BottomRadius * Atmosphere.BottomRadius)
    {
        return result;	// Camera is inside the planet ground
    }

	float3 ClipSpace = UvToClipSpace(uv);

	// Compute next intersection with atmosphere or ground 
	float3 earthO = float3(0.0f, 0.0f, 0.0f);
    float tMax = 0.0f;
#if 0
	float tBottom = raySphereIntersectNearest(WorldPos, WorldDir, earthO, Atmosphere.BottomRadius);
	float tTop = raySphereIntersectNearest(WorldPos, WorldDir, earthO, Atmosphere.TopRadius);
	if (tBottom < 0.0f)
	{
		if (tTop < 0.0f)
		{
			tMax = 0.0f; // No intersection with earth nor atmosphere: stop right away  
			return result;
		}
		else
		{
			tMax = tTop;
		}
	}
	else
	{
		if (tTop > 0.0f)
		{
			tMax = min(tTop, tBottom);
		}
	}
#else
    float tBottom = 0.0f;
    float2 SolB = rayIntersectSphere(WorldPos, WorldDir, float4(earthO, Atmosphere.BottomRadius));
    float2 SolT = rayIntersectSphere(WorldPos, WorldDir, float4(earthO, Atmosphere.TopRadius));

    const bool bNoBotIntersection = all(SolB < 0.0f);
    const bool bNoTopIntersection = all(SolT < 0.0f);
    if (bNoTopIntersection)
    {
        // No intersection with planet or its atmosphere.
        tMax = 0.0f;
        return result;
    }
    else if (bNoBotIntersection)
    {
        // No intersection with planet, so we trace up to the far end of the top atmosphere 
        // (looking up from ground or edges when see from afar in space).
        tMax = max(SolT.x, SolT.y);
    }
    else
    {
        // Interesection with planet and atmospehre: we simply trace up to the planet ground.
        // We know there is at least one intersection thanks to bNoBotIntersection.
        // If one of the solution is invalid=-1, that means we are inside the planet: we stop tracing by setting tBottom=0.
        tBottom = max(0.0f, min(SolB.x, SolB.y));
        tMax = tBottom;
    }
#endif

	if (DepthBufferValue >= 0.0f)
	{
		ClipSpace.z = DepthBufferValue;
		if (ClipSpace.z < 1.0f)
		{
			float4 DepthBufferWorldPos = mul(ce_InvProjection, float4(ClipSpace, 1.0));
   	 		DepthBufferWorldPos /= DepthBufferWorldPos.w;
    		DepthBufferWorldPos = mul(ce_InvView, DepthBufferWorldPos);

#ifdef CE_USE_DOUBLE_TRANSFORM
			DepthBufferWorldPos.xyz = GetLargeCoordinateAbsolutePosition(DepthBufferWorldPos.xyz, ce_CameraTilePosition);
#endif
			DepthBufferWorldPos.xyz = ToUEVec(0.00001f * (DepthBufferWorldPos.xyz ));

		    float3 TraceStartToSurfaceWorldKm;
			if (PLANET_TOP_AT_ORIGIN)
			{
				TraceStartToSurfaceWorldKm = DepthBufferWorldPos.xyz - (WorldPos + float3(0.0, 0.0, -Atmosphere.BottomRadius)); // pply earth offset to go back to origin as top of earth mode. 
			}
			else
			{
				TraceStartToSurfaceWorldKm = DepthBufferWorldPos.xyz - WorldPos;
			}

		    float tDepth = length(TraceStartToSurfaceWorldKm);
		    
		    //float tDepth = length(DepthBufferWorldPos.xyz - (WorldPos + float3(0.0, 0.0, -Atmosphere.BottomRadius))); // apply earth offset to go back to origin as top of earth mode. 
			if (tDepth < tMax)
			{
				tMax = tDepth;
			}

		    if (dot(WorldDir, TraceStartToSurfaceWorldKm) < 0.0)
		    {
		        return result;
		    }
		}
		//		if (VariableSampleCount && ClipSpace.z == 1.0f)
		//			return result;
	}
	tMax = min(tMax, tMaxMax);

	// Sample count 
	float SampleCount = Sampling.SampleCountIni;
	float SampleCountFloor = Sampling.SampleCountIni;
	float tMaxFloor = tMax;
	if (Sampling.VariableSampleCount)
	{
        SampleCount = lerp(Sampling.MinSampleCount, Sampling.MaxSampleCount, saturate(tMax * Sampling.DistanceToSampleCountMaxInv));
		SampleCountFloor = floor(SampleCount);
		tMaxFloor = tMax * SampleCountFloor / SampleCount;	// rescale tMax to map to the last entire step segment.
	}
	float dt = tMax / SampleCount;

	// Phase functions
	const float uniformPhase = 1.0 / (4.0 * PI_SA);
	const float3 wo = WorldDir;
	
	float MiePhaseValues[3];
	float RayleighPhaseValues[3];
	float3 globalL[3] = { float3(1,1,1), float3(1,1,1), float3(1,1,1) };

	// Calculate phase functions and illuminance for all lights
	[unroll]
	for(uint i = 0; i < LightCount; i++)
	{
		float cosTheta = dot(LightParams[i].Direction, wo);
		// For Reversed lights , don't negate cosTheta
		MiePhaseValues[i] = hgPhase(Atmosphere.MiePhaseG, LightParams[i].Reversed ? cosTheta : -cosTheta);
		RayleighPhaseValues[i] = RayleighPhase(cosTheta);

		globalL[i] = 1.0f;
		if(!ILLUMINANCE_IS_ONE)
		{
			globalL[i] = LightParams[i].Illuminance;
		}
	}

	// Ray march the atmosphere to integrate optical depth
	float3 L = 0.0f;
	float3 throughput = 1.0;
	float3 OpticalDepth = 0.0;
	float t = 0.0f;
	float tPrev = 0.0;
	const float SampleSegmentT = PER_PIXEL_NOISE ? GetSkyAtmospherePixelNoise(uv) : DEFAULT_PIXEL_NOISE;//0.3f;
	for (float s = 0.0f; s < SampleCount; s += 1.0f)
	{
		if (Sampling.VariableSampleCount)
		{
		    // More expenssive but artefact free
		    float t0 = (s) / SampleCountFloor;
		    float t1 = (s + 1.0f) / SampleCountFloor;;
		    // Non linear distribution of samples within the range.
		    t0 = t0 * t0;
		    t1 = t1 * t1;
		    // Make t0 and t1 world space distances.
		    t0 = tMaxFloor * t0;
		    if (t1 > 1.0f)
		    {
		        t1 = tMax;
		        //t1 = tMaxFloor;	// this reveal depth slices
		    }
		    else
		    {
		        t1 = tMaxFloor * t1;
		    }
		    t = t0 + (t1 - t0) * PER_PIXEL_NOISE;
		    dt = t1 - t0;
		}
		else
		{
			t = tMax * (s + SampleSegmentT) / SampleCount;
			// Exact difference, important for accuracy of multiple scattering
			// float NewT = tMax * (s + SampleSegmentT) / SampleCount;
			// dt = NewT - t;
			// t = NewT;
		}
		float3 P = WorldPos + t * WorldDir;

		MediumSampleRGB medium = sampleMediumRGB(P, Atmosphere);
	    const float3 SampleOpticalDepth = medium.extinction * dt;
	    const float3 SampleTransmittance = exp(-SampleOpticalDepth);
		OpticalDepth += SampleOpticalDepth;

		float pHeight = length(P);
		const float3 UpVector = P / pHeight;
		
		float3 S = float3(0,0,0);
		
		// Process all lights
		[unroll]
		for(uint i = 0; i < LightCount; i++)
		{
    		bool shouldProcessLight = LightParams[i].IsSun || (USE_SECONDARY_LIGHT && !LightParams[i].IsSun);
   			if(!shouldProcessLight) continue;

			float SunZenithCosAngle = dot(LightParams[i].Reversed? -LightParams[i].Direction : LightParams[i].Direction, UpVector);
			
			float3 TransmittanceToSun = 0.0f;
			// Special handling for city lights (last light)
			if(LightParams[i].Reversed)
			{
				if(LightParams[i].ReversedLightRadius > 0){
					float radiusParam = saturate(LightParams[i].ReversedLightRadius / Atmosphere.BottomRadius);
					float pReversedBottom = raySphereIntersectNearest(P, LightParams[i].Direction, earthO, Atmosphere.BottomRadius);
					float pReversedRadius = raySphereIntersectNearest(P, LightParams[i].Direction, earthO, Atmosphere.BottomRadius * radiusParam);
					if(pReversedBottom > 0 && pReversedRadius > 0 && SunZenithCosAngle > 0){
						// Add safety checks to prevent division by zero and invalid values
						float denominator = max(Atmosphere.BottomRadius, 0.0001); // Prevent division by zero
						float numerator = pHeight * SunZenithCosAngle - pReversedBottom;
						float CosAlpha = clamp(numerator / denominator, -1.0, 1.0); // Clamp to valid cosine range
						float2 uv2;
						LutTransmittanceParamsToUv(Atmosphere, Atmosphere.BottomRadius, CosAlpha, uv2);
						float sqrtTerm = sqrt(1.0 - CosAlpha * CosAlpha);
						float attenuation = 1.0 - saturate(sqrtTerm / radiusParam);
						attenuation = saturate(attenuation); // Clamp to [0,1] range
						TransmittanceToSun = _TransmittanceLutTextureReadOnly.SampleLevel(ce_Sampler_Clamp, uv2, 0).rgb / _TransmittanceLutTextureReadOnly.SampleLevel(ce_Sampler_Clamp, uv, 0).rgb * attenuation;
					}
				}
			}
			else
			{
			    float2 TransmittanceUV;
			    LutTransmittanceParamsToUv(Atmosphere, pHeight,  SunZenithCosAngle, TransmittanceUV);
				TransmittanceToSun = _TransmittanceLutTextureReadOnly.SampleLevel(ce_Sampler_Clamp, TransmittanceUV, 0).rgb;
			}

			float3 PhaseTimesScattering;
			if (MieRayPhase)
			{
				PhaseTimesScattering = medium.scatteringMie * MiePhaseValues[i] + medium.scatteringRay * RayleighPhaseValues[i];
			}
			else
			{
				PhaseTimesScattering = medium.scattering * uniformPhase;
			}

			// Dual scattering for multi scattering

			float3 multiScatteredLuminance = 0.0f;
        	if(MULTISCATAPPROX_ENABLED && LightParams[i].IsSun){
				multiScatteredLuminance = GetMultipleScattering(Atmosphere, medium.scattering, medium.extinction, P, SunZenithCosAngle);
			}
		    
			// Calculate shadows and scattering contribution for each light
			float shadow = 1.0f;
			float earthShadow = 1.0f;
			
			// Only calculate earth shadow and VSM shadows for sun lights (not city lights)
			if(!LightParams[i].Reversed)
			{
				float tEarth = raySphereIntersectNearest(P, LightParams[i].Direction, earthO + PLANET_RADIUS_OFFSET * UpVector, Atmosphere.BottomRadius);
				earthShadow = tEarth >= 0.0f ? 0.0f : 1.0f;

				float3 ShadowP = t * ToCEVec(WorldDir) * KM2CM + ce_CameraPos;
				
#if SHADOWMAP_ENABLED && ATMOSPHERE_USE_VSM
				int VSMId = (i == 0) ? VirtualShadowMapId0 : VirtualShadowMapId1;
				if (VSMId != INDEX_NONE)
				{
					shadow *= GetDirectionalShadowAttenuation_Direct(ShadowP, 0, VSMId);
				}
#endif

#if SAMPLE_CLOUD_SHADOW
				shadow *= GetAvgCloudShadow(ShadowP);
#endif
			}

			// Add this light's contribution
			float3 contribution = globalL[i] * earthShadow * shadow * TransmittanceToSun * PhaseTimesScattering;
			
			// Add multi-scattered luminance only for the first (main) light
			if(LightParams[i].IsSun)
			{
				contribution += globalL[i] * multiScatteredLuminance * medium.scattering;
			}
			
			S += contribution;
		}
	    
		// When using the power serie to accumulate all sattering order, serie r must be <1 for a serie to converge.
		// Under extreme coefficient, MultiScatAs1 can grow larger and thus result in broken visuals.
		// The way to fix that is to use a proper analytical integration as proposed in slide 28 of http://www.frostbite.com/2015/08/physically-based-unified-volumetric-rendering-in-frostbite/
		// However, it is possible to disable as it can also work using simple power serie sum unroll up to 5th order. The rest of the orders has a really low contribution.
#define MULTI_SCATTERING_POWER_SERIE 0
	    const float3 SafeMediumExtinction = max(medium.extinction, 1.e-9);
#if MULTI_SCATTERING_POWER_SERIE==0 
	    result.MultiScatAs1 += throughput * medium.scattering * 1 * dt;
#else
	    float3 MS = medium.scattering * 1;
	    float3 MSint = (MS - MS * SampleTransmittance) / SafeMediumExtinction;
	    result.MultiScatAs1 += throughput * MSint;
#endif

#if 0
		L += throughput * S * dt;
		throughput *= SampleTransmittance;
#else

	    // TODO(scolu): Check fucking SampleTransmittance
	    // See slide 28 at http://www.frostbite.com/2015/08/physically-based-unified-volumetric-rendering-in-frostbite/ 
	    float3 Sint			= (S        - S        * SampleTransmittance) / SafeMediumExtinction;	// integrate along the current step segment 
	    L			+= throughput * Sint;														// accumulate and also take into account the transmittance from previous steps
	    throughput	*= SampleTransmittance;
#endif

		tPrev = t;
	}

	if (ground && tMax == tBottom && tBottom > 0.0)
	{
		// Account for bounced light off the earth
		float3 P = WorldPos + tBottom * WorldDir;
		float pHeight = length(P);

		float3 SunDir0 = LightParams[0].Direction;
		float3 SunDir1 = LightParams[1].Direction;

		const float3 UpVector = P / pHeight;
		float SunZenithCosAngle0 = dot(SunDir0, UpVector);
		float2 uv2;
		LutTransmittanceParamsToUv(Atmosphere, pHeight, SunZenithCosAngle0, uv2);
		float3 TransmittanceToSun0 = _TransmittanceLutTextureReadOnly.SampleLevel(ce_Sampler_Clamp, uv2, 0).rgb;

		const float NdotL0 = saturate(dot(normalize(UpVector), normalize(SunDir0)));
		L += globalL[0] * TransmittanceToSun0 * throughput * NdotL0 * Atmosphere.GroundAlbedo / PI_SA;
		if(USE_SECONDARY_LIGHT)
		{
			float SunZenithCosAngle1 = dot(SunDir1, UpVector);
			LutTransmittanceParamsToUv(Atmosphere, pHeight, SunZenithCosAngle1, uv2);
			float3 TransmittanceToSun1 = _TransmittanceLutTextureReadOnly.SampleLevel(ce_Sampler_Clamp, uv2, 0).rgb;

			const float NdotL1 = saturate(dot(normalize(UpVector), normalize(SunDir1)));
			L += globalL[1] * TransmittanceToSun1 * throughput * NdotL1 * Atmosphere.GroundAlbedo / PI_SA;
		}
	}

	result.L = L;
	result.OpticalDepth = OpticalDepth;
	result.Transmittance = throughput;
	return result;
}

float3 ComputeTransmittanceToTopAtmosphereBoundaryTexture(float2 frag_cord)
{
	float2 pixPos = frag_cord.xy + float2(0.5, 0.5);
    
	AtmosphereParametersUE Atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);
	// Compute camera position from LUT coords
	float2 uv = (pixPos) / float2(TRANSMITTANCE_TEXTURE_WIDTH, TRANSMITTANCE_TEXTURE_HEIGHT);
	float viewHeight;
	float viewZenithCosAngle;
	UvToLutTransmittanceParams(Atmosphere, viewHeight, viewZenithCosAngle, uv);

	//  A few extra needed constants
	float3 WorldPos = float3(0.0f, 0.0f, viewHeight);
	float3 WorldDir = float3(0.0f, sqrt(1.0 - viewZenithCosAngle * viewZenithCosAngle), viewZenithCosAngle);

	SamplingSetup Sampling = (SamplingSetup)0;
	{
		Sampling.VariableSampleCount = false;
		Sampling.SampleCountIni = 10.0f; 	// Can go a low as 10 sample but energy lost starts to be visible.
		//Sampling.MinSampleCount = SkyAtmosphere.SampleCountMin;
		//Sampling.MaxSampleCount = SkyAtmosphere.SampleCountMax;
		//Sampling.DistanceToSampleCountMaxInv = SkyAtmosphere.DistanceToSampleCountMaxInv;
	}
	const bool ground = false;
	const float DepthBufferValue = -1.0;
	const bool MieRayPhase = false;
	AtmosphereLightParam LightParams[3];
	LightParams[0].Direction = sun_direction0;
	LightParams[0].Illuminance = ce_AtmosphereLightData[0].LightIlluminanceOuterSpace;
	LightParams[0].IsSun = true;
	LightParams[0].Reversed = false;
	LightParams[0].ReversedLightRadius = 0;

	LightParams[1].Direction = sun_direction1;
	LightParams[1].Illuminance = ce_AtmosphereLightData[1].LightIlluminanceOuterSpace;
	LightParams[1].IsSun = false;
	LightParams[1].Reversed = false;
	LightParams[1].ReversedLightRadius = 0;

	LightParams[2].Direction = city_direction;
	LightParams[2].Illuminance = ce_AtmosphereLightData[2].LightIlluminanceOuterSpace;
	LightParams[2].IsSun = false;
	LightParams[2].Reversed = false;
	LightParams[2].ReversedLightRadius = 0;

    SingleScatteringResult ss = IntegrateScatteredLuminance(uv, WorldPos, WorldDir, LightParams, 3, Atmosphere, ground, Sampling, DepthBufferValue, MieRayPhase);

	float3 transmittance = exp(-ss.OpticalDepth);

	// Opetical depth to transmittance
	return transmittance;
}

float3 ComputeCameraPosition(float bottomRadius, float3 TilePosition = float3(0, 0, 0))
{
	float UnitToKMScaler = UnitM ? 0.001f : 0.00001f;
    float3 camera = GetLargeCoordinateReltvPosition(ce_CameraPos, TilePosition, float3(0.0, 0.0, 0.0)) * UnitToKMScaler;

	if (PLANET_TOP_AT_ORIGIN)
	{
		camera = camera + float3(0, bottomRadius, 0);
	}

	if(length(camera) <= bottomRadius + PLANET_RADIUS_OFFSET)
	{
		camera = normalize(camera) * (bottomRadius + PLANET_RADIUS_OFFSET);
	}
	return ToUEVec(camera);
}
