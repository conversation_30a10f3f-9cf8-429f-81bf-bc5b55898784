#define <PERSON>AT<PERSON><PERSON> [flatten]
#define UNROLL  [unroll]
#define BRANCH  [branch]

#define DOWNSAMPLE_QUALITY_LOW 0
#define DOWNSAMPLE_QUALITY_HIGH 1

#ifndef DOWNSAMPLE_QUALITY
// #error DOWNSAMPLE_QUALITY is not defined.
#define DOWNSAMPLE_QUALITY DOWNSAMPLE_QUALITY_HIGH
#endif

Texture2D InputTexture;
SamplerState ce_Sampler_Clamp;

cbuffer constant
{
    float2 Input_UVViewportBilinearMin;
    float2 Input_UVViewportBilinearMax;
    float2 Input_ExtentInverse;
    float2 Output_ExtentInverse;
#ifdef COMPUTESHADER
    uint2 Output_ViewportMin;
#endif
}

float4 SampleInput(float2 UV)
{
	UV = clamp(UV, Input_UVViewportBilinearMin, Input_UVViewportBilinearMax);

    return InputTexture.SampleLevel(ce_Sampler_Clamp, UV, 0);
}

float4 DownsampleCommon(float2 UV)
{
	float4 OutColor;

#if DOWNSAMPLE_QUALITY == DOWNSAMPLE_QUALITY_LOW
	// Output: low quality, 1 filtered sample
	OutColor = SampleInput(UV);
#elif DOWNSAMPLE_QUALITY == DOWNSAMPLE_QUALITY_HIGH
	// Output: float4(RGBA), 4 filtered samples
	float2 UVs[4];

	// Blur during downsample (4x4 kernel) to get better quality especially for HDR content.
	UVs[0] = UV + Input_ExtentInverse * float2(-1, -1);
	UVs[1] = UV + Input_ExtentInverse * float2( 1, -1);
	UVs[2] = UV + Input_ExtentInverse * float2(-1,  1);
	UVs[3] = UV + Input_ExtentInverse * float2( 1,  1);

	float4 Sample[4];

	UNROLL
	for(uint i = 0; i < 4; ++i)
	{
		Sample[i] = SampleInput(UVs[i]);
	}

	OutColor = (Sample[0] + Sample[1] + Sample[2] + Sample[3]) * 0.25f;

	// Fixed rarely occurring yellow color tint of the whole viewport (certain viewport size, need to investigate more)
	OutColor.rgb = max(float3(0,0,0), OutColor.rgb);
#else
#error Invalid quality level specified.
#endif

	return OutColor;
}
