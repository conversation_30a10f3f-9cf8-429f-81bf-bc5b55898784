#pragma compute MainCS
//#pragma enable debug_symbol

#include "common.h"

#define THREADGROUP_SIZEX		16
#define THREADGROUP_SIZEY		16
#define THREADGROUP_TOTALSIZE	(THREADGROUP_SIZEX * THREADGROUP_SIZEY)

groupshared float4 Shared[THREADGROUP_TOTALSIZE];

cbuffer constant
{
    uint2 		ViewportMin;
    uint2 		ViewportMax;
	float4x4 	fReprojection;
	float4 		DeviceToViewDepth;
    float 		VelocityScale;
    float 		VelocityMax;
	float		DownscaleValue;
}

Texture2D DepthTexture;
Texture2D VelocityTexture;
RWTexture2D<float4> OutVelocityFlatTexture;
RWTexture2D<float4>	OutVelocityTileTexture;

int2 ComputeLrPosFromHrPos(int2 iPxHrPos, float DownscaleFactor)
{
    const float2 fSamplePosHr = iPxHrPos + 0.5f;
    const float2 fPxLrPos = fSamplePosHr * DownscaleFactor;                   // Source resolution output pixel center position
    const int2 iPxLrPos = int2(floor(fPxLrPos));							  // TD: what about weird upscale factors...
	return iPxLrPos;
}

[numthreads(THREADGROUP_SIZEX, THREADGROUP_SIZEY, 1)]
void MainCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID,
	uint GroupIndex : SV_GroupIndex) 
{ 
	int2 PixelPos = min(DispatchThreadId.xy + ViewportMin, ViewportMax - 1);
	int2 DepthOffset = 0;

	{
		const int Cross = 1;
		// For motion vector, use camera/dynamic motion from min depth pixel in pattern around pixel.
		// This enables better quality outline on foreground against different motion background.
		
		float4 Depths;
		Depths.x = DepthTexture[ PixelPos + int2(-Cross, -Cross) ].x;
		Depths.y = DepthTexture[ PixelPos + int2( Cross, -Cross) ].x;
		Depths.z = DepthTexture[ PixelPos + int2(-Cross,  Cross) ].x;
		Depths.w = DepthTexture[ PixelPos + int2( Cross,  Cross) ].x;

		DepthOffset = Cross;
		int DepthOffsetXx = Cross;

		// Nearest depth is the largest depth (depth surface 0=far, 1=near).
		if(Depths.x > Depths.y) 
		{
			DepthOffsetXx = -Cross;
		}
		if(Depths.z > Depths.w) 
		{
			DepthOffset.x = -Cross;
		}
		float DepthsXY = max(Depths.x, Depths.y);
		float DepthsZW = max(Depths.z, Depths.w);
		if(DepthsXY > DepthsZW) 
		{
			DepthOffset.y = -Cross;
			DepthOffset.x = DepthOffsetXx; 
		}
	}
	
	int2 LrVelocityOffsetPixelPos = ComputeLrPosFromHrPos(PixelPos + DepthOffset, DownscaleValue);

	float2 EncodeVelocity = VelocityTexture[ LrVelocityOffsetPixelPos ];
	float2 Velocity = 0;

	if (EncodeVelocity.x > 0)
	{
		Velocity = DecodeVelocityFromTexture(EncodeVelocity);
	}
	else
	{
		Velocity = ComputeStaticVelocity(PixelPos, fReprojection, ViewportMax, DepthTexture[ PixelPos ].x);
	}

    Velocity *= float2(1, -float(ViewportMax.y) / ViewportMax.x);

    // // Apply the exposure time and convert to the pixel space.
    // Velocity *= VelocityScale / _VelocityTexture_TexelSize;

    // // Clamp the vector with the maximum blur radius.
    // Velocity /= max(1, length(Velocity) * _RcpMaxBlurRadius);
	
	float2 VelocityPolar = CartesianToPolar(Velocity);

	// If the velocity vector was zero length, VelocityPolar will contain NaNs.
	if (any(isnan(VelocityPolar)))
	{
		VelocityPolar = float2(0.0f, 0.0f);
	}

	bool bInsideViewport = all(PixelPos.xy < ViewportMax);

	float2 EncodedPolarVelocity;
	EncodedPolarVelocity.x = VelocityPolar.x;
	EncodedPolarVelocity.y = VelocityPolar.y * (0.5 / PI) + 0.5;

	float Depth = DepthTexture[ PixelPos + DepthOffset ].x;
	uint2 OutVelocityFlatPos = bInsideViewport ? uint2(PixelPos) : uint(~0).xx;
	
	// Pack into 11:11:10 UFloat (VelocityLength, VelocityAngle, Depth)
	// OutVelocityFlatTexture[OutVelocityFlatPos] = float4(EncodedPolarVelocity, SceneDepthFromDeviceZ(Depth, DeviceToViewDepth), 0);
	OutVelocityFlatTexture[OutVelocityFlatPos] = float4(EncodedPolarVelocity, ConvertFromDeviceZ(Depth, DeviceToViewDepth), 0);

	// Limit velocity
	VelocityPolar.x = min(VelocityPolar.x, VelocityMax / VelocityScale);

	float4 VelocityMinMax = VelocityPolar.xyxy;
	VelocityMinMax.x = bInsideViewport ? VelocityMinMax.x : 2;
	VelocityMinMax.z = bInsideViewport ? VelocityMinMax.z : 0;

	Shared[GroupIndex] = VelocityMinMax;

	GroupMemoryBarrierWithGroupSync();

#if THREADGROUP_TOTALSIZE > 512
	if (GroupIndex < 512) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex + 512]);
	GroupMemoryBarrierWithGroupSync();
#endif
#if THREADGROUP_TOTALSIZE > 256
	if (GroupIndex < 256) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex + 256]);
	GroupMemoryBarrierWithGroupSync();
#endif
#if THREADGROUP_TOTALSIZE > 128
	if (GroupIndex < 128) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex + 128]);
	GroupMemoryBarrierWithGroupSync();
#endif
#if THREADGROUP_TOTALSIZE > 64
	if (GroupIndex <  64) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex +  64]);
	GroupMemoryBarrierWithGroupSync();
#endif

	// Safe for vector sizes 32 or larger, AMD and NV
	// TODO Intel variable size vector
	if (GroupIndex < 32) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex + 32]);
	if (GroupIndex < 16) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex + 16]);
	if (GroupIndex <  8) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex +  8]);
	if (GroupIndex <  4) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex +  4]);
	if (GroupIndex <  2) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex +  2]);
	if (GroupIndex <  1) Shared[GroupIndex] = MinMaxLengthPolar(Shared[GroupIndex], Shared[GroupIndex +  1]);

	uint2 OutVelocityTilePos = GroupIndex == 0 ? GroupId : uint(~0).xx;
	OutVelocityTileTexture[OutVelocityTilePos] = float4(PolarToCartesian(Shared[0].xy), PolarToCartesian(Shared[0].zw));
}