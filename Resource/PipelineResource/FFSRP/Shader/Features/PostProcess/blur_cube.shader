#pragma vertex VSMain
#pragma pixel PSMain
#pragma keyword _REVERSE_Z

TextureCube<float4> _InColorMap : register(space0);
SamplerState ce_Sampler_Point : register(space0);

cbuffer para : register(space0)
{
    matrix _InvViewProjMatrix;
    float _BlurFactor;
}

#define _NumSamples 256

#define PI 3.1415926

float RadicalInverse(uint bits) {
    //reverse bit
    //高低16位换位置
    bits = (bits << 16u) | (bits >> 16u);
    //A是5的按位取反
    bits = ((bits & 0x55555555) << 1u) | ((bits & 0xAAAAAAAA) >> 1u);
    //C是3的按位取反
    bits = ((bits & 0x33333333) << 2u) | ((bits & 0xCCCCCCCC) >> 2u);
    bits = ((bits & 0x0F0F0F0F) << 4u) | ((bits & 0xF0F0F0F0) >> 4u);
    bits = ((bits & 0x00FF00FF) << 8u) | ((bits & 0xFF00FF00) >> 8u);
    return  float(bits) * 2.3283064365386963e-10;
}

float2 Hammersley(uint i, uint N) {
    return float2(float(i) / float(N), RadicalInverse(i));
}

float3 ImportanceSampleGGX(float2 Xi, float Roughness, float3 N)
{
    float a = Roughness * Roughness;
    float Phi = 2 * PI * Xi.x;
    float CosTheta = sqrt((1 - Xi.y) / (1 + (a * a - 1) * Xi.y));
    float SinTheta = sqrt(1 - CosTheta * CosTheta);
    float3 H;
    H.x = SinTheta * cos(Phi);
    H.y = SinTheta * sin(Phi);
    H.z = CosTheta;
    float3 UpVector = abs(N.z) < 0.999 ? float3(0, 0, 1) : float3(1, 0, 0);
    float3 TangentX = normalize(cross(UpVector, N));
    float3 TangentY = cross(N, TangentX);
    // Tangent to world space
    return TangentX * H.x + TangentY * H.y + N * H.z;
}


float3 PrefilterEnvMap(float Roughness, float3 R)
{
    float3 N = R;
    float3 V = R;
    float3 PrefilteredColor = 0;
    const uint NumSamples = (uint)(_NumSamples);// 1024;
    float TotalWeight = 0;
    for (uint i = 0; i < NumSamples; i++)
    {
        float2 Xi = Hammersley(i, NumSamples);
        float3 H = ImportanceSampleGGX(Xi, Roughness, N);
        float3 L = 2 * dot(V, H) * H - V;
        float NoL = saturate(dot(N, L));
        if (NoL > 0)
        {
            PrefilteredColor += _InColorMap.Sample(ce_Sampler_Point, L).rgb * NoL;
            TotalWeight += NoL;
        }
    }
    return PrefilteredColor / TotalWeight;
}


struct VS2PS
{
	float4 Pos : 		SV_POSITION;
	float2 UV : 		TEXCOORD0;
};

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos = Pos;
	Outpos.xy = -1.0f + 2.0f * Pos.xy;
	Outpos.xy *= float2(1, -1);
	ret.Pos = Outpos;
	ret.UV = uv;

	return ret;
}


float4 PSMain(VS2PS input) : SV_TARGET
{
	//return _InColorMap.Sample(ce_Sampler_Clamp, input.UV);
    float2 uv = float2(input.UV.x,1.0 - input.UV.y);
#ifdef _REVERSE_Z
    float4 positionCS = float4(uv * 2.0 - 1.0, 0.0, 1.0);
#else
    float4 positionCS = float4(uv * 2.0 - 1.0, 1.0, 1.0);
#endif

    float4 hpositionWS = mul(_InvViewProjMatrix, positionCS);
    float3 positionWS = hpositionWS.xyz / hpositionWS.w;
    float3 sampleDir = normalize(positionWS);

    //return _InColorMap.Sample(ce_Sampler_Point, sampleDir);
    return float4(PrefilterEnvMap(_BlurFactor, sampleDir), 1.0);
}