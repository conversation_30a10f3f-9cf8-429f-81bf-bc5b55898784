#include "WWiseAudioEngine.h"
#include "Geometry.h"
#include "CrossBase/Log.h"
#include "CECommon/Geometry/Intersection.h"

#include <AK/SoundEngine/Common/AkMemoryMgr.h>
#include <AK/SoundEngine/Common/AkMemoryMgrModule.h>
#include <AK/SoundEngine/Common/AkSoundEngine.h>
#include <AK/MusicEngine/Common/AkMusicEngine.h>
#include <AK/SpatialAudio/Common/AkSpatialAudio.h>
#include <AK/Plugin/AllPluginsFactories.h>

static constexpr wchar_t const * const BANKNAME_INIT = L"Init";
static constexpr float const DEFAULT_LENGTH_TO_METER = 1 / 100.0f; // can not use AkInitSettings::fGameUnitsToMeters, it may be not supported by AK::SpatialAudio
static constexpr cross::AudioEngine::ObjectID const DEFAULT_LISTENER_ID = 0;

namespace cross
{
    static AkCurveInterpolation Convert(AudioEngine::CurveInterpolation const c)
    {
        switch(c)
        {
        case AudioEngine::CurveInterpolation::Linear:
            return AkCurveInterpolation_Linear;
        case AudioEngine::CurveInterpolation::Log1:
            return AkCurveInterpolation_Log1;
        case AudioEngine::CurveInterpolation::Log3:
            return AkCurveInterpolation_Log3;
        case AudioEngine::CurveInterpolation::Sine:
            return AkCurveInterpolation_Sine;
        case AudioEngine::CurveInterpolation::SCurve:
            return AkCurveInterpolation_SCurve;
        case AudioEngine::CurveInterpolation::InvSCurve:
            return AkCurveInterpolation_InvSCurve;
        case AudioEngine::CurveInterpolation::Exp1:
            return AkCurveInterpolation_Exp1;
        case AudioEngine::CurveInterpolation::Exp3:
            return AkCurveInterpolation_Exp3;
        case AudioEngine::CurveInterpolation::SineRecip:
            return AkCurveInterpolation_SineRecip;
        case AudioEngine::CurveInterpolation::Constant:
            return AkCurveInterpolation_Constant;
        }
        return AkCurveInterpolation_Linear;
    }

    static void GetFrontAndUpNormalized(Double4x4 const & transform, Double3 & position, Float3 & front, Float3 & up)
    {
        position = Double4x4::TransformPointF3(transform, Double3(0, 0, 0));
        front = Float3(Double4x4::Transform(transform, Double4(0, 0, 1, 0)).XYZ());
        up = Float3(Double4x4::Transform(transform, Double4(0, 1, 0, 0)).XYZ());
        front.Normalize();
        up.Normalize();
    }
    static void GetFrontAndUpNotNormalized(Double4x4 const & transform, Double3 & position, Float3 & front, Float3 & up)
    {
        position = Double4x4::TransformPointF3(transform, Double3(0, 0, 0));
        front = Float3(Double4x4::Transform(transform, Double4(0, 0, 1, 0)).XYZ());
        up = Float3(Double4x4::Transform(transform, Double4(0, 1, 0, 0)).XYZ());
    }

    // constructor and destructor
    WWiseAudioEngine::WWiseAudioEngine()
        : cross::threading::IRunnable(cross::threading::ThreadID::ExternalThread)
    {
    }

    WWiseAudioEngine::~WWiseAudioEngine()
    {
    }

    AudioEngine::Result WWiseAudioEngine::Init(Char const * const path, Char const * const language)
    {
        AkMemSettings mem_settings;
        AK::MemoryMgr::GetDefaultSettings(mem_settings);
        if ( AK::MemoryMgr::Init( &mem_settings ) != AK_Success )
        {
            LOG_ERROR("Could not create the memory manager.");
            return -1;
        }
    
        AkStreamMgrSettings stm_settings;
        AK::StreamMgr::GetDefaultSettings( stm_settings );
    
        if ( !AK::StreamMgr::Create( stm_settings ) )
        {
            LOG_ERROR("Could not create the Streaming Manager.");
            return -1;
        }
    
        AkDeviceSettings dev_settings;
        AK::StreamMgr::GetDefaultDeviceSettings( dev_settings );
    
        if ( AK_Success != mLowLevelIO.Init(dev_settings) )
        {
            LOG_ERROR("Could not create the streaming device and Low-Level I/O system.");
            return -1;
        }

        AK::SoundEngine::RegisterGlobalCallback(StaticGlobalCallbackFunc
            , AkGlobalCallbackLocation_Begin | AkGlobalCallbackLocation_End
            , this);

        AkInitSettings init_settings;
        AkPlatformInitSettings plat_settings;
        AK::SoundEngine::GetDefaultInitSettings( init_settings );
        AK::SoundEngine::GetDefaultPlatformInitSettings( plat_settings );
        //init_settings.bUseLEngineThread = false;
        if ( AK::SoundEngine::Init( &init_settings, &plat_settings ) != AK_Success )
        {
            LOG_ERROR("Could not initialize the Sound Engine.");
            return -1;
        }

        AkMusicSettings mus_settings;
        AK::MusicEngine::GetDefaultInitSettings( mus_settings );
        if ( AK::MusicEngine::Init( &mus_settings ) != AK_Success )
        {
            LOG_ERROR("Could not initialize the Music Engine.");
            return -1;
        }

        AkSpatialAudioInitSettings spa_settings;
        if ( AK::SpatialAudio::Init( spa_settings ) != AK_Success )
        {
            LOG_ERROR("Could not initialize the Spatial Audio.");
            return -1;
        }

        mRootPath = path;
        mLanguage = language;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::Deinit()
    {
        //AK::SpatialAudio::Term();
        AK::MusicEngine::Term();
        AK::SoundEngine::Term();
        AK::SoundEngine::UnregisterGlobalCallback(StaticGlobalCallbackFunc
            , AkGlobalCallbackLocation_Begin | AkGlobalCallbackLocation_End);
        mLowLevelIO.Term();
        if ( AK::IAkStreamMgr::Get() )
            AK::IAkStreamMgr::Get()->Destroy();
        AK::MemoryMgr::Term();
        return 0;
    }

    // processing
    AudioEngine::Result WWiseAudioEngine::Begin()
    {
        if(mRootPath.length())
        {
            std::string const current_path = PathHelper::GetCurrentDirectoryPath();
            std::string const config_path = current_path + mRootPath;
            AkOSChar* config_path_os = nullptr;
            CONVERT_CHAR_TO_OSCHAR(config_path.c_str(), config_path_os);
            //std::wstring const wpath = ConvertUTF8toUTF16(config_path);
            if (AK_Success != mLowLevelIO.SetBasePath(config_path_os))
            {
                LOG_ERROR("Could not SetBasePath.");
                return -1;
            }

            if (mLanguage.length())
            {
                AkOSChar* language_os = nullptr;
                CONVERT_CHAR_TO_OSCHAR(mLanguage.c_str(), language_os);
                //std::wstring const wlanguage = ConvertUTF8toUTF16(language);
                if (AK_Success != AK::StreamMgr::SetCurrentLanguage(language_os))
                {
                    LOG_ERROR("Could not SetCurrentLanguage.");
                    return -1;
                }
            }

            AkBankID bank_id;
            if (AK_Success != AK::SoundEngine::LoadBank(BANKNAME_INIT, bank_id))
            {
                LOG_ERROR("Could not load bank INIT.");
                return -1;
            }
        }

        if (SpatialSetOutside("Outside", 1.0f, 0.0f))
            return -1;

        {
            AK::SoundEngine::RegisterGameObj(DEFAULT_LISTENER_ID, "Listener (Default)");
            AK::SoundEngine::SetDefaultListeners(&DEFAULT_LISTENER_ID, 1);
        }

        mHasBegan = true;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::End()
    {
        mHasBegan = false;

        // stop all
        {
            AK::SoundEngine::StopAll();
        }

        {
            AK::SoundEngine::UnregisterGameObj(DEFAULT_LISTENER_ID);
        }

        // clear call backs
        {
            std::lock_guard<std::mutex> lock_guard(mEndCallbacksMutex);
            mEndCallbacks.clear();
        }
        {
            std::lock_guard<std::mutex> lock_guard(mMarkerCallbacksMutex);
            mMarkerCallbacks.clear();
        }

        // clear spatial audio
        for (std::pair<PortalID, Portal> const& portal_pair : mPortals)
            (AK_Success != AK::SpatialAudio::RemovePortal(portal_pair.first));
        mPortals.clear();

        for (std::pair<RoomID, Room> const& room_pair : mRooms)
        {
            (AK_Success != AK::SpatialAudio::RemoveRoom(room_pair.first));
            (AK_Success != AK::SpatialAudio::RemoveGeometryInstance(room_pair.first));
        }
        mRooms.clear();

        for(ObstacleID const & obstacle_id : mObstacles)
            (AK_Success != AK::SpatialAudio::RemoveGeometryInstance(obstacle_id));
        mObstacles.clear();

        for(std::pair<GeometryID, Geometry *> const& geom_pair : mGeometries)
        {
            (AK_Success != AK::SpatialAudio::RemoveGeometry(geom_pair.first));
            delete geom_pair.second;
        }
        mGeometries.clear();

        // clear banks // not necessary, read ClearBanks for more information
        //for (std::pair<ID, Bank> const& bank_pair : mBanks)
        //    AK::SoundEngine::PrepareBank(AK::SoundEngine::Preparation_Unload, bank_pair.first);
        //mBanks.clear();

        // clear resources
        //(AK_Success != AK::SoundEngine::ClearPreparedEvents()); // not necessary, read ClearBanks for more information
        (AK_Success != AK::SoundEngine::ClearBanks());
        
        // clear basic resources
        AK::SpatialAudio::RemoveRoom(AK::SpatialAudio::kOutdoorRoomID);
        //AK::SoundEngine::UnloadBank(BANKNAME_INIT, NULL); // already done in ClearBanks();
        return 0;
    }

    void WWiseAudioEngine::StaticCallback(AkCallbackType const type, AkCallbackInfo* const callback_info)
    {
        reinterpret_cast<WWiseAudioEngine *>(callback_info->pCookie)->WWiseCallback(type, callback_info);
    }
    void WWiseAudioEngine::StaticBusCallbackFunc(AkSpeakerVolumeMatrixCallbackInfo* const callback_info)
    {
        reinterpret_cast<WWiseAudioEngine *>(callback_info->pCookie)->WWiseBusCallbackFunc(callback_info);
    }
    void WWiseAudioEngine::StaticBusMeteringCallbackFunc(AkBusMeteringCallbackInfo* const callback_info)
    {
        reinterpret_cast<WWiseAudioEngine *>(callback_info->pCookie)->WWiseBusMeteringCallbackFunc(callback_info);
    }
    void WWiseAudioEngine::StaticOutputDeviceMeteringCallbackFunc(AkOutputDeviceMeteringCallbackInfo * const callback_info)
    {
        reinterpret_cast<WWiseAudioEngine *>(callback_info->pCookie)->WWiseOutputDeviceMeteringCallbackFunc(callback_info);
    }
    void WWiseAudioEngine::StaticBankCallbackFunc(AkUInt32 const bank_id, void const * const memory_bank_ptr, AKRESULT const load_result, void * const cookie)
    {
        reinterpret_cast<WWiseAudioEngine *>(cookie)->WWiseBankCallbackFunc(bank_id, memory_bank_ptr, load_result, cookie);
    }
    void WWiseAudioEngine::StaticGlobalCallbackFunc(AK::IAkGlobalPluginContext * const context, AkGlobalCallbackLocation const location, void * const cookie)
    {
        reinterpret_cast<WWiseAudioEngine *>(cookie)->WWiseGlobalCallbackFunc(context, location, cookie);
    }
    void WWiseAudioEngine::StaticResourceMonitorCallbackFunc(AkResourceMonitorDataSummary* const data_summary)
    {
    }

    void WWiseAudioEngine::WWiseCallback(AkCallbackType const type, AkCallbackInfo* const callback_info)
    {
        switch(type)
        {
        case AK_EndOfEvent:
        {
            AkEventCallbackInfo const* const info = reinterpret_cast<AkEventCallbackInfo const*>(callback_info);
            {
                std::lock_guard<std::mutex> lock_guard(mEndCallbacksMutex);
                MapEndCallback::const_iterator const& cb = mEndCallbacks.find(info->gameObjID);
                if (cb != mEndCallbacks.end())
                {
                    threading::Dispatch<threading::ThreadID::GameThreadLocal>([cb
                        , eventID = info->eventID, playingID = info->playingID]
                        (auto const&) mutable {
                        QUICK_SCOPED_CPU_TIMING("AudioEngine End Callback");
                        cb->second(static_cast<EventID>(eventID), static_cast<EventPtr>(playingID));
                    });
                }
            }
        }
        break;
        case AK_EndOfDynamicSequenceItem:
        {
            AkDynamicSequenceItemCallbackInfo const* const info = reinterpret_cast<AkDynamicSequenceItemCallbackInfo const*>(callback_info);
        }
        break;
        case AK_Marker:
        {
            AkMarkerCallbackInfo const* const info = reinterpret_cast<AkMarkerCallbackInfo const*>(callback_info);
            {
                std::lock_guard<std::mutex> lock_guard(mMarkerCallbacksMutex);
                MapMarkerCallback::const_iterator const& cb = mMarkerCallbacks.find(info->gameObjID);
                if (cb != mMarkerCallbacks.end())
                {
                    threading::Dispatch<threading::ThreadID::GameThreadLocal>([cb
                        , eventID = info->eventID, playingID = info->playingID, identifier = info->uIdentifier, position = info->uPosition, str = std::string(info->strLabel)]
                        (auto const&) mutable {
                        QUICK_SCOPED_CPU_TIMING("AudioEngine Marker Callback");
                        cb->second(static_cast<EventID>(eventID), static_cast<EventPtr>(playingID), static_cast<ID>(identifier), static_cast<TimeMs>(position), str);
                    });
                }
            }
        }
        break;
        case AK_Duration:
        {
            AkDurationCallbackInfo const* const info = reinterpret_cast<AkDurationCallbackInfo const*>(callback_info);
        }
        break;
        case AK_SpeakerVolumeMatrix:
        {
            AkSpeakerVolumeMatrixCallbackInfo const* const info = reinterpret_cast<AkSpeakerVolumeMatrixCallbackInfo const*>(callback_info);
        }
        break;
        case AK_Starvation:
        {
            AkEventCallbackInfo const* const info = reinterpret_cast<AkEventCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicPlaylistSelect:
        {
            AkMusicPlaylistCallbackInfo const* const info = reinterpret_cast<AkMusicPlaylistCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicPlayStarted:
        {
            AkEventCallbackInfo const* const info = reinterpret_cast<AkEventCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicSyncBeat:
        {
            AkMusicSyncCallbackInfo const* const info = reinterpret_cast<AkMusicSyncCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicSyncBar:
        {
            AkMusicSyncCallbackInfo const* const info = reinterpret_cast<AkMusicSyncCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicSyncEntry:
        {
            AkMusicSyncCallbackInfo const* const info = reinterpret_cast<AkMusicSyncCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicSyncExit:
        {
            AkMusicSyncCallbackInfo const* const info = reinterpret_cast<AkMusicSyncCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicSyncGrid:
        {
            AkMusicSyncCallbackInfo const* const info = reinterpret_cast<AkMusicSyncCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicSyncUserCue:
        {
            AkMusicSyncCallbackInfo const* const info = reinterpret_cast<AkMusicSyncCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MusicSyncPoint:
        {
            AkMusicSyncCallbackInfo const* const info = reinterpret_cast<AkMusicSyncCallbackInfo const*>(callback_info);
        }
        break;
        case AK_MIDIEvent:
        {
            AkMIDIEventCallbackInfo const* const info = reinterpret_cast<AkMIDIEventCallbackInfo const*>(callback_info);
        }
        break;
        default:
            break;
        }
    }

    void WWiseAudioEngine::WWiseBusCallbackFunc(AkSpeakerVolumeMatrixCallbackInfo* const callback_info)
    {
    }

    void WWiseAudioEngine::WWiseBusMeteringCallbackFunc(AkBusMeteringCallbackInfo* const callback_info)
    {
    }

    void WWiseAudioEngine::WWiseOutputDeviceMeteringCallbackFunc(AkOutputDeviceMeteringCallbackInfo * const callback_info)
    {
    }

    void WWiseAudioEngine::WWiseBankCallbackFunc(AkUInt32 const bank_id, void const * const memory_bank_ptr, AKRESULT const load_result, void * const cookie)
    {
    }

    void WWiseAudioEngine::WWiseGlobalCallbackFunc(AK::IAkGlobalPluginContext * const context, AkGlobalCallbackLocation const location, void * const cookie)
    {
        switch (location)
        {
        case AkGlobalCallbackLocation_Begin:
            if (false == mThreadAttached)
            {
                mThreadAttached = true;
                threading::TaskSystem::Get()->AttachThread(threading::ThreadID::ExternalThread, this);
            }
            break;
        case AkGlobalCallbackLocation_Register:
        case AkGlobalCallbackLocation_PreProcessMessageQueueForRender:
        case AkGlobalCallbackLocation_PostMessagesProcessed:
        case AkGlobalCallbackLocation_BeginRender:
        case AkGlobalCallbackLocation_EndRender:
        case AkGlobalCallbackLocation_End:
        case AkGlobalCallbackLocation_Term:
        case AkGlobalCallbackLocation_Monitor:
        case AkGlobalCallbackLocation_MonitorRecap:
        case AkGlobalCallbackLocation_Init:
        case AkGlobalCallbackLocation_Suspend:
        case AkGlobalCallbackLocation_WakeupFromSuspend:
        case AkGlobalCallbackLocation_ProfilerConnect:
        case AkGlobalCallbackLocation_ProfilerDisconnect:
        default:
            LOG_TRACE("WWiseAudioEngine::WWiseGlobalCallbackFunc(%X)", location);
            break;
        }
    }

    void WWiseAudioEngine::Run()
    {
        LOG_TRACE("WWiseAudioEngine::Run");
    }

//#pragma optimize( "", off )
    AudioEngine::Result WWiseAudioEngine::Process()
    {
        if(false == mHasBegan)
            return 0;

        if(mPortals.size())
        {
            for(std::pair<PortalID, Portal> const & portal : mPortals)
            {
                // get position and orientation
                Double3 position_cm;
                Float3 front1, up1;
                GetFrontAndUpNotNormalized(portal.second.transform_cm, position_cm, front1, up1);

                // find front and back room
                RoomID frontRoomId = -1;
                RoomID backRoomId = -1;
                Double3 frontVector_cm = Double3(front1) * portal.second.extent_cm.z / 2;
                DetermineRooms(position_cm + frontVector_cm, position_cm - frontVector_cm, frontRoomId, backRoomId);

                // setup parameters
                front1.Normalize();
                up1.Normalize();
                AkPortalParams params;
                params.Transform.SetPosition({DEFAULT_LENGTH_TO_METER * position_cm.x, DEFAULT_LENGTH_TO_METER * position_cm.y, DEFAULT_LENGTH_TO_METER * position_cm.z});
                params.Transform.SetOrientation({front1.x, front1.y, front1.z}, {up1.x, up1.y, up1.z});
                // Portal extent. Defines the dimensions of the portal relative to its center; all components must be positive numbers. The local X and Y dimensions (side and top) are used in diffraction calculations, 
                // whereas the Z dimension (front) defines a depth value which is used to implement smooth transitions between rooms. It is recommended that users experiment with different portal depths to find a value 
                // that results in appropriately smooth transitions between rooms.
                // Important: divide width and height by 2, because Extent expresses dimensions relative to the center (like a radius).
                params.Extent = {DEFAULT_LENGTH_TO_METER * portal.second.extent_cm.x / 2, DEFAULT_LENGTH_TO_METER * portal.second.extent_cm.y / 2, DEFAULT_LENGTH_TO_METER * portal.second.extent_cm.z / 2};
                params.bEnabled = true;
                params.FrontRoom = frontRoomId == -1 ? AK::SpatialAudio::kOutdoorRoomID : AkRoomID(frontRoomId);
                params.BackRoom = backRoomId == -1 ? AK::SpatialAudio::kOutdoorRoomID : AkRoomID(backRoomId);
                if (AK_Success != AK::SpatialAudio::SetPortal(portal.first, params))
                    continue;
            }

            mPortals.clear();
        }

        if (AK_Success != AK::SoundEngine::RenderAudio())
            return -1;
        return 0;
    }
//#pragma optimize( "", on )

    // ID and string convertion
    AudioEngine::ID WWiseAudioEngine::StringToID(Char const* const str)
    {
        return AK::SoundEngine::GetIDFromString(str);
    }

    // configuration
    AudioEngine::Result WWiseAudioEngine::LanguageSet(Char const * const language_name)
    {
        AkOSChar* language_name_os = nullptr;
        CONVERT_CHAR_TO_OSCHAR(language_name, language_name_os);
        //std::wstring const wlanguage = ConvertUTF8toUTF16(language_name);
        if (AK_Success != AK::StreamMgr::SetCurrentLanguage(language_name_os))
        {
            LOG_ERROR("Could not SetCurrentLanguage.");
            return -1;
        }
        mLanguage = language_name;

        for(std::pair<ID, Bank> const & bank_pair : mBanks)
        {
            if(bank_pair.second.language_specific)
            {
                (AK_Success != AK::SoundEngine::PrepareBank(AK::SoundEngine::Preparation_Unload, bank_pair.second.name.c_str()));
                if (AK_Success != AK::SoundEngine::PrepareBank(AK::SoundEngine::Preparation_Load, bank_pair.second.name.c_str()))
                    LOG_ERROR("Could not load bank {} when setting language.", bank_pair.second.name);
            }
        }
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::RootpathSet(Char const * const rootpath)
    {
        // unload previous bank
        AK::SoundEngine::UnloadBank(BANKNAME_INIT, NULL);

        // set new root path
        AkOSChar* rootpath_os = nullptr;
        CONVERT_CHAR_TO_OSCHAR(rootpath, rootpath_os);
        if (AK_Success != mLowLevelIO.SetBasePath(rootpath_os))
        {
            LOG_ERROR("Could not SetBasePath.");
            return -1;
        }
        mRootPath = rootpath;

        // loac new bank Init
        AkBankID bank_id;
        if (AK_Success != AK::SoundEngine::LoadBank(BANKNAME_INIT, bank_id))
        {
            LOG_ERROR("Could not load bank INIT.");
            return -1;
        }
        return 0;
    }

    // bank API
    AudioEngine::Result WWiseAudioEngine::BankLoad(Char const * const bank_name)
    {
        //AkBankID bank_id;
        //AKRESULT const load_result = AK::SoundEngine::LoadBank(bank_name, bank_id);
        //if (AK_Success != load_result)
        //{
        //    LOG_ERROR("Load {} failed with reason {}", bank_name, load_result);
        //    return -1;
        //}

        ID const bank_id = AK::SoundEngine::GetIDFromString(bank_name);
        MapBank::iterator bank_it = mBanks.find(bank_id);
        if(bank_it != mBanks.end())
        {
            ++bank_it->second.ref_count;
        }
        else
        {
            AKRESULT const load_result = AK::SoundEngine::PrepareBank(AK::SoundEngine::Preparation_Load, bank_name);
            if (AK_Success != load_result)
            {
                LOG_ERROR("Load {} failed with reason {}", bank_name, load_result);
                return -1;
            }

            // is language specific?
            bool is_language_specific = false;
            AkFileSystemFlags file_flags;
            file_flags.bIsLanguageSpecific = true;
            AkOSChar* bank_name_os = nullptr;
            CONVERT_CHAR_TO_OSCHAR(bank_name, bank_name_os);
            AkOSChar bank_name_ext_os[AK_MAX_PATH];
            AKPLATFORM::SafeStrCpy(bank_name_ext_os, bank_name_os, AK_MAX_PATH);
            AKPLATFORM::SafeStrCat(bank_name_ext_os, L".bnk", AK_MAX_PATH);
            AkFileOpenData file_open(bank_name_ext_os, &file_flags);
            AkFileDesc* out_file = nullptr;
            if(AK_Success == mLowLevelIO.Open(file_open, out_file))
            {
                (AK_Success != mLowLevelIO.Close(out_file));
                is_language_specific = true;
            }

            // insert bank
            mBanks.insert(std::make_pair(bank_id, std::move(Bank(is_language_specific, bank_name))));
        }

        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::BankUnload(Char const * const bank_name)
    {
        //if (AK_Success != AK::SoundEngine::UnloadBank(bank_name, NULL)) return -1;

        ID const bank_id = AK::SoundEngine::GetIDFromString(bank_name);
        MapBank::iterator bank_it = mBanks.find(bank_id);
        if(bank_it != mBanks.end())
        {
            --bank_it->second.ref_count;
            if(bank_it->second.ref_count == 0)
            {
                if (AK_Success != AK::SoundEngine::PrepareBank(AK::SoundEngine::Preparation_Unload, bank_name))
                    return -1;
                mBanks.erase(bank_it);
            }
        }
        else
        {
            LOG_ERROR("There is no bank {}", bank_name);
        }
        return 0;
    }

    // object API
    AudioEngine::Result WWiseAudioEngine::ObjectRegister(ObjectID const object_id)
    {
        if (AK_Success != AK::SoundEngine::RegisterGameObj(object_id, std::to_string(object_id).c_str()))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ObjectUnregister(ObjectID const object_id)
    {
        if (AK_Success != AK::SoundEngine::UnregisterGameObj(object_id))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ObjectSetPose(ObjectID const object_id, Float3 const& position, Float3 const& forward, Float3 const& up)
    {
        AkSoundPosition pos;
        pos.Set(DEFAULT_LENGTH_TO_METER * position.x, DEFAULT_LENGTH_TO_METER * position.y, DEFAULT_LENGTH_TO_METER * position.z
            , forward.x, forward.y, forward.z
            , up.x, up.y, up.z);
        if (AK_Success != AK::SoundEngine::SetPosition(object_id, pos))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ObjectSetPose(ObjectID const object_id, Double3 const& position, Double3 const& forward, Double3 const& up)
    {
        AkSoundPosition pos;
        pos.Set(DEFAULT_LENGTH_TO_METER * position.x, DEFAULT_LENGTH_TO_METER * position.y, DEFAULT_LENGTH_TO_METER * position.z
            , static_cast<AkReal32>(forward.x), static_cast<AkReal32>(forward.y), static_cast<AkReal32>(forward.z)
            , static_cast<AkReal32>(up.x), static_cast<AkReal32>(up.y), static_cast<AkReal32>(up.z));
        if (AK_Success != AK::SoundEngine::SetPosition(object_id, pos))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ObjectSetListeners(ObjectID const object_id, ObjectID const* const listeners, Size const num_listeners)
    {
        std::vector<AkGameObjectID> ids(num_listeners);
        for(Size i = 0; i < num_listeners; ++i)
            ids.at(i) = AkGameObjectID(listeners[i]);
        if (AK_Success != AK::SoundEngine::SetListeners( object_id, ids.data(), AkUInt32(num_listeners)))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ObjectAddListener(ObjectID const object_id, ObjectID const listener)
    {
        if (AK_Success != AK::SoundEngine::AddListener( object_id, listener))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ObjectRemoveListener(ObjectID const object_id, ObjectID const listener)
    {
        if (AK_Success != AK::SoundEngine::RemoveListener( object_id, listener))
            return -1;
        return 0;
    }

	AudioEngine::Result WWiseAudioEngine::ObjectSetVolume(ObjectID const object_id, ObjectID const listener, Float const volume) const
    {
        if (AK_Success != AK::SoundEngine::SetGameObjectOutputBusVolume(object_id, listener, volume))
            return -1;
        return 0;
	}

    // event API
    AudioEngine::Result WWiseAudioEngine::EventPrepare(EventID const* const event_ids, Size const size)
    {
        std::vector<AkUniqueID> ids(size);
        for(Size i = 0; i < size; ++i)
            ids.at(i) = AkUniqueID(event_ids[i]);
        if (AK_Success != AK::SoundEngine::PrepareEvent(AK::SoundEngine::Preparation_Load, ids.data(), AkUInt32(size)))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::EventUnprepare(EventID const* const event_ids, Size const size)
    {
        std::vector<AkUniqueID> ids(size);
        for(Size i = 0; i < size; ++i)
            ids.at(i) = AkUniqueID(event_ids[i]);
        if (AK_Success != AK::SoundEngine::PrepareEvent(AK::SoundEngine::Preparation_Unload, ids.data(), AkUInt32(size)))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::EventPost(ObjectID const object_id, EventID const event_id, EventPtr* const out_event_ptr
            , EndCallback const & end_callback, MarkerCallback const & marker_callback, void* const cookie)
    {
        AkUInt32 flags = 0;
        if (end_callback)
            flags |= AK_EndOfEvent;
        if (marker_callback)
            flags |= AK_Marker;
        *out_event_ptr = AK::SoundEngine::PostEvent(AkUniqueID(event_id), object_id, flags, &StaticCallback, this);
        if (AK_INVALID_PLAYING_ID == *out_event_ptr)
            return -1;
        if (end_callback)
        {
            std::lock_guard<std::mutex> lock_guard(mEndCallbacksMutex);
            mEndCallbacks[object_id] = end_callback;
        }
        if (marker_callback)
        {
            std::lock_guard<std::mutex> lock_guard(mMarkerCallbacksMutex);
            mMarkerCallbacks[object_id] = marker_callback;
        }
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::EventStop(EventPtr const event_ptr, TimeMs const duration, CurveInterpolation const curve)
    {
        AK::SoundEngine::StopPlayingID(AkPlayingID(event_ptr), duration, Convert(curve));
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::EventStopAll(ObjectID const object_id)
    {
        AK::SoundEngine::StopAll(object_id);
        return 0;
    }

    // parameter API
    AudioEngine::Result WWiseAudioEngine::ParamSet(ParamID const param_id, Float const value, TimeMs const duration, CurveInterpolation const curve)
    {
        if (AK_Success != AK::SoundEngine::SetRTPCValue(AkRtpcID(param_id), AkRtpcValue(value), AK_INVALID_GAME_OBJECT, AkTimeMs(duration), Convert(curve)))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ParamSet(ObjectID const object_id, ParamID const param_id, Float const value, TimeMs const duration, CurveInterpolation const curve)
    {
        if (AK_Success != AK::SoundEngine::SetRTPCValue(AkRtpcID(param_id), AkRtpcValue(value), AkGameObjectID(object_id), AkTimeMs(duration), Convert(curve)))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ParamSet(EventPtr const event_ptr, ParamID const param_id, Float const value, TimeMs const duration, CurveInterpolation const curve)
    {
        if (AK_Success != AK::SoundEngine::SetRTPCValueByPlayingID(AkRtpcID(param_id), AkRtpcValue(value), AkPlayingID(event_ptr), AkTimeMs(duration), Convert(curve)))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::ParamReset(ObjectID const object_id, ParamID const param_id, TimeMs const duration, CurveInterpolation const curve)
    {
        if (AK_Success != AK::SoundEngine::ResetRTPCValue(AkRtpcID(param_id), AkGameObjectID(object_id), AkTimeMs(duration), Convert(curve)))
            return -1;
        return 0;
    }

    // switch API
    AudioEngine::Result WWiseAudioEngine::SwitchSet(ObjectID const object_id, SwitchID const switch_id, SwitchValueID const switch_value_id)
    {
        if (AK_Success != AK::SoundEngine::SetSwitch(AkSwitchGroupID(switch_id), AkSwitchStateID(switch_value_id), object_id))
            return -1;
        return 0;
    }

    // state API
    AudioEngine::Result WWiseAudioEngine::StateSet(StateID const state_id, StateValueID const state_value_id)
    {
        if (AK_Success != AK::SoundEngine::SetState(AkStateGroupID(state_id), AkStateID(state_value_id)))
            return -1;
        return 0;
    }

    WWiseAudioEngine::OutputId::OutputId()
        : shareset_name("")
        , shareset_id(0)
        , device_id(0)
        , name("")
    {}

    WWiseAudioEngine::OutputId::OutputId(std::string const & in_sharset_name, AkUInt32 const in_shareset_id, AkUInt32 const in_device_id, std::string const& in_name)
        : shareset_name(in_sharset_name)
        , shareset_id(in_shareset_id)
        , device_id(in_device_id)
        , name(in_name)
    {}

    AudioEngine::Index WWiseAudioEngine::OutputGetNum()
    {
        OutputRefresh();
        return static_cast<Index>(mOutputIds.size());
    }

    AudioEngine::Index WWiseAudioEngine::OutputGetDefault()
    {
        if (mOutputIds.size() == 0)
            OutputRefresh();
        return mOutputDefault;
    }

    AudioEngine::Result WWiseAudioEngine::OutputGetName(Index const output_index, std::string & out_name)
    {
        if (mOutputIds.size() == 0)
            OutputRefresh();
        if (output_index < 0 || output_index >= mOutputIds.size())
            return -1;
        out_name = mOutputIds.at(output_index).name;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::OutputAdd(Index const output_index, ObjectID const object_id)
    {
        if (mOutputIds.size() == 0)
            OutputRefresh();
        if (output_index < 0 || output_index >= mOutputIds.size())
            return -1;
        OutputId const& output = mOutputIds.at(output_index);
        AkOutputSettings outputSettings(output.shareset_name.c_str(), output.device_id);
        if (AK_Success != AK::SoundEngine::AddOutput(outputSettings, nullptr, &object_id, 1))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::OutputRemove(Index const output_index)
    {
        if (mOutputIds.size() == 0)
            OutputRefresh();
        Index oid = output_index;
        if (oid < 0)
            oid = mOutputDefault;
        if (oid < 0 || oid >= mOutputIds.size())
            return -1;

        OutputId const& output = mOutputIds.at(oid);
        if (AK_Success != AK::SoundEngine::RemoveOutput(AK::SoundEngine::GetOutputID(output.shareset_id, output.device_id)))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::OutputReplace(Index const output_index_src, Index const output_index_dest)
    {
        if (mOutputIds.size() == 0)
            OutputRefresh();
        if (output_index_src >= static_cast<int>(mOutputIds.size()))
            return -1;
        if (output_index_src == output_index_dest)
            return 0;

        AkOutputDeviceID output_src_id = 0;
        if (output_index_src >= 0)
        {
            OutputId const& output_src = mOutputIds.at(output_index_src);
            output_src_id = AK::SoundEngine::GetOutputID(output_src.shareset_id, output_src.device_id);
        }

        Index oid = output_index_dest;
        if (oid < 0)
            oid = mOutputDefault;
        if (oid < 0 || oid >= mOutputIds.size())
            return -1;

        OutputId const& output_dest = mOutputIds.at(oid);
        AkOutputSettings outputSettings(output_dest.shareset_name.c_str(), output_dest.device_id);
        if (AK_Success != AK::SoundEngine::ReplaceOutput(outputSettings, output_src_id))
            return -1;
        return 0;
    }
		
	AudioEngine::Result WWiseAudioEngine::OutputSetVolume(Index const output_index, Float const volume)
    {
        if (mOutputIds.size() == 0)
            OutputRefresh();
        OutputId const& output = mOutputIds.at(output_index);
        if (AK_Success != AK::SoundEngine::SetOutputVolume( output.device_id, volume))
            return -1;
        return 0;
	}

    void WWiseAudioEngine::OutputRefresh()
    {
        mOutputIds.clear();
        static std::vector<const char*> supported_sharesets;
    
        if (supported_sharesets.empty())
        {
            supported_sharesets.push_back("System");
#if defined(AK_PS4)
            supported_sharesets.push_back("Pad_Output");
#elif defined(AK_GGP)
            // To route all sounds to the voice chat audio endpoint.
            // This would usually be a secondary output output but in this demo
            // you can switch between System and Communication_Output to test
            // either the game audio endpoint or the voice chat endpoint individually.
            supported_sharesets.push_back("Communication_Output");
#elif defined(AK_XBOX)
            // XboxOne supports the following sharesets but can use and will list the same outputs for all of them.
            // So only keep the System sink to prevent redundancy.
            //supported_sharesets.push_back("Controller_Headphones");
            //supported_sharesets.push_back("DVR_Bypass");
#endif
        }

        const AkInitSettings* initSettings = AK::SoundEngine::GetGlobalPluginContext()->GetInitSettings();
        //AkOutputDeviceID idCurrent = AK::SoundEngine::GetOutputID((AkUniqueID)0, 0 /*Default for this shareset*/);

        for (const auto& shareset_name : supported_sharesets)
        {
            AkUInt32 shareset_id = AK::SoundEngine::GetIDFromString(shareset_name);
            AkUInt32 output_count = 0;
            AKRESULT res = AK::SoundEngine::GetDeviceList(shareset_id, output_count, nullptr);
            std::vector<AkDeviceDescription> outputs(output_count);
            AK::SoundEngine::GetDeviceList(shareset_id, output_count, outputs.data());

            for (AkUInt32 i = 0; i < output_count; ++i)
            {
                if (outputs[i].deviceStateMask == AkDeviceState_Active)
                {
                    char* outputName = NULL;
                    CONVERT_OSCHAR_TO_CHAR(outputs[i].deviceName, outputName);
                    char name[AK_MAX_PATH] = { 0 };
                    snprintf(name, AK_MAX_PATH, "%s - %s", shareset_name, outputName);

                    if (outputs[i].isDefaultDevice && shareset_id == initSettings->settingsMainOutput.audioDeviceShareset && outputs[i].idDevice == initSettings->settingsMainOutput.idDevice)
                        mOutputDefault = static_cast<int>(mOutputIds.size());
                    mOutputIds.push_back(OutputId(shareset_name, shareset_id, outputs[i].idDevice, name));
                }
            }
        }
    }

    // spatial audio API
    AudioEngine::Result WWiseAudioEngine::SpatialSetOutside(std::string const & reverbAuxBus
        , Float const reverbLevel, Float const auxSendLevelToSelf)
    {
        AkRoomParams params;
        params.Front = {0.0f, 0.0f, 1.0f};
        params.Up = {0.0f, 1.0f, 0.0f};
        params.ReverbAuxBus = AK::SoundEngine::GetIDFromString(reverbAuxBus.c_str());
        params.ReverbLevel = reverbLevel;
        params.TransmissionLoss = 0.0f;
        params.RoomGameObj_AuxSendLevelToSelf = auxSendLevelToSelf;
        params.RoomGameObj_KeepRegistered = false; 
        params.GeometryInstanceID = AkGeometryInstanceID(); // Invalid ID - no geometry for outside.
        params.RoomPriority = 0; // lowest priority
        if (AK_Success != AK::SpatialAudio::SetRoom(AK::SpatialAudio::kOutdoorRoomID, params, "_Outside_"))
        {
            LOG_ERROR("Set outside room.");
            return -1;
        }
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialGetGeometry(GeometryID const geometry_id)
    {
        MapGeometry::iterator const & it = mGeometries.find(geometry_id);
        if(it != mGeometries.end())
        {
            if(nullptr == it->second)
                return -1;
            ++(it->second->ref_count);
            return 0;
        }
        return -1;
    }
    
    AudioEngine::Result WWiseAudioEngine::SpatialCreateGeometry(GeometryID const geometry_id
        , std::vector<Float3> const & vertexes_cm, std::vector<Short4> const & triangles, std::vector<Surface> const & surfaces
        , bool const enableDiffraction, bool const enableDiffractionOnBoundaryEdges)
    {
        if (false == mHasBegan)
            return -1;

        // ref count of geometries
        if (0 == SpatialGetGeometry(geometry_id))
            return 0;

        // check vertexes and triangles
        if(0 == vertexes_cm.size() || 0 == triangles.size())
            return -1;

        // convert vertex position unit to meter
        BoundingBox bbox_cm(vertexes_cm.at(0), Float3(0, 0, 0));
        std::vector<cross::Float3> vertexes_meter(vertexes_cm.size());
        for(size_t vertexIndex = 0 ; vertexIndex < vertexes_cm.size() ; ++vertexIndex)
        {
            Float3 vertex_cm = vertexes_cm.at(vertexIndex);
            vertexes_meter.at(vertexIndex) = vertex_cm * DEFAULT_LENGTH_TO_METER;
            bbox_cm.Encapsulate(vertex_cm);
        }

        // convert surfaces
        std::vector<AkAcousticSurface> surf(surfaces.size());
        for (size_t surface_index = 0; surface_index < surfaces.size(); ++surface_index)
        {
            Surface const& sur = surfaces.at(surface_index);
            AkAcousticSurface aks;
            aks.textureID = sur.texture_id;
            aks.transmissionLoss = sur.transmission_loss;
            aks.strName = nullptr;
            surf.at(surface_index) = aks;
        }

        // build params
        AkGeometryParams params;
        params.Vertices = reinterpret_cast<AkVertex *>(const_cast<Float3 *>(vertexes_meter.data()));
        params.NumVertices = static_cast<AkVertIdx>(vertexes_meter.size());
        params.Triangles = reinterpret_cast<AkTriangle *>(const_cast<Short4 *>(triangles.data()));
        params.NumTriangles = static_cast<AkTriIdx>(triangles.size());
        params.Surfaces = surf.data();
        params.NumSurfaces = static_cast<AkSurfIdx>(surfaces.size());
        params.EnableDiffraction = enableDiffraction;
        params.EnableDiffractionOnBoundaryEdges = enableDiffractionOnBoundaryEdges;

        // set geometry
        if (AK_Success != AK::SpatialAudio::SetGeometry(geometry_id, params))
            return -1;

        // record the new geometry
        Geometry * geometry = new Geometry();
        geometry->ref_count = 1;
        geometry->vertexes_cm = vertexes_cm;
        geometry->triangles = triangles;
        geometry->bbox_cm = bbox_cm;
        mGeometries.insert(std::make_pair(geometry_id, std::move(geometry)));
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialDestroyGeometry(GeometryID const geometry_id)
    {
        if (false == mHasBegan)
            return 0;

        MapGeometry::iterator const & it = mGeometries.find(geometry_id);
        if (it != mGeometries.end())
        {
            --(it->second->ref_count);
            if (0 == it->second->ref_count)
            {
                delete it->second;
                mGeometries.erase(it);
                if (AK_Success != AK::SpatialAudio::RemoveGeometry(geometry_id))
                    return -1;
            }
        }
        else
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialCreateObstacle(ObstacleID const obstacle_id, GeometryID const geometry_id
        , Double4x4 const & transform_cm
        , bool const useForReflectionAndDiffraction, bool const bypassPortalSubtraction, bool const isSolid)
    {
        if (false == mHasBegan)
            return -1;

        Double3 position_cm;
        Float3 front1, up1;
        GetFrontAndUpNormalized(transform_cm, position_cm, front1, up1);

        AkWorldTransform trans;
        trans.SetPosition({DEFAULT_LENGTH_TO_METER * position_cm.x, DEFAULT_LENGTH_TO_METER * position_cm.y, DEFAULT_LENGTH_TO_METER * position_cm.z});
        trans.SetOrientation({front1.x, front1.y, front1.z}, {up1.x, up1.y, up1.z});

        Double3 scale, translation_cm;
        Quaternion64 rotation;
        transform_cm.Decompose(scale, rotation, translation_cm);
        //translation_cm *= DEFAULT_LENGTH_TO_METER; // not used

        AkGeometryInstanceParams params;
        params.PositionAndOrientation = trans;
        params.Scale = {static_cast<AkReal32>(scale.x), static_cast<AkReal32>(scale.y), static_cast<AkReal32>(scale.z)};
        params.GeometrySetID = geometry_id;
        params.UseForReflectionAndDiffraction = useForReflectionAndDiffraction;
        params.BypassPortalSubtraction = bypassPortalSubtraction;
        params.IsSolid = isSolid;
        //LOG_INFO("WWiseAudioEngine::SpatialCreateObstacle p({:6.1f}, {:6.1f}, {:6.1f}) f({:6.1f}, {:6.1f}, {:6.1f}) u({:6.1f}, {:6.1f}, {:6.1f}) s({:6.1f}, {:6.1f}, {:6.1f})"
        //    , position.x, position.y, position.z, front.x, front.y, front.z, up.x, up.y,up.z, scale.x, scale.y, scale.z);
        if (AK_Success != AK::SpatialAudio::SetGeometryInstance(obstacle_id, params))
            return -1;
        mObstacles.insert(obstacle_id);
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialDestroyObstacle(ObstacleID const obstacle_id)
    {
        if (false == mHasBegan)
            return 0;

        if (AK_Success != AK::SpatialAudio::RemoveGeometryInstance(obstacle_id))
            return -1;
        mObstacles.erase(obstacle_id);
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialCreateRoom(RoomID const room_id, GeometryID const geometry_id
        , Double4x4 const & transform_cm
        , bool const useForReflectionAndDiffraction, bool const bypassPortalSubtraction, bool const isSolid
        , std::string const & reverbAuxBus, Float const reverbLevel, Float const transmissionLoss
        , Float const auxSendLevelToSelf, Float const priority)
    {
        if (false == mHasBegan)
            return -1;

        Double3 position_cm;
        Float3 front1, up1;
        GetFrontAndUpNormalized(transform_cm, position_cm, front1, up1);

        Double3 scale;
        {
            AkWorldTransform trans;
            trans.SetPosition({DEFAULT_LENGTH_TO_METER * position_cm.x, DEFAULT_LENGTH_TO_METER * position_cm.y, DEFAULT_LENGTH_TO_METER * position_cm.z});
            trans.SetOrientation({front1.x, front1.y, front1.z}, {up1.x, up1.y, up1.z});

            Double3 translation_cm;
            Quaternion64 rotation;
            transform_cm.Decompose(scale, rotation, translation_cm);
            //translation_cm *= DEFAULT_LENGTH_TO_METER; // not used

            AkGeometryInstanceParams params;
            params.PositionAndOrientation = trans;
            params.Scale = {static_cast<AkReal32>(scale.x), static_cast<AkReal32>(scale.y), static_cast<AkReal32>(scale.z)};
            params.GeometrySetID = geometry_id;
            params.UseForReflectionAndDiffraction = useForReflectionAndDiffraction;
            params.BypassPortalSubtraction = bypassPortalSubtraction;
            params.IsSolid = isSolid;
            if (AK_Success != AK::SpatialAudio::SetGeometryInstance(room_id, params))
                return -1;
        }
        {
            AkRoomParams params;
            params.Front = {front1.x, front1.y, front1.z};
            params.Up = {up1.x, up1.y, up1.z};
            params.ReverbAuxBus = AK::SoundEngine::GetIDFromString(reverbAuxBus.c_str());
            params.ReverbLevel = reverbLevel;
            params.TransmissionLoss = transmissionLoss;
            params.RoomGameObj_AuxSendLevelToSelf = auxSendLevelToSelf;
            params.RoomGameObj_KeepRegistered = true; //HERE
            params.GeometryInstanceID = room_id;
            params.RoomPriority = priority;
            if (AK_Success != AK::SpatialAudio::SetRoom(room_id, params))
                return -1;
        }
        {
            Room room;
            room.geometryId = geometry_id;
            room.transform_cm = transform_cm;
            room.inverseTransform_cm = transform_cm.Inverted();
            room.scale = scale;
            mRooms.insert(std::make_pair(room_id, std::move(room)));
        }
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialDestroyRoom(RoomID const room_id)
    {
        if (false == mHasBegan)
            return 0;

        if (AK_Success != AK::SpatialAudio::RemoveRoom(room_id)) return -1;
        if (AK_Success != AK::SpatialAudio::RemoveGeometryInstance(room_id)) return -1;

        {
            MapRoom::const_iterator const & it = mRooms.find(room_id);
            if(it == mRooms.end())
                return -1;
            mRooms.erase(it);
        }
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialCreatePortal(PortalID const portal_id
        , Float3 const & extent_cm, Double4x4 const & transform_cm)
    {
        if (false == mHasBegan)
            return -1;

        // create protal later, after all the rooms are setup.
        Portal portal;
        portal.extent_cm = extent_cm;
        portal.transform_cm = transform_cm;
        mPortals.insert(std::make_pair(portal_id, std::move(portal)));
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialDestroyPortal(PortalID const portal_id)
    {
        if (false == mHasBegan)
            return 0;

        MapPortal::const_iterator const & it = mPortals.find(portal_id);
        if(it != mPortals.end())
            mPortals.erase(it);
        else
        {
            if (AK_Success != AK::SpatialAudio::RemovePortal(portal_id))
                return -1;
        }
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialSetListener(ObjectID const object_id) const
    {
        if (AK_Success != AK::SpatialAudio::RegisterListener(object_id))
            return -1;
        return 0;
    }

    AudioEngine::Result WWiseAudioEngine::SpatialUnsetListener(ObjectID const object_id) const
    {
        if (AK_Success != AK::SpatialAudio::UnregisterListener(object_id))
            return -1;
        return 0;
    }

//#pragma optimize( "", off )
    bool WWiseAudioEngine::IsInGeometry(Float3 const & point_cm, Geometry const & geometry)
    {
        Float2 const point2_cm = Float2(point_cm.x, point_cm.y);
        int positiveCount = 0;
        int negativeCount = 0;
        if(geometry.bbox_cm.Contains(point_cm) == ContainmentContain)
        {
            for(size_t i = 0; i < geometry.triangles.size(); ++i)
            {
                Short4 const & triangle = geometry.triangles[i];
                Float3 const & va3 = geometry.vertexes_cm[triangle.x];
                Float3 const & vb3 = geometry.vertexes_cm[triangle.y];
                Float3 const & vc3 = geometry.vertexes_cm[triangle.z];
                Float2 const va2 = Float2(va3.x, va3.y);
                Float2 const vb2 = Float2(vb3.x, vb3.y);
                Float2 const vc2 = Float2(vc3.x, vc3.y);

                if(IsPointInTriangle(point2_cm, va2, vb2, vc2))
                {
                    Float4 const& plane = GetPlaneEquation(va3, vb3, vc3);
                    float const z = GetZFromPlane(point2_cm, plane);
                    if(z > point_cm.z)
                        ++positiveCount;
                    else
                        ++negativeCount;
                }
            }
            if ((positiveCount % 2) && (negativeCount % 2))
                return true;
        }
        return false;
    }

    bool WWiseAudioEngine::DetermineRooms(Double3 const & from_cm, Double3 const & to_cm, RoomID & fromRoom, RoomID & toRoom) const
    {
        for(std::pair<RoomID, Room> const & rooma : mRooms)
        {
            // transform point to room local space
            Double3 const localFrom64_cm = Double4x4::TransformPointF3(rooma.second.inverseTransform_cm, from_cm);
            Double3 const localTo64_cm = Double4x4::TransformPointF3(rooma.second.inverseTransform_cm, to_cm);

            // it should be OK to use 32 bit float for intersection, because the intersection is done in local space
            Float3 const localFrom_cm = Float3(static_cast<float>(localFrom64_cm.x), static_cast<float>(localFrom64_cm.y), static_cast<float>(localFrom64_cm.z));
            Float3 const localTo_cm = Float3(static_cast<float>(localTo64_cm.x), static_cast<float>(localTo64_cm.y), static_cast<float>(localTo64_cm.z));

            // check if point is in bounding box
            if(rooma.second.geometryId != -1)
            {
                MapGeometry::const_iterator const & geometry = mGeometries.find(rooma.second.geometryId);
                if(geometry != mGeometries.end())
                {
                    if(geometry->second)
                    {
                        if(IsInGeometry(localFrom_cm, *geometry->second))
                        {
                            if(-1 == fromRoom)
                                fromRoom = rooma.first;
                            else
                            {
                                // determine whick room to use with their volume,
                                // although this method is not accurate, 
                                // it is good enough for now and can cover most cases.
                                Float3 const exta = geometry->second->bbox_cm.GetExtent();
                                float const vola = exta.x * exta.y * exta.z
                                    * static_cast<float>(rooma.second.scale.x) * static_cast<float>(rooma.second.scale.y) * static_cast<float>(rooma.second.scale.z);

                                MapRoom::const_iterator const & roomb = mRooms.find(fromRoom);
                                if(roomb != mRooms.end())
                                {
                                    MapGeometry::const_iterator const & geob = mGeometries.find(roomb->second.geometryId);
                                    if(geometry != mGeometries.end())
                                    {
                                        if(geometry->second)
                                        {
                                            Float3 const extb = geob->second->bbox_cm.GetExtent();
                                            float const volb = extb.x * extb.y * extb.z
                                                * static_cast<float>(roomb->second.scale.x) * static_cast<float>(roomb->second.scale.y) * static_cast<float>(roomb->second.scale.z);
                                            if(vola < volb)
                                                fromRoom = rooma.first;
                                        }
                                        else fromRoom = rooma.first;
                                    }
                                    else fromRoom = rooma.first;
                                }
                                else fromRoom = rooma.first;
                            }
                        }

                        if(IsInGeometry(localTo_cm, *geometry->second))
                        {
                            if(-1 == toRoom)
                                toRoom = rooma.first;
                            else
                            {
                                // determine whick room to use with their volume,
                                // although this method is not accurate, 
                                // it is good enough for now and can cover most cases.
                                Float3 const exta = geometry->second->bbox_cm.GetExtent();
                                float const vola = exta.x * exta.y * exta.z
                                    * static_cast<float>(rooma.second.scale.x) * static_cast<float>(rooma.second.scale.y) * static_cast<float>(rooma.second.scale.z);

                                MapRoom::const_iterator const & roomb = mRooms.find(toRoom);
                                if(roomb != mRooms.end())
                                {
                                    MapGeometry::const_iterator const & geob = mGeometries.find(roomb->second.geometryId);
                                    if(geometry != mGeometries.end())
                                    {
                                        if(geometry->second)
                                        {
                                            Float3 const extb = geob->second->bbox_cm.GetExtent();
                                            float const volb = extb.x * extb.y * extb.z
                                                * static_cast<float>(roomb->second.scale.x) * static_cast<float>(roomb->second.scale.y) * static_cast<float>(roomb->second.scale.z);
                                            if(vola < volb)
                                                toRoom = rooma.first;
                                        }
                                        else toRoom = rooma.first;
                                    }
                                    else toRoom = rooma.first;
                                }
                                else toRoom = rooma.first;
                            }
                        }
                    }
                }
            }
        }
        return true;
    }
//#pragma optimize( "", on )
}   // namespace cross