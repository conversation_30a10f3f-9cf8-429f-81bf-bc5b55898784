if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_WIN32})

    project(XInputInterface)
    file(GLOB_RECURSE XINPUT_H ${CMAKE_CURRENT_SOURCE_DIR}/*.h ${CMAKE_CURRENT_SOURCE_DIR}/*.hpp)
    file(GLOB_RECURSE XINPUT_S ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp ${CMAKE_CURRENT_SOURCE_DIR}/*.c)

    source_group("XInputInterface" FILES
        ${XINPUT_H}
        ${XINPUT_S})

    include(ModulesConfig)
    get_module_generated_code(${PROJECT_NAME} GENERATED_CODE)
    Set3rdLinkDirs()

    if(${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_ANDROID} OR ${CROSSENGINE_PLATFORM} STREQUAL ${CROSSENGINE_IOS})
        add_library(${PROJECT_NAME} STATIC ${XINPUT_H} ${XINPUT_S} ${GENERATED_CODE})
    else()
        add_library(${PROJECT_NAME} SHARED ${XINPUT_H} ${XINPUT_S} ${GENERATED_CODE})
    endif()

    set(dependItems CrossEngine CrossBase CECommon)
    ConfigureCEDepensModules("${dependItems}")
    SetOutput(${PROJECT_NAME})
    
    add_custom_command(TARGET ${PROJECT_NAME}
				POST_BUILD COMMAND xcopy /S /I /F /Y \"${CMAKE_CURRENT_SOURCE_DIR}/Dependencies/DLL/xinput1_3.dll\" \"$<TARGET_FILE_DIR:${PROJECT_NAME}>\")
endif()